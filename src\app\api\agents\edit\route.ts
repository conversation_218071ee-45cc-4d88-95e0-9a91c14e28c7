import { z } from 'zod'
import { vercelAIClient } from '@/lib/ai/vercel-ai-client'
import { withAIRoute, apiResponse, apiError } from '@/lib/api/middleware'
import { ValidationSchemas } from '@/lib/api/validation-schemas'
import { AI_MODELS, AI_TEMPERATURE } from '@/lib/config/ai-settings'
import { logger } from '@/lib/services/logger'

export const POST = withAIRoute(
  async (request, { user, supabase, body }) => {
    const { action, selectedText, beforeCursor, afterCursor, fullContent, prompt, customPrompt } = body as z.infer<typeof ValidationSchemas.editText>

    // For continuation actions, we need context, not selected text
    if (action === 'continue' && !beforeCursor && !fullContent) {
      return apiError('Context required for continuation', 400)
    }
    
    // For other actions, we need selected text
    if (action !== 'continue' && action !== 'custom' && !selectedText) {
      return apiError('No text selected', 400)
    }

    let systemPrompt = ''
    let userPrompt = ''

    switch (action) {
      case 'improve':
        systemPrompt = 'You are an expert writing editor. Improve the given text by enhancing clarity, flow, and readability while maintaining the original meaning and voice.'
        userPrompt = `Please improve this text:\n\n"${selectedText}"\n\nProvide only the improved version without explanations.`
        break
        
      case 'expand':
        systemPrompt = 'You are an expert writing editor. Expand the given text by adding more detail, description, or development while maintaining the original style and voice.'
        userPrompt = `Please expand this text with more detail:\n\n"${selectedText}"\n\nProvide only the expanded version without explanations.`
        break
        
      case 'rewrite':
        systemPrompt = 'You are an expert writing editor. Completely rewrite the given text while maintaining the core meaning and improving the style and flow.'
        userPrompt = `Please rewrite this text:\n\n"${selectedText}"\n\nProvide only the rewritten version without explanations.`
        break
        
      case 'suggest':
        systemPrompt = 'You are an expert writing editor. Provide 2-3 alternative ways to write the given text, each with a different style or approach.'
        userPrompt = `Please provide alternative versions of this text:\n\n"${selectedText}"\n\nFormat as: Option 1: [text]\nOption 2: [text]\nOption 3: [text]`
        break
        
      case 'continue':
        systemPrompt = 'You are an expert creative writer. Continue the story naturally from where it left off, maintaining the same style, tone, and voice. Write 1-3 paragraphs that flow seamlessly from the existing text.'
        const contextBefore = beforeCursor || fullContent?.substring(Math.max(0, (fullContent?.length || 0) - 1000))
        const contextAfter = afterCursor || ''
        userPrompt = `Continue writing from this point:\n\nContext before: "${contextBefore}"\n${contextAfter ? `\nContext after: "${contextAfter}"` : ''}\n\nContinue the story naturally. Provide only the new text without explanations.`
        break
        
      case 'summarize':
        systemPrompt = 'You are an expert writing editor. Create a concise summary of the given text, capturing the key points and main ideas.'
        userPrompt = `Please summarize this text:\n\n"${selectedText}"\n\nProvide only the summary without explanations.`
        break
        
      case 'rephrase':
        systemPrompt = 'You are an expert writing editor. Rephrase the given text to say the same thing in a different way, maintaining the meaning but changing the words and structure.'
        userPrompt = `Please rephrase this text:\n\n"${selectedText}"\n\nProvide only the rephrased version without explanations.`
        break
        
      case 'custom':
        if (!customPrompt && !prompt) {
          return apiError('Custom prompt required', 400)
        }
        systemPrompt = 'You are an expert writing editor. Follow the user\'s instructions to edit the given text.'
        const instruction = customPrompt || prompt
        const textToEdit = selectedText || beforeCursor || ''
        userPrompt = `${textToEdit ? `Original text: "${textToEdit}"\n\n` : ''}Instructions: ${instruction}\n\nProvide only the result without explanations.`
        break
        
      default:
        return apiError('Invalid action', 400)
    }

    // Check if client accepts streaming
    const acceptsStreaming = request.headers.get('accept') === 'text/event-stream'
    
    let editedText: string
    let tokensUsed: number | undefined

    try {
      if (acceptsStreaming) {
        // Use streaming for real-time feedback
        let fullText = ''
        const stream = await vercelAIClient.streamTextWithFallback(
          userPrompt,
          {
            systemPrompt,
            model: AI_MODELS.TASKS.EDITING,
            temperature: AI_TEMPERATURE.TASKS.EDITING || 0.3,
            maxTokens: 2000
          },
          {
            onToken: (token) => {
              fullText += token
            },
            onComplete: (content) => {
              fullText = content
            }
          }
        )
        editedText = fullText
      } else {
        // Non-streaming response with fallback
        editedText = await vercelAIClient.generateTextWithFallback(
          userPrompt,
          {
            systemPrompt,
            model: AI_MODELS.TASKS.EDITING,
            temperature: AI_TEMPERATURE.TASKS.EDITING || 0.3,
            maxTokens: 2000
          }
        )
      }
    } catch (error) {
      logger.error('Text editing failed:', error)
      return apiError('Failed to generate edited text', 500)
    }

    if (!editedText) {
      return apiError('No response from AI', 500)
    }

    // Log the editing session
    await supabase.from('editing_sessions').insert({
      user_id: user.id,
      selected_text: selectedText || beforeCursor || fullContent?.substring(0, 1000) || '',
      ai_prompt: customPrompt || prompt || action,
      ai_response: editedText,
      action_type: action
    })

    return apiResponse({
      success: true,
      editedText,
      suggestion: editedText, // For backward compatibility
      action,
      tokensUsed
    })
  },
  'generation',
  {
    validateBody: ValidationSchemas.editText
  }
)