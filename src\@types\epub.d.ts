declare module 'epub' {
  interface TocItem {
    title: string;
    href: string;
    id?: string;
    order?: number;
  }

  interface SpineItem {
    id: string;
    href: string;
    'media-type': string;
  }

  interface Manifest {
    [id: string]: {
      href: string;
      'media-type': string;
    };
  }

  interface Metadata {
    title?: string;
    creator?: string;
    description?: string;
    language?: string;
    publisher?: string;
    date?: string;
    [key: string]: string | undefined;
  }

  interface EPubOptions {
    imageroot?: string;
    linkroot?: string;
  }

  class EPub {
    constructor(epubPath: string, options?: EPubOptions);
    
    metadata: Metadata;
    manifest: Manifest;
    spine: SpineItem[];
    flow: SpineItem[];
    toc: TocItem[];
    
    parse(): void;
    getChapter(chapterId: string, callback: (err: Error | null, text?: string) => void): void;
    getChapterRaw(chapterId: string, callback: (err: Error | null, data?: Buffer) => void): void;
    getImage(imageId: string, callback: (err: Error | null, data?: Buffer, mimeType?: string) => void): void;
    
    on(event: 'end', callback: () => void): void;
    on(event: 'error', callback: (err: Error) => void): void;
    on(event: string, callback: (...args: unknown[]) => void): void;
  }

  export = EPub;
}