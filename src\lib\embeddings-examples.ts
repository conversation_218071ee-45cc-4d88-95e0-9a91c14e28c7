/**
 * Example usage patterns for the content embeddings functionality
 * This file demonstrates how to use the embedding operations in practice
 */

import { db } from './db/client';
import { logger } from '@/lib/services/logger';

import type { ContentType } from './db/types';

// Example: Create embedding for a chapter
export async function createChapterEmbedding(
  projectId: string,
  chapterId: string,
  chapterContent: string,
  chapterTitle?: string
) {
  try {
    const embedding = await db.embeddings.create({
      project_id: projectId,
      content_type: 'chapter' as ContentType,
      content_id: chapterId,
      text_content: chapterContent,
      metadata: {
        title: chapterTitle,
        type: 'full_chapter',
        word_count: chapterContent.split(/\s+/).length,
      },
    });

    logger.info('Chapter embedding created:', embedding.id);
    return embedding;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to create chapter embedding:', errorMessage);
    throw new Error(`Failed to create chapter embedding: ${errorMessage}`);
  }
}

// Example: Create embedding for a character
export async function createCharacterEmbedding(
  projectId: string,
  characterId: string,
  characterData: {
    name: string;
    description: string;
    backstory: string;
    personality: string;
  }
) {
  try {
    // Combine character information into searchable text
    const textContent = `
      Name: ${characterData.name}
      Description: ${characterData.description}
      Backstory: ${characterData.backstory}
      Personality: ${characterData.personality}
    `.trim();

    const embedding = await db.embeddings.create({
      project_id: projectId,
      content_type: 'character' as ContentType,
      content_id: characterId,
      text_content: textContent,
      metadata: {
        name: characterData.name,
        type: 'character_profile',
      },
    });

    logger.info('Character embedding created:', embedding.id);
    return embedding;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to create character embedding:', errorMessage);
    throw new Error(`Failed to create character embedding: ${errorMessage}`);
  }
}

// Example: Search for similar content
export async function searchSimilarContent(
  projectId: string,
  query: string,
  contentType?: ContentType
) {
  try {
    const results = await db.embeddings.search(projectId, query, {
      contentType,
      similarityThreshold: 0.7,
      matchCount: 10,
    });

    logger.info(`Found ${results.length} similar content pieces`);
    return results.map((result: {
      id: string;
      content_type: string;
      content_id: string;
      similarity: number;
      text_content: string;
      metadata: Record<string, unknown>;
    }) => ({
      id: result.id,
      contentType: result.content_type,
      contentId: result.content_id,
      similarity: result.similarity,
      text: result.text_content.substring(0, 200) + '...', // Preview
      metadata: result.metadata,
    }));
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to search similar content:', errorMessage);
    throw new Error(`Failed to search similar content: ${errorMessage}`);
  }
}

// Example: Batch create embeddings for project indexing
export async function indexProjectContent(projectId: string) {
  try {
    // Get all chapters
    const chapters = await db.chapters.getAll(projectId);
    
    // Get all characters
    const characters = await db.characters.getAll(projectId);
    
    // Get all story bible entries
    const storyBibleEntries = await db.storyBible.getAll(projectId);

    // Prepare batch embedding data
    const embeddingDataArray = [];

    // Add chapters
    for (const chapter of chapters) {
      if (chapter.content) {
        embeddingDataArray.push({
          project_id: projectId,
          content_type: 'chapter' as ContentType,
          content_id: chapter.id,
          text_content: chapter.content,
          metadata: {
            title: chapter.title,
            chapter_number: chapter.chapter_number,
            word_count: chapter.actual_word_count,
          },
        });
      }
    }

    // Add characters
    for (const character of characters) {
      const textContent = `
        Name: ${character.name}
        Role: ${character.role}
        Description: ${character.description || ''}
        Backstory: ${character.backstory || ''}
      `.trim();

      embeddingDataArray.push({
        project_id: projectId,
        content_type: 'character' as ContentType,
        content_id: character.id,
        text_content: textContent,
        metadata: {
          name: character.name,
          role: character.role,
        },
      });
    }

    // Add story bible entries
    for (const entry of storyBibleEntries) {
      if (entry.entry_data && typeof entry.entry_data === 'object') {
        const textContent = JSON.stringify(entry.entry_data);
        
        embeddingDataArray.push({
          project_id: projectId,
          content_type: 'story_bible' as ContentType,
          content_id: entry.id,
          text_content: textContent,
          metadata: {
            entry_type: entry.entry_type,
            entry_key: entry.entry_key,
            chapter_introduced: entry.chapter_introduced,
          },
        });
      }
    }

    // Create embeddings in batch
    if (embeddingDataArray.length > 0) {
      const embeddings = await db.embeddings.createBatch(embeddingDataArray);
      logger.info(`Created ${embeddings.length} embeddings for project ${projectId}`);
      return embeddings;
    } else {
      logger.info('No content found to index for project', projectId);
      return [];
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to index project content:', errorMessage);
    throw new Error(`Failed to index project content: ${errorMessage}`);
  }
}

// Example: Find character moments or relationships
export async function findCharacterMoments(
  projectId: string,
  characterName: string,
  emotion?: string
) {
  try {
    const query = emotion 
      ? `${characterName} ${emotion} emotional moment dialogue interaction`
      : `${characterName} character development moment dialogue`;

    const results = await db.embeddings.search(projectId, query, {
      contentType: 'chapter',
      similarityThreshold: 0.6,
      matchCount: 15,
    });

    return results.filter((result: {
      text_content: string;
    }) => 
      result.text_content.toLowerCase().includes(characterName.toLowerCase())
    );
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to find character moments:', errorMessage);
    throw new Error(`Failed to find character moments: ${errorMessage}`);
  }
}

// Example: Update embedding when content changes
export async function updateContentEmbedding(
  projectId: string,
  contentType: ContentType,
  contentId: string,
  newContent: string,
  metadata?: Record<string, unknown>
) {
  try {
    // Find existing embedding
    const existingEmbeddings = await db.embeddings.getByProject(projectId, contentType);
    const existingEmbedding = existingEmbeddings.find(e => e.content_id === contentId);

    if (existingEmbedding) {
      // Update existing embedding
      const updated = await db.embeddings.update(existingEmbedding.id, {
        text_content: newContent,
        metadata,
      });
      logger.info('Updated embedding:', updated.id);
      return updated;
    } else {
      // Create new embedding
      const created = await db.embeddings.create({
        project_id: projectId,
        content_type: contentType,
        content_id: contentId,
        text_content: newContent,
        metadata,
      });
      logger.info('Created new embedding:', created.id);
      return created;
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to update content embedding:', errorMessage);
    throw new Error(`Failed to update content embedding: ${errorMessage}`);
  }
}

// Example: Clean up embeddings when content is deleted
export async function cleanupContentEmbeddings(
  projectId: string,
  contentType: ContentType,
  contentId: string
) {
  try {
    await db.embeddings.deleteByContent(projectId, contentType, contentId);
    logger.info(`Cleaned up embeddings for ${contentType}:${contentId}`);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to cleanup content embeddings:', errorMessage);
    throw new Error(`Failed to cleanup content embeddings: ${errorMessage}`);
  }
}

// Example: Semantic search for writing assistance
export async function getWritingInspiration(
  projectId: string,
  prompt: string,
  excludeContentIds: string[] = []
) {
  try {
    const results = await db.embeddings.search(projectId, prompt, {
      similarityThreshold: 0.5,
      matchCount: 20,
    });

    // Filter out excluded content and return diverse results
    return results
      .filter((result: { content_id: string }) => !excludeContentIds.includes(result.content_id))
      .slice(0, 10)
      .map((result: {
        content_type: string;
        text_content: string;
        similarity: number;
        metadata: Record<string, unknown>;
      }) => ({
        type: result.content_type,
        content: result.text_content,
        similarity: result.similarity,
        metadata: result.metadata,
      }));
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to get writing inspiration:', errorMessage);
    throw new Error(`Failed to get writing inspiration: ${errorMessage}`);
  }
}