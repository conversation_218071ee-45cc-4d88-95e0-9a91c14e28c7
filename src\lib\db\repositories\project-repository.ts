import { SupabaseClient } from '@supabase/supabase-js';
import { BaseRepository, RepositoryResult, RepositoryListResult } from './base-repository';
import { Database } from '@/lib/db/types';

type Project = Database['public']['Tables']['projects']['Row'];
type ProjectInsert = Database['public']['Tables']['projects']['Insert'];
type ProjectUpdate = Database['public']['Tables']['projects']['Update'];

export interface ProjectWithStats extends Project {
  chapter_count?: number;
  character_count?: number;
  last_activity?: string;
}

export class ProjectRepository extends BaseRepository<Project> {
  constructor(supabase: SupabaseClient) {
    super(supabase, 'projects');
  }
  
  /**
   * Find all projects for a user
   */
  async findByUserId(userId: string): Promise<RepositoryListResult<Project>> {
    return this.findAll({ user_id: userId });
  }
  
  /**
   * Find project with related data
   */
  async findByIdWithRelations(
    id: string,
    userId: string
  ): Promise<RepositoryResult<ProjectWithStats>> {
    return this.executeQuery<ProjectWithStats>((query) =>
      query
        .select(`
          *,
          chapters (count),
          characters (count)
        `)
        .eq('id', id)
        .eq('user_id', userId)
        .single()
    );
  }
  
  /**
   * Find active projects for a user
   */
  async findActiveProjects(userId: string): Promise<RepositoryListResult<Project>> {
    return this.executeQuery<Project[]>((query) =>
      query
        .select(this.selectColumns)
        .eq('user_id', userId)
        .eq('status', 'active')
        .is('deleted_at', null)
        .order('updated_at', { ascending: false })
    );
  }
  
  /**
   * Update project word count
   */
  async updateWordCount(
    projectId: string,
    wordCount: number
  ): Promise<RepositoryResult<Project>> {
    return this.update(projectId, {
      current_word_count: wordCount,
      updated_at: new Date().toISOString()
    });
  }
  
  /**
   * Find projects by genre
   */
  async findByGenre(
    genre: string,
    userId?: string
  ): Promise<RepositoryListResult<Project>> {
    return this.executeQuery<Project[]>((query) => {
      let q = query
        .select(this.selectColumns)
        .eq('primary_genre', genre)
        .is('deleted_at', null);
        
      if (userId) {
        q = q.eq('user_id', userId);
      }
      
      return q.order('created_at', { ascending: false });
    });
  }
  
  /**
   * Get project statistics
   */
  async getProjectStats(projectId: string): Promise<RepositoryResult<{
    total_chapters: number;
    completed_chapters: number;
    total_characters: number;
    total_words: number;
    avg_chapter_length: number;
  }>> {
    // First get basic project data
    const projectResult = await this.findById(projectId);
    if (projectResult.error || !projectResult.data) {
      return { error: projectResult.error };
    }
    
    // Get chapter statistics
    const { data: chapters, error: chaptersError } = await this.supabase
      .from('chapters')
      .select('actual_word_count, status')
      .eq('project_id', projectId)
      .is('deleted_at', null);
      
    if (chaptersError) {
      return { error: new Error(chaptersError.message) };
    }
    
    // Get character count
    const { count: characterCount, error: charError } = await this.supabase
      .from('characters')
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId)
      .is('deleted_at', null);
      
    if (charError) {
      return { error: new Error(charError.message) };
    }
    
    const chapterData = chapters || [];
    const totalChapters = chapterData.length;
    const completedChapters = chapterData.filter(ch => ch.status === 'complete' || ch.status === 'final').length;
    const totalWords = chapterData.reduce((sum, ch) => sum + (ch.actual_word_count || 0), 0);
    const avgChapterLength = totalChapters > 0 ? Math.round(totalWords / totalChapters) : 0;
    
    return {
      data: {
        total_chapters: totalChapters,
        completed_chapters: completedChapters,
        total_characters: characterCount || 0,
        total_words: totalWords,
        avg_chapter_length: avgChapterLength
      }
    };
  }
  
  /**
   * Search projects by title or description
   */
  async searchProjects(
    userId: string,
    searchTerm: string
  ): Promise<RepositoryListResult<Project>> {
    return this.executeQuery<Project[]>((query) =>
      query
        .select(this.selectColumns)
        .eq('user_id', userId)
        .is('deleted_at', null)
        .or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
        .order('updated_at', { ascending: false })
    );
  }
  
  /**
   * Clone a project
   */
  async cloneProject(
    projectId: string,
    userId: string,
    newTitle: string
  ): Promise<RepositoryResult<Project>> {
    // Get original project
    const originalResult = await this.findById(projectId);
    if (originalResult.error || !originalResult.data) {
      return { error: originalResult.error || new Error('Project not found') };
    }
    
    const original = originalResult.data;
    
    // Create new project with cloned data
    const clonedData: Omit<ProjectInsert, 'id' | 'created_at' | 'updated_at'> = {
      user_id: userId,
      title: newTitle,
      description: original.description,
      primary_genre: original.primary_genre,
      secondary_genre: original.secondary_genre,
      target_word_count: original.target_word_count,
      target_chapters: original.target_chapters,
      current_word_count: 0,
      status: 'planning',
      settings: original.settings,
      metadata: {
        ...(original.metadata as object || {}),
        cloned_from: projectId,
        cloned_at: new Date().toISOString()
      }
    };
    
    return this.create(clonedData);
  }
}