import type { ProjectSelections } from './project'

export interface StoryStructure {
  title: string
  genre: string
  themes: string[]
  acts: {
    number: number
    title: string
    description: string
    keyEvents: string[]
    wordCount: number
  }[]
  conflicts: {
    type: 'internal' | 'external' | 'relational'
    description: string
    resolution?: string
  }[]
  timeline: {
    event: string
    chapter?: number
    importance: 'critical' | 'major' | 'minor'
  }[]
}

export interface CharacterProfile {
  id: string
  name: string
  role: 'protagonist' | 'antagonist' | 'supporting' | 'minor'
  description: string
  backstory: string
  personality: {
    traits: string[]
    motivations: string[]
    fears: string[]
    goals: string[]
  }
  physicalDescription: string
  relationships: {
    characterId: string
    type: string
    description: string
  }[]
  arc: {
    startingPoint: string
    transformation: string
    endingPoint: string
  }
  voiceCharacteristics: string[]
}

export interface ChapterOutline {
  chapterNumber: number
  title: string
  targetWordCount: number
  summary: string
  scenes: {
    id: string
    setting: string
    characters: string[]
    purpose: string
    conflict: string
    outcome: string
    estimatedWords: number
  }[]
  povCharacter?: string
  timeline: string
  keyEvents: string[]
  cliffhanger?: string
  transitions: {
    from?: string
    to?: string
  }
}

export interface ChapterContent {
  chapterNumber: number
  title: string
  content: string
  wordCount: number
  scenes: {
    id: string
    content: string
    wordCount: number
  }[]
  characterVoices: Record<string, string[]>
  consistencyNotes: string[]
}

export interface EditorialReview {
  chapterNumber: number
  overallScore: number
  consistency: {
    score: number
    issues: string[]
    suggestions: string[]
  }
  characterVoices: {
    score: number
    issues: string[]
    suggestions: string[]
  }
  plotProgression: {
    score: number
    issues: string[]
    suggestions: string[]
  }
  styleAndTone: {
    score: number
    issues: string[]
    suggestions: string[]
  }
  recommendations: string[]
  revisionPriority: 'low' | 'medium' | 'high'
}

export interface BookContext {
  projectId: string
  projectSelections: ProjectSelections
  storyStructure?: StoryStructure
  characters: CharacterProfile[]
  chapterOutlines?: ChapterOutline[]
  completedChapters: ChapterContent[]
  storyBible: {
    worldRules: Record<string, string>
    timeline: Array<{ event: string; chapter: number }>
    characterStates: Record<string, { lastAppearance?: number; [key: string]: unknown }>
    plotThreads: Array<{ id: string; description: string; status: 'active' | 'resolved' }>
  }
  currentChapter?: number
  metadata: {
    totalWordCount: number
    chaptersCompleted: number
    lastUpdated: string
  }
}

export interface AgentResponse<T> {
  success: boolean
  data?: T
  error?: string
  context?: Partial<BookContext>
  executionTime?: number
  tokensUsed?: number
}

export interface AgentConfig {
  model: string
  temperature: number
  maxTokens: number
  systemPrompt: string
}

export interface StoryArchitectInput {
  projectSelections: ProjectSelections
  storyPrompt: string
  targetWordCount: number
  targetChapters: number
}

export interface CharacterDeveloperInput {
  storyStructure: StoryStructure
  projectSelections: ProjectSelections
  characterRequirements?: {
    protagonistCount: number
    antagonistCount: number
    supportingCount: number
  }
}

export interface ChapterPlannerInput {
  storyStructure: StoryStructure
  characters: CharacterProfile[]
  targetWordCount: number
  targetChapters: number
  projectSelections: ProjectSelections
}

export interface WritingAgentInput {
  chapterOutline: ChapterOutline
  characters: CharacterProfile[]
  context: BookContext
  projectSelections: ProjectSelections
  previousChapterContent?: string
}

export interface EditorAgentInput {
  chapterContent: ChapterContent
  context: BookContext
  projectSelections: ProjectSelections
  chapterOutline: ChapterOutline
}
export type { ProjectSelections } from './project'