import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { BaseAgent } from '../base-agent';
import type { BookContext } from '../types';
import OpenAI from 'openai';

// Mock OpenAI
jest.mock('openai');

// Create a concrete implementation for testing
class TestAgent extends BaseAgent {
  async execute() {
    return { success: true };
  }
}

describe('BaseAgent', () => {
  let agent: TestAgent;
  let mockContext: BookContext;
  let mockOpenAI: jest.Mocked<OpenAI>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockContext = {
      projectId: 'test-project-id',
      settings: {
        primaryGenre: 'fantasy',
        secondaryGenres: ['adventure'],
        targetAudience: 'adult',
        writingStyle: 'descriptive',
        narrativeVoice: 'third-person',
        tense: 'past',
        pacing: 'medium',
        violenceLevel: 'moderate',
        romanceLevel: 'low',
        profanityLevel: 'mild',
        useDeepPOV: true,
        showDontTell: true,
        varyProse: true,
      },
      targetWordCount: 80000,
      targetChapters: 20,
    };

    mockOpenAI = {
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    } as unknown as jest.Mocked<OpenAI>;

    (OpenAI as jest.MockedClass<typeof OpenAI>).mockImplementation(() => mockOpenAI);
    
    agent = new TestAgent(mockContext);
  });

  describe('constructor', () => {
    it('should initialize with provided context', () => {
      expect(agent).toBeDefined();
      expect(agent['context']).toBe(mockContext);
    });
  });

  describe('createSystemPrompt', () => {
    it('should create a system prompt with agent name and instructions', () => {
      const prompt = agent['createSystemPrompt']('TestAgent', 'Test instructions');
      
      expect(prompt).toContain('TestAgent');
      expect(prompt).toContain('Test instructions');
      expect(prompt).toContain('fantasy');
      expect(prompt).toContain('80,000 words');
      expect(prompt).toContain('20 chapters');
    });

    it('should include writing techniques when enabled', () => {
      const prompt = agent['createSystemPrompt']('TestAgent', 'Test instructions');
      
      expect(prompt).toContain('Deep POV');
      expect(prompt).toContain('Show, Don\'t Tell');
      expect(prompt).toContain('Varied Prose');
    });

    it('should not include writing techniques when disabled', () => {
      mockContext.settings!.useDeepPOV = false;
      mockContext.settings!.showDontTell = false;
      mockContext.settings!.varyProse = false;
      
      agent = new TestAgent(mockContext);
      const prompt = agent['createSystemPrompt']('TestAgent', 'Test instructions');
      
      expect(prompt).not.toContain('Deep POV');
      expect(prompt).not.toContain('Show, Don\'t Tell');
      expect(prompt).not.toContain('Varied Prose');
    });
  });

  describe('createCompletion', () => {
    it('should call OpenAI API with correct parameters', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Test response',
            tool_calls: null,
          },
          finish_reason: 'stop',
        }],
        usage: {
          prompt_tokens: 100,
          completion_tokens: 50,
          total_tokens: 150,
        },
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const messages = [{ role: 'user' as const, content: 'Test message' }];
      const result = await agent['createCompletion'](messages);

      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: 'gpt-4-turbo-preview',
        messages,
        temperature: 0.7,
        max_tokens: 4000,
      });

      expect(result).toBe(mockResponse);
    });

    it('should include tools when provided', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: null,
            tool_calls: [{
              id: 'call_123',
              type: 'function',
              function: {
                name: 'test_function',
                arguments: '{"result": "success"}',
              },
            }],
          },
          finish_reason: 'tool_calls',
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const messages = [{ role: 'user' as const, content: 'Test message' }];
      const tools = [{
        type: 'function' as const,
        function: {
          name: 'test_function',
          description: 'Test function',
          parameters: { type: 'object', properties: {} },
        },
      }];

      await agent['createCompletion'](messages, tools);

      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({
          tools,
        })
      );
    });

    it('should handle API errors gracefully', async () => {
      const error = new Error('OpenAI API error');
      mockOpenAI.chat.completions.create.mockRejectedValue(error);

      const messages = [{ role: 'user' as const, content: 'Test message' }];

      await expect(agent['createCompletion'](messages)).rejects.toThrow('OpenAI API error');
    });
  });

  describe('buildStoryBibleContext', () => {
    it('should return empty string when no story structure or characters', () => {
      const context = agent['buildStoryBibleContext']();
      expect(context).toBe('');
    });

    it('should build context from story structure and characters', () => {
      mockContext.storyStructure = {
        title: 'Test Story',
        premise: 'A test premise',
        genre: 'fantasy',
        themes: ['courage', 'friendship'],
        acts: [],
        conflicts: [],
        timeline: [],
        worldBuilding: {
          setting: {
            timeForPeriod: 'medieval',
            locations: [],
            culture: 'European-inspired',
            technology: 'pre-industrial',
          },
          rules: ['Magic exists', 'Dragons are real'],
          history: [],
        },
        plotPoints: [],
      };

      mockContext.characters = {
        protagonists: [{
          id: '1',
          name: 'Hero',
          role: 'protagonist',
          appearance: 'Tall and brave',
          personality: {
            traits: ['brave', 'kind'],
            strengths: ['leadership'],
            weaknesses: ['impulsive'],
            fears: ['failure'],
            desires: ['peace'],
          },
          backstory: 'Born in a village',
          motivation: 'Save the world',
          arc: {
            type: 'positive_change',
            startingPoint: 'Naive',
            endingPoint: 'Wise',
            keyMoments: [],
            internalConflict: 'Self-doubt',
            externalConflict: 'Evil lord',
          },
          voice: {
            speakingStyle: 'Direct',
            vocabulary: 'Simple',
            mannerisms: ['Scratches head'],
          },
          relationships: [],
        }],
        antagonists: [],
        supporting: [],
        relationships: [],
      };

      agent = new TestAgent(mockContext);
      const context = agent['buildStoryBibleContext']();

      expect(context).toContain('Hero');
      expect(context).toContain('protagonist');
      expect(context).toContain('brave');
      expect(context).toContain('fantasy');
      expect(context).toContain('courage');
      expect(context).toContain('Magic exists');
    });
  });
});