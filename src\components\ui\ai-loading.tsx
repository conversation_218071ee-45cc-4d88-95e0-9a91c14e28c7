'use client';

import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface AILoadingProps {
  message?: string;
  submessage?: string;
  progress?: number;
  steps?: Array<{
    label: string;
    status: 'pending' | 'active' | 'complete';
  }>;
}

export function AILoading({ 
  message = "AI is thinking...", 
  submessage,
  progress,
  steps 
}: AILoadingProps) {
  return (
    <Card className="p-6 border-primary/20 bg-background/95 backdrop-blur">
      <div className="space-y-4">
        <div className="flex items-center justify-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="relative"
          >
            <Sparkles className="w-8 h-8 text-primary" />
            <motion.div
              className="absolute inset-0"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Sparkles className="w-8 h-8 text-primary opacity-30" />
            </motion.div>
          </motion.div>
        </div>

        <div className="text-center space-y-2">
          <p className="font-medium text-lg">{message}</p>
          {submessage && (
            <p className="text-sm text-muted-foreground">{submessage}</p>
          )}
        </div>

        {progress !== undefined && (
          <Progress value={progress} className="h-2" />
        )}

        {steps && (
          <div className="space-y-2">
            {steps.map((step, index) => (
              <div key={index} className="flex items-center gap-3">
                <div className={`w-2 h-2 rounded-full ${
                  step.status === 'complete' ? 'bg-primary' :
                  step.status === 'active' ? 'bg-primary animate-pulse' :
                  'bg-muted'
                }`} />
                <span className={`text-sm ${
                  step.status === 'complete' ? 'text-foreground' :
                  step.status === 'active' ? 'text-foreground font-medium' :
                  'text-muted-foreground'
                }`}>
                  {step.label}
                </span>
                {step.status === 'active' && (
                  <Loader2 className="w-3 h-3 animate-spin text-primary" />
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </Card>
  );
}

// Inline loading indicator for buttons
export function AIButtonLoading({ label = "Generating..." }: { label?: string }) {
  return (
    <>
      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
      {label}
    </>
  );
}

// Full-screen loading overlay
export function AILoadingOverlay({ 
  message = "AI agents are working...",
  submessage,
  onCancel
}: AILoadingProps & { onCancel?: () => void }) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex items-center justify-center p-4"
    >
      <motion.div
        initial={{ scale: 0.9 }}
        animate={{ scale: 1 }}
        className="max-w-md w-full"
      >
        <AILoading message={message} submessage={submessage} />
        {onCancel && (
          <button
            onClick={onCancel}
            className="mt-4 w-full text-sm text-muted-foreground hover:text-foreground transition-colors"
          >
            Cancel
          </button>
        )}
      </motion.div>
    </motion.div>
  );
}