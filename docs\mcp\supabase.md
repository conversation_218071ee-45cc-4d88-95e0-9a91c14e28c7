# Supabase MCP Server

The Supabase MCP server provides direct access to your Supabase database, authentication, and storage capabilities within Claude.

## Overview

Supabase MCP enables you to:
- Query and modify database tables
- Manage user authentication and profiles
- Handle file storage operations
- Execute custom SQL queries
- Monitor database performance
- Manage real-time subscriptions

## Configuration

### Environment Variables

Add these to your Claude Desktop config:

```json
{
  "mcpServers": {
    "supabase": {
      "command": "npx", 
      "args": ["@supabase/mcp-server-supabase"],
      "env": {
        "SUPABASE_URL": "https://your-project.supabase.co",
        "SUPABASE_ANON_KEY": "your-anon-key",
        "SUPABASE_SERVICE_ROLE_KEY": "your-service-role-key"
      }
    }
  }
}
```

### Getting Supabase Credentials

1. **Project URL**: Found in Supabase Dashboard → Settings → API
2. **Anon Key**: Public key for client-side operations
3. **Service Role Key**: Private key for server-side operations with full access

## Available Tools

### Database Operations
- **Query Tables**: SELECT operations with filtering and joins
- **Insert Records**: Add new data to tables
- **Update Records**: Modify existing data
- **Delete Records**: Remove data from tables
- **Execute SQL**: Run custom SQL queries
- **Table Schema**: View table structure and relationships

### Authentication Management
- **List Users**: View registered users
- **Create User**: Add new user accounts
- **Update User**: Modify user profiles and metadata
- **Delete User**: Remove user accounts
- **User Sessions**: Manage active sessions

### Storage Operations
- **List Buckets**: View available storage buckets
- **Upload Files**: Store files and media
- **Download Files**: Retrieve stored files
- **Delete Files**: Remove files from storage
- **Generate URLs**: Create signed URLs for file access

### Real-time Operations
- **Create Subscriptions**: Set up real-time listeners
- **Manage Channels**: Handle WebSocket connections
- **Broadcast Messages**: Send real-time updates

### Analytics & Monitoring
- **Query Logs**: View database query performance
- **Usage Statistics**: Monitor API usage and limits
- **Error Tracking**: View database errors and issues

## Example Usage

### Database Queries
```
Show me all users who signed up in the last 7 days
```

```
Get the top 10 most popular posts with their author information
```

### User Management
```
Create a new user with email "<EMAIL>" and set their role to "admin"
```

```
Update user profile for user ID "123" with new avatar URL
```

### File Operations
```
Upload the image file to the "avatars" bucket and generate a public URL
```

```
List all files in the "documents" bucket from the last month
```

### Analytics
```
Show me the slowest database queries from today
```

```
What's our current API usage and rate limit status?
```

## Security & Permissions

### Row Level Security (RLS)
- Enable RLS on sensitive tables
- Create policies for different user roles
- Test policies thoroughly before production

### API Key Management
- Use anon key for client-side operations
- Use service role key for admin operations only
- Rotate keys regularly
- Monitor key usage

### Data Access Patterns
- Follow principle of least privilege
- Use parameterized queries to prevent SQL injection
- Validate input data
- Audit sensitive operations

## Common Use Cases

### Content Management
- Manage blog posts and articles
- Handle user-generated content
- Store and serve media files
- Track content analytics

### User Management
- Handle user registration/login
- Manage user profiles and preferences
- Implement role-based access control
- Track user activity

### E-commerce
- Product catalog management
- Order processing and tracking
- Inventory management
- Customer data analysis

### Analytics & Reporting
- Track application metrics
- Generate business reports
- Monitor user behavior
- Performance analytics

## Performance Optimization

### Query Optimization
- Use appropriate indexes
- Limit query results with pagination
- Use joins efficiently
- Monitor slow queries

### Connection Management
- Use connection pooling
- Implement proper timeout settings
- Handle connection errors gracefully

### Caching Strategies
- Cache frequently accessed data
- Use Supabase's built-in caching
- Implement client-side caching

## Error Handling

### Common Error Types
- **Authentication**: Invalid credentials
- **Authorization**: Insufficient permissions
- **Validation**: Invalid data format
- **Rate Limits**: Too many requests
- **Database**: SQL errors and constraints

### Retry Strategies
- Implement exponential backoff
- Handle temporary network issues
- Log errors for debugging

## Real-time Features

### Subscriptions
- Subscribe to table changes
- Filter real-time events
- Handle connection drops
- Manage subscription lifecycle

### Broadcasting
- Send messages to specific channels
- Implement chat features
- Real-time notifications
- Live updates

## Migration & Schema Management

### Database Migrations
- Use Supabase CLI for migrations
- Version control schema changes
- Test migrations on staging
- Backup before major changes

### Schema Design
- Follow PostgreSQL best practices
- Use foreign keys for relationships
- Implement proper constraints
- Document schema decisions

## Troubleshooting

### Connection Issues
- Verify URL and keys are correct
- Check network connectivity
- Validate SSL certificates
- Monitor Supabase status page

### Permission Errors
- Review RLS policies
- Check user roles and permissions
- Validate API key scopes
- Test with service role key

### Performance Issues
- Monitor query execution time
- Check for missing indexes
- Review connection pool settings
- Analyze slow query logs

## Links

- [Supabase MCP GitHub](https://github.com/supabase-community/supabase-mcp)
- [Supabase Documentation](https://supabase.com/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)