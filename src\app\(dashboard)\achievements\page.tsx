import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { AchievementsPage } from '@/components/achievements/achievements-page'

export default async function AchievementsRoute() {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect('/login')
  }
  
  return <AchievementsPage userId={user.id} />
}