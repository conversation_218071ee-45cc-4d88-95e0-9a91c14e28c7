import { ADVANCED_WRITING_TECHNIQUES } from '@/constants/project-settings';
import type { ProjectSettings } from '@/lib/types/project-settings';

/**
 * Get a list of active writing techniques for a project
 */
export function getActiveWritingTechniques(settings: Partial<ProjectSettings>): string[] {
  return Object.entries(ADVANCED_WRITING_TECHNIQUES)
    .filter(([key]) => settings[key as keyof ProjectSettings] === true)
    .map(([_, technique]) => technique.label);
}

/**
 * Get a formatted string of active techniques for use in prompts
 */
export function getWritingTechniquesPrompt(settings: Partial<ProjectSettings>): string {
  const activeTechniques = Object.entries(ADVANCED_WRITING_TECHNIQUES)
    .filter(([key]) => settings[key as keyof ProjectSettings] === true)
    .map(([_, technique]) => `- ${technique.label}: ${technique.description}`);

  if (activeTechniques.length === 0) {
    return '';
  }

  return `
ADVANCED WRITING TECHNIQUES TO APPLY:
${activeTechniques.join('\n')}
`;
}

/**
 * Check if any advanced writing techniques are enabled
 */
export function hasAdvancedWritingTechniques(settings: Partial<ProjectSettings>): boolean {
  return Object.keys(ADVANCED_WRITING_TECHNIQUES).some(
    key => settings[key as keyof ProjectSettings] === true
  );
}

/**
 * Get a count of active writing techniques
 */
export function getWritingTechniquesCount(settings: Partial<ProjectSettings>): number {
  return Object.keys(ADVANCED_WRITING_TECHNIQUES).filter(
    key => settings[key as keyof ProjectSettings] === true
  ).length;
}