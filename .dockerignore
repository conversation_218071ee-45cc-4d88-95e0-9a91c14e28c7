# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js build output
.next/
out/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.sentry-build-plugin

# Testing
coverage/
.nyc_output
*.lcov

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Documentation
docs/
README.md
*.md

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Vercel
.vercel

# TypeScript
*.tsbuildinfo

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Supabase
supabase/

# Scripts (not needed in production)
scripts/

# Test files
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts
jest.config.js
jest.setup.js

# Build artifacts
build/
dist/

# Linting
.eslintrc*
eslint-report.json

# Other config files not needed in production
.prettierrc*
.editorconfig
tsconfig.json
tailwind.config.ts
postcss.config.js
playwright.config.ts
