'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { PenTool, BookOpen, Zap, Target, ChevronRight, ChevronLeft, X } from 'lucide-react'
import Link from 'next/link'

interface WelcomeTourProps {
  isNewUser: boolean
  hasProjects: boolean
  onComplete: () => void
}

const tourSteps = [
  {
    title: 'Welcome to BookScribe AI!',
    description: 'Your AI-powered writing companion for creating complete novels.',
    icon: PenTool,
    content: 'BookScribe AI helps you write full-length novels with advanced AI agents that maintain context, character consistency, and narrative flow across hundreds of thousands of words.',
  },
  {
    title: 'AI-Powered Story Creation',
    description: 'Specialized AI agents work together to bring your story to life.',
    icon: Zap,
    content: 'Our AI agents handle story structure development, character creation, world-building, chapter planning, and writing assistance while maintaining narrative consistency throughout your entire book.',
  },
  {
    title: 'Guided Writing Process',
    description: 'From concept to finished manuscript in simple steps.',
    icon: Target,
    content: 'Start with your story idea, let AI generate a complete structure with characters and plot, then write chapters with AI assistance. Track progress and export when ready.',
  },
  {
    title: 'Ready to Experience BookScribe AI?',
    description: 'Choose how you\'d like to begin your writing journey.',
    icon: BookOpen,
    content: 'Browse proven story templates for your genre, try a complete sample project, or create your own custom project with guided assistance.',
    action: {
      text: 'Browse Templates',
      href: '/templates'
    }
  },
]

export function WelcomeTour({ isNewUser, hasProjects, onComplete }: WelcomeTourProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [hasSeenTour, setHasSeenTour] = useState(false)

  useEffect(() => {
    // Check if user has seen the tour before
    const seenTour = localStorage.getItem('bookscribe-tour-completed')
    setHasSeenTour(!!seenTour)

    // Show tour for new users who haven't seen it and don't have projects
    if (isNewUser && !hasProjects && !seenTour) {
      setIsOpen(true)
    }
  }, [isNewUser, hasProjects])

  const handleNext = () => {
    if (currentStep < tourSteps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleClose = () => {
    setIsOpen(false)
    localStorage.setItem('bookscribe-tour-completed', 'true')
    onComplete()
  }

  const handleSkip = () => {
    handleClose()
  }

  const step = tourSteps[currentStep]
  
  if (!isOpen || hasSeenTour || !step) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(true)}
          className="shadow-lg"
        >
          Take Tour
        </Button>
      </div>
    )
  }

  const Icon = step.icon

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <Icon className="h-5 w-5 text-primary" />
              {step.title}
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
              aria-label="Close tour"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogDescription>
            {step.description}
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-center mb-4">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                  <Icon className="w-8 h-8 text-primary" />
                </div>
              </div>
              <p className="text-center text-muted-foreground">
                {step.content}
              </p>
            </CardContent>
          </Card>
          
          {/* Progress indicator */}
          <div className="flex justify-center mt-6 gap-2">
            {tourSteps.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentStep
                    ? 'bg-primary'
                    : index < currentStep
                    ? 'bg-primary/50'
                    : 'bg-gray-200'
                }`}
              />
            ))}
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button variant="ghost" onClick={handleSkip}>
              Skip Tour
            </Button>
            {currentStep > 0 && (
              <Button variant="outline" onClick={handlePrevious}>
                <ChevronLeft className="w-4 h-4 mr-1" />
                Previous
              </Button>
            )}
          </div>
          
          {currentStep === tourSteps.length - 1 && step.action ? (
            <Link href={step.action.href} onClick={handleClose}>
              <Button>
                {step.action.text}
                <ChevronRight className="w-4 h-4 ml-1" />
              </Button>
            </Link>
          ) : currentStep === tourSteps.length - 1 ? (
            <Button onClick={handleClose}>
              Get Started
            </Button>
          ) : (
            <Button onClick={handleNext}>
              Next
              <ChevronRight className="w-4 h-4 ml-1" />
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}