import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import ServiceManager from '@/lib/services/service-manager';
import { SemanticSearchService } from '@/lib/services/semantic-search';
import { authenticateUserForProject } from '@/lib/api/auth-helpers';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, projectId, content } = body;
    
    // Check authentication and project access
    const { user, error: authError } = await authenticateUserForProject(projectId);
    if (authError) return authError;

    const serviceManager = ServiceManager.getInstance();
    await serviceManager.initialize();
    
    const searchService = await serviceManager.getService('semantic-search');

    if (!searchService) {
      return NextResponse.json(
        { error: 'Semantic search service not available' },
        { status: 503 }
      );
    }

    switch (action) {
      case 'index_project':
        if (!projectId) {
          return NextResponse.json(
            { error: 'projectId is required' },
            { status: 400 }
          );
        }

        const indexResult = await (searchService as SemanticSearchService).indexProject(projectId);
        
        if (!indexResult.success) {
          return NextResponse.json(
            { error: indexResult.error || 'Indexing failed' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          jobId: indexResult.data,
          message: 'Project indexing started'
        });

      case 'index_content':
        if (!content || !projectId) {
          return NextResponse.json(
            { error: 'Content object and projectId are required' },
            { status: 400 }
          );
        }

        const contentResult = await (searchService as SemanticSearchService).indexContent({
          ...content,
          projectId
        });

        return NextResponse.json({
          success: contentResult.success,
          error: contentResult.error
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use "index_project" or "index_content"' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Search indexing API error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const jobId = url.searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json(
        { error: 'jobId parameter required' },
        { status: 400 }
      );
    }

    const serviceManager = ServiceManager.getInstance();
    await serviceManager.initialize();
    
    const searchService = await serviceManager.getService('semantic-search');

    if (!searchService) {
      return NextResponse.json(
        { error: 'Semantic search service not available' },
        { status: 503 }
      );
    }

    const statusResult = await (searchService as SemanticSearchService).getIndexingStatus(jobId);

    if (!statusResult.success) {
      return NextResponse.json(
        { error: statusResult.error || 'Failed to get status' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      job: statusResult.data
    });

  } catch (error) {
    console.error('Search indexing status API error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}