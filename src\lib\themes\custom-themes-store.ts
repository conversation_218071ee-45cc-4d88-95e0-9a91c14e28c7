/**
 * Custom Themes Store
 * Manages user-created custom themes with local storage persistence
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface CustomTheme {
  id: string;
  name: string;
  mode: 'light' | 'dark';
  createdAt: string;
  settings: {
    // Typography settings
    textSize: string;
    customTextSize?: number;
    editorFont: string;
    uiFont: string;
    readingFont: string;
    // Theme settings
    baseTheme: string;
    themeMode: string;
  };
  // Color overrides (for future expansion)
  colors?: {
    primary?: string;
    secondary?: string;
    background?: string;
    foreground?: string;
  };
}

interface CustomThemesState {
  customThemes: CustomTheme[];
  addCustomTheme: (theme: Omit<CustomTheme, 'id' | 'createdAt'>) => void;
  removeCustomTheme: (id: string) => void;
  updateCustomTheme: (id: string, updates: Partial<CustomTheme>) => void;
  getThemesByMode: (mode: 'light' | 'dark') => CustomTheme[];
  getThemeById: (id: string) => CustomTheme | undefined;
}

export const useCustomThemes = create<CustomThemesState>()(
  persist(
    (set, get) => ({
      customThemes: [],

      addCustomTheme: (themeData) => {
        const newTheme: CustomTheme = {
          ...themeData,
          id: `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date().toISOString(),
        };

        set((state) => ({
          customThemes: [...state.customThemes, newTheme],
        }));

        return newTheme.id;
      },

      removeCustomTheme: (id) => {
        set((state) => ({
          customThemes: state.customThemes.filter((theme) => theme.id !== id),
        }));
      },

      updateCustomTheme: (id, updates) => {
        set((state) => ({
          customThemes: state.customThemes.map((theme) =>
            theme.id === id ? { ...theme, ...updates } : theme
          ),
        }));
      },

      getThemesByMode: (mode) => {
        return get().customThemes.filter((theme) => theme.mode === mode);
      },

      getThemeById: (id) => {
        return get().customThemes.find((theme) => theme.id === id);
      },
    }),
    {
      name: 'bookscribe-custom-themes',
      version: 1,
    }
  )
);

/**
 * Helper function to determine theme mode from current settings
 */
export function determineThemeMode(currentTheme: string, themeMode: string): 'light' | 'dark' {
  if (themeMode === 'system') {
    // For system mode, we'll default to light for saving purposes
    // In a real app, you'd check the actual system preference
    return 'light';
  }
  
  if (themeMode === 'light' || themeMode === 'dark') {
    return themeMode;
  }
  
  // Fallback: determine from theme name
  if (currentTheme.includes('dark') || currentTheme.includes('night') || currentTheme.includes('evening')) {
    return 'dark';
  }
  
  return 'light';
}

/**
 * Helper function to create a theme preview card data structure
 */
export function createCustomThemePreview(customTheme: CustomTheme) {
  return {
    id: customTheme.id,
    name: customTheme.name,
    description: `Custom theme created ${new Date(customTheme.createdAt).toLocaleDateString()}`,
    colors: customTheme.colors || {
      primary: '#8B4513',
      secondary: '#D2B48C', 
      background: customTheme.mode === 'light' ? '#FEFEFE' : '#1A1A1A',
      foreground: customTheme.mode === 'light' ? '#2C2C2C' : '#E5E5E5',
    },
    isCustom: true,
    settings: customTheme.settings,
  };
}
