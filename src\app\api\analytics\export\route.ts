import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { AnalyticsEngine } from '@/lib/services/analytics-engine';
import { generatePDFReport } from '@/lib/utils/pdf-generator';
import { generateCSVData } from '@/lib/utils/csv-generator';
import { startOfDay, endOfDay, parseISO } from 'date-fns';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { 
      userId, 
      projectId, 
      dateRange, 
      format = 'pdf', 
      scope = 'current',
      data: providedData 
    } = body;

    // Validate user access
    if (userId !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // If project specified, verify user has access
    if (projectId) {
      const { data: project } = await supabase
        .from('projects')
        .select('id')
        .eq('id', projectId)
        .eq('user_id', user.id)
        .single();

      if (!project) {
        return NextResponse.json({ error: 'Project not found' }, { status: 404 });
      }
    }

    // Prepare date range
    const startDate = dateRange?.from 
      ? startOfDay(parseISO(dateRange.from))
      : startOfDay(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));
    const endDate = dateRange?.to 
      ? endOfDay(parseISO(dateRange.to))
      : endOfDay(new Date());

    let analyticsData;

    if (scope === 'all' || !providedData) {
      // Fetch fresh data from database
      const engine = new AnalyticsEngine(supabase);
      
      const [overview, activity, quality, goals] = await Promise.all([
        engine.getOverviewMetrics(userId, startDate, endDate, projectId),
        engine.getActivityData(userId, startDate, endDate, projectId),
        engine.getQualityMetrics(userId, projectId),
        engine.getGoalsData(userId, projectId),
      ]);

      // Fetch additional data from database
      const { data: userProfile } = await supabase
        .from('user_profiles')
        .select('display_name, bio')
        .eq('user_id', userId)
        .single();

      const { data: projects } = await supabase
        .from('projects')
        .select('id, title, primary_genre, current_word_count, target_word_count')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      analyticsData = {
        user: {
          id: userId,
          name: userProfile?.display_name || 'Unknown Author',
          bio: userProfile?.bio || '',
        },
        dateRange: {
          from: startDate.toISOString(),
          to: endDate.toISOString(),
        },
        overview: overview.data,
        activity: activity.data,
        quality: quality.data,
        goals: goals.data,
        projects: projects || [],
      };
    } else {
      // Use provided data from client
      analyticsData = providedData;
    }

    // Generate report based on format
    if (format === 'csv') {
      const csvData = await generateCSVData(analyticsData);
      
      return new NextResponse(csvData, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="analytics-report-${new Date().toISOString().split('T')[0]}.csv"`,
        },
      });
    } else if (format === 'pdf') {
      const pdfBuffer = await generatePDFReport(analyticsData);
      
      return new NextResponse(pdfBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="analytics-report-${new Date().toISOString().split('T')[0]}.pdf"`,
        },
      });
    } else {
      return NextResponse.json({ error: 'Invalid format' }, { status: 400 });
    }
  } catch (error) {
    console.error('Analytics export error:', error);
    return NextResponse.json(
      { error: 'Failed to export analytics' },
      { status: 500 }
    );
  }
}