import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useCelebration } from '@/contexts/celebration-context'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'

interface ChapterCompletionOptions {
  chapterId: string
  projectId: string
  onComplete?: () => void
}

export function useChapterCompletion({
  chapterId,
  projectId,
  onComplete
}: ChapterCompletionOptions) {
  const [isCompleting, setIsCompleting] = useState(false)
  const { celebrateChapter, celebrateQuality } = useCelebration()
  const { toast } = useToast()
  const supabase = createClient()

  const completeChapter = async () => {
    if (isCompleting) return
    
    setIsCompleting(true)
    
    try {
      // Get chapter details
      const { data: chapter, error: chapterError } = await supabase
        .from('chapters')
        .select('*')
        .eq('id', chapterId)
        .single()

      if (chapterError) throw chapterError

      // Mark chapter as complete
      const { error: updateError } = await supabase
        .from('chapters')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', chapterId)

      if (updateError) throw updateError

      // Get quality score if available
      const { data: qualityReport } = await supabase
        .from('quality_reports')
        .select('overall_score')
        .eq('project_id', projectId)
        .eq('chapter_id', chapterId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      // Trigger celebrations
      celebrateChapter(chapter.chapter_number || 1)

      // If quality score is high, celebrate that too
      if (qualityReport && qualityReport.overall_score >= 85) {
        setTimeout(() => {
          celebrateQuality(Math.round(qualityReport.overall_score))
        }, 4000) // Delay to avoid overlapping celebrations
      }

      toast({
        title: 'Chapter completed!',
        description: `Chapter ${chapter.chapter_number} has been marked as complete.`
      })

      onComplete?.()
    } catch (error) {
      logger.error('Error completing chapter', error)
      toast({
        title: 'Error',
        description: 'Failed to complete chapter. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsCompleting(false)
    }
  }

  return {
    completeChapter,
    isCompleting
  }
}