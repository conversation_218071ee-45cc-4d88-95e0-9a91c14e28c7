'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { HelpCircle, X } from 'lucide-react';

interface FeatureTooltipProps {
  title: string;
  description: string;
  feature: string;
  children: React.ReactNode;
  side?: 'top' | 'bottom' | 'left' | 'right';
}

export function FeatureTooltip({ 
  title, 
  description, 
  feature,
  children, 
  side = 'top' 
}: FeatureTooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  // Check if user has seen this tooltip before
  const tooltipKey = `bookscribe_tooltip_${feature}`;
  const hasSeenTooltip = typeof window !== 'undefined' && 
    localStorage.getItem(tooltipKey) === 'true';

  const showTooltip = () => {
    if (!hasSeenTooltip && !isDismissed) {
      setIsVisible(true);
    }
  };

  const hideTooltip = () => {
    setIsVisible(false);
    setIsDismissed(true);
    if (typeof window !== 'undefined') {
      localStorage.setItem(tooltipKey, 'true');
    }
  };

  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
  };

  return (
    <div 
      className="relative inline-block"
      onMouseEnter={showTooltip}
      onFocus={showTooltip}
    >
      {children}
      
      <AnimatePresence>
        {isVisible && !hasSeenTooltip && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className={`absolute z-50 ${positionClasses[side]}`}
          >
            <Card className="w-80 border-primary/20 bg-background/95 backdrop-blur-sm shadow-lg">
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <HelpCircle className="w-4 h-4 text-primary" />
                    <h4 className="font-medium text-sm">{title}</h4>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 -mr-2 -mt-1"
                    onClick={hideTooltip}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
                
                <p className="text-xs text-muted-foreground leading-relaxed">
                  {description}
                </p>
                
                <Button
                  size="sm"
                  className="w-full mt-3 h-7 text-xs"
                  onClick={hideTooltip}
                >
                  Got it!
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}