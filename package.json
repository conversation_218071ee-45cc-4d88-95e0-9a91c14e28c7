{"name": "bookscribe", "version": "0.1.0", "private": true, "scripts": {"start:dev": "node scripts/start-dev.js", "dev": "next dev", "dev:demo": "cross-env NEXT_PUBLIC_DEMO_MODE=true next dev", "build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 next build", "build:win": "set NODE_OPTIONS=--max-old-space-size=4096 && next build", "build:analyze": "cross-env ANALYZE=true NODE_OPTIONS=--max-old-space-size=4096 next build", "start": "next start", "start:demo": "cross-env NEXT_PUBLIC_DEMO_MODE=true next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:puppeteer": "node scripts/test-puppeteer.js", "generate:types": "node scripts/generate-supabase-types.js", "stripe:setup": "npx tsx scripts/create-stripe-tables.ts", "docker:build": "docker build -t bookscribe-ai .", "docker:build:dev": "docker build -f Dockerfile.dev -t bookscribe-ai:dev .", "docker:build:deps": "docker build -f Dockerfile.deps -t bookscribe-ai:deps .", "docker:run": "docker run -d --name bookscribe-app -p 3000:3000 --env-file .env.local bookscribe-ai", "docker:run:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:run:deps": "docker-compose -f docker-compose.deps.yml up -d", "docker:stop": "docker stop bookscribe-app && docker rm bookscribe-app", "docker:stop:deps": "docker-compose -f docker-compose.deps.yml down", "docker:logs": "docker logs -f bookscribe-app", "docker:logs:deps": "docker-compose -f docker-compose.deps.yml logs -f", "docker:clean": "docker system prune -f", "docker:health": "curl -f http://localhost:3000/api/health?basic=true", "security:audit": "npm audit", "security:fix": "npm audit fix", "security:check": "npm audit --audit-level moderate", "security:report": "node scripts/security-check.js", "install:system-deps": "node scripts/install-system-deps.js", "postinstall": "node scripts/install-system-deps.js"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "^1.3.23", "@ai-sdk/xai": "^1.2.18", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/nextjs": "^9.38.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/stripe-sync-engine": "^0.39.0", "@supabase/supabase-js": "^2.50.0", "ai": "^4.3.19", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "docx": "^9.5.0", "dotenv": "^17.2.0", "epub": "^1.3.0", "framer-motion": "^12.18.1", "jspdf": "^3.0.1", "jszip": "^3.10.1", "lru-cache": "^11.1.0", "lucide-react": "^0.515.0", "mammoth": "^1.8.0", "monaco-editor": "^0.52.2", "next": "15.3.3", "next-themes": "^0.4.6", "openai": "^5.10.1", "pdf-parse": "^1.1.1", "puppeteer": "^23.11.1", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "recharts": "^2.15.4", "remark-gfm": "^4.0.1", "sonner": "^2.0.6", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1", "tsx": "^4.20.3", "zod": "^3.25.76", "zod-to-json-schema": "^3.24.6", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.3", "@playwright/test": "^1.54.1", "@swc/core": "^1.12.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^24", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "madge": "^8.0.0", "postcss": "^8.5.5", "supabase": "^2.30.4", "swc-loader": "^0.2.6", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.45.0"}}