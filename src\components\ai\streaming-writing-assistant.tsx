/**
 * Streaming Writing Assistant Component
 * Real-time AI writing assistance with streaming responses
 */

'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { AI_MODELS } from '@/lib/config/ai-settings'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useStreamingContent, useStreamingChat } from '@/hooks/use-streaming-ai'
import { 
  Wand2, 
  MessageSquare, 
  StopCircle, 
  RefreshCw, 
  Copy, 
  Check,
  Sparkles,
  Clock,
  BarChart3
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface StreamingWritingAssistantProps {
  projectId?: string
  chapterId?: string
  initialContent?: string
  context?: {
    genre?: string
    style?: string
    characters?: string[]
    tone?: string
  }
  onContentGenerated?: (content: string) => void
  className?: string
}

export function StreamingWritingAssistant({
  projectId,
  chapterId,
  initialContent = '',
  context,
  onContentGenerated,
  className
}: StreamingWritingAssistantProps) {
  const [prompt, setPrompt] = useState('')
  const [contentType, setContentType] = useState<'chapter' | 'scene' | 'dialogue' | 'description'>('scene')
  const [selectedModel, setSelectedModel] = useState<string>(AI_MODELS.XAI_PRIMARY)
  const [copied, setCopied] = useState(false)
  const [activeTab, setActiveTab] = useState('generate')
  
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Content generation hook
  const {
    content: generatedContent,
    isLoading: isGenerating,
    isStreaming,
    error: generationError,
    progress,
    quality,
    generateContent,
    cancelGeneration,
    reset: resetGeneration
  } = useStreamingContent({
    onComplete: (content) => {
      if (onContentGenerated) {
        onContentGenerated(content)
      }
    },
    onProgress: (progress) => {
      // Real-time progress updates
      console.log('Generation progress:', progress)
    },
    onQualityUpdate: (quality) => {
      console.log('Content quality:', quality)
    }
  })

  // Chat hook for interactive assistance
  const {
    messages,
    input: chatInput,
    handleInputChange,
    handleSubmit: handleChatSubmit,
    isLoading: isChatLoading,
    error: chatError,
    clearChat
  } = useStreamingChat({
    systemPrompt: `You are an expert creative writing assistant. Help the user with their writing project.
    ${context?.genre ? `Genre: ${context.genre}` : ''}
    ${context?.style ? `Style: ${context.style}` : ''}
    ${context?.tone ? `Tone: ${context.tone}` : ''}
    ${context?.characters ? `Characters: ${context.characters.join(', ')}` : ''}
    
    Provide constructive feedback, suggestions, and creative ideas to improve their writing.`,
    onComplete: (message) => {
      console.log('Chat response completed:', message)
    }
  })

  const handleGenerate = async () => {
    if (!prompt.trim()) return

    const systemPrompt = buildSystemPrompt(contentType, context, selectedModel)
    const estimatedTokens = getEstimatedTokens(contentType)

    await generateContent(prompt, systemPrompt, estimatedTokens, selectedModel)
  }

  const handleCopy = async () => {
    if (generatedContent) {
      await navigator.clipboard.writeText(generatedContent)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  const buildSystemPrompt = (type: string, context?: any, model?: string) => {
    // Model-specific base prompts
    const modelPrompts = {
      [AI_MODELS.XAI_PRIMARY]: 'You are Grok, an expert creative writing assistant with a witty and engaging personality. Bring creativity and humor to your writing while maintaining high quality.',
      [AI_MODELS.ALTERNATIVE_PRIMARY]: 'You are an expert creative writing assistant focused on structured, precise, and well-crafted content.',
      [AI_MODELS.FAST]: 'You are an efficient creative writing assistant that delivers quality content quickly and concisely.'
    }

    const basePrompt = modelPrompts[model as keyof typeof modelPrompts] || modelPrompts[AI_MODELS.ALTERNATIVE_PRIMARY]

    const typePrompts = {
      chapter: 'Generate a complete chapter with proper pacing, character development, and plot advancement.',
      scene: 'Create a vivid, engaging scene with sensory details and emotional depth.',
      dialogue: 'Write natural, character-appropriate dialogue that reveals personality and advances the story.',
      description: 'Create immersive, sensory-rich descriptions that paint a clear picture.'
    }

    let prompt = `${basePrompt} ${typePrompts[type] || typePrompts.scene}`

    // Add model-specific style guidance
    if (model === AI_MODELS.XAI_PRIMARY) {
      prompt += ' Feel free to add subtle wit and personality to the content while maintaining the story\'s tone.'
    } else if (model === AI_MODELS.FAST) {
      prompt += ' Focus on efficiency and clarity while maintaining quality.'
    }

    if (context) {
      if (context.genre) prompt += ` Genre: ${context.genre}.`
      if (context.style) prompt += ` Writing style: ${context.style}.`
      if (context.tone) prompt += ` Tone: ${context.tone}.`
      if (context.characters) prompt += ` Key characters: ${context.characters.join(', ')}.`
    }

    return prompt
  }

  const getEstimatedTokens = (type: string) => {
    const tokenEstimates = {
      chapter: 2000,
      scene: 1000,
      dialogue: 500,
      description: 800
    }
    return tokenEstimates[type] || 1000
  }

  const getQualityColor = (quality?: number) => {
    if (!quality) return 'bg-gray-200'
    if (quality >= 80) return 'bg-green-500'
    if (quality >= 60) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const getQualityLabel = (quality?: number) => {
    if (!quality) return 'Unknown'
    if (quality >= 80) return 'Excellent'
    if (quality >= 60) return 'Good'
    return 'Needs Improvement'
  }

  return (
    <Card className={cn('w-full max-w-4xl mx-auto', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-purple-500" />
          AI Writing Assistant
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="generate" className="flex items-center gap-2">
              <Wand2 className="h-4 w-4" />
              Generate Content
            </TabsTrigger>
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Chat Assistant
            </TabsTrigger>
          </TabsList>

          <TabsContent value="generate" className="space-y-4">
            {/* Content Type and Model Selection */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Content Type</label>
                <div className="flex gap-2 flex-wrap">
                  {(['chapter', 'scene', 'dialogue', 'description'] as const).map((type) => (
                    <Button
                      key={type}
                      variant={contentType === type ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setContentType(type)}
                      className="capitalize"
                    >
                      {type}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">AI Model</label>
                <div className="flex gap-2 flex-wrap">
                  {([
                    { id: AI_MODELS.XAI_PRIMARY, name: 'Grok 3', desc: 'Creative & Witty' },
                    { id: AI_MODELS.ALTERNATIVE_PRIMARY, name: 'GPT-4', desc: 'Structured & Precise' },
                    { id: AI_MODELS.FAST, name: 'GPT-4o Mini', desc: 'Fast & Efficient' }
                  ] as const).map((model) => (
                    <Button
                      key={model.id}
                      variant={selectedModel === model.id ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedModel(model.id as any)}
                      className="flex flex-col h-auto p-2"
                      title={model.desc}
                    >
                      <span className="text-xs font-medium">{model.name}</span>
                      <span className="text-xs opacity-70">{model.desc}</span>
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            {/* Prompt Input */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                What would you like me to write?
              </label>
              <Textarea
                ref={textareaRef}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder={`Describe the ${contentType} you want me to create...`}
                className="min-h-[100px]"
                disabled={isGenerating}
              />
            </div>

            {/* Generation Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  onClick={handleGenerate}
                  disabled={!prompt.trim() || isGenerating}
                  className="flex items-center gap-2"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-4 w-4" />
                      Generate {contentType}
                    </>
                  )}
                </Button>

                {isGenerating && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={cancelGeneration}
                    className="flex items-center gap-2"
                  >
                    <StopCircle className="h-4 w-4" />
                    Stop
                  </Button>
                )}

                {generatedContent && !isGenerating && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetGeneration}
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Reset
                  </Button>
                )}
              </div>

              {/* Quality and Progress Indicators */}
              <div className="flex items-center gap-4">
                {isStreaming && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-500" />
                    <Progress value={progress.percentComplete} className="w-20" />
                    <span className="text-sm text-muted-foreground">
                      {Math.round(progress.percentComplete)}%
                    </span>
                  </div>
                )}

                {quality && (
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4 text-green-500" />
                    <Badge variant="outline" className={cn('text-white', getQualityColor(quality))}>
                      {getQualityLabel(quality)}
                    </Badge>
                  </div>
                )}
              </div>
            </div>

            {/* Error Display */}
            {generationError && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{generationError}</p>
              </div>
            )}

            {/* Generated Content */}
            {generatedContent && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Generated Content</label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopy}
                    className="flex items-center gap-2"
                  >
                    {copied ? (
                      <>
                        <Check className="h-4 w-4" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4" />
                        Copy
                      </>
                    )}
                  </Button>
                </div>
                <div className="p-4 bg-gray-50 rounded-md border max-h-96 overflow-y-auto">
                  <pre className="whitespace-pre-wrap text-sm font-mono">
                    {generatedContent}
                  </pre>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="chat" className="space-y-4">
            {/* Chat Messages */}
            <div className="space-y-4 max-h-96 overflow-y-auto p-4 bg-gray-50 rounded-md border">
              {messages.length === 0 ? (
                <div className="text-center text-muted-foreground">
                  <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>Start a conversation with your AI writing assistant</p>
                </div>
              ) : (
                messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      'flex gap-3 p-3 rounded-lg',
                      message.role === 'user'
                        ? 'bg-blue-100 ml-8'
                        : 'bg-white mr-8 shadow-sm'
                    )}
                  >
                    <div className="flex-1">
                      <div className="text-sm font-medium mb-1">
                        {message.role === 'user' ? 'You' : 'AI Assistant'}
                      </div>
                      <div className="text-sm whitespace-pre-wrap">
                        {message.content}
                      </div>
                    </div>
                  </div>
                ))
              )}
              
              {isChatLoading && (
                <div className="flex gap-3 p-3 rounded-lg bg-white mr-8 shadow-sm">
                  <div className="flex-1">
                    <div className="text-sm font-medium mb-1">AI Assistant</div>
                    <div className="flex items-center gap-2">
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      <span className="text-sm text-muted-foreground">Thinking...</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Chat Input */}
            <form onSubmit={handleChatSubmit} className="flex gap-2">
              <Textarea
                value={chatInput}
                onChange={handleInputChange}
                placeholder="Ask me anything about your writing..."
                className="flex-1 min-h-[60px] resize-none"
                disabled={isChatLoading}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault()
                    handleChatSubmit(e as any)
                  }
                }}
              />
              <div className="flex flex-col gap-2">
                <Button
                  type="submit"
                  disabled={!chatInput.trim() || isChatLoading}
                  className="flex items-center gap-2"
                >
                  <MessageSquare className="h-4 w-4" />
                  Send
                </Button>
                {messages.length > 0 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={clearChat}
                    disabled={isChatLoading}
                  >
                    Clear
                  </Button>
                )}
              </div>
            </form>

            {/* Chat Error */}
            {chatError && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{chatError.message}</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
