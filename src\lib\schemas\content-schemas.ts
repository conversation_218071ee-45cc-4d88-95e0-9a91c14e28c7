/**
 * Zod Schemas for Content Generation
 * These schemas are used for structured outputs in content generation services
 */

import { z } from 'zod'

// Scene Outline Schema
export const sceneOutlineSchema = z.object({
  outline: z.string().min(1),
  keyEvents: z.array(z.string()),
  conflicts: z.array(z.string()),
  emotionalBeats: z.array(z.string())
})

export type SceneOutline = z.infer<typeof sceneOutlineSchema>

// Dialogue Schema
export const dialogueResponseSchema = z.object({
  dialogue: z.array(z.object({
    speaker: z.string().min(1),
    line: z.string().min(1),
    emotion: z.string().optional()
  })).min(1),
  tags: z.array(z.string()),
  subtext: z.array(z.string())
})

export type DialogueResponse = z.infer<typeof dialogueResponseSchema>

// Description Enhancement Schema
export const descriptionEnhancementSchema = z.object({
  enhanced: z.string().min(1),
  sensoryDetails: z.array(z.string()),
  moodElements: z.array(z.string()),
  symbolism: z.array(z.string()).optional()
})

export type DescriptionEnhancement = z.infer<typeof descriptionEnhancementSchema>

// Plot Outline Schema
export const plotOutlineSchema = z.object({
  acts: z.array(z.object({
    number: z.number().int().positive(),
    title: z.string().min(1),
    description: z.string().min(1),
    chapters: z.array(z.number().int().positive()),
    turningPoint: z.string().min(1)
  })).min(1),
  majorPlotPoints: z.array(z.object({
    name: z.string().min(1),
    chapter: z.number().int().positive(),
    description: z.string().min(1),
    impact: z.enum(['low', 'medium', 'high'])
  })),
  subplots: z.array(z.object({
    name: z.string().min(1),
    description: z.string().min(1),
    chapters: z.array(z.number().int().positive()),
    resolution: z.string().optional()
  }))
})

export type PlotOutline = z.infer<typeof plotOutlineSchema>

// Character Voice Schema
export const characterVoiceSchema = z.object({
  character: z.string().min(1),
  voiceProfile: z.object({
    speechPatterns: z.array(z.string()),
    vocabulary: z.array(z.string()),
    catchphrases: z.array(z.string()).optional(),
    emotionalTriggers: z.array(z.string()),
    speakingStyle: z.string().min(1)
  }),
  exampleDialogue: z.array(z.string()).min(1)
})

export type CharacterVoice = z.infer<typeof characterVoiceSchema>

// Chapter Summary Schema (for analysis)
export const chapterSummarySchema = z.object({
  chapterNumber: z.number().int().positive(),
  summary: z.string().min(1),
  keyEvents: z.array(z.string()),
  characterDevelopments: z.array(z.object({
    character: z.string().min(1),
    development: z.string().min(1)
  })),
  plotAdvancement: z.string().min(1),
  themes: z.array(z.string()),
  cliffhanger: z.string().optional()
})

export type ChapterSummary = z.infer<typeof chapterSummarySchema>

// Character Profile Schema (for content generator)
export const characterProfileGenerationSchema = z.object({
  profile: z.object({
    name: z.string().min(1),
    age: z.number().int().positive(),
    appearance: z.string().min(1),
    personality: z.string().min(1),
    background: z.string().min(1),
    motivations: z.array(z.string()).min(1),
    fears: z.array(z.string()).min(1),
    strengths: z.array(z.string()).min(1),
    weaknesses: z.array(z.string()).min(1),
    relationships: z.array(z.object({
      character: z.string().min(1),
      relationship: z.string().min(1)
    })),
    arc: z.string().min(1),
    voice: z.string().min(1)
  })
})

export type CharacterProfileGeneration = z.infer<typeof characterProfileGenerationSchema>

// World Building Schema
export const worldBuildingSchema = z.object({
  worldElement: z.object({
    name: z.string().min(1),
    type: z.string().min(1),
    description: z.string().min(1),
    details: z.record(z.unknown()),
    connections: z.array(z.string()),
    atmosphere: z.string().min(1),
    significance: z.string().min(1)
  })
})

export type WorldBuilding = z.infer<typeof worldBuildingSchema>

// Comprehensive Story Generation Schemas
export const storyStructureGenerationSchema = z.object({
  title: z.string().min(1),
  premise: z.string().min(1),
  genre: z.string().min(1),
  themes: z.array(z.string()).min(3).max(5),
  acts: z.array(z.object({
    number: z.number().int().positive(),
    title: z.string().min(1),
    description: z.string().min(1),
    chapters: z.array(z.number().int().positive()),
    keyEvents: z.array(z.string()),
    wordCountTarget: z.number().int().positive()
  })).min(3),
  plotPoints: z.array(z.object({
    type: z.enum(['inciting_incident', 'plot_point_1', 'midpoint', 'plot_point_2', 'climax', 'resolution']),
    chapter: z.number().int().positive(),
    description: z.string().min(1)
  })),
  worldBuilding: z.object({
    setting: z.string().min(1),
    rules: z.array(z.string()),
    locations: z.array(z.object({
      name: z.string().min(1),
      description: z.string().min(1)
    }))
  })
})

export type StoryStructureGeneration = z.infer<typeof storyStructureGenerationSchema>

// Characters array wrapper for comprehensive generation
export const charactersGenerationSchema = z.object({
  characters: z.array(z.object({
    name: z.string().min(1),
    role: z.enum(['protagonist', 'antagonist', 'supporting', 'minor']),
    description: z.string().min(1),
    personality: z.object({
      traits: z.array(z.string()).min(1),
      motivations: z.array(z.string()).min(1),
      fears: z.array(z.string()).min(1),
      goals: z.array(z.string()).min(1)
    }),
    background: z.string().min(1),
    characterArc: z.string().min(1),
    relationships: z.array(z.object({
      character: z.string().min(1),
      relationship: z.string().min(1)
    }))
  })).min(4).max(6)
})

export type CharactersGeneration = z.infer<typeof charactersGenerationSchema>