'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ADVANCED_WRITING_TECHNIQUES } from '@/constants/project-settings';
import type { ProjectSettings } from '@/lib/types/project-settings';

interface WritingTechniquesDisplayProps {
  settings: Partial<ProjectSettings>;
}

export function WritingTechniquesDisplay({ settings }: WritingTechniquesDisplayProps) {
  const activeTechniques = Object.entries(ADVANCED_WRITING_TECHNIQUES).filter(
    ([key]) => settings[key as keyof ProjectSettings] === true
  );

  if (activeTechniques.length === 0) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Advanced Writing Techniques</CardTitle>
        <CardDescription>
          These specialized techniques are being applied to enhance your prose quality
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-2">
          {activeTechniques.map(([key, technique]) => (
            <Badge 
              key={key} 
              variant="secondary" 
              className="px-3 py-1"
              title={technique.description}
            >
              {technique.label}
            </Badge>
          ))}
        </div>
        {activeTechniques.length > 0 && (
          <p className="text-sm text-muted-foreground mt-4">
            The AI agents will prioritize these techniques when generating and reviewing content.
          </p>
        )}
      </CardContent>
    </Card>
  );
}