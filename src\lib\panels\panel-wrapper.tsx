'use client'

import { ComponentType, useEffect, useRef, useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  X, 
  Pin, 
  PinOff, 
  Maximize2, 
  Minimize2,
  Move,
  Settings
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { PanelPlugin, PanelProps, PanelState } from './types'
import { usePanels } from './panel-context'
import { motion, AnimatePresence } from 'framer-motion'
import { useResizable } from '@/hooks/use-resizable-panel'

interface PanelWrapperProps {
  plugin: PanelPlugin
  panelProps: Omit<PanelProps, 'onClose' | 'onAction' | 'onSettingsChange'>
  className?: string
}

export function PanelWrapper({ plugin, panelProps, className }: PanelWrapperProps) {
  const { panelStates, updatePanelState, togglePanel, manager } = usePanels()
  const panelState = panelStates.get(plugin.id)
  const [isMaximized, setIsMaximized] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const panelRef = useRef<HTMLDivElement>(null)
  
  // Use resizable hook for non-floating panels
  const { size, isResizing, startResize } = useResizable({
    defaultSize: { 
      width: panelState?.width || plugin.defaultWidth || 300,
      height: panelState?.height || plugin.defaultHeight || 400
    },
    minSize: {
      width: plugin.minWidth || 200,
      height: plugin.minHeight || 200
    },
    maxSize: {
      width: plugin.maxWidth || 800,
      height: plugin.maxHeight || 800
    },
    onResize: (newSize) => {
      updatePanelState(plugin.id, {
        width: newSize.width,
        height: newSize.height
      })
    }
  })

  if (!panelState?.visible) return null

  const handleClose = () => {
    togglePanel(plugin.id)
  }

  const handlePin = () => {
    updatePanelState(plugin.id, { pinned: !panelState.pinned })
  }

  const handleMaximize = () => {
    setIsMaximized(!isMaximized)
  }

  const handleAction = (action: string, data?: any) => {
    // Handle panel-specific actions
    if (action === 'settings') {
      // Open settings modal for this panel
      return
    }
    
    // Emit event for other components to handle
    manager.emitEvent({
      source: plugin.id,
      type: `panel:action:${action}`,
      data,
      timestamp: Date.now()
    })
  }

  const handleSettingsChange = (settings: Record<string, any>) => {
    updatePanelState(plugin.id, { settings })
  }

  const PanelComponent = plugin.component as ComponentType<PanelProps>

  const panelContent = (
    <Card 
      ref={panelRef}
      className={cn(
        'flex flex-col overflow-hidden',
        isMaximized && 'fixed inset-4 z-50',
        panelState.position === 'floating' && 'absolute shadow-lg',
        panelState.position === 'left' && 'border-r',
        panelState.position === 'right' && 'border-l',
        panelState.position === 'bottom' && 'border-t',
        isDragging && 'cursor-move',
        className
      )}
      style={{
        ...(panelState.position === 'floating' && {
          left: panelState.x || 100,
          top: panelState.y || 100,
          width: isMaximized ? 'auto' : size.width,
          height: isMaximized ? 'auto' : size.height
        }),
        ...(!isMaximized && panelState.position !== 'floating' && {
          width: panelState.position === 'bottom' ? '100%' : size.width,
          height: panelState.position === 'bottom' ? size.height : '100%'
        })
      }}
    >
      {/* Panel Header */}
      <div className="flex items-center justify-between p-2 border-b bg-warm-50">
        <div className="flex items-center gap-2">
          <plugin.icon className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">{plugin.name}</span>
        </div>
        <div className="flex items-center gap-1">
          {plugin.canPin !== false && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={handlePin}
            >
              {panelState.pinned ? (
                <Pin className="h-3 w-3" />
              ) : (
                <PinOff className="h-3 w-3" />
              )}
            </Button>
          )}
          {panelState.position === 'floating' && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={handleMaximize}
            >
              {isMaximized ? (
                <Minimize2 className="h-3 w-3" />
              ) : (
                <Maximize2 className="h-3 w-3" />
              )}
            </Button>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={handleClose}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Panel Content */}
      <div className="flex-1 overflow-auto">
        <PanelComponent
          {...panelProps}
          onClose={handleClose}
          onAction={handleAction}
          onSettingsChange={handleSettingsChange}
        />
      </div>

      {/* Resize Handle */}
      {!isMaximized && panelState.position !== 'floating' && (
        <div
          className={cn(
            'absolute bg-warm-200 hover:bg-warm-300 transition-colors cursor-resize',
            panelState.position === 'left' && 'right-0 top-0 w-1 h-full cursor-ew-resize',
            panelState.position === 'right' && 'left-0 top-0 w-1 h-full cursor-ew-resize',
            panelState.position === 'bottom' && 'left-0 top-0 w-full h-1 cursor-ns-resize'
          )}
          onMouseDown={startResize}
        />
      )}
    </Card>
  )

  if (panelState.position === 'floating') {
    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
        >
          {panelContent}
        </motion.div>
      </AnimatePresence>
    )
  }

  return panelContent
}