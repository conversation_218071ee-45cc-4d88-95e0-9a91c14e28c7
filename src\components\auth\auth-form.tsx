'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/contexts/auth-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { InputField } from '@/components/ui/form-field'
import { Checkbox } from '@/components/ui/checkbox'
import { useFormValidation } from '@/hooks/use-form-validation'
import { loginSchema, signupSchema, type LoginFormData, type SignupFormData } from '@/lib/validation/auth-schemas'
import { useToast } from '@/hooks/use-toast'

export function AuthForm({ mode }: { mode: 'login' | 'signup' }) {
  const [formData, setFormData] = useState<LoginFormData | SignupFormData>({
    email: '',
    password: '',
  })
  const [rememberMe, setRememberMe] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const { signIn, signUp } = useAuth()
  const { toast } = useToast()

  const schema = mode === 'signup' ? signupSchema : loginSchema
  
  const {
    isValidating,
    validateForm,
    handleFieldBlur,
    getFieldError,
    shouldShowFieldError,
  } = useFormValidation({
    schema,
    validateOnBlur: true,
    validateOnChange: false,
  })

  const handleFieldChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setError(null) // Clear form-level errors when user types
  }

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    
    const validation = await validateForm(formData)
    if (!validation.isValid) {
      return
    }

    setLoading(true)

    try {
      if (mode === 'signup') {
        const { error } = await signUp(formData.email, formData.password)
        if (error) throw error
        toast({
          title: 'Account Created!',
          description: 'Check your email for the confirmation link!',
        })
      } else {
        const { error } = await signIn(formData.email, formData.password)
        if (error) throw error
        router.push('/dashboard')
        router.refresh()
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>{mode === 'login' ? 'Welcome back' : 'Create an account'}</CardTitle>
        <CardDescription>
          {mode === 'login'
            ? 'Enter your credentials to access your account'
            : 'Start your novel-writing journey with BookScribe AI'}
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleAuth}>
        <CardContent className="space-y-4">
          <InputField
            id="email"
            label="Email"
            type="email"
            placeholder="<EMAIL>"
            value={formData.email}
            onChange={(value) => handleFieldChange('email', value as string)}
            onBlur={() => handleFieldBlur('email', formData.email, formData)}
            error={shouldShowFieldError('email') ? getFieldError('email') : undefined}
            required
            disabled={loading}
          />
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Password {mode === 'signup' && <span className="text-destructive ml-1">*</span>}
              </span>
              {mode === 'login' && (
                <Link href="/forgot-password" className="text-sm text-primary hover:underline">
                  Forgot password?
                </Link>
              )}
            </div>
            <InputField
              id="password"
              label=""
              type="password"
              placeholder={mode === 'signup' ? 'Must contain uppercase, lowercase, and number' : 'Enter your password'}
              value={formData.password}
              onChange={(value) => handleFieldChange('password', value as string)}
              onBlur={() => handleFieldBlur('password', formData.password, formData)}
              error={shouldShowFieldError('password') ? getFieldError('password') : undefined}
              required
              disabled={loading}
            />
          </div>
          
          {mode === 'login' && (
            <div className="flex items-center space-x-2">
              <Checkbox
                id="remember-me"
                checked={rememberMe}
                onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                disabled={loading}
              />
              <label
                htmlFor="remember-me"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
              >
                Remember me
              </label>
            </div>
          )}
          
          {error && (
            <div className="rounded-lg border border-destructive/50 bg-destructive/10 p-3">
              <p className="text-sm text-destructive" role="alert">{error}</p>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button 
            type="submit" 
            className="w-full" 
            disabled={loading || isValidating}
          >
            {loading ? 'Loading...' : mode === 'login' ? 'Sign In' : 'Sign Up'}
          </Button>
          <p className="text-sm text-muted-foreground">
            {mode === 'login' ? (
              <>
                Don&apos;t have an account?{' '}
                <a href="/signup" className="text-primary hover:underline">
                  Sign up
                </a>
              </>
            ) : (
              <>
                Already have an account?{' '}
                <a href="/login" className="text-primary hover:underline">
                  Sign in
                </a>
              </>
            )}
          </p>
        </CardFooter>
      </form>
    </Card>
  )
}