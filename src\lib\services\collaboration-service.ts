import { logger } from './logger'
import { hasRealTimeCollaboration } from '../subscription'
import type { UserSubscription } from '../subscription'

export interface CollaborationUser {
  id: string
  name: string
  email: string
  color: string
  cursor?: {
    line: number
    column: number
  }
  selection?: {
    startLine: number
    startColumn: number
    endLine: number
    endColumn: number
  }
}

export interface CollaborationSession {
  id: string
  projectId: string
  documentId: string
  users: Map<string, CollaborationUser>
  isActive: boolean
  createdAt: Date
}

export interface CollaborationChange {
  userId: string
  sessionId: string
  type: 'insert' | 'delete' | 'replace'
  range: {
    startLine: number
    startColumn: number
    endLine: number
    endColumn: number
  }
  text: string
  timestamp: number
}

export interface CursorPosition {
  userId: string
  line: number
  column: number
  timestamp: number
}

export interface SelectionRange {
  userId: string
  startLine: number
  startColumn: number
  endLine: number
  endColumn: number
  timestamp: number
}

type CollaborationEventType = 
  | 'user.joined'
  | 'user.left'
  | 'cursor.moved'
  | 'selection.changed'
  | 'content.changed'
  | 'session.created'
  | 'session.ended'

interface CollaborationEvent {
  type: CollaborationEventType
  sessionId: string
  userId: string
  data: any
  timestamp: number
}

export class CollaborationService {
  private static instance: CollaborationService
  private websocket: WebSocket | null = null
  private sessions: Map<string, CollaborationSession> = new Map()
  private listeners: Map<string, Set<(event: CollaborationEvent) => void>> = new Map()
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private heartbeatInterval: NodeJS.Timeout | null = null
  
  private constructor() {}
  
  static getInstance(): CollaborationService {
    if (!CollaborationService.instance) {
      CollaborationService.instance = new CollaborationService()
    }
    return CollaborationService.instance
  }

  /**
   * Check if real-time collaboration is available for the user
   */
  canUseRealTimeCollaboration(subscription: UserSubscription | null): boolean {
    if (!subscription) return false
    return hasRealTimeCollaboration(subscription.tierId)
  }

  /**
   * Connect to collaboration WebSocket server
   */
  async connect(
    sessionId: string,
    userId: string,
    subscription: UserSubscription | null
  ): Promise<void> {
    // Check subscription
    if (!this.canUseRealTimeCollaboration(subscription)) {
      throw new Error('Real-time collaboration requires Studio tier subscription')
    }
    
    // Close existing connection if any
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.close()
    }
    
    try {
      // In production, this would connect to a WebSocket server
      // For now, we'll simulate the connection
      const wsUrl = process.env.NEXT_PUBLIC_COLLAB_WS_URL || 'ws://localhost:8080'
      
      logger.info('Connecting to collaboration server', { sessionId, userId, wsUrl })
      
      // Simulate connection for demo purposes
      this.simulateConnection(sessionId, userId)
      
      // In production:
      // this.websocket = new WebSocket(`${wsUrl}/collab/${sessionId}?userId=${userId}`)
      // this.setupWebSocketHandlers()
      
    } catch (error) {
      logger.error('Failed to connect to collaboration server', error)
      throw error
    }
  }

  /**
   * Disconnect from collaboration server
   */
  disconnect(sessionId: string): void {
    if (this.websocket) {
      this.websocket.close()
      this.websocket = null
    }
    
    this.sessions.delete(sessionId)
    this.clearHeartbeat()
    
    logger.info('Disconnected from collaboration session', { sessionId })
  }

  /**
   * Send cursor position update
   */
  sendCursorPosition(sessionId: string, position: Omit<CursorPosition, 'timestamp'>): void {
    this.sendMessage({
      type: 'cursor.moved',
      sessionId,
      userId: position.userId,
      data: {
        line: position.line,
        column: position.column
      },
      timestamp: Date.now()
    })
  }

  /**
   * Send selection range update
   */
  sendSelectionRange(sessionId: string, selection: Omit<SelectionRange, 'timestamp'>): void {
    this.sendMessage({
      type: 'selection.changed',
      sessionId,
      userId: selection.userId,
      data: {
        startLine: selection.startLine,
        startColumn: selection.startColumn,
        endLine: selection.endLine,
        endColumn: selection.endColumn
      },
      timestamp: Date.now()
    })
  }

  /**
   * Send content change
   */
  sendContentChange(sessionId: string, change: Omit<CollaborationChange, 'timestamp'>): void {
    this.sendMessage({
      type: 'content.changed',
      sessionId,
      userId: change.userId,
      data: {
        type: change.type,
        range: change.range,
        text: change.text
      },
      timestamp: Date.now()
    })
  }

  /**
   * Subscribe to collaboration events
   */
  subscribe(
    sessionId: string,
    callback: (event: CollaborationEvent) => void
  ): () => void {
    if (!this.listeners.has(sessionId)) {
      this.listeners.set(sessionId, new Set())
    }
    
    this.listeners.get(sessionId)!.add(callback)
    
    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(sessionId)
      if (listeners) {
        listeners.delete(callback)
        if (listeners.size === 0) {
          this.listeners.delete(sessionId)
        }
      }
    }
  }

  /**
   * Get active session
   */
  getSession(sessionId: string): CollaborationSession | undefined {
    return this.sessions.get(sessionId)
  }

  /**
   * Get all active users in a session
   */
  getActiveUsers(sessionId: string): CollaborationUser[] {
    const session = this.sessions.get(sessionId)
    return session ? Array.from(session.users.values()) : []
  }

  // Private methods

  private sendMessage(event: CollaborationEvent): void {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(event))
    } else {
      // Queue message for when connection is restored
      logger.warn('WebSocket not connected, queueing message', event)
    }
  }

  private setupWebSocketHandlers(): void {
    if (!this.websocket) return
    
    this.websocket.onopen = () => {
      logger.info('WebSocket connection established')
      this.reconnectAttempts = 0
      this.startHeartbeat()
    }
    
    this.websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data) as CollaborationEvent
        this.handleCollaborationEvent(data)
      } catch (error) {
        logger.error('Failed to parse WebSocket message', error)
      }
    }
    
    this.websocket.onerror = (error) => {
      logger.error('WebSocket error', error)
    }
    
    this.websocket.onclose = () => {
      logger.info('WebSocket connection closed')
      this.clearHeartbeat()
      this.attemptReconnect()
    }
  }

  private handleCollaborationEvent(event: CollaborationEvent): void {
    // Update local session state
    const session = this.sessions.get(event.sessionId)
    
    if (session) {
      switch (event.type) {
        case 'user.joined':
          session.users.set(event.userId, event.data)
          break
        case 'user.left':
          session.users.delete(event.userId)
          break
        case 'cursor.moved':
          const user = session.users.get(event.userId)
          if (user) {
            user.cursor = event.data
          }
          break
        case 'selection.changed':
          const userSel = session.users.get(event.userId)
          if (userSel) {
            userSel.selection = event.data
          }
          break
      }
    }
    
    // Notify listeners
    const listeners = this.listeners.get(event.sessionId)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(event)
        } catch (error) {
          logger.error('Error in collaboration event listener', error)
        }
      })
    }
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached')
      return
    }
    
    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    
    logger.info(`Attempting reconnection in ${delay}ms (attempt ${this.reconnectAttempts})`)
    
    setTimeout(() => {
      // Attempt to reconnect with the last known session
      // This would need to be implemented based on your needs
    }, delay)
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(JSON.stringify({ type: 'ping' }))
      }
    }, 30000) // Send heartbeat every 30 seconds
  }

  private clearHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  // Simulation methods for demo/development

  private simulateConnection(sessionId: string, userId: string): void {
    // Create a simulated session
    const session: CollaborationSession = {
      id: sessionId,
      projectId: sessionId.split('-')[0], // Extract project ID from session ID
      documentId: sessionId.split('-')[1] || 'default',
      users: new Map(),
      isActive: true,
      createdAt: new Date()
    }
    
    // Add the current user
    session.users.set(userId, {
      id: userId,
      name: 'Current User',
      email: '<EMAIL>',
      color: this.generateUserColor()
    })
    
    this.sessions.set(sessionId, session)
    
    // Simulate other users joining
    setTimeout(() => {
      this.simulateUserJoin(sessionId, {
        id: 'demo-user-1',
        name: 'Alice Writer',
        email: '<EMAIL>',
        color: this.generateUserColor()
      })
    }, 2000)
    
    setTimeout(() => {
      this.simulateUserJoin(sessionId, {
        id: 'demo-user-2',
        name: 'Bob Editor',
        email: '<EMAIL>',
        color: this.generateUserColor()
      })
    }, 4000)
  }

  private simulateUserJoin(sessionId: string, user: CollaborationUser): void {
    const session = this.sessions.get(sessionId)
    if (!session) return
    
    session.users.set(user.id, user)
    
    this.handleCollaborationEvent({
      type: 'user.joined',
      sessionId,
      userId: user.id,
      data: user,
      timestamp: Date.now()
    })
    
    // Simulate cursor movements
    this.simulateCursorMovements(sessionId, user.id)
  }

  private simulateCursorMovements(sessionId: string, userId: string): void {
    const interval = setInterval(() => {
      const session = this.sessions.get(sessionId)
      if (!session || !session.isActive) {
        clearInterval(interval)
        return
      }
      
      // Random cursor position
      const position = {
        line: Math.floor(Math.random() * 50) + 1,
        column: Math.floor(Math.random() * 80) + 1
      }
      
      this.handleCollaborationEvent({
        type: 'cursor.moved',
        sessionId,
        userId,
        data: position,
        timestamp: Date.now()
      })
    }, 3000 + Math.random() * 3000) // Random interval between 3-6 seconds
  }

  private generateUserColor(): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57',
      '#FF6348', '#FFA502', '#FF4757', '#5F27CD', '#00D2D3'
    ]
    return colors[Math.floor(Math.random() * colors.length)]
  }
}

export const collaborationService = CollaborationService.getInstance()