'use client';

import { useEffect, useState } from 'react';
import { logger } from '@/lib/services/logger';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Sparkles,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { chapterQualityAnalyzer } from '@/lib/services/chapter-quality-analyzer';

interface QualityMetricsCardProps {
  projectId: string;
}

interface QualityData {
  avgOverallScore: number;
  avgReadability: number;
  avgCharacterConsistency: number;
  avgPlotConsistency: number;
  avgDialogueAuthenticity: number;
  avgPacing: number;
  avgCreativity: number;
  avgEmotionalImpact: number;
  avgMarketPotential: number;
  chaptersAnalyzed: number;
  lastAnalyzedAt: string | null;
  trend: { chapterNumber: number; overallScore: number }[];
}

export function QualityMetricsCard({ projectId }: QualityMetricsCardProps) {
  const [qualityData, setQualityData] = useState<QualityData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadQualityData();
  }, [projectId]);

  const loadQualityData = async () => {
    try {
      // Get project quality metrics
      const metrics = await chapterQualityAnalyzer.getProjectQualityMetrics(projectId);
      
      // Get quality trend
      const trend = await chapterQualityAnalyzer.getProjectQualityTrend(projectId, 5);
      
      if (metrics) {
        setQualityData({
          ...metrics,
          trend
        });
      }
    } catch (error) {
      logger.error('Error loading quality data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  const getScoreBadge = (score: number): string => {
    if (score >= 90) return 'Bestseller Quality';
    if (score >= 80) return 'Publisher Ready';
    if (score >= 70) return 'Strong';
    if (score >= 60) return 'Developing';
    return 'Needs Work';
  };

  const getTrendIcon = () => {
    if (!qualityData?.trend || qualityData.trend.length < 2) return null;
    
    const recent = qualityData.trend[qualityData.trend.length - 1];
    const previous = qualityData.trend[qualityData.trend.length - 2];
    
    if (recent.overallScore > previous.overallScore) {
      return <TrendingUp className="w-4 h-4 text-green-500" />;
    } else if (recent.overallScore < previous.overallScore) {
      return <TrendingDown className="w-4 h-4 text-red-500" />;
    }
    return null;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Sparkles className="w-5 h-5" />
            Quality Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-32 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (!qualityData || qualityData.chaptersAnalyzed === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Sparkles className="w-5 h-5" />
            Quality Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground">
              No quality analysis available yet.
              <br />
              <span className="text-sm">Quality metrics will appear after you save chapter content.</span>
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const topMetrics = [
    { name: 'Overall', score: qualityData.avgOverallScore },
    { name: 'Readability', score: qualityData.avgReadability },
    { name: 'Character Depth', score: qualityData.avgCharacterConsistency },
    { name: 'Plot Coherence', score: qualityData.avgPlotConsistency },
    { name: 'Market Potential', score: qualityData.avgMarketPotential }
  ];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Sparkles className="w-5 h-5" />
            Quality Analysis
          </CardTitle>
          {getTrendIcon()}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Score */}
        <div className="text-center">
          <div className={`text-4xl font-bold ${getScoreColor(qualityData.avgOverallScore)}`}>
            {Math.round(qualityData.avgOverallScore)}
          </div>
          <Badge variant="secondary" className="mt-1">
            {getScoreBadge(qualityData.avgOverallScore)}
          </Badge>
          <p className="text-xs text-muted-foreground mt-1">
            Based on {qualityData.chaptersAnalyzed} analyzed chapter{qualityData.chaptersAnalyzed !== 1 ? 's' : ''}
          </p>
        </div>

        {/* Key Metrics */}
        <div className="space-y-3">
          {topMetrics.map(metric => (
            <div key={metric.name}>
              <div className="flex items-center justify-between text-sm mb-1">
                <span className="font-medium">{metric.name}</span>
                <span className={getScoreColor(metric.score)}>
                  {Math.round(metric.score)}
                </span>
              </div>
              <Progress 
                value={metric.score} 
                className="h-2"
              />
            </div>
          ))}
        </div>

        {/* Additional Metrics */}
        <div className="grid grid-cols-2 gap-2 pt-2 border-t">
          <div className="text-sm">
            <span className="text-muted-foreground">Dialogue:</span>
            <span className={`ml-2 font-medium ${getScoreColor(qualityData.avgDialogueAuthenticity)}`}>
              {Math.round(qualityData.avgDialogueAuthenticity)}
            </span>
          </div>
          <div className="text-sm">
            <span className="text-muted-foreground">Pacing:</span>
            <span className={`ml-2 font-medium ${getScoreColor(qualityData.avgPacing)}`}>
              {Math.round(qualityData.avgPacing)}
            </span>
          </div>
          <div className="text-sm">
            <span className="text-muted-foreground">Creativity:</span>
            <span className={`ml-2 font-medium ${getScoreColor(qualityData.avgCreativity)}`}>
              {Math.round(qualityData.avgCreativity)}
            </span>
          </div>
          <div className="text-sm">
            <span className="text-muted-foreground">Impact:</span>
            <span className={`ml-2 font-medium ${getScoreColor(qualityData.avgEmotionalImpact)}`}>
              {Math.round(qualityData.avgEmotionalImpact)}
            </span>
          </div>
        </div>

        {/* Status */}
        <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
          <div className="flex items-center gap-1">
            <CheckCircle className="w-3 h-3" />
            <span>Auto-analyzing on save</span>
          </div>
          {qualityData.lastAnalyzedAt && (
            <span>
              Updated {new Date(qualityData.lastAnalyzedAt).toLocaleDateString()}
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}