import { BaseService } from './base-service';
import { logger } from '@/lib/services/logger';

import { ContextData, ServiceResponse } from './types';
import { createClient } from '../supabase/client';
import { consistencyValidator } from './consistency-validator';
import type { BookContext } from '../agents/types';

export class ContextManager extends BaseService {
  private contextCache: Map<string, ContextData> = new Map();
  private memoryCompression: Map<string, { compressed: string; timestamp: number }> = new Map();
  private supabase = createClient();

  constructor() {
    super({
      name: 'context-manager',
      version: '1.0.0',
      status: 'inactive',
      endpoints: ['/api/context/story-bible', '/api/context/continuity'],
      dependencies: [],
      healthCheck: '/api/context/health'
    });
  }

  async initialize(): Promise<void> {
    this.startMemoryCleanup();
    this.isInitialized = true;
    this.setStatus('active');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string; uptime: number }>> {
    return this.createResponse(true, {
      status: `${this.contextCache.size} contexts cached, ${this.memoryCompression.size} compressed memories`,
      uptime: Date.now() - (this.isInitialized ? Date.now() - 1000 : Date.now()),
    });
  }

  async shutdown(): Promise<void> {
    this.contextCache.clear();
    this.memoryCompression.clear();
    this.setStatus('inactive');
  }

  async getProjectContext(projectId: string, refresh = false): Promise<ServiceResponse<ContextData>> {
    return this.withErrorHandling(async () => {
      if (!refresh && this.contextCache.has(projectId)) {
        return this.contextCache.get(projectId)!;
      }

      const context = await this.buildProjectContext(projectId);
      this.contextCache.set(projectId, context);
      
      return context;
    });
  }

  async updateStoryBible(projectId: string, updates: Partial<ContextData['storyBible']>): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const { data: currentBible } = await this.supabase
        .from('story_bibles')
        .select('*')
        .eq('project_id', projectId)
        .single();

      const updatedBible = {
        ...currentBible,
        character_data: updates.characters ? { 
          ...currentBible?.character_data, 
          protagonists: updates.characters 
        } : currentBible?.character_data,
        world_data: updates.worldBuilding ? {
          ...currentBible?.world_data,
          locations: updates.worldBuilding
        } : currentBible?.world_data,
        plot_data: updates.plotlines ? {
          ...currentBible?.plot_data,
          plotlines: updates.plotlines
        } : currentBible?.plot_data,
        theme_data: updates.themes ? {
          ...currentBible?.theme_data,
          themes: updates.themes
        } : currentBible?.theme_data,
        style_data: updates.style ? {
          ...currentBible?.style_data,
          ...updates.style
        } : currentBible?.style_data
      };

      await this.supabase
        .from('story_bibles')
        .upsert([{
          project_id: projectId,
          ...updatedBible
        }]);

      // Update cache
      const cachedContext = this.contextCache.get(projectId);
      if (cachedContext) {
        cachedContext.storyBible = {
          characters: updates.characters || cachedContext.storyBible.characters,
          worldBuilding: updates.worldBuilding || cachedContext.storyBible.worldBuilding,
          plotlines: updates.plotlines || cachedContext.storyBible.plotlines,
          themes: updates.themes || cachedContext.storyBible.themes,
          style: updates.style || cachedContext.storyBible.style
        };
        this.contextCache.set(projectId, cachedContext);
      }

      return true;
    });
  }

  async validateContinuity(projectId: string, newContent: string, chapterNumber: number): Promise<ServiceResponse<{
    consistencyScore: number;
    issues: { type: string; description: string; severity: 'low' | 'medium' | 'high' }[];
    suggestions: string[];
  }>> {
    return this.withErrorHandling(async () => {
      const contextResponse = await this.getProjectContext(projectId);
      if (!contextResponse.success || !contextResponse.data) {
        throw new Error('Failed to get project context');
      }

      const context = contextResponse.data;
      const issues: { type: string; description: string; severity: 'low' | 'medium' | 'high' }[] = [];
      let consistencyScore = 100;

      // Character consistency checks
      const mentionedCharacters = this.extractCharacterMentions(newContent, context.storyBible.characters);
      mentionedCharacters.forEach(async (character) => {
        const characterData = context.storyBible.characters.find(c => (c as { name: string }).name === character);
        if (characterData) {
          const consistencyIssues = await this.checkCharacterConsistency(newContent, characterData);
          issues.push(...consistencyIssues);
        }
      });

      // Timeline consistency
      const timelineIssues = await this.checkTimelineConsistency(projectId, newContent, chapterNumber);
      issues.push(...timelineIssues);

      // World consistency
      const worldIssues = await this.checkWorldConsistency(newContent, context.storyBible.worldBuilding);
      issues.push(...worldIssues);

      // Calculate consistency score
      const highSeverityCount = issues.filter(i => i.severity === 'high').length;
      const mediumSeverityCount = issues.filter(i => i.severity === 'medium').length;
      const lowSeverityCount = issues.filter(i => i.severity === 'low').length;

      consistencyScore -= (highSeverityCount * 20) + (mediumSeverityCount * 10) + (lowSeverityCount * 5);
      consistencyScore = Math.max(0, consistencyScore);

      const suggestions = this.generateContinuitySuggestions(issues);

      return {
        consistencyScore,
        issues,
        suggestions
      };
    });
  }

  async compressMemory(projectId: string, content: string[]): Promise<ServiceResponse<string>> {
    return this.withErrorHandling(async () => {
      const combinedContent = content.join('\n\n');
      
      // Simple compression: extract key points and themes
      const keyPoints = await this.extractKeyPoints(combinedContent);
      const compressed = keyPoints.join('. ');
      
      this.memoryCompression.set(projectId, {
        compressed,
        timestamp: Date.now()
      });

      return compressed;
    });
  }

  async getCompressedMemory(projectId: string): Promise<ServiceResponse<string | null>> {
    return this.withErrorHandling(async () => {
      const memory = this.memoryCompression.get(projectId);
      return memory ? memory.compressed : null;
    });
  }

  async trackCrossProjectElements(projectIds: string[]): Promise<ServiceResponse<{
    sharedCharacters: { name: string; projects: string[] }[];
    sharedLocations: { name: string; projects: string[] }[];
    sharedThemes: { theme: string; projects: string[] }[];
  }>> {
    return this.withErrorHandling(async () => {
      const projectContexts = await Promise.all(
        projectIds.map(async id => {
          const response = await this.getProjectContext(id);
          return { id, context: response.data };
        })
      );

      const sharedCharacters: { name: string; projects: string[] }[] = [];
      const sharedLocations: { name: string; projects: string[] }[] = [];
      const sharedThemes: { theme: string; projects: string[] }[] = [];

      // Find shared characters
      const allCharacters = new Map<string, string[]>();
      projectContexts.forEach(({ id, context }) => {
        if (context) {
          context.storyBible.characters.forEach(char => {
            const name = (char as { name: string }).name || 'Unknown Character';
            if (!allCharacters.has(name)) {
              allCharacters.set(name, []);
            }
            allCharacters.get(name)!.push(id);
          });
        }
      });

      allCharacters.forEach((projects, name) => {
        if (projects.length > 1) {
          sharedCharacters.push({ name, projects });
        }
      });

      // Find shared locations
      const allLocations = new Map<string, string[]>();
      projectContexts.forEach(({ id, context }) => {
        if (context) {
          context.storyBible.worldBuilding.forEach(location => {
            const name = (location as { name: string }).name || 'Unknown Location';
            if (!allLocations.has(name)) {
              allLocations.set(name, []);
            }
            allLocations.get(name)!.push(id);
          });
        }
      });

      allLocations.forEach((projects, name) => {
        if (projects.length > 1) {
          sharedLocations.push({ name, projects });
        }
      });

      // Find shared themes
      const allThemes = new Map<string, string[]>();
      projectContexts.forEach(({ id, context }) => {
        if (context) {
          context.storyBible.themes.forEach(theme => {
            if (!allThemes.has(theme)) {
              allThemes.set(theme, []);
            }
            allThemes.get(theme)!.push(id);
          });
        }
      });

      allThemes.forEach((projects, theme) => {
        if (projects.length > 1) {
          sharedThemes.push({ theme, projects });
        }
      });

      return {
        sharedCharacters,
        sharedLocations,
        sharedThemes
      };
    });
  }

  private async buildProjectContext(projectId: string): Promise<ContextData> {
    // Get story bible
    const { data: storyBible } = await this.supabase
      .from('story_bibles')
      .select('*')
      .eq('project_id', projectId)
      .single();

    // Get chapters for timeline
    const { data: chapters } = await this.supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .order('chapter_number');

    const context: ContextData = {
      projectId,
      storyBible: {
        characters: storyBible?.character_data?.protagonists || [],
        worldBuilding: storyBible?.world_data?.locations || [],
        plotlines: storyBible?.plot_data?.plotlines || [],
        themes: storyBible?.theme_data?.themes || [],
        style: storyBible?.style_data || {}
      },
      continuity: {
        timeline: this.buildTimeline(chapters || []),
        relationships: this.extractRelationships(storyBible),
        consistency: await this.calculateOverallConsistency(projectId)
      },
      memory: {
        shortTerm: await this.getRecentContent(projectId, 5),
        longTerm: await this.getKeyContent(projectId),
        compressed: [this.memoryCompression.get(projectId)?.compressed || ''].filter(Boolean)
      }
    };

    return context;
  }

  private extractCharacterMentions(content: string, characters: Array<Record<string, unknown>>): string[] {
    const mentions: string[] = [];
    characters.forEach(char => {
      const name = (char as { name: string }).name;
      if (name && content.toLowerCase().includes(name.toLowerCase())) {
        mentions.push(name);
      }
    });
    return mentions;
  }

  private async checkCharacterConsistency(content: string, character: Record<string, unknown>): Promise<{
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
  }[]> {
    const issues: { type: string; description: string; severity: 'low' | 'medium' | 'high' }[] = [];

    // Check for trait consistency
    const characterName = (character as { name: string }).name;
    const characterPersonality = (character as { personality: string }).personality;
    if (characterPersonality && characterName && content.toLowerCase().includes(characterName.toLowerCase())) {
      const traits = characterPersonality.toLowerCase().split(' ');
      // Simplified check - would be more sophisticated in production
      if (traits.includes('shy') && content.toLowerCase().includes('boldly')) {
        issues.push({
          type: 'personality',
          description: `${character.name} is described as shy but acts boldly`,
          severity: 'medium'
        });
      }
    }

    return issues;
  }

  private async checkTimelineConsistency(projectId: string, content: string, chapterNumber: number): Promise<{
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
  }[]> {
    const issues: { type: string; description: string; severity: 'low' | 'medium' | 'high' }[] = [];

    // Get previous chapters to check timeline
    const { data: previousChapters } = await this.supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .lt('chapter_number', chapterNumber)
      .order('chapter_number');

    // Simple timeline check - look for temporal inconsistencies
    const timeReferences = content.match(/\b(yesterday|today|tomorrow|last week|next month)\b/gi) || [];
    
    if (timeReferences.length > 0 && previousChapters && previousChapters.length > 0) {
      // Would implement more sophisticated timeline tracking
    }

    return issues;
  }

  private async checkWorldConsistency(content: string, worldBuilding: Array<Record<string, unknown>>): Promise<{
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
  }[]> {
    const issues: { type: string; description: string; severity: 'low' | 'medium' | 'high' }[] = [];

    worldBuilding.forEach(element => {
      const elementName = (element as { name: string }).name;
      if (elementName && content.toLowerCase().includes(elementName.toLowerCase())) {
        // Check for consistency with established world rules
        if (element.rules) {
          // Would implement rule checking
        }
      }
    });

    return issues;
  }

  private generateContinuitySuggestions(issues: { type: string; description: string; severity: 'low' | 'medium' | 'high' }[]): string[] {
    const suggestions: string[] = [];

    issues.forEach(issue => {
      switch (issue.type) {
        case 'personality':
          suggestions.push('Review character personality traits and ensure consistent behavior');
          break;
        case 'timeline':
          suggestions.push('Check timeline references for logical sequence');
          break;
        case 'world':
          suggestions.push('Verify world-building elements match established rules');
          break;
      }
    });

    return Array.from(new Set(suggestions)); // Remove duplicates
  }

  private async extractKeyPoints(content: string): Promise<string[]> {
    // Simple key point extraction - would use more sophisticated NLP in production
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
    const keyPoints: string[] = [];

    sentences.forEach(sentence => {
      const trimmed = sentence.trim();
      if (trimmed.length > 30 && trimmed.length < 200) {
        // Simple heuristic: sentences with character names or action words
        if (/\b(said|did|went|saw|felt|thought)\b/i.test(trimmed)) {
          keyPoints.push(trimmed);
        }
      }
    });

    return keyPoints.slice(0, 10); // Limit to top 10 key points
  }

  private buildTimeline(chapters: Array<Record<string, unknown>>): Array<Record<string, unknown>> {
    return chapters.map((chapter, index) => ({
      chapter: chapter.chapter_number,
      events: [], // Would extract events from content
      timeReferences: [], // Would extract time references
      order: index
    }));
  }

  private extractRelationships(storyBible: Record<string, unknown>): Array<Record<string, unknown>> {
    const characterData = storyBible?.character_data as { protagonists?: unknown[] };
    if (!characterData?.protagonists) return [];

    const relationships: Array<Record<string, unknown>> = [];
    const characters = characterData.protagonists;

    (characters as Array<Record<string, unknown>>).forEach((char: Record<string, unknown>) => {
      if (char.relationships) {
        ((char.relationships as Array<Record<string, unknown>>)).forEach((rel: Record<string, unknown>) => {
          relationships.push({
            from: char.name,
            to: rel.character,
            type: rel.relationship,
            strength: rel.strength || 50
          });
        });
      }
    });

    return relationships;
  }

  private async calculateOverallConsistency(projectId?: string): Promise<number> {
    // If we have a project ID and consistency validator is available, use it
    if (projectId && consistencyValidator.getStatus() === 'active') {
      try {
        // Build a minimal BookContext for validation
        const { data: project } = await this.supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single();

        if (project) {
          const { bookContextBuilder } = await import('../utils/book-context-builder');
          const context = await bookContextBuilder.buildFromProjectId(
            projectId, 
            project.user_id
          );

          const report = await consistencyValidator.validateBookConsistency(context);
          if (report.success && report.data) {
            return report.data.overallScore;
          }
        }
      } catch (error) {
        logger.warn('Failed to calculate consistency with validator:', error);
      }
    }
    
    // Fallback to simple calculation
    return 85;
  }

  private async getRecentContent(projectId: string, limit: number): Promise<string[]> {
    const { data: recentChapters } = await this.supabase
      .from('chapters')
      .select('content')
      .eq('project_id', projectId)
      .order('updated_at', { ascending: false })
      .limit(limit);

    return recentChapters?.map(ch => ch.content || '').filter(Boolean) || [];
  }

  private async getKeyContent(projectId: string): Promise<string[]> {
    const { data: keyChapters } = await this.supabase
      .from('chapters')
      .select('content')
      .eq('project_id', projectId)
      .in('chapter_number', [1, 5, 10, 15, 20]) // Key chapters
      .order('chapter_number');

    return keyChapters?.map(ch => ch.content || '').filter(Boolean) || [];
  }

  private startMemoryCleanup(): void {
    // Clean up old compressed memories every hour
    setInterval(() => {
      const oneHourAgo = Date.now() - (60 * 60 * 1000);
      Array.from(this.memoryCompression.entries()).forEach(([projectId, memory]) => {
        if (memory.timestamp < oneHourAgo) {
          this.memoryCompression.delete(projectId);
        }
      });
    }, 60 * 60 * 1000);
  }
}