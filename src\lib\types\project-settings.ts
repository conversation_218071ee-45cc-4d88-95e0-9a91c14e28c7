export interface ProjectSettings {
  // Project Basics
  projectName: string;
  name?: string; // Alias for compatibility
  description: string;
  targetAudience: TargetAudience;
  contentRating: ContentRating;
  projectScope: ProjectScope;
  initialConcept: string;

  // Genre & Style
  primaryGenre: Genre;
  genre?: Genre; // Alias for compatibility
  subgenre: string;
  customGenre?: string;
  writingStyle: WritingStyle;
  tone: Tone[];
  toneOptions?: Tone[]; // Alias for compatibility
  narrativeVoice: NarrativeVoice;
  tense: Tense;
  customStyleDescription?: string;

  // Advanced Writing Techniques (all optional)
  useDeepPOV?: boolean;
  showDontTell?: boolean;
  layeredMetaphors?: boolean;
  sensoryRich?: boolean;
  subtextHeavy?: boolean;
  varyProse?: boolean;
  emotionalNuance?: boolean;
  cinematicScenes?: boolean;
  literaryAllusions?: boolean;
  preciseLanguage?: boolean;

  // Story Structure & Pacing
  structureType: StructureType;
  narrativeStructure?: NarrativeStructure;
  pacingPreference: PacingPreference;
  pacingPreferences?: PacingPreference; // Alias for compatibility
  chapterStructure: ChapterStructure;
  timelineComplexity: TimelineComplexity;
  customStructureNotes?: string;
  plotDevices?: PlotDevice[];
  tensionLevel?: number;
  conflictType?: ConflictType;
  storyBeginning?: StoryBeginning;
  storyEnding?: StoryEnding;
  storyInspirations?: string;

  // Character & World Building
  protagonistTypes: ProtagonistType[];
  antagonistTypes: AntagonistType[];
  characterComplexity: CharacterComplexity;
  characterArcTypes: CharacterArcType[];
  customCharacterConcepts?: string;
  timePeriod: TimePeriod;
  geographicSetting: GeographicSetting;
  worldType: WorldType;
  magicTechLevel: MagicTechLevel;
  customSettingDescription?: string;

  // Themes & Content
  majorThemes: MajorTheme[];
  philosophicalThemes: PhilosophicalTheme[];
  socialThemes: SocialTheme[];
  customThemes?: string;
  contentWarnings: ContentWarning[];
  culturalSensitivityNotes?: string;

  // Series & Scope
  seriesType?: SeriesType;
  interconnectionLevel?: InterconnectionLevel;
  customScopeDescription?: string;

  // Technical Specifications
  targetWordCount: number;
  targetChapters?: number;
  chapterCountType: ChapterCountType;
  povCharacterCount: number;
  povCharacterType: POVType;

  // Research & References
  researchNeeds: ResearchNeed[];
  factCheckingLevel: FactCheckingLevel;
  customResearchNotes?: string;
}

export type Genre = 
  | 'fantasy'
  | 'science_fiction'
  | 'mystery_thriller'
  | 'romance'
  | 'historical_fiction'
  | 'literary_fiction'
  | 'horror'
  | 'adventure'
  | 'young_adult'
  | 'contemporary_fiction';

export type FantasySubgenre =
  | 'epic_fantasy'
  | 'urban_fantasy'
  | 'dark_fantasy'
  | 'high_fantasy'
  | 'low_fantasy'
  | 'sword_sorcery'
  | 'magical_realism';

export type SciFiSubgenre =
  | 'space_opera'
  | 'cyberpunk'
  | 'dystopian'
  | 'time_travel'
  | 'hard_scifi'
  | 'soft_scifi'
  | 'steampunk'
  | 'biopunk';

export type MysterySubgenre =
  | 'cozy_mystery'
  | 'police_procedural'
  | 'noir'
  | 'psychological_thriller'
  | 'legal_thriller'
  | 'espionage';

export type NarrativeVoice =
  | 'first_person'
  | 'third_person_limited'
  | 'third_person_omniscient'
  | 'second_person'
  | 'multiple_pov';

export type Tense = 'present' | 'past' | 'mixed';

export type Tone =
  | 'dark_gritty'
  | 'light_humorous'
  | 'epic_heroic'
  | 'intimate_personal'
  | 'mysterious_suspenseful'
  | 'romantic_passionate'
  | 'philosophical_contemplative';

export type WritingStyle =
  | 'literary'
  | 'commercial'
  | 'pulp'
  | 'experimental'
  | 'minimalist'
  | 'descriptive'
  | 'dialogue_heavy';

export type StructureType =
  | 'three_act'
  | 'heros_journey'
  | 'save_the_cat'
  | 'freytags_pyramid'
  | 'seven_point'
  | 'fichtean_curve';

export type PacingPreference =
  | 'fast_paced'
  | 'slow_burn'
  | 'balanced'
  | 'character_driven'
  | 'plot_driven';

export type ChapterStructure =
  | 'fixed_length'
  | 'variable_length'
  | 'scene_based'
  | 'time_based';

export type TimelineComplexity =
  | 'linear'
  | 'flashbacks'
  | 'multiple_timelines'
  | 'non_linear';

export type ProtagonistType =
  | 'the_hero'
  | 'the_antihero'
  | 'the_reluctant_hero'
  | 'the_tragic_hero'
  | 'the_everyman'
  | 'the_mentor'
  | 'the_innocent';

export type AntagonistType =
  | 'the_villain'
  | 'the_shadow'
  | 'the_rival'
  | 'the_skeptic'
  | 'the_threshold_guardian'
  | 'the_shapeshifter';

export type CharacterComplexity =
  | 'simple_archetypal'
  | 'complex_layered'
  | 'morally_ambiguous'
  | 'ensemble_cast';

export type CharacterArcType =
  | 'positive_change'
  | 'negative_change'
  | 'flat_arc'
  | 'corruption_arc'
  | 'redemption_arc';

export type TimePeriod =
  | 'contemporary'
  | 'historical'
  | 'near_future'
  | 'far_future'
  | 'alternate_history'
  | 'timeless';

export type GeographicSetting =
  | 'urban'
  | 'rural'
  | 'suburban'
  | 'wilderness'
  | 'island'
  | 'underground'
  | 'space'
  | 'alternate_dimension';

export type WorldType =
  | 'real_world'
  | 'alternate_reality'
  | 'fantasy_world'
  | 'scifi_universe'
  | 'post_apocalyptic'
  | 'steampunk'
  | 'cyberpunk';

export type MagicTechLevel =
  | 'no_magic_current_tech'
  | 'low_magic_near_future'
  | 'high_magic_advanced_tech'
  | 'magitech_fusion';

export type MajorTheme =
  | 'love_relationships'
  | 'good_vs_evil'
  | 'coming_of_age'
  | 'redemption'
  | 'sacrifice'
  | 'power_corruption'
  | 'identity'
  | 'family'
  | 'survival'
  | 'justice';

export type PhilosophicalTheme =
  | 'existentialism'
  | 'morality'
  | 'free_will_vs_destiny'
  | 'nature_vs_nurture'
  | 'technology_vs_humanity';

export type SocialTheme =
  | 'class_struggle'
  | 'prejudice_discrimination'
  | 'war_peace'
  | 'environmental_issues'
  | 'political_intrigue';

export type TargetAudience =
  | 'children_8_12'
  | 'young_adult_13_17'
  | 'new_adult_18_25'
  | 'adult_25_plus'
  | 'all_ages';

export type ContentRating = 'G' | 'PG' | 'PG13' | 'R' | 'NC17';

export type ContentWarning =
  | 'violence_mild'
  | 'violence_moderate'
  | 'violence_graphic'
  | 'sexual_content_mild'
  | 'sexual_content_moderate'
  | 'sexual_content_explicit'
  | 'language_mild'
  | 'language_moderate'
  | 'language_strong'
  | 'substance_use'
  | 'mental_health_triggers'
  | 'abuse_themes'
  | 'death_themes';

export type ProjectScope =
  | 'standalone'
  | 'duology'
  | 'trilogy'
  | 'series_4_7'
  | 'epic_series_8_plus'
  | 'anthology';

export type SeriesType =
  | 'sequential'
  | 'parallel_timelines'
  | 'different_characters_same_world'
  | 'generational_saga';

export type InterconnectionLevel =
  | 'loose_connection'
  | 'moderate_connection'
  | 'tight_continuity'
  | 'shared_universe';

export type ChapterCountType = 'fixed' | 'flexible' | 'scene_based';

export type POVType =
  | 'single_pov'
  | 'dual_pov'
  | 'multiple_pov_3_5'
  | 'ensemble_cast_6_plus';

export type ResearchNeed =
  | 'historical_accuracy'
  | 'scientific_accuracy'
  | 'cultural_authenticity'
  | 'technical_expertise'
  | 'medical_accuracy'
  | 'legal_accuracy'
  | 'military_tactics';

export type FactCheckingLevel =
  | 'minimal'
  | 'moderate'
  | 'extensive'
  | 'expert_review';

export type NarrativeStructure = 
  | 'three_act_structure'
  | 'heros_journey'
  | 'save_the_cat'
  | 'fichtean_curve'
  | 'freytags_pyramid'
  | 'seven_point_structure'
  | 'custom';

export type PlotDevice = 
  | 'macguffin'
  | 'red_herring'
  | 'chekhov_gun'
  | 'flashback'
  | 'flash_forward'
  | 'deus_ex_machina'
  | 'plot_twist'
  | 'cliffhanger'
  | 'unreliable_narrator'
  | 'frame_story';

export type ConflictType = 
  | 'person_vs_person'
  | 'person_vs_self'
  | 'person_vs_society'
  | 'person_vs_nature'
  | 'person_vs_technology'
  | 'person_vs_supernatural'
  | 'person_vs_fate';

export type StoryBeginning = 
  | 'action'
  | 'dialogue'
  | 'setting'
  | 'character'
  | 'mystery'
  | 'backstory'
  | 'normal_world';

export type StoryEnding = 
  | 'closed'
  | 'open'
  | 'twist'
  | 'cliffhanger'
  | 'circular'
  | 'epilogue';

// SelectionProfile is now defined in database.ts to avoid conflicts