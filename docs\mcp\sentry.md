# Sentry MCP Server

The Sentry MCP server provides access to Sentry's error monitoring and performance tracking capabilities directly within Claude.

## Overview

Sentry MCP enables you to:
- Query application errors and exceptions
- Monitor performance metrics and traces
- Manage projects and team members
- Create and update issues
- Access release and deployment data

## Configuration

### Environment Variables

Add these to your Claude Desktop config:

```json
{
  "mcpServers": {
    "sentry": {
      "command": "npx",
      "args": ["@sentry/mcp-server"],
      "env": {
        "SENTRY_ORG": "your-organization-slug",
        "SENTRY_PROJECT": "your-project-slug", 
        "SENTRY_AUTH_TOKEN": "your-auth-token"
      }
    }
  }
}
```

### Getting Sentry Credentials

1. **Organization Slug**: Found in Sentry Settings → General
2. **Project Slug**: Found in Project Settings → General  
3. **Auth Token**: Create at https://sentry.io/settings/account/api/auth-tokens/
   - Needs scopes: `project:read`, `event:read`, `org:read`

## Available Tools

### Error Monitoring
- **Query Issues**: Search and filter application errors
- **Get Issue Details**: Retrieve full error context and stack traces
- **Update Issues**: Change status, assign to team members
- **Create Comments**: Add notes and context to issues

### Performance Monitoring
- **Query Transactions**: Search performance traces
- **Get Transaction Details**: Analyze slow operations
- **Performance Metrics**: Get aggregate performance data

### Project Management
- **List Projects**: Get all accessible projects
- **Project Stats**: View error counts and performance metrics
- **Team Management**: List and manage project members

### Releases & Deployments
- **List Releases**: Get deployment history
- **Release Details**: View commits and deployment info
- **Create Releases**: Track new deployments

## Example Usage

### Query Recent Errors
```
Show me the top 5 errors from the last 24 hours
```

### Investigate Specific Issue
```
Get details for Sentry issue #12345 including stack trace and user context
```

### Performance Analysis
```
What are the slowest API endpoints in production this week?
```

### Release Tracking
```
Show me all releases deployed in the last month with error counts
```

## Prompts and Resources

The Sentry MCP provides helpful prompts for:
- Error investigation workflows
- Performance optimization strategies
- Release management best practices

## Common Use Cases

### Bug Triage
- Quickly identify high-impact errors
- Get context on when issues started occurring
- Assign issues to the right team members

### Performance Optimization
- Find slow database queries and API calls
- Identify performance regressions after deployments
- Monitor key performance metrics

### Release Management
- Track error rates after deployments
- Compare performance across releases
- Monitor deployment health

## Security Notes

- Auth tokens should have minimal required scopes
- Use project-specific tokens when possible
- Regularly rotate authentication tokens
- Monitor token usage in Sentry settings

## Troubleshooting

### Authentication Issues
- Verify auth token has correct scopes
- Check organization and project slugs are correct
- Ensure token hasn't expired

### Permission Errors
- Confirm access to specified projects
- Check team membership and role permissions
- Verify organization membership

### API Rate Limits
- Sentry API has rate limits per organization
- Requests are automatically throttled
- Use specific queries to reduce API calls

## Links

- [Sentry MCP GitHub](https://github.com/getsentry/sentry-mcp)
- [Sentry API Documentation](https://docs.sentry.io/api/)
- [Sentry Authentication Guide](https://docs.sentry.io/api/auth/)