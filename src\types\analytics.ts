// Analytics type definitions
export interface AnalyticsInsight {
  title: string;
  content: string;
  recommendation?: string;
  type?: 'productivity' | 'quality' | 'consistency' | 'improvement';
  priority?: 'high' | 'medium' | 'low';
}

export interface AnalyticsAchievement {
  icon: string;
  title: string;
  description: string;
  unlockedAt?: string;
}

export interface AnalyticsGoal {
  id: string;
  type: string;
  title: string;
  current: number;
  target: number;
  progress: number;
  unit: string;
  deadline: string;
}

export interface AnalyticsQualityDimension {
  metric: string;
  score: number;
  description: string;
}

export interface AnalyticsChartData {
  date: string;
  value: number;
  [key: string]: string | number;
}

export interface AnalyticsProjectData {
  id: string;
  title: string;
  wordCount: number;
  progress: number;
  lastActive: string;
  quality?: number;
}

export interface AnalyticsTrend {
  value: number;
  direction: 'up' | 'down' | 'neutral';
}

export interface AnalyticsData {
  // Overview metrics
  totalWords: number;
  wordsTrend: AnalyticsTrend;
  currentStreak: number;
  streakTrend: AnalyticsTrend;
  avgDailyWords: number;
  avgWordsTrend: AnalyticsTrend;
  activeProjects: number;
  projectsTrend: AnalyticsTrend;
  
  // Charts data
  dailyWordCount: AnalyticsChartData[];
  heatmapData: Array<{ date: string; value: number; words: number }>;
  hourlyPattern: AnalyticsChartData[];
  weeklyPattern: AnalyticsChartData[];
  
  // Session data
  avgSessionDuration: number;
  avgWordsPerSession: number;
  totalSessions: number;
  
  // Project data
  projectProgress: AnalyticsChartData[];
  projectLines?: Array<{ dataKey: string; name: string; color: string }>;
  wordsByProject: AnalyticsChartData[];
  projectQuality: Array<{ metric: string; score: number }>;
  projects?: AnalyticsProjectData[];
  
  // Quality data
  qualityDimensions: AnalyticsQualityDimension[];
  qualityTrends: AnalyticsChartData[];
  qualityLines?: Array<{ dataKey: string; name: string; color: string }>;
  improvementAreas: Array<{ title: string; description: string }>;
  
  // Goals data
  activeGoals: AnalyticsGoal[];
  achievements: AnalyticsAchievement[];
  goalProgress: AnalyticsChartData[];
  
  // Insights
  insights: {
    recommendations: AnalyticsInsight[];
    achievements: AnalyticsAchievement[];
  };
  tips: Array<{
    title: string;
    content: string;
  }>;
}

export interface AnalyticsOverviewData {
  totalWords: number;
  wordsTrend: number;
  currentStreak: number;
  streakTrend: number; 
  avgDailyWords: number;
  avgWordsTrend: number;
  activeProjects: number;
  projectsTrend: number;
}