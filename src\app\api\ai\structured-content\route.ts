/**
 * Structured Content Generation API
 * Provides streaming structured content generation using Vercel AI SDK
 */

import { NextRequest } from 'next/server'
import { streamObject } from 'ai'
import { openai } from '@ai-sdk/openai'
import { authenticateUser, handleRouteError } from '@/lib/auth'
import { aiLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter'
import { AI_MODELS, AI_TEMPERATURE, AI_MAX_TOKENS } from '@/lib/config/ai-settings'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import {
  sceneOutlineSchema,
  dialogueResponseSchema,
  characterProfileGenerationSchema,
  worldBuildingSchema
} from '@/lib/schemas/content-schemas'

// Request validation schema
const structuredContentSchema = z.object({
  contentType: z.enum(['scene_outline', 'dialogue', 'character_profile', 'world_building']),
  prompt: z.string().min(1).max(10000),
  parameters: z.record(z.any()).optional(),
  model: z.string().optional(),
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().min(1).max(4000).optional(),
  streaming: z.boolean().optional().default(true)
})

// Schema mapping
const schemaMap = {
  scene_outline: sceneOutlineSchema,
  dialogue: dialogueResponseSchema,
  character_profile: characterProfileGenerationSchema,
  world_building: worldBuildingSchema
}

// System prompts for different content types
const systemPrompts = {
  scene_outline: 'You are an expert story structure specialist. Create detailed, engaging scene outlines that advance the plot and develop characters.',
  dialogue: 'You are an expert dialogue writer. Create natural, character-appropriate dialogue that reveals personality and advances the story.',
  character_profile: 'You are an expert character development specialist. Create rich, complex characters with clear motivations and compelling backstories.',
  world_building: 'You are an expert world-building specialist. Create immersive, consistent world elements that enhance the story\'s atmosphere.'
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for structured content (15 requests per hour)
    const clientIP = getClientIP(request)
    const rateLimitResult = aiLimiter.check(15, clientIP)
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset)
    }

    // Authenticate user
    const { user } = await authenticateUser(request)
    if (!user) {
      return new Response('Unauthorized', { status: 401 })
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = structuredContentSchema.parse(body)

    const {
      contentType,
      prompt,
      parameters = {},
      model = AI_MODELS.PRIMARY,
      temperature = AI_TEMPERATURE.BALANCED,
      maxTokens = AI_MAX_TOKENS.STANDARD,
      streaming = true
    } = validatedData

    // Get the appropriate schema and system prompt
    const schema = schemaMap[contentType]
    const systemPrompt = systemPrompts[contentType]

    if (!schema) {
      return new Response(
        JSON.stringify({ error: `Unsupported content type: ${contentType}` }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Map our model names to provider models
    const modelMap: Record<string, string> = {
      'gpt-4.1-2025-04-14': 'gpt-4-turbo',
      'gpt-4o-mini': 'gpt-4o-mini',
      'gpt-4-turbo': 'gpt-4-turbo',
      'gpt-4-0125-preview': 'gpt-4-turbo',
      'gpt-3.5-turbo': 'gpt-3.5-turbo',
      'grok-beta': 'grok-beta',
      'grok-vision-beta': 'grok-vision-beta',
    }

    const mappedModel = modelMap[model] || 'gpt-4-turbo'

    // Build enhanced prompt with parameters
    let enhancedPrompt = prompt
    if (Object.keys(parameters).length > 0) {
      enhancedPrompt += '\n\nAdditional parameters:\n' + 
        Object.entries(parameters)
          .map(([key, value]) => `- ${key}: ${value}`)
          .join('\n')
    }

    logger.info(`Structured content generation started for user ${user.id}`, {
      contentType,
      model: mappedModel,
      promptLength: enhancedPrompt.length,
      streaming
    })

    if (streaming) {
      // Create streaming structured response
      const result = await streamObject({
        model: openai(mappedModel),
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: enhancedPrompt }
        ],
        schema,
        temperature,
        maxTokens,
        onFinish: async ({ object, finishReason, usage }) => {
          logger.info(`Structured content generation completed for user ${user.id}`, {
            contentType,
            finishReason,
            tokensUsed: usage?.totalTokens,
            hasResult: !!object
          })
        }
      })

      return result.toTextStreamResponse()
    } else {
      // Non-streaming response (for backward compatibility)
      const { generateObject } = await import('ai')
      
      const result = await generateObject({
        model: openai(mappedModel),
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: enhancedPrompt }
        ],
        schema,
        temperature,
        maxTokens
      })

      logger.info(`Structured content generation completed for user ${user.id}`, {
        contentType,
        tokensUsed: result.usage?.totalTokens,
        hasResult: !!result.object
      })

      return new Response(
        JSON.stringify({
          success: true,
          data: result.object,
          usage: result.usage
        }),
        { headers: { 'Content-Type': 'application/json' } }
      )
    }

  } catch (error) {
    logger.error('Error in structured content generation:', error)
    
    if (error instanceof z.ZodError) {
      return new Response(
        JSON.stringify({ 
          error: 'Invalid request data', 
          details: error.errors 
        }), 
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    return handleRouteError(error, 'Structured Content Generation')
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'content-types':
        return new Response(
          JSON.stringify({
            success: true,
            data: {
              types: Object.keys(schemaMap),
              descriptions: {
                scene_outline: 'Detailed scene structure with plot points and character actions',
                dialogue: 'Natural character conversations with context and subtext',
                character_profile: 'Comprehensive character development with backstory and motivations',
                world_building: 'Rich world elements including locations, cultures, and history'
              },
              schemas: Object.fromEntries(
                Object.entries(schemaMap).map(([key, schema]) => [
                  key,
                  schema.describe ? schema.describe() : 'Schema description not available'
                ])
              )
            }
          }),
          { headers: { 'Content-Type': 'application/json' } }
        )

      case 'models':
        return new Response(
          JSON.stringify({
            success: true,
            data: {
              available: Object.keys(AI_MODELS),
              default: AI_MODELS.PRIMARY,
              recommended: AI_MODELS.PRIMARY
            }
          }),
          { headers: { 'Content-Type': 'application/json' } }
        )

      case 'parameters':
        const contentType = searchParams.get('contentType')
        const parameterGuides = {
          scene_outline: {
            chapterTitle: 'string - Title of the chapter',
            chapterSummary: 'string - Brief summary of the chapter',
            previousScenes: 'array - List of previous scene summaries',
            tone: 'string - Desired tone (dramatic, comedic, etc.)',
            style: 'string - Writing style preference'
          },
          dialogue: {
            characters: 'array - List of character names',
            context: 'string - Scene context and situation',
            tone: 'string - Conversation tone',
            length: 'string - short, medium, or long'
          },
          character_profile: {
            name: 'string - Character name',
            role: 'string - Character role in story',
            genre: 'string - Story genre',
            traits: 'array - Key personality traits',
            background: 'string - Character background'
          },
          world_building: {
            genre: 'string - Story genre',
            setting: 'string - World setting description',
            scope: 'string - location, culture, history, or politics',
            details: 'string - Additional world details'
          }
        }

        return new Response(
          JSON.stringify({
            success: true,
            data: contentType && parameterGuides[contentType] 
              ? parameterGuides[contentType]
              : parameterGuides
          }),
          { headers: { 'Content-Type': 'application/json' } }
        )

      default:
        return new Response(
          JSON.stringify({
            service: 'structured-content-generator',
            version: '2.0.0',
            endpoints: [
              'POST /api/ai/structured-content - Generate structured content',
              'GET /api/ai/structured-content?action=content-types - Get available content types',
              'GET /api/ai/structured-content?action=models - Get available models',
              'GET /api/ai/structured-content?action=parameters&contentType=X - Get parameter guide'
            ],
            features: [
              'Streaming structured responses',
              'Multiple content types',
              'Schema validation',
              'Parameter customization',
              'Rate limiting protection'
            ],
            usage: {
              rateLimit: '15 requests per hour',
              authentication: 'Required',
              streaming: 'Supported'
            }
          }),
          { headers: { 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    return handleRouteError(error, 'Structured Content GET')
  }
}
