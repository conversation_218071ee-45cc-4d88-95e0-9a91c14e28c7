// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sen<PERSON> from "@sentry/nextjs";

// Temporarily disable <PERSON><PERSON> to debug 500 errors
// Sentry.init({
//   dsn: "https://<EMAIL>/4509668380049408",

//   // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.
//   tracesSampleRate: 1,

//   // Setting this option to true will print useful information to the console while you're setting up Sentry.
//   debug: false,
// });
