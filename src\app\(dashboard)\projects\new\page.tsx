'use client'

import { useSearchParams } from 'next/navigation'
import { UnifiedProjectWizard } from '@/components/wizard/unified-project-wizard'

export default function NewProjectPage() {
  const searchParams = useSearchParams()
  const isGuided = searchParams.get('guided') === 'true'

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container flex h-16 items-center justify-between">
          <h1 className="text-2xl font-bold">Create New Project</h1>
        </div>
      </header>
      
      <main className="container py-8">
        <UnifiedProjectWizard 
          mode="live"
          display="page"
          guided={isGuided}
        />
      </main>
    </div>
  )
}