'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

function BasicUITest() {
  return (
    <div className="space-y-2">
      <Button>Test Button</Button>
      <Card className="p-4">Test Card</Card>
    </div>
  )
}

function EditorStoreTest() {
  try {
    // Dynamic import test without hooks
    return <div>Store test: Ready to load</div>
  } catch (e) {
    return <div>Store error: {(e as Error).message}</div>
  }
}

function NavigatorTest() {
  try {
    // Component availability test
    return <div>EnhancedChapterNavigator: Available</div>
  } catch (e) {
    return <div>Navigator error: {(e as Error).message}</div>
  }
}

function MonacoTest() {
  try {
    // Monaco editor test
    return <div>LazyMonacoEditor: Available</div>
  } catch (e) {
    return <div>Monaco error: {(e as Error).message}</div>
  }
}

function SidebarTest() {
  try {
    // Sidebar panel test
    return <div>EnhancedSidebarPanel: Available</div>
  } catch (e) {
    return <div>Sidebar error: {(e as Error).message}</div>
  }
}

export default function TestComponentsPage() {
  const [step, setStep] = useState(0)
  const [error, setError] = useState<string | null>(null)

  const testSteps = [
    {
      name: 'Basic UI Components',
      component: BasicUITest
    },
    {
      name: 'Editor Store',
      component: EditorStoreTest
    },
    {
      name: 'Enhanced Chapter Navigator',
      component: NavigatorTest
    },
    {
      name: 'Monaco Editor',
      component: MonacoTest
    },
    {
      name: 'Enhanced Sidebar Panel',
      component: SidebarTest
    }
  ]

  const runTest = (index: number) => {
    setError(null)
    try {
      setStep(index)
    } catch (e) {
      setError((e as Error).message)
    }
  }

  const CurrentTest = testSteps[step]?.component || BasicUITest

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center gap-4">
        <h1 className="text-2xl font-bold">Component Tests</h1>
        <div className="text-sm text-muted-foreground">
          Step {step + 1} of {testSteps.length}
        </div>
      </div>

      <div className="flex gap-2 flex-wrap">
        {testSteps.map((test, index) => (
          <Button
            key={index}
            variant={step === index ? 'default' : 'outline'}
            size="sm"
            onClick={() => runTest(index)}
          >
            {test.name}
          </Button>
        ))}
      </div>

      <Card className="p-6">
        <h2 className="font-semibold mb-4">{testSteps[step]?.name}</h2>
        <CurrentTest />
      </Card>

      {error && (
        <Card className="p-4 border-destructive bg-destructive/10">
          <div className="text-destructive font-medium">Error:</div>
          <div className="text-sm text-destructive/80">{error}</div>
        </Card>
      )}
    </div>
  )
}