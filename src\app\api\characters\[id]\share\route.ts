import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { logger } from '@/lib/services/logger';

export const runtime = 'nodejs';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function POST(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id: characterId } = params;
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { sourceSeriesId, targetSeriesId, shareType = 'reference', versionNotes } = body;

    if (!sourceSeriesId || !targetSeriesId) {
      return NextResponse.json({ 
        error: 'Source and target series IDs are required' 
      }, { status: 400 });
    }

    // Verify user owns the source series
    const { data: sourceSeries, error: sourceError } = await supabase
      .from('series')
      .select('id')
      .eq('id', sourceSeriesId)
      .eq('user_id', user.user.id)
      .single();

    if (sourceError || !sourceSeries) {
      return NextResponse.json({ 
        error: 'You do not have permission to share from this series' 
      }, { status: 403 });
    }

    // Verify the character belongs to the source series
    const { data: character, error: charError } = await supabase
      .from('characters')
      .select(`
        id,
        name,
        book:books!inner(
          id,
          series_books!inner(
            series_id
          )
        )
      `)
      .eq('id', characterId)
      .single();

    if (charError || !character) {
      return NextResponse.json({ error: 'Character not found' }, { status: 404 });
    }

    // Check if character belongs to source series
    const belongsToSource = character.book.series_books.some(
      (sb: { series_id: string }) => sb.series_id === sourceSeriesId
    );

    if (!belongsToSource) {
      return NextResponse.json({ 
        error: 'Character does not belong to the source series' 
      }, { status: 400 });
    }

    // Create the share using the database function
    const { data: share, error: shareError } = await supabase
      .rpc('share_character_to_series', {
        p_character_id: characterId,
        p_source_series_id: sourceSeriesId,
        p_target_series_id: targetSeriesId,
        p_share_type: shareType,
        p_version_notes: versionNotes
      });

    if (shareError) {
      logger.error('Error sharing character:', shareError);
      return NextResponse.json({ error: 'Failed to share character' }, { status: 500 });
    }

    // Fetch the complete share details
    const { data: shareDetails, error: detailsError } = await supabase
      .from('character_shares')
      .select(`
        *,
        character:characters(
          id,
          name,
          role,
          description
        ),
        source_series:series!character_shares_source_series_id_fkey(
          id,
          title
        ),
        target_series:series!character_shares_target_series_id_fkey(
          id,
          title
        )
      `)
      .eq('id', share)
      .single();

    if (detailsError) {
      logger.error('Error fetching share details:', detailsError);
      return NextResponse.json({ share: { id: share } });
    }

    return NextResponse.json({ share: shareDetails });
  } catch (error) {
    logger.error('Error in POST /api/characters/[id]/share:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id: characterId } = params;
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all shares for this character
    const { data: shares, error } = await supabase
      .from('character_shares')
      .select(`
        *,
        source_series:series!character_shares_source_series_id_fkey(
          id,
          title
        ),
        target_series:series!character_shares_target_series_id_fkey(
          id,
          title
        )
      `)
      .eq('character_id', characterId)
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('Error fetching character shares:', error);
      return NextResponse.json({ error: 'Failed to fetch character shares' }, { status: 500 });
    }

    return NextResponse.json({ shares });
  } catch (error) {
    logger.error('Error in GET /api/characters/[id]/share:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}