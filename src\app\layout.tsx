import type { Metada<PERSON> } from "next";
import "./globals.css";
import { AuthProvider } from "@/contexts/auth-context";
import { ThemeProvider } from "@/components/theme-provider";
import { ThemeInitializer } from "@/components/theme-initializer";
import { Toaster } from "@/components/ui/toaster";
import { ErrorBoundary } from "@/components/error/error-boundary";
import { CelebrationProvider } from "@/contexts/celebration-context";

export const metadata: Metadata = {
  title: "BookScribe AI - AI-Powered Novel Writing IDE",
  description: "Create epic novels with AI agents that maintain context across hundreds of thousands of words",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased" suppressHydrationWarning>
        <ErrorBoundary>
          <ThemeProvider>
            <ThemeInitializer />
            <AuthProvider>
              <CelebrationProvider>
                {children}
              </CelebrationProvider>
            </AuthProvider>
            <Toaster />
          </ThemeProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
