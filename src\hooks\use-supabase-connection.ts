'use client'

import { useEffect, useState, useCallback, useRef } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useToast } from '@/hooks/use-toast'

export type ConnectionState = 'connected' | 'connecting' | 'disconnected' | 'error'

interface ConnectionStatus {
  state: ConnectionState
  lastChecked: Date | null
  error?: string
  retryCount: number
}

export function useSupabaseConnection() {
  const [status, setStatus] = useState<ConnectionStatus>({
    state: 'connecting',
    lastChecked: null,
    retryCount: 0,
  })
  const { toast } = useToast()
  const statusRef = useRef(status)
  
  // Keep ref updated
  useEffect(() => {
    statusRef.current = status
  }, [status])
  
  const checkConnection = useCallback(async () => {
    const supabase = createClient()
    
    try {
      setStatus(prev => ({ ...prev, state: 'connecting' }))
      
      // Perform a simple query to check connection
      const { error } = await supabase
        .from('profiles')
        .select('count')
        .limit(1)
        .maybeSingle()
      
      if (error) {
        throw error
      }
      
      setStatus({
        state: 'connected',
        lastChecked: new Date(),
        retryCount: 0,
      })
      
      // Show reconnection toast if we were previously disconnected
      if (statusRef.current.state === 'disconnected' || statusRef.current.state === 'error') {
        toast({
          title: 'Connection restored',
          description: 'Successfully reconnected to the server.',
        })
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      
      setStatus(prev => ({
        state: 'error',
        lastChecked: new Date(),
        error: errorMessage,
        retryCount: prev.retryCount + 1,
      }))
      
      // Only show error toast on first failure or after multiple retries
      if (statusRef.current.state === 'connected' || statusRef.current.retryCount >= 3) {
        toast({
          title: 'Connection error',
          description: 'Unable to connect to the server. Some features may be unavailable.',
          variant: 'destructive',
        })
      }
    }
  }, [toast])
  
  // Check connection on mount
  useEffect(() => {
    checkConnection()
  }, [checkConnection])
  
  // Set up periodic checking based on connection state
  useEffect(() => {
    // Check every 30 seconds if connected, every 5 seconds if disconnected
    const interval = setInterval(
      checkConnection,
      status.state === 'connected' ? 30000 : 5000
    )
    
    return () => clearInterval(interval)
  }, [checkConnection, status.state])
  
  // Set up event listeners
  useEffect(() => {
    // Also check on window focus
    const handleFocus = () => {
      if (document.visibilityState === 'visible') {
        checkConnection()
      }
    }
    
    // Check on network status change
    const handleOnline = () => {
      toast({
        title: 'Network connected',
        description: 'Checking server connection...',
      })
      checkConnection()
    }
    
    const handleOffline = () => {
      setStatus({
        state: 'disconnected',
        lastChecked: new Date(),
        error: 'No network connection',
        retryCount: 0,
      })
      toast({
        title: 'Network disconnected',
        description: 'You are currently offline.',
        variant: 'destructive',
      })
    }
    
    window.addEventListener('focus', handleFocus)
    window.addEventListener('visibilitychange', handleFocus)
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    
    return () => {
      window.removeEventListener('focus', handleFocus)
      window.removeEventListener('visibilitychange', handleFocus)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [checkConnection, toast])
  
  const retry = useCallback(() => {
    setStatus(prev => ({ ...prev, retryCount: 0 }))
    checkConnection()
  }, [checkConnection])
  
  return {
    ...status,
    retry,
    isConnected: status.state === 'connected',
    isConnecting: status.state === 'connecting',
    hasError: status.state === 'error',
  }
}