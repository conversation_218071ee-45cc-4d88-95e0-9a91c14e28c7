-- =====================================================
-- COMPLETE BOOKSCRIBE AI DATABASE FIX
-- Creates ALL missing tables in correct order
-- =====================================================

-- 1. CREATE USER_ACHIEVEMENTS TABLES
-- =====================================================

-- Create achievements table for tracking user achievements
CREATE TABLE IF NOT EXISTS public.user_achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id TEXT NOT NULL,
  
  -- Achievement details
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('writing', 'consistency', 'quality', 'milestones', 'collaboration', 'special')),
  tier TEXT NOT NULL CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')),
  icon TEXT NOT NULL,
  
  -- Progress tracking
  current_value INTEGER DEFAULT 0,
  target_value INTEGER NOT NULL,
  unlocked_at TIMESTAMP WITH TIME ZONE,
  
  -- Metadata
  metadata JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique achievements per user
  UNIQUE(user_id, achievement_id)
);

-- Create achievement_definitions table for all possible achievements
CREATE TABLE IF NOT EXISTS public.achievement_definitions (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('writing', 'consistency', 'quality', 'milestones', 'collaboration', 'special')),
  tier TEXT NOT NULL CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')),
  icon TEXT NOT NULL,
  target_value INTEGER NOT NULL,
  
  -- Conditions for unlocking
  condition_type TEXT NOT NULL CHECK (condition_type IN ('word_count', 'streak', 'quality_score', 'chapters', 'projects', 'sessions', 'custom')),
  condition_params JSONB DEFAULT '{}',
  
  -- Display order and status
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  is_secret BOOLEAN DEFAULT false,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. CREATE QUALITY_METRICS TABLE
-- =====================================================

-- Create quality_metrics table to store chapter quality analysis results
CREATE TABLE IF NOT EXISTS public.quality_metrics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  chapter_id uuid REFERENCES public.chapters(id) ON DELETE CASCADE,
  project_id uuid REFERENCES public.projects(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Overall quality scores
  overall_score numeric(5,2) CHECK (overall_score >= 0 AND overall_score <= 100),
  
  -- Fundamental quality metrics
  coherence numeric(5,2) CHECK (coherence >= 0 AND coherence <= 100),
  style numeric(5,2) CHECK (style >= 0 AND style <= 100),
  grammar numeric(5,2) CHECK (grammar >= 0 AND grammar <= 100),
  creativity numeric(5,2) CHECK (creativity >= 0 AND creativity <= 100),
  pacing numeric(5,2) CHECK (pacing >= 0 AND pacing <= 100),
  character_consistency numeric(5,2) CHECK (character_consistency >= 0 AND character_consistency <= 100),
  plot_consistency numeric(5,2) CHECK (plot_consistency >= 0 AND plot_consistency <= 100),
  emotional_impact numeric(5,2) CHECK (emotional_impact >= 0 AND emotional_impact <= 100),
  readability numeric(5,2) CHECK (readability >= 0 AND readability <= 100),
  
  -- Bestseller-specific metrics
  show_dont_tell_ratio numeric(5,2) CHECK (show_dont_tell_ratio >= 0 AND show_dont_tell_ratio <= 100),
  sensory_engagement numeric(5,2) CHECK (sensory_engagement >= 0 AND sensory_engagement <= 100),
  dialogue_authenticity numeric(5,2) CHECK (dialogue_authenticity >= 0 AND dialogue_authenticity <= 100),
  hook_strength numeric(5,2) CHECK (hook_strength >= 0 AND hook_strength <= 100),
  pageturner_quality numeric(5,2) CHECK (pageturner_quality >= 0 AND pageturner_quality <= 100),
  literary_merit numeric(5,2) CHECK (literary_merit >= 0 AND literary_merit <= 100),
  market_potential numeric(5,2) CHECK (market_potential >= 0 AND market_potential <= 100),
  memorability numeric(5,2) CHECK (memorability >= 0 AND memorability <= 100),
  
  -- Analysis results
  strengths text[] DEFAULT ARRAY[]::text[],
  weaknesses text[] DEFAULT ARRAY[]::text[],
  suggestions text[] DEFAULT ARRAY[]::text[],
  
  -- Timestamps
  analyzed_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 3. CREATE PROJECT_QUALITY_METRICS TABLE
-- =====================================================

-- Create aggregated quality metrics table for project-level analysis
CREATE TABLE IF NOT EXISTS public.project_quality_metrics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id uuid REFERENCES public.projects(id) ON DELETE CASCADE UNIQUE,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Aggregated scores (averages across all chapters)
  avg_overall_score numeric(5,2),
  avg_coherence numeric(5,2),
  avg_style numeric(5,2),
  avg_grammar numeric(5,2),
  avg_creativity numeric(5,2),
  avg_pacing numeric(5,2),
  avg_character_consistency numeric(5,2),
  avg_plot_consistency numeric(5,2),
  avg_emotional_impact numeric(5,2),
  avg_readability numeric(5,2),
  avg_show_dont_tell_ratio numeric(5,2),
  avg_sensory_engagement numeric(5,2),
  avg_dialogue_authenticity numeric(5,2),
  avg_hook_strength numeric(5,2),
  avg_pageturner_quality numeric(5,2),
  avg_literary_merit numeric(5,2),
  avg_market_potential numeric(5,2),
  avg_memorability numeric(5,2),
  
  -- Additional fields that the API expects
  overall_score numeric(5,2),
  avg_consistency numeric(5,2),
  avg_engagement numeric(5,2),
  feedback text,
  improvement_suggestions text[],
  
  -- Chapter count and last analysis
  chapters_analyzed integer DEFAULT 0,
  last_analyzed_at timestamp with time zone,
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 4. CREATE INDEXES
-- =====================================================

-- Indexes for user_achievements
CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON public.user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_achievement_id ON public.user_achievements(achievement_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_unlocked_at ON public.user_achievements(unlocked_at);
CREATE INDEX IF NOT EXISTS idx_user_achievements_category ON public.user_achievements(category);

-- Indexes for quality_metrics
CREATE INDEX IF NOT EXISTS idx_quality_metrics_chapter_id ON public.quality_metrics(chapter_id);
CREATE INDEX IF NOT EXISTS idx_quality_metrics_project_id ON public.quality_metrics(project_id);
CREATE INDEX IF NOT EXISTS idx_quality_metrics_user_id ON public.quality_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_quality_metrics_analyzed_at ON public.quality_metrics(analyzed_at DESC);
CREATE INDEX IF NOT EXISTS idx_quality_metrics_overall_score ON public.quality_metrics(overall_score DESC);

-- Unique constraint for quality metrics
CREATE UNIQUE INDEX IF NOT EXISTS idx_quality_metrics_chapter_unique ON public.quality_metrics(chapter_id);

-- Indexes for project_quality_metrics
CREATE INDEX IF NOT EXISTS idx_project_quality_metrics_project_id ON public.project_quality_metrics(project_id);
CREATE INDEX IF NOT EXISTS idx_project_quality_metrics_user_id ON public.project_quality_metrics(user_id);

-- 5. ENABLE ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.achievement_definitions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quality_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_quality_metrics ENABLE ROW LEVEL SECURITY;

-- 6. CREATE RLS POLICIES
-- =====================================================

-- RLS policies for user_achievements
DROP POLICY IF EXISTS "Users can view their own achievements" ON public.user_achievements;
CREATE POLICY "Users can view their own achievements" ON public.user_achievements
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "System can create user achievements" ON public.user_achievements;
CREATE POLICY "System can create user achievements" ON public.user_achievements
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "System can update user achievements" ON public.user_achievements;
CREATE POLICY "System can update user achievements" ON public.user_achievements
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS policies for achievement_definitions (public read)
DROP POLICY IF EXISTS "Anyone can view achievement definitions" ON public.achievement_definitions;
CREATE POLICY "Anyone can view achievement definitions" ON public.achievement_definitions
  FOR SELECT USING (true);

-- RLS policies for quality_metrics
DROP POLICY IF EXISTS "Users can view their own quality metrics" ON public.quality_metrics;
CREATE POLICY "Users can view their own quality metrics" ON public.quality_metrics
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own quality metrics" ON public.quality_metrics;
CREATE POLICY "Users can insert their own quality metrics" ON public.quality_metrics
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own quality metrics" ON public.quality_metrics;
CREATE POLICY "Users can update their own quality metrics" ON public.quality_metrics
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own quality metrics" ON public.quality_metrics;
CREATE POLICY "Users can delete their own quality metrics" ON public.quality_metrics
  FOR DELETE USING (auth.uid() = user_id);

-- RLS policies for project_quality_metrics
DROP POLICY IF EXISTS "Users can view their own project quality metrics" ON public.project_quality_metrics;
CREATE POLICY "Users can view their own project quality metrics" ON public.project_quality_metrics
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own project quality metrics" ON public.project_quality_metrics;
CREATE POLICY "Users can insert their own project quality metrics" ON public.project_quality_metrics
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own project quality metrics" ON public.project_quality_metrics;
CREATE POLICY "Users can update their own project quality metrics" ON public.project_quality_metrics
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own project quality metrics" ON public.project_quality_metrics;
CREATE POLICY "Users can delete their own project quality metrics" ON public.project_quality_metrics
  FOR DELETE USING (auth.uid() = user_id);

-- 7. SUCCESS MESSAGE
-- =====================================================

SELECT 'SUCCESS: All database tables created successfully!' as status;
