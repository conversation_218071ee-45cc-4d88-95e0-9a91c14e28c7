/**
 * React Hooks for Streaming AI Content
 * Provides easy-to-use hooks for streaming AI content generation
 */

import { useState, useCallback, useRef, useEffect } from 'react'
import { useCompletion, useChat } from 'ai/react'

export interface StreamingState {
  content: string
  isLoading: boolean
  isStreaming: boolean
  error: string | null
  progress: {
    tokens: number
    estimatedTokens: number
    percentComplete: number
  }
  quality?: number
  metadata?: Record<string, unknown>
}

export interface UseStreamingContentOptions {
  onComplete?: (content: string) => void
  onError?: (error: Error) => void
  onProgress?: (progress: { tokens: number; content: string }) => void
  onQualityUpdate?: (quality: number) => void
  maxRetries?: number
}

/**
 * Hook for streaming text content generation
 */
export function useStreamingContent(options: UseStreamingContentOptions = {}) {
  const [state, setState] = useState<StreamingState>({
    content: '',
    isLoading: false,
    isStreaming: false,
    error: null,
    progress: {
      tokens: 0,
      estimatedTokens: 1000,
      percentComplete: 0
    }
  })

  const abortControllerRef = useRef<AbortController | null>(null)

  const { complete, completion, isLoading, error, stop } = useCompletion({
    api: '/api/ai/stream-content',
    onResponse: (response) => {
      if (!response.ok) {
        setState(prev => ({
          ...prev,
          error: `HTTP ${response.status}: ${response.statusText}`,
          isLoading: false,
          isStreaming: false
        }))
      }
    },
    onFinish: (prompt, completion) => {
      setState(prev => ({
        ...prev,
        content: completion,
        isLoading: false,
        isStreaming: false,
        progress: {
          ...prev.progress,
          percentComplete: 100
        }
      }))
      
      if (options.onComplete) {
        options.onComplete(completion)
      }
    },
    onError: (error) => {
      setState(prev => ({
        ...prev,
        error: error.message,
        isLoading: false,
        isStreaming: false
      }))
      
      if (options.onError) {
        options.onError(error)
      }
    }
  })

  // Update state when completion changes (streaming)
  useEffect(() => {
    if (completion !== state.content) {
      const tokens = completion.split(' ').length
      const percentComplete = Math.min((tokens / state.progress.estimatedTokens) * 100, 95)
      
      setState(prev => ({
        ...prev,
        content: completion,
        isStreaming: isLoading,
        progress: {
          ...prev.progress,
          tokens,
          percentComplete
        }
      }))

      if (options.onProgress) {
        options.onProgress({ tokens, content: completion })
      }
    }
  }, [completion, isLoading, state.content, state.progress.estimatedTokens, options])

  const generateContent = useCallback(async (
    prompt: string,
    systemPrompt?: string,
    estimatedTokens?: number,
    model?: string
  ) => {
    setState(prev => ({
      ...prev,
      content: '',
      isLoading: true,
      isStreaming: true,
      error: null,
      progress: {
        tokens: 0,
        estimatedTokens: estimatedTokens || 1000,
        percentComplete: 0
      }
    }))

    abortControllerRef.current = new AbortController()

    try {
      await complete(prompt, {
        body: {
          systemPrompt,
          estimatedTokens,
          model
        }
      })
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error',
        isLoading: false,
        isStreaming: false
      }))
    }
  }, [complete])

  const cancelGeneration = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    stop()
    setState(prev => ({
      ...prev,
      isLoading: false,
      isStreaming: false
    }))
  }, [stop])

  const reset = useCallback(() => {
    setState({
      content: '',
      isLoading: false,
      isStreaming: false,
      error: null,
      progress: {
        tokens: 0,
        estimatedTokens: 1000,
        percentComplete: 0
      }
    })
  }, [])

  return {
    ...state,
    generateContent,
    cancelGeneration,
    reset,
    isLoading: isLoading || state.isLoading
  }
}

/**
 * Hook for streaming chat conversations
 */
export function useStreamingChat(options: { 
  systemPrompt?: string
  onComplete?: (message: string) => void
  onError?: (error: Error) => void
} = {}) {
  const { messages, input, handleInputChange, handleSubmit, isLoading, error, stop, reload } = useChat({
    api: '/api/ai/chat',
    initialMessages: options.systemPrompt ? [
      { id: 'system', role: 'system', content: options.systemPrompt }
    ] : [],
    onFinish: (message) => {
      if (options.onComplete) {
        options.onComplete(message.content)
      }
    },
    onError: (error) => {
      if (options.onError) {
        options.onError(error)
      }
    }
  })

  const sendMessage = useCallback((message: string) => {
    handleSubmit(new Event('submit') as any, { data: { message } })
  }, [handleSubmit])

  const clearChat = useCallback(() => {
    // Reset to initial state with system message if provided
    const initialMessages = options.systemPrompt ? [
      { id: 'system', role: 'system', content: options.systemPrompt }
    ] : []
    
    // This will be handled by the chat hook's reset functionality
    reload()
  }, [reload, options.systemPrompt])

  return {
    messages: messages.filter(m => m.role !== 'system'), // Hide system messages from UI
    input,
    handleInputChange,
    handleSubmit,
    sendMessage,
    isLoading,
    error,
    stop,
    clearChat,
    reload
  }
}

/**
 * Hook for structured content generation with streaming
 */
export function useStreamingStructuredContent<T>(
  endpoint: string,
  options: UseStreamingContentOptions = {}
) {
  const [state, setState] = useState<StreamingState & { structuredData?: T }>({
    content: '',
    isLoading: false,
    isStreaming: false,
    error: null,
    progress: {
      tokens: 0,
      estimatedTokens: 1000,
      percentComplete: 0
    }
  })

  const generateStructuredContent = useCallback(async (
    params: Record<string, any>,
    estimatedTokens?: number
  ) => {
    setState(prev => ({
      ...prev,
      content: '',
      structuredData: undefined,
      isLoading: true,
      isStreaming: true,
      error: null,
      progress: {
        tokens: 0,
        estimatedTokens: estimatedTokens || 1000,
        percentComplete: 0
      }
    }))

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...params,
          streaming: true
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body')
      }

      let content = ''
      let tokens = 0

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n').filter(line => line.trim())

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              
              if (data.token) {
                content += data.token
                tokens++
                
                setState(prev => ({
                  ...prev,
                  content,
                  progress: {
                    ...prev.progress,
                    tokens,
                    percentComplete: Math.min((tokens / prev.progress.estimatedTokens) * 100, 95)
                  }
                }))

                if (options.onProgress) {
                  options.onProgress({ tokens, content })
                }
              }

              if (data.structured) {
                setState(prev => ({
                  ...prev,
                  structuredData: data.structured as T
                }))
              }

              if (data.quality && options.onQualityUpdate) {
                options.onQualityUpdate(data.quality)
              }

              if (data.done) {
                setState(prev => ({
                  ...prev,
                  isLoading: false,
                  isStreaming: false,
                  progress: {
                    ...prev.progress,
                    percentComplete: 100
                  }
                }))

                if (options.onComplete) {
                  options.onComplete(content)
                }
                break
              }
            } catch (e) {
              // Skip invalid JSON lines
            }
          }
        }
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error',
        isLoading: false,
        isStreaming: false
      }))

      if (options.onError) {
        options.onError(error instanceof Error ? error : new Error('Unknown error'))
      }
    }
  }, [endpoint, options])

  const reset = useCallback(() => {
    setState({
      content: '',
      structuredData: undefined,
      isLoading: false,
      isStreaming: false,
      error: null,
      progress: {
        tokens: 0,
        estimatedTokens: 1000,
        percentComplete: 0
      }
    })
  }, [])

  return {
    ...state,
    generateStructuredContent,
    reset
  }
}
