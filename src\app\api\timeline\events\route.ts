import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { db } from '@/lib/db/client';
import { getTimelineValidator } from '@/lib/timeline/timeline-instances';
import { createServerSupabaseClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createServerSupabaseClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    // Verify user owns the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single();

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      );
    }

    const validator = getTimelineValidator(projectId);
    
    // Get all events from database
    const events = await db.timelineEvents.getAll(projectId);
    
    // Validate timeline
    const validation = await validator.validateTimeline();
    
    return NextResponse.json({
      success: true,
      events,
      validation
    });

  } catch (error) {
    console.error('Error getting timeline events:', error);
    return NextResponse.json(
      { error: 'Failed to get timeline events' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createServerSupabaseClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { projectId, chapterContent, chapterNumber } = await request.json();
    
    if (!projectId || !chapterContent || !chapterNumber) {
      return NextResponse.json(
        { error: 'Project ID, chapter content, and chapter number are required' },
        { status: 400 }
      );
    }

    // Verify user owns the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single();

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      );
    }

    const validator = getTimelineValidator(projectId);
    
    // Extract timeline events from chapter content
    const extractedEvents = await validator.extractTimelineFromChapter(
      chapterContent,
      chapterNumber
    );

    // Add events to timeline
    const eventIds = [];
    for (const event of extractedEvents) {
      const eventId = await validator.addEvent(event);
      eventIds.push(eventId);
    }

    // Validate updated timeline
    const validation = await validator.validateTimeline();
    
    return NextResponse.json({
      success: true,
      extractedEvents: eventIds.length,
      validation
    });

  } catch (error) {
    console.error('Error adding timeline events:', error);
    return NextResponse.json(
      { error: 'Failed to add timeline events' },
      { status: 500 }
    );
  }
}

