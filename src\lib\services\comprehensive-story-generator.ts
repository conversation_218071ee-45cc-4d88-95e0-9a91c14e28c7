import { logger } from '@/lib/services/logger';
import { vercelAIClient } from '@/lib/ai/vercel-ai-client'
import { createClient } from '@/lib/supabase/server'
import { config } from '@/lib/config'
import { z } from 'zod'
import { AI_MODELS, getAIConfig, AI_CONCURRENCY } from '@/lib/config/ai-settings'
import { withRetry, ErrorContext } from '@/lib/services/error-handler'
import { withCircuitBreaker, CIRCUIT_BREAKER_PRESETS } from '@/lib/services/circuit-breaker'
import { 
  createStructuredOutputSchema, 
  parseStructuredOutput, 
  supportsStructuredOutputs,
  createJsonObjectFallback 
} from '../utils/structured-outputs'
import {
  storyStructureGenerationSchema,
  charactersGenerationSchema
} from '../schemas/content-schemas'
import type { Project } from '@/lib/db/types/tables'
import type { StoryBible } from '@/lib/agents/types'

export interface StoryGenerationRequest {
  projectId: string
  action: 'generate_complete_story' | 'generate_structure' | 'generate_characters' | 'generate_chapters' | 'write_chapter'
  chapterNumber?: number
  options?: {
    includeCharacters?: boolean
    includeWorldBuilding?: boolean
    includeStoryBible?: boolean
    regenerateExisting?: boolean
  }
}

export interface GeneratedStoryStructure {
  title: string
  premise: string
  genre: string
  themes: string[]
  acts: Array<{
    number: number
    title: string
    description: string
    chapters: number[]
    keyEvents: string[]
    wordCountTarget: number
  }>
  plotPoints: Array<{
    type: 'inciting_incident' | 'plot_point_1' | 'midpoint' | 'plot_point_2' | 'climax' | 'resolution'
    chapter: number
    description: string
  }>
  worldBuilding: {
    setting: string
    rules: string[]
    locations: Array<{ name: string; description: string }>
  }
}

export interface GeneratedCharacter {
  name: string
  role: 'protagonist' | 'antagonist' | 'supporting' | 'minor'
  description: string
  personality: {
    traits: string[]
    motivations: string[]
    fears: string[]
    goals: string[]
  }
  background: string
  characterArc: string
  relationships: Array<{ character: string; relationship: string }>
}

export interface GeneratedChapter {
  chapterNumber: number
  title: string
  summary: string
  scenes: Array<{
    number: number
    setting: string
    characters: string[]
    purpose: string
    conflict: string
    outcome: string
  }>
  keyEvents: string[]
  characterDevelopment: string[]
  plotAdvancement: string
  wordCountTarget: number
}

export class ComprehensiveStoryGenerator {
  private aiClient = vercelAIClient
  private supabase: ReturnType<typeof createClient>

  constructor() {
    // vercelAIClient is already initialized as a singleton
  }

  async initialize() {
    this.supabase = await createClient()
  }

  async generateCompleteStory(request: StoryGenerationRequest): Promise<{
    structure?: GeneratedStoryStructure
    characters?: GeneratedCharacter[]
    chapters?: GeneratedChapter[]
    storyBible?: Partial<StoryBible>
  }> {
    await this.initialize()

    // Get project details
    const { data: project } = await this.supabase
      .from('projects')
      .select('*')
      .eq('id', request.projectId)
      .single()

    if (!project) {
      throw new Error('Project not found')
    }

    logger.info(`Generating ${request.action} for project:`, project.title);

    let structure: GeneratedStoryStructure | undefined
    let characters: GeneratedCharacter[] | undefined
    let chapters: GeneratedChapter[] | undefined
    let storyBible: Partial<StoryBible> | undefined

    // Handle different action types
    switch (request.action) {
      case 'generate_structure':
        // Generate only story structure
        structure = await this.generateStoryStructure(project)
        if (request.options?.includeWorldBuilding) {
          storyBible = {
            worldBuilding: structure.worldBuilding,
            themes: structure.themes,
            plotPoints: structure.plotPoints
          }
        }
        // Save structure to database
        if (structure) {
          await this.saveStructureToDatabase(request.projectId, structure)
        }
        break

      case 'generate_characters':
        // Get existing structure if available
        const existingStructure = await this.getExistingStructure(request.projectId)
        structure = existingStructure || await this.generateStoryStructure(project)
        // Generate only characters
        characters = await this.generateCharacters(project, structure)
        // Save characters to database
        if (characters) {
          await this.saveCharactersToDatabase(request.projectId, characters)
        }
        break

      case 'generate_chapters':
        // Get existing structure and characters
        structure = await this.getExistingStructure(request.projectId) || await this.generateStoryStructure(project)
        characters = await this.getExistingCharacters(request.projectId) || await this.generateCharacters(project, structure)
        // Generate chapter outlines
        chapters = await this.generateChapterOutlines(project, structure, characters)
        // Save chapters to database
        if (chapters) {
          await this.saveChaptersToDatabase(request.projectId, chapters)
        }
        break

      case 'generate_complete_story':
      default:
        // Generate everything
        structure = await this.generateStoryStructure(project)
        characters = await this.generateCharacters(project, structure)
        chapters = await this.generateChapterOutlines(project, structure, characters)
        storyBible = await this.generateStoryBible(project, structure, characters)
        // Save everything to database
        await this.saveToDatabase(request.projectId, structure, characters, chapters, storyBible)
        break
    }

    return {
      structure,
      characters,
      chapters,
      storyBible
    }
  }

  private async generateStoryStructure(project: Project): Promise<GeneratedStoryStructure> {
    const prompt = `
Create a NYT bestseller-quality story structure for a ${project.primary_genre} novel titled "${project.title}".

Project Description: ${project.description || 'A compelling story'}
Target Word Count: ${project.target_word_count || 80000}
Target Chapters: ${project.target_chapters || 20}
Narrative Voice: ${project.narrative_voice || 'Third person'}
Tone: ${project.tone_options?.join(', ') || 'Engaging'}

BESTSELLER REQUIREMENTS:
- Create a structure that could compete with the top novels in ${project.primary_genre}
- Design hooks and twists that rival successful bestsellers
- Build emotional resonance that creates word-of-mouth buzz
- Ensure every element serves both artistic and commercial goals

Generate a bestseller-worthy story structure including:
1. Title that demands attention on bookstore shelves
2. Premise with instant "I must read this" appeal
3. Core themes that tap into universal human experiences (3-5)
3. Three-act structure with specific chapters
4. Key plot points with chapter locations
5. World building elements
6. Setting and locations

Format as JSON with this structure:
{
  "title": "string",
  "premise": "string", 
  "genre": "string",
  "themes": ["string"],
  "acts": [{"number": 1, "title": "string", "description": "string", "chapters": [1,2,3], "keyEvents": ["string"], "wordCountTarget": 26000}],
  "plotPoints": [{"type": "inciting_incident", "chapter": 2, "description": "string"}],
  "worldBuilding": {"setting": "string", "rules": ["string"], "locations": [{"name": "string", "description": "string"}]}
}
`

    const errorContext: ErrorContext = {
      operation: 'ComprehensiveStoryGenerator.generateStoryStructure',
      projectId: project.id,
      metadata: {
        projectTitle: project.title,
        targetWordCount: project.target_word_count
      }
    }

    const aiConfig = getAIConfig('STORY_STRUCTURE')
    
    // Use structured outputs if supported
    const responseFormat = supportsStructuredOutputs(aiConfig.model)
      ? createStructuredOutputSchema(storyStructureGenerationSchema, 'story_structure', 'Complete story structure with acts and plot points')
      : createJsonObjectFallback()

    try {
      // Use structured output with fallback support
      const result = await this.aiClient.generateObjectWithFallback(
        prompt,
        storyStructureGenerationSchema,
        {
          model: aiConfig.model,
          temperature: aiConfig.temperature,
          maxTokens: aiConfig.max_tokens,
          systemPrompt: 'You are an expert story architect creating bestseller-worthy novel structures. Respond with valid JSON that matches the required schema.'
        },
        errorContext
      )
      
      return result
    } catch (error) {
      logger.error('Failed to generate story structure:', error)
      
      // If structured generation fails, try text generation and parse manually
      try {
        const textResult = await this.aiClient.generateTextWithFallback(
          prompt,
          {
            model: aiConfig.model,
            temperature: aiConfig.temperature,
            maxTokens: aiConfig.max_tokens,
            systemPrompt: 'You are an expert story architect. Generate a JSON response with the story structure.'
          },
          errorContext
        )
        
        return JSON.parse(textResult)
      } catch (parseError) {
        logger.error('Failed to parse story structure:', parseError)
        throw new Error('Failed to generate valid story structure')
      }
    }
  }

  private async generateCharacters(project: Project, structure: GeneratedStoryStructure): Promise<GeneratedCharacter[]> {
    const prompt = `
Create detailed characters for the ${structure.genre} novel "${structure.title}".

Story Premise: ${structure.premise}
Themes: ${structure.themes.join(', ')}
Setting: ${structure.worldBuilding.setting}

Generate 4-6 main characters including:
- 1 protagonist
- 1 main antagonist  
- 2-4 supporting characters

For each character include: name, role, description, personality (traits, motivations, fears, goals), background, character arc, and key relationships.

Format as JSON array of character objects.
`

    const errorContext: ErrorContext = {
      operation: 'ComprehensiveStoryGenerator.generateCharacters',
      projectId: project.id,
      metadata: {
        storyTitle: structure.title,
        genre: structure.genre
      }
    }

    const aiConfig = getAIConfig('CHARACTER_DEVELOPMENT')
    
    // Use structured outputs if supported
    const responseFormat = supportsStructuredOutputs(aiConfig.model)
      ? createStructuredOutputSchema(charactersGenerationSchema, 'character_roster', 'Complete character roster with relationships')
      : createJsonObjectFallback()

    try {
      // Use structured output with fallback support
      const charactersWrapper = await this.aiClient.generateObjectWithFallback(
        prompt,
        charactersGenerationSchema,
        {
          model: aiConfig.model,
          temperature: aiConfig.temperature,
          maxTokens: aiConfig.max_tokens,
          systemPrompt: 'You are an expert character designer creating compelling characters for bestselling novels. Generate a diverse cast of characters with deep personalities and rich relationships.'
        },
        errorContext
      )
      
      // Extract the characters array from the wrapped response
      return charactersWrapper.characters
    } catch (error) {
      logger.error('Failed to generate characters:', error)
      
      // If structured generation fails, try text generation and parse manually
      try {
        const textResult = await this.aiClient.generateTextWithFallback(
          prompt,
          {
            model: aiConfig.model,
            temperature: aiConfig.temperature,
            maxTokens: aiConfig.max_tokens,
            systemPrompt: 'You are an expert character designer. Generate a JSON response with a "characters" array containing detailed character profiles.'
          },
          errorContext
        )
        
        const parsed = JSON.parse(textResult)
        return Array.isArray(parsed) ? parsed : (parsed.characters || [])
      } catch (parseError) {
        logger.error('Failed to parse characters:', parseError)
        throw new Error('Failed to generate valid characters')
      }
    }
  }

  private async generateChapterOutlines(
    project: Project, 
    structure: GeneratedStoryStructure, 
    characters: GeneratedCharacter[]
  ): Promise<GeneratedChapter[]> {
    const targetChapters = project.target_chapters || 20
    const wordsPerChapter = Math.floor((project.target_word_count || 80000) / targetChapters)
    const maxConcurrent = AI_CONCURRENCY.MAX_CONCURRENT_CHAPTERS

    // Generate chapters in batches to respect concurrency limits
    const chapters: GeneratedChapter[] = []
    
    for (let i = 1; i <= targetChapters; i += maxConcurrent) {
      const batchSize = Math.min(maxConcurrent, targetChapters - i + 1)
      const batchPromises: Promise<GeneratedChapter>[] = []
      
      for (let j = 0; j < batchSize; j++) {
        const chapterNumber = i + j
        if (chapterNumber <= targetChapters) {
          batchPromises.push(
            this.generateSingleChapterOutline(chapterNumber, structure, characters, wordsPerChapter)
          )
        }
      }
      
      const batchResults = await Promise.all(batchPromises)
      chapters.push(...batchResults)
      
      logger.info(`Generated chapters ${i} to ${Math.min(i + batchSize - 1, targetChapters)} of ${targetChapters}`)
    }

    // Sort chapters by number to ensure correct order
    return chapters.sort((a, b) => a.chapterNumber - b.chapterNumber)
  }

  private async generateSingleChapterOutline(
    chapterNumber: number,
    structure: GeneratedStoryStructure,
    characters: GeneratedCharacter[],
    wordCountTarget: number
  ): Promise<GeneratedChapter> {
    // Find which act this chapter belongs to
    const act = structure.acts.find(act => act.chapters.includes(chapterNumber)) || structure.acts[0]
    
    // Find relevant plot points
    const plotPoint = structure.plotPoints.find(pp => pp.chapter === chapterNumber)

    const prompt = `
Create a detailed outline for Chapter ${chapterNumber} of "${structure.title}".

Act: ${act.title} - ${act.description}
${plotPoint ? `Plot Point: ${plotPoint.type} - ${plotPoint.description}` : ''}

Available Characters: ${characters.map(c => `${c.name} (${c.role})`).join(', ')}
Target Word Count: ${wordCountTarget}

Generate:
1. Chapter title
2. Brief summary
3. 2-4 scenes with settings, characters, purpose, conflict, and outcome
4. Key events that happen
5. Character development moments
6. How this chapter advances the plot

Format as JSON.
`

    const errorContext: ErrorContext = {
      operation: 'ComprehensiveStoryGenerator.generateSingleChapterOutline',
      metadata: {
        chapterNumber,
        wordCountTarget
      }
    }

    const aiConfig = getAIConfig('CHAPTER_PLANNING')
    
    // Define schema for chapter outline
    const chapterOutlineSchema = z.object({
      title: z.string(),
      summary: z.string(),
      scenes: z.array(z.object({
        number: z.number(),
        setting: z.string(),
        characters: z.array(z.string()),
        purpose: z.string(),
        conflict: z.string(),
        outcome: z.string()
      })),
      keyEvents: z.array(z.string()),
      characterDevelopment: z.array(z.string()),
      plotAdvancement: z.string()
    })

    try {
      const outline = await this.aiClient.generateObjectWithFallback(
        prompt,
        chapterOutlineSchema,
        {
          model: aiConfig.model,
          temperature: aiConfig.temperature,
          maxTokens: Math.min(aiConfig.max_tokens, 1500),
          systemPrompt: 'You are an expert story planner creating compelling chapter outlines for bestselling novels. Generate detailed chapter structures that maintain narrative momentum.'
        },
        errorContext
      )
      
      return {
        chapterNumber,
        wordCountTarget,
        ...outline
      }
    } catch (error) {
      logger.error(`Failed to generate chapter ${chapterNumber} outline:`, error)
      
      // Return a basic structure if generation fails
      return {
        chapterNumber,
        title: `Chapter ${chapterNumber}`,
        summary: `Chapter ${chapterNumber} continues the story.`,
        scenes: [],
        keyEvents: [],
        characterDevelopment: [],
        plotAdvancement: `Chapter ${chapterNumber} advances the plot.`,
        wordCountTarget
      }
    }
  }

  private async generateStoryBible(
    project: Project,
    structure: GeneratedStoryStructure,
    characters: GeneratedCharacter[]
  ): Promise<Partial<StoryBible>> {
    return {
      worldBuilding: structure.worldBuilding,
      characterProfiles: characters,
      themes: structure.themes,
      timeline: structure.plotPoints,
      rules: structure.worldBuilding.rules,
      locations: structure.worldBuilding.locations
    }
  }

  private async saveToDatabase(
    projectId: string,
    structure: GeneratedStoryStructure,
    characters: GeneratedCharacter[],
    chapters: GeneratedChapter[],
    storyBible: Partial<StoryBible>
  ) {
    // Save story arcs (acts)
    for (const act of structure.acts) {
      await this.supabase.from('story_arcs').upsert({
        project_id: projectId,
        act_number: act.number,
        description: act.description,
        key_events: act.keyEvents
      })
    }

    // Save characters
    for (const character of characters) {
      await this.supabase.from('characters').upsert({
        project_id: projectId,
        name: character.name,
        role: character.role,
        description: character.description,
        personality_traits: character.personality,
        character_arc: character.characterArc
      })
    }

    // Save chapters
    for (const chapter of chapters) {
      await this.supabase.from('chapters').upsert({
        project_id: projectId,
        chapter_number: chapter.chapterNumber,
        title: chapter.title,
        target_word_count: chapter.wordCountTarget,
        outline: JSON.stringify(chapter),
        status: 'planned'
      })
    }

    // Save story bible entries
    const storyBibleEntries = [
      { entry_type: 'world_building', entry_key: 'setting', entry_data: structure.worldBuilding },
      { entry_type: 'themes', entry_key: 'main_themes', entry_data: { themes: structure.themes } },
      { entry_type: 'plot_structure', entry_key: 'acts', entry_data: { acts: structure.acts } }
    ]

    for (const entry of storyBibleEntries) {
      await this.supabase.from('story_bible').upsert({
        project_id: projectId,
        ...entry
      })
    }
  }

  private async saveStructureToDatabase(projectId: string, structure: GeneratedStoryStructure) {
    // Save story arcs (acts)
    if (structure.acts) {
      await this.supabase.from('story_arcs').delete().eq('project_id', projectId)
      
      for (const act of structure.acts) {
        await this.supabase.from('story_arcs').insert({
          project_id: projectId,
          act_number: act.number,
          description: act.description,
          key_events: act.keyEvents
        })
      }
    }

    // Save story bible entries for structure
    const storyBibleEntries = [
      { entry_type: 'world_building', entry_key: 'setting', entry_data: structure.worldBuilding },
      { entry_type: 'themes', entry_key: 'main_themes', entry_data: { themes: structure.themes } },
      { entry_type: 'plot_structure', entry_key: 'acts', entry_data: { acts: structure.acts } }
    ]

    for (const entry of storyBibleEntries) {
      await this.supabase.from('story_bible').upsert({
        project_id: projectId,
        ...entry
      })
    }
  }

  private async saveCharactersToDatabase(projectId: string, characters: GeneratedCharacter[]) {
    if (characters && characters.length > 0) {
      await this.supabase.from('characters').delete().eq('project_id', projectId)
      
      for (const character of characters) {
        await this.supabase.from('characters').insert({
          project_id: projectId,
          name: character.name,
          role: character.role,
          description: character.description,
          personality_traits: character.personality,
          character_arc: character.characterArc,
          backstory: character.background,
          relationships: character.relationships
        })
      }
    }
  }

  private async saveChaptersToDatabase(projectId: string, chapters: GeneratedChapter[]) {
    if (chapters && chapters.length > 0) {
      await this.supabase.from('chapters').delete().eq('project_id', projectId)
      
      for (const chapter of chapters) {
        await this.supabase.from('chapters').insert({
          project_id: projectId,
          chapter_number: chapter.chapterNumber,
          title: chapter.title,
          target_word_count: chapter.wordCountTarget,
          outline: JSON.stringify(chapter),
          status: 'planned'
        })
      }
    }
  }

  private async getExistingStructure(projectId: string): Promise<GeneratedStoryStructure | undefined> {
    const { data: arcs } = await this.supabase
      .from('story_arcs')
      .select('*')
      .eq('project_id', projectId)
      .order('act_number')

    if (!arcs || arcs.length === 0) return undefined

    const { data: project } = await this.supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single()

    if (!project) return undefined

    const { data: storyBible } = await this.supabase
      .from('story_bible')
      .select('*')
      .eq('project_id', projectId)

    const themes = storyBible?.find(entry => entry.entry_key === 'main_themes')?.entry_data?.themes || []
    const worldBuilding = storyBible?.find(entry => entry.entry_key === 'setting')?.entry_data || {
      setting: '',
      rules: [],
      locations: []
    }

    return {
      title: project.title,
      premise: project.description || '',
      genre: project.primary_genre,
      themes,
      acts: arcs.map(arc => ({
        number: arc.act_number,
        title: `Act ${arc.act_number}`,
        description: arc.description,
        chapters: [],
        keyEvents: arc.key_events || [],
        wordCountTarget: Math.floor((project.target_word_count || 80000) / 3)
      })),
      plotPoints: [],
      worldBuilding
    }
  }

  private async getExistingCharacters(projectId: string): Promise<GeneratedCharacter[] | undefined> {
    const { data: characters } = await this.supabase
      .from('characters')
      .select('*')
      .eq('project_id', projectId)

    if (!characters || characters.length === 0) return undefined

    return characters.map(char => ({
      name: char.name,
      role: char.role as 'protagonist' | 'antagonist' | 'supporting' | 'minor',
      description: char.description,
      personality: {
        traits: char.personality_traits?.traits || [],
        motivations: char.personality_traits?.motivations || [],
        fears: char.personality_traits?.fears || [],
        goals: char.personality_traits?.goals || []
      },
      background: char.backstory || '',
      characterArc: char.character_arc?.arc || '',
      relationships: char.relationships || []
    }))
  }
}
