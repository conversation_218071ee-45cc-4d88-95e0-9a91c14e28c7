import { vercelAIClient } from '@/lib/ai/vercel-ai-client';
import { BaseService } from './base-service';
import { ServiceResponse } from './types';
import { config } from '@/lib/config';
import { AI_MODELS, AI_QUALITY_THRESHOLDS, getAIConfig } from '../config/ai-settings';
import { logger } from './logger';
import { z } from 'zod';

interface QualityMetrics {
  overall: number;
  coherence: number;
  style: number;
  grammar: number;
  creativity: number;
  pacing: number;
  characterConsistency: number;
  plotConsistency: number;
  emotionalImpact: number;
  readability: number;
  // Bestseller-specific metrics
  showDontTellRatio: number;
  sensoryEngagement: number;
  dialogueAuthenticity: number;
  hookStrength: number;
  pageturnerQuality: number;
  literaryMerit: number;
  marketPotential: number;
  memorability: number;
}

interface QualityAnalysisResult {
  metrics: QualityMetrics;
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
  embedding?: number[];
}

interface ContentComparison {
  similarity: number;
  consistencyScore: number;
  styleDifference: number;
  issues: string[];
}

// Zod schemas for structured outputs
const qualityMetricsSchema = z.object({
  overall: z.number().min(0).max(100),
  coherence: z.number().min(0).max(100),
  style: z.number().min(0).max(100),
  grammar: z.number().min(0).max(100),
  creativity: z.number().min(0).max(100),
  pacing: z.number().min(0).max(100),
  characterConsistency: z.number().min(0).max(100),
  plotConsistency: z.number().min(0).max(100),
  emotionalImpact: z.number().min(0).max(100),
  readability: z.number().min(0).max(100),
  showDontTellRatio: z.number().min(0).max(100),
  sensoryEngagement: z.number().min(0).max(100),
  dialogueAuthenticity: z.number().min(0).max(100),
  hookStrength: z.number().min(0).max(100),
  pageturnerQuality: z.number().min(0).max(100),
  literaryMerit: z.number().min(0).max(100),
  marketPotential: z.number().min(0).max(100),
  memorability: z.number().min(0).max(100)
});

const qualityAnalysisSchema = z.object({
  metrics: qualityMetricsSchema,
  strengths: z.array(z.string()),
  weaknesses: z.array(z.string()),
  suggestions: z.array(z.string())
});

export class QualityAnalyzer extends BaseService {
  private aiClient = vercelAIClient;
  private embeddings: Map<string, number[]> = new Map();
  private referenceEmbeddings: Map<string, number[]> = new Map();

  constructor() {
    super({
      name: 'quality-analyzer',
      version: '1.0.0',
      status: 'inactive',
      endpoints: ['/api/quality/analyze', '/api/quality/compare'],
      dependencies: [],
      healthCheck: '/api/quality/health'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, {
      status: `${this.embeddings.size} embeddings cached`
    });
  }

  async shutdown(): Promise<void> {
    this.embeddings.clear();
    this.referenceEmbeddings.clear();
    this.setStatus('inactive');
  }

  /**
   * Analyze content quality using AI and embeddings
   */
  async analyzeContentQuality(
    content: string,
    contentType: 'chapter' | 'dialogue' | 'description' | 'scene' | 'character',
    context?: {
      previousContent?: string;
      characterProfiles?: Record<string, unknown>;
      storyContext?: Record<string, unknown>;
    }
  ): Promise<ServiceResponse<QualityAnalysisResult>> {
    return this.withErrorHandling(async () => {
      // Generate embedding for the content
      const embedding = await this.generateEmbedding(content);
      
      // Use AI to analyze quality with structured output
      const aiConfig = getAIConfig('ANALYSIS');
      const analysisPrompt = this.buildQualityAnalysisPrompt(content, contentType, context);
      
      const systemPrompt = `You are a legendary literary critic and bestselling author consultant who has guided numerous novels to the NYT bestseller list. Analyze the provided ${contentType} content against the highest standards of commercial and literary excellence.

EVALUATION EXPERTISE:
- Assess both literary merit and commercial viability
- Compare against current bestsellers and award winners
- Identify what makes writing "unputdownable"
- Recognize elements that create word-of-mouth buzz
- Evaluate emotional resonance and memorability

Provide a comprehensive quality analysis with detailed metrics, strengths, weaknesses, and suggestions.`;

      try {
        // Use structured output with fallback
        const analysisResult = await this.aiClient.generateObjectWithFallback(
          analysisPrompt,
          qualityAnalysisSchema,
          {
            model: aiConfig.model,
            temperature: aiConfig.temperature,
            maxTokens: aiConfig.max_tokens,
            systemPrompt
          }
        );

        const result: QualityAnalysisResult = {
          ...analysisResult,
          embedding
        };

      // Ensure minimum quality thresholds
      const minScore = AI_QUALITY_THRESHOLDS.MINIMUM_SCORES[
        contentType.toUpperCase() as keyof typeof AI_QUALITY_THRESHOLDS.MINIMUM_SCORES
      ] || AI_QUALITY_THRESHOLDS.ACCEPTABLE;

      if (result.metrics.overall < minScore) {
        result.weaknesses.push(`Overall quality below minimum threshold of ${minScore}`);
        result.suggestions.push(`Revise to improve overall quality to at least ${minScore}`);
      }

        return result;
      } catch (error) {
        logger.error('Failed to analyze content quality:', error);
        // Return default metrics if AI fails
        const defaultResult: QualityAnalysisResult = {
          metrics: {
            overall: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            coherence: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            style: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            grammar: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            creativity: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            pacing: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            characterConsistency: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            plotConsistency: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            emotionalImpact: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            readability: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            showDontTellRatio: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            sensoryEngagement: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            dialogueAuthenticity: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            hookStrength: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            pageturnerQuality: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            literaryMerit: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            marketPotential: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
            memorability: AI_QUALITY_THRESHOLDS.ACCEPTABLE
          },
          strengths: ['Content submitted for analysis'],
          weaknesses: ['AI analysis temporarily unavailable'],
          suggestions: ['Try again later for detailed analysis'],
          embedding
        };
        return defaultResult;
      }
    });
  }

  /**
   * Compare two pieces of content for consistency and style
   */
  async compareContent(
    content1: string,
    content2: string,
    comparisonType: 'style' | 'consistency' | 'both' = 'both'
  ): Promise<ServiceResponse<ContentComparison>> {
    return this.withErrorHandling(async () => {
      // Generate embeddings for both contents
      const [embedding1, embedding2] = await Promise.all([
        this.generateEmbedding(content1),
        this.generateEmbedding(content2)
      ]);

      // Calculate cosine similarity
      const similarity = this.cosineSimilarity(embedding1, embedding2);

      // Use AI for detailed comparison
      const aiConfig = getAIConfig('ANALYSIS');
      
      // Define schema for comparison result
      const comparisonSchema = z.object({
        consistencyScore: z.number().min(0).max(100),
        styleDifference: z.number().min(0).max(100),
        issues: z.array(z.string())
      });

      const comparisonPrompt = `Compare these two pieces of content for ${comparisonType}:

Content 1:
${content1.substring(0, 1000)}${content1.length > 1000 ? '...' : ''}

Content 2:
${content2.substring(0, 1000)}${content2.length > 1000 ? '...' : ''}

Provide a detailed analysis of:
1. Consistency issues (plot, characters, world-building)
2. Style differences (voice, tone, pacing)
3. Specific issues that need attention

Return the analysis as JSON with consistencyScore (0-100), styleDifference (0-100), and issues array.`;
      
      const systemPrompt = 'You are an expert at comparing literary content for consistency and style differences. Analyze the content and return a structured comparison.';
      
      let comparisonResult;
      try {
        comparisonResult = await this.aiClient.generateObjectWithFallback(
          comparisonPrompt,
          comparisonSchema,
          {
            model: aiConfig.model,
            temperature: aiConfig.temperature,
            maxTokens: aiConfig.max_tokens,
            systemPrompt
          }
        );
      } catch (error) {
        logger.error('Failed to compare content:', error);
        // Return default comparison if AI fails
        comparisonResult = {
          consistencyScore: 80,
          styleDifference: 20,
          issues: ['AI comparison temporarily unavailable']
        };
      }

      const result: ContentComparison = {
        similarity: similarity * 100,
        consistencyScore: comparisonResult.consistencyScore,
        styleDifference: comparisonResult.styleDifference,
        issues: comparisonResult.issues
      };

      return result;
    });
  }

  /**
   * Generate embeddings for content
   */
  private async generateEmbedding(text: string): Promise<number[]> {
    const cacheKey = this.hashText(text);
    
    if (this.embeddings.has(cacheKey)) {
      return this.embeddings.get(cacheKey)!;
    }

    const embedding = await this.aiClient.generateEmbedding(text.substring(0, 8000)); // Limit to max input size
    this.embeddings.set(cacheKey, embedding);
    
    return embedding;
  }

  /**
   * Calculate cosine similarity between two embeddings
   */
  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      throw new Error('Embeddings must have the same length');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);

    return normA === 0 || normB === 0 ? 0 : dotProduct / (normA * normB);
  }

  /**
   * Build quality analysis prompt
   */
  private buildQualityAnalysisPrompt(
    content: string,
    contentType: string,
    context?: {
      previousContent?: string;
      characterProfiles?: Record<string, unknown>;
      storyContext?: Record<string, unknown>;
    }
  ): string {
    let prompt = `Analyze this ${contentType} content for NYT bestseller-level quality:

${content}

EVALUATE AGAINST BESTSELLER STANDARDS:

FUNDAMENTAL QUALITY:
1. Overall quality compared to published bestsellers
2. Coherence and logical flow
3. Writing style and voice consistency
4. Grammar and technical excellence
5. Creativity and fresh perspectives

COMMERCIAL APPEAL:
6. Hook strength - Would readers continue after page 1?
7. Page-turner quality - Micro-hooks and chapter endings
8. Market potential - Comparison to successful titles
9. Memorability - Quotable lines and unforgettable moments
10. Crossover appeal - Multiple audience potential

LITERARY EXCELLENCE:
11. Show don't tell ratio - Action/dialogue vs exposition
12. Sensory engagement - Use of all five senses
13. Dialogue authenticity - Natural, character-revealing speech
14. Emotional impact - Depth of reader connection
15. Literary merit - Prose beauty and thematic depth

CHARACTER & PLOT:
16. Character consistency and depth
17. Plot consistency and surprise
18. Pacing - Balance of action and reflection
19. Conflict intensity and stakes
20. Resolution satisfaction

Compare to bestsellers like:
- Genre masters (Stephen King, John Grisham, Danielle Steel)
- Literary bestsellers (Donna Tartt, Anthony Doerr)
- Breakout hits (Gillian Flynn, Paula Hawkins)
- Award winners (Pulitzer, Booker, National Book Award)

Rate each metric 0-100 where:
- 90-100: Bestseller/award-winning quality
- 80-89: Publisher-ready with minor polish
- 70-79: Strong but needs development
- Below 70: Significant improvement needed`;

    if (context?.previousContent) {
      prompt += `\n\nPrevious content for context:\n${context.previousContent.substring(0, 1000)}...`;
    }

    if (context?.characterProfiles) {
      prompt += `\n\nCharacter profiles:\n${JSON.stringify(context.characterProfiles, null, 2).substring(0, 1000)}...`;
    }

    return prompt;
  }

  /**
   * Simple hash function for text caching
   */
  private hashText(text: string): string {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  /**
   * Store reference embeddings for style matching
   */
  async storeReferenceEmbedding(
    name: string,
    content: string
  ): Promise<ServiceResponse<{ stored: boolean }>> {
    return this.withErrorHandling(async () => {
      const embedding = await this.generateEmbedding(content);
      this.referenceEmbeddings.set(name, embedding);
      return { stored: true };
    });
  }

  /**
   * Compare content against reference style
   */
  async compareToReference(
    content: string,
    referenceName: string
  ): Promise<ServiceResponse<number>> {
    return this.withErrorHandling(async () => {
      const referenceEmbedding = this.referenceEmbeddings.get(referenceName);
      if (!referenceEmbedding) {
        throw new Error(`Reference embedding '${referenceName}' not found`);
      }

      const contentEmbedding = await this.generateEmbedding(content);
      return this.cosineSimilarity(contentEmbedding, referenceEmbedding) * 100;
    });
  }
}

// Singleton instance
export const qualityAnalyzer = new QualityAnalyzer();