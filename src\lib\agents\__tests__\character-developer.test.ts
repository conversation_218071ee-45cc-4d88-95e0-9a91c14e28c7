import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { CharacterDeveloper } from '../character-developer';
import type { BookContext, StoryStructure, CharacterProfiles, Character } from '../types';
import OpenAI from 'openai';

// Mock OpenAI
jest.mock('openai');

describe('CharacterDeveloper', () => {
  let developer: CharacterDeveloper;
  let mockContext: BookContext;
  let mockOpenAI: jest.Mocked<OpenAI>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockContext = {
      projectId: 'test-project-id',
      settings: {
        primaryGenre: 'fantasy',
        secondaryGenres: ['adventure'],
        targetAudience: 'young-adult',
        writingStyle: 'descriptive',
        narrativeVoice: 'third-person',
        tense: 'past',
        pacing: 'medium',
        violenceLevel: 'moderate',
        romanceLevel: 'moderate',
        profanityLevel: 'mild',
      },
      targetWordCount: 80000,
      targetChapters: 20,
      storyStructure: {
        title: 'The Quest',
        premise: 'A hero must save the kingdom',
        genre: 'fantasy',
        themes: ['courage', 'friendship', 'sacrifice'],
        acts: [],
        conflicts: [
          { type: 'external', description: 'Evil sorcerer threatens kingdom' },
          { type: 'internal', description: 'Hero doubts their worthiness' },
        ],
        timeline: [],
        worldBuilding: {
          setting: {
            timeForPeriod: 'Medieval fantasy',
            locations: [],
            culture: 'Feudal kingdom with magic',
            technology: 'Medieval',
          },
          rules: ['Magic has a cost', 'Prophecies always come true'],
          history: [],
        },
        plotPoints: [],
      },
    };

    mockOpenAI = {
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    } as unknown as jest.Mocked<OpenAI>;

    (OpenAI as jest.MockedClass<typeof OpenAI>).mockImplementation(() => mockOpenAI);
    
    developer = new CharacterDeveloper(mockContext);
  });

  describe('execute', () => {
    it('should generate character profiles', async () => {
      const mockCharacters: CharacterProfiles = {
        protagonists: [
          {
            id: 'char-1',
            name: 'Aria Starweaver',
            role: 'protagonist',
            age: 19,
            appearance: 'Tall with silver hair and violet eyes',
            personality: {
              traits: ['brave', 'compassionate', 'impulsive'],
              strengths: ['natural leader', 'quick learner'],
              weaknesses: ['self-doubt', 'too trusting'],
              fears: ['failing others', 'losing control'],
              desires: ['acceptance', 'peace'],
            },
            backstory: 'Orphaned at young age, raised by village elder',
            motivation: 'Protect the innocent and prove worthy of power',
            arc: {
              type: 'positive_change',
              startingPoint: 'Insecure farm girl',
              endingPoint: 'Confident hero',
              keyMoments: [
                {
                  chapterNumber: 5,
                  description: 'First successful use of magic',
                  emotionalState: 'Exhilarated but scared',
                  growth: 'Realizes potential',
                },
              ],
              internalConflict: 'Am I worthy of this power?',
              externalConflict: 'Must defeat the sorcerer',
            },
            voice: {
              speakingStyle: 'Direct and honest',
              vocabulary: 'Simple but growing sophisticated',
              mannerisms: ['Tugs hair when nervous', 'Quotes village sayings'],
            },
            relationships: ['mentor', 'best-friend'],
          },
        ],
        antagonists: [
          {
            id: 'char-2',
            name: 'Lord Malachar',
            role: 'antagonist',
            appearance: 'Imposing figure in dark robes',
            personality: {
              traits: ['ruthless', 'intelligent', 'patient'],
              strengths: ['master strategist', 'powerful magic'],
              weaknesses: ['arrogance', 'underestimates youth'],
              fears: ['death', 'being forgotten'],
              desires: ['immortality', 'absolute power'],
            },
            backstory: 'Former court wizard corrupted by dark magic',
            motivation: 'Achieve immortality through ancient ritual',
            arc: {
              type: 'flat_arc',
              startingPoint: 'Corrupted sorcerer',
              endingPoint: 'Defeated but unrepentant',
              keyMoments: [],
              internalConflict: 'None - fully committed to evil',
              externalConflict: 'Heroes threaten his plans',
            },
            voice: {
              speakingStyle: 'Eloquent and menacing',
              vocabulary: 'Archaic and sophisticated',
              mannerisms: ['Never raises voice', 'Quotes ancient texts'],
            },
            relationships: ['rival'],
          },
        ],
        supporting: [
          {
            id: 'char-3',
            name: 'Master Aldric',
            role: 'supporting',
            age: 65,
            appearance: 'Elderly wizard with kind eyes',
            personality: {
              traits: ['wise', 'patient', 'secretive'],
              strengths: ['vast knowledge', 'powerful ally'],
              weaknesses: ['aging', 'guilt over past'],
              fears: ['past mistakes repeated'],
              desires: ['redemption', 'worthy successor'],
            },
            backstory: 'Former rival of Malachar, now mentor to Aria',
            motivation: 'Guide Aria to fulfill prophecy',
            arc: {
              type: 'flat_arc',
              startingPoint: 'Wise mentor',
              endingPoint: 'Sacrifices self for heroes',
              keyMoments: [],
              internalConflict: 'Guilt over creating Malachar',
              externalConflict: 'Time running out',
            },
            voice: {
              speakingStyle: 'Measured and cryptic',
              vocabulary: 'Educated with occasional humor',
              mannerisms: ['Strokes beard', 'Speaks in riddles'],
            },
            relationships: ['mentor-to-aria', 'rival-to-malachar'],
          },
        ],
        relationships: [
          {
            character1Id: 'char-1',
            character2Id: 'char-3',
            type: 'mentor_mentee',
            description: 'Aldric guides Aria in magic and wisdom',
            dynamics: 'Paternal care mixed with hard lessons',
            evolution: [
              {
                chapterNumber: 3,
                change: 'Initial distrust to respect',
                reason: 'Aldric saves Aria from magical backlash',
              },
            ],
          },
          {
            character1Id: 'char-1',
            character2Id: 'char-2',
            type: 'enemy',
            description: 'Prophesied enemies',
            dynamics: 'Cat and mouse becoming direct conflict',
            evolution: [
              {
                chapterNumber: 10,
                change: 'First direct confrontation',
                reason: 'Aria powerful enough to challenge',
              },
            ],
          },
        ],
      };

      const mockResponse = {
        choices: [{
          message: {
            tool_calls: [{
              function: {
                arguments: JSON.stringify({
                  protagonists: mockCharacters.protagonists,
                  antagonists: mockCharacters.antagonists,
                  supporting: mockCharacters.supporting,
                  relationships: mockCharacters.relationships,
                }),
              },
            }],
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const result = await developer.execute();

      expect(result).toEqual(mockCharacters);
      expect(result.protagonists).toHaveLength(1);
      expect(result.antagonists).toHaveLength(1);
      expect(result.supporting).toHaveLength(1);
      expect(result.relationships).toHaveLength(2);
    });

    it('should throw error when story structure is missing', async () => {
      const incompleteContext = { ...mockContext };
      delete incompleteContext.storyStructure;
      
      developer = new CharacterDeveloper(incompleteContext);
      
      await expect(developer.execute()).rejects.toThrow('Character Developer requires story structure');
    });

    it('should create characters that align with story themes', async () => {
      const mockResponse = {
        choices: [{
          message: {
            tool_calls: [{
              function: {
                arguments: JSON.stringify({
                  protagonists: [{
                    id: '1',
                    name: 'Hero',
                    role: 'protagonist',
                    personality: {
                      traits: ['courageous', 'loyal', 'self-sacrificing'],
                      strengths: ['bravery', 'friendship'],
                      weaknesses: ['self-doubt'],
                      fears: ['failure'],
                      desires: ['peace'],
                    },
                    motivation: 'Embodies themes of courage and sacrifice',
                    arc: {
                      type: 'positive_change',
                      startingPoint: 'Reluctant',
                      endingPoint: 'Heroic',
                      keyMoments: [],
                      internalConflict: 'Fear vs duty',
                      externalConflict: 'Evil threat',
                    },
                    voice: {
                      speakingStyle: 'Honest',
                      vocabulary: 'Simple',
                      mannerisms: [],
                    },
                    backstory: 'Humble origins',
                    relationships: [],
                    appearance: 'Average looking',
                  }],
                  antagonists: [],
                  supporting: [],
                  relationships: [],
                }),
              },
            }],
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const result = await developer.execute();

      // Check that character embodies story themes
      const hero = result.protagonists[0];
      expect(hero.personality.traits).toContain('courageous');
      expect(hero.personality.traits).toContain('self-sacrificing');
      expect(hero.motivation).toContain('courage');
      expect(hero.motivation).toContain('sacrifice');
    });

    it('should generate appropriate character count based on story scope', async () => {
      // For an 80k word novel, expect reasonable cast size
      const mockResponse = {
        choices: [{
          message: {
            tool_calls: [{
              function: {
                arguments: JSON.stringify({
                  protagonists: [
                    { id: '1', name: 'Main Hero', role: 'protagonist' },
                    { id: '2', name: 'Secondary Hero', role: 'protagonist' },
                  ],
                  antagonists: [
                    { id: '3', name: 'Main Villain', role: 'antagonist' },
                  ],
                  supporting: [
                    { id: '4', name: 'Mentor', role: 'supporting' },
                    { id: '5', name: 'Friend', role: 'supporting' },
                    { id: '6', name: 'Ally', role: 'supporting' },
                  ],
                  relationships: [],
                }),
              },
            }],
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const result = await developer.execute();

      const totalCharacters = 
        result.protagonists.length + 
        result.antagonists.length + 
        result.supporting.length;

      // Expect reasonable cast size for novel length
      expect(totalCharacters).toBeGreaterThanOrEqual(4);
      expect(totalCharacters).toBeLessThanOrEqual(12);
    });

    it('should create meaningful character relationships', async () => {
      const mockResponse = {
        choices: [{
          message: {
            tool_calls: [{
              function: {
                arguments: JSON.stringify({
                  protagonists: [{ id: 'hero', name: 'Hero', role: 'protagonist' }],
                  antagonists: [{ id: 'villain', name: 'Villain', role: 'antagonist' }],
                  supporting: [{ id: 'friend', name: 'Friend', role: 'supporting' }],
                  relationships: [
                    {
                      character1Id: 'hero',
                      character2Id: 'friend',
                      type: 'friendship',
                      description: 'Childhood friends',
                      dynamics: 'Supportive but honest',
                      evolution: [{
                        chapterNumber: 5,
                        change: 'Tested by conflict',
                        reason: 'Friend questions hero choices',
                      }],
                    },
                    {
                      character1Id: 'hero',
                      character2Id: 'villain',
                      type: 'enemy',
                      description: 'Mortal enemies',
                      dynamics: 'Increasingly personal conflict',
                      evolution: [{
                        chapterNumber: 15,
                        change: 'Revelation of connection',
                        reason: 'Discover shared past',
                      }],
                    },
                  ],
                }),
              },
            }],
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const result = await developer.execute();

      expect(result.relationships).toHaveLength(2);
      
      // Check friendship relationship
      const friendship = result.relationships.find(r => r.type === 'friendship');
      expect(friendship).toBeDefined();
      expect(friendship?.evolution).toHaveLength(1);
      
      // Check enemy relationship
      const enemyRel = result.relationships.find(r => r.type === 'enemy');
      expect(enemyRel).toBeDefined();
      expect(enemyRel?.dynamics).toContain('conflict');
    });
  });
});