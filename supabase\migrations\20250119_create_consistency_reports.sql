-- Create consistency_reports table
CREATE TABLE IF NOT EXISTS public.consistency_reports (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
  
  -- Report details
  check_type TEXT NOT NULL CHECK (check_type IN ('chapter', 'book', 'timeline', 'characters')),
  overall_score INTEGER NOT NULL CHECK (overall_score >= 0 AND overall_score <= 100),
  
  -- Consistency dimensions
  character_consistency INTEGER CHECK (character_consistency >= 0 AND character_consistency <= 100),
  timeline_consistency INTEGER CHECK (timeline_consistency >= 0 AND timeline_consistency <= 100),
  plot_consistency INTEGER CHECK (plot_consistency >= 0 AND plot_consistency <= 100),
  world_consistency INTEGER CHECK (world_consistency >= 0 AND world_consistency <= 100),
  style_consistency INTEGER CHECK (style_consistency >= 0 AND style_consistency <= 100),
  
  -- Issues found
  issues JSONB DEFAULT '[]',
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_consistency_reports_user_id ON public.consistency_reports(user_id);
CREATE INDEX idx_consistency_reports_project_id ON public.consistency_reports(project_id);
CREATE INDEX idx_consistency_reports_chapter_id ON public.consistency_reports(chapter_id);
CREATE INDEX idx_consistency_reports_check_type ON public.consistency_reports(check_type);
CREATE INDEX idx_consistency_reports_created_at ON public.consistency_reports(created_at);

-- Enable RLS
ALTER TABLE public.consistency_reports ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can view their own consistency reports" ON public.consistency_reports
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own consistency reports" ON public.consistency_reports
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own consistency reports" ON public.consistency_reports
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own consistency reports" ON public.consistency_reports
  FOR DELETE USING (auth.uid() = user_id);