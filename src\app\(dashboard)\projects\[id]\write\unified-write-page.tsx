'use client'

import { useEffect, useState } from 'react'
import { useParams, useSearchParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { useEditorStore } from '@/stores/editor-store'
import { LazyMonacoEditor } from '@/components/editor/lazy-monaco-editor'
import { SaveStatusIndicator, useOnlineStatus } from '@/components/version-history/save-status-indicator'
import { ErrorBoundary } from '@/components/ui/error-boundary'
import { CollaborationIndicator } from '@/components/collaboration/collaboration-indicator'
import { useCollaboration } from '@/hooks/use-collaboration'
import { useSubscription } from '@/lib/subscription'
import { useAutoSave, useUnsavedChanges } from '@/hooks/use-auto-save'
import { useChapterGeneration, UserChanges } from '@/hooks/use-chapter-generation'
import { Button } from '@/components/ui/button'
import { BookImportDialog } from '@/components/import/book-import-dialog'
import { toast } from '@/hooks/use-toast'
import { WritingSessionTracker } from '@/components/editor/writing-session-tracker'
import { AgentStatusWidget } from '@/components/agents/agent-status-widget'
import { PanelProvider, usePanelSystem, PanelLayout } from '@/lib/panels'
import { PanelMenu } from '@/components/editor/panel-menu'
import { LazyChapterReviewPanel } from '@/components/lazy'
import { logger } from '@/lib/services/logger'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu'
import { 
  Save, 
  ArrowLeft,
  Wand2,
  Eye,
  Upload,
  Globe,
  BookOpenCheck,
  ChevronDown
} from 'lucide-react'
import Link from 'next/link'
import { Database } from '@/lib/db/types'
import { User } from '@supabase/supabase-js'

type Project = Database['public']['Tables']['projects']['Row']
type Chapter = Database['public']['Tables']['chapters']['Row']

function WritePageContent() {
  const params = useParams()
  const searchParams = useSearchParams()
  const projectId = params.id as string
  const chapterId = searchParams.get('chapter')
  
  const {
    content,
    setContent,
    selectedText,
    currentChapter,
    setCurrentChapter,
    updateChapterList
  } = useEditorStore()

  const [project, setProject] = useState<Project | null>(null)
  const [currentChapterData, setCurrentChapterData] = useState<Chapter | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [originalGeneratedContent, setOriginalGeneratedContent] = useState<string>('')
  const [focusMode, setFocusMode] = useState(false)
  const [seriesBooks, setSeriesBooks] = useState<Array<{id: string; title: string; book_number: number}>>([])
  
  const supabase = createClient()
  const isOnline = useOnlineStatus()
  const { setUnsavedChanges } = useUnsavedChanges()
  const subscription = useSubscription()
  
  // Real-time collaboration
  const collaborationSessionId = `project:${projectId}:chapter:${currentChapterData?.id}`
  const {
    sessionId: activeSessionId,
    participants,
    isConnected: isCollaborationConnected,
    joinSession,
    leaveSession,
    updateCursor,
  } = useCollaboration({
    sessionId: collaborationSessionId,
    userId: user?.id || '',
    onDocumentChange: (content) => {
      setContent(content)
      setHasUnsavedChanges(true)
    },
    onParticipantChange: (participants) => {
      // Handle participant changes
    },
    onCursorMove: (userId, position) => {
      // Handle cursor movement
    }
  })

  // Initialize panel system
  usePanelSystem()

  // Chapter generation hook
  const chapterGeneration = useChapterGeneration(projectId, currentChapterData?.id || '')

  // Auto-save hook
  const { saveNow } = useAutoSave(
    {
      chapterId: currentChapterData?.id || '',
      content,
      wordCount: content.trim().split(/\s+/).filter(word => word.length > 0).length
    },
    {
      enabled: !!currentChapterData?.id && isOnline,
      delay: 3000,
      onSave: (success) => {
        if (success) {
          setLastSaved(new Date())
          setHasUnsavedChanges(false)
          setUnsavedChanges(false)
        }
      },
      onError: (error) => {
        logger.error('Auto-save failed:', error);
      }
    }
  )

  useEffect(() => {
    loadUser()
    loadProject()
    loadChapters()
  }, [projectId])
  
  // Load series books when project is loaded
  useEffect(() => {
    if (project?.series_books?.[0]?.series_id) {
      loadSeriesBooks(project.series_books[0].series_id)
    }
  }, [project])

  const loadUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
  }

  const loadSeriesBooks = async (seriesId: string) => {
    const { data } = await supabase
      .from('series_books')
      .select(`
        book_number,
        project:projects(
          id,
          title
        )
      `)
      .eq('series_id', seriesId)
      .order('book_number')
    
    if (data) {
      setSeriesBooks(data.map(item => ({
        id: item.project.id,
        title: item.project.title,
        book_number: item.book_number
      })))
    }
  }

  // Track content changes for unsaved state
  useEffect(() => {
    if (currentChapterData && content !== (currentChapterData.content || '')) {
      setHasUnsavedChanges(true)
      setUnsavedChanges(true)
    }
  }, [content, currentChapterData, setUnsavedChanges])

  useEffect(() => {
    if (chapterId) {
      loadChapter(chapterId)
    } else {
      // Load or create first chapter
      loadFirstChapter()
    }
  }, [chapterId])

  const loadProject = async () => {
    const { data } = await supabase
      .from('projects')
      .select(`
        *,
        series_books!series_books_project_id_fkey(
          series_id,
          series:series(
            id,
            title
          )
        )
      `)
      .eq('id', projectId)
      .single()
    
    if (data) setProject(data)
  }

  const loadChapters = async () => {
    const { data } = await supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .order('chapter_number')
    
    if (data) {
      updateChapterList(data.map(ch => ({
        id: ch.id,
        number: ch.chapter_number,
        title: ch.title || `Chapter ${ch.chapter_number}`,
        status: ch.status,
        wordCount: ch.actual_word_count || 0
      })))
    }
  }

  const loadChapter = async (id: string) => {
    const { data } = await supabase
      .from('chapters')
      .select('*')
      .eq('id', id)
      .single()
    
    if (data) {
      setCurrentChapterData(data)
      setContent(data.content || '')
      setCurrentChapter(data.chapter_number)
    }
  }

  const loadFirstChapter = async () => {
    const { data } = await supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .order('chapter_number')
      .limit(1)
      .single()
    
    if (data) {
      setCurrentChapterData(data)
      setContent(data.content || '')
      setCurrentChapter(data.chapter_number)
    } else {
      // Create first chapter
      createNewChapter()
    }
  }

  const createNewChapter = async () => {
    const chapterNumber = currentChapter || 1
    const { data } = await supabase
      .from('chapters')
      .insert({
        project_id: projectId,
        chapter_number: chapterNumber,
        title: `Chapter ${chapterNumber}`,
        content: '',
        status: 'writing'
      })
      .select()
      .single()

    if (data) {
      setCurrentChapterData(data)
      setContent('')
      loadChapters()
    }
  }

  const saveChapter = async () => {
    if (!currentChapterData) return
    
    setIsSaving(true)
    try {
      const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length
      
      const { error } = await supabase
        .from('chapters')
        .update({
          content,
          actual_word_count: wordCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', currentChapterData.id)

      if (!error) {
        setLastSaved(new Date())
        loadChapters() // Refresh chapter list
      }
    } finally {
      setIsSaving(false)
    }
  }

  const handleChapterSelect = (id: string) => {
    window.history.pushState({}, '', `/projects/${projectId}/write?chapter=${id}`)
    loadChapter(id)
  }

  const handleCreateChapter = async () => {
    const newChapterNumber = Math.max(...useEditorStore.getState().chapters.map(ch => ch.number), 0) + 1
    
    const { data } = await supabase
      .from('chapters')
      .insert({
        project_id: projectId,
        chapter_number: newChapterNumber,
        title: `Chapter ${newChapterNumber}`,
        content: '',
        status: 'writing'
      })
      .select()
      .single()

    if (data) {
      loadChapters()
      handleChapterSelect(data.id)
    }
  }

  const handleGenerateChapter = async () => {
    if (!currentChapterData) return

    const generatedData = await chapterGeneration.generateChapter(currentChapterData.chapter_number)
    if (generatedData) {
      setOriginalGeneratedContent(generatedData.content)
      setContent(generatedData.content)
      
      // Start the review process
      chapterGeneration.startReview(generatedData.content, generatedData.content)
    }
  }

  const handleReviewChanges = () => {
    if (originalGeneratedContent && content !== originalGeneratedContent) {
      chapterGeneration.startReview(originalGeneratedContent, content)
    }
  }

  const handleSubmitChanges = async (changes: UserChanges) => {
    const success = await chapterGeneration.submitChanges(changes)
    if (success) {
      setOriginalGeneratedContent('')
      loadChapters() // Refresh chapter list
    }
    return success
  }

  const handleApproveGeneration = async () => {
    const success = await chapterGeneration.approveGeneration()
    if (success) {
      setOriginalGeneratedContent('')
      loadChapters() // Refresh chapter list
    }
    return success
  }

  // Panel metadata for context
  const panelMetadata = {
    onChapterSelect: handleChapterSelect,
    onCreateChapter: handleCreateChapter
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <header className="border-b px-4 py-2 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href={`/projects/${projectId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Project
            </Button>
          </Link>
          
          <div className="flex items-center gap-2">
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                {project?.series_books?.[0]?.series?.universe && (
                  <>
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      {project.series_books[0].series.universe.name}
                    </span>
                    <span className="text-muted-foreground">→</span>
                  </>
                )}
                {project?.series_books?.[0]?.series && (
                  <>
                    <BookOpenCheck className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      {project.series_books[0].series.title} (Book {project.series_books[0].book_number})
                    </span>
                    <span className="text-muted-foreground">→</span>
                  </>
                )}
                {/* Project title with series navigation dropdown */}
                {seriesBooks.length > 1 ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="p-0 h-auto font-semibold text-lg">
                        {project?.title || 'Loading...'}
                        <ChevronDown className="h-4 w-4 ml-1" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-64">
                      <DropdownMenuLabel>Books in this Series</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {seriesBooks.map((book) => (
                        <DropdownMenuItem
                          key={book.id}
                          onClick={() => {
                            if (book.id !== projectId) {
                              window.location.href = `/projects/${book.id}/write`
                            }
                          }}
                          className={book.id === projectId ? 'bg-muted' : ''}
                        >
                          <div className="flex items-center gap-2">
                            <BookOpenCheck className="h-4 w-4" />
                            <div className="flex flex-col">
                              <span className="font-medium">{book.title}</span>
                              <span className="text-xs text-muted-foreground">
                                Book {book.book_number}
                              </span>
                            </div>
                          </div>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <h1 className="text-lg font-semibold">
                    {project?.title || 'Loading...'}
                  </h1>
                )}
                {currentChapterData && (
                  <span className="text-sm text-muted-foreground">
                    • Chapter {currentChapterData.chapter_number}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Real-time Collaboration Indicator */}
          {currentChapterData && user && (
            <CollaborationIndicator
              sessionId={activeSessionId || collaborationSessionId}
              isConnected={isCollaborationConnected}
              collaborators={participants.map(p => ({
                id: p.userId,
                name: `User ${p.userId.slice(0, 8)}`,
                email: `${p.userId}@example.com`,
                color: ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6'][Math.abs(p.userId.split('').reduce((a, b) => a + b.charCodeAt(0), 0)) % 5],
                cursor: p.cursor,
                lastSeen: new Date()
              }))}
              currentUserId={user.id}
              canInvite={subscription?.plan === 'studio'}
              onToggleCollaboration={async (enabled) => {
                if (enabled && !isCollaborationConnected) {
                  await joinSession('editor')
                } else if (!enabled && isCollaborationConnected) {
                  await leaveSession()
                }
              }}
            />
          )}
          
          {/* Agent Status */}
          <AgentStatusWidget projectId={projectId} />
          
          {/* Writing Session Display */}
          {user && (
            <WritingSessionTracker 
              userId={user.id}
              projectId={projectId}
              chapterId={currentChapterData?.id}
              variant="compact"
            />
          )}
          <SaveStatusIndicator 
            isSaving={isSaving}
            lastSaved={lastSaved}
            hasUnsavedChanges={hasUnsavedChanges}
            isOnline={isOnline}
            onManualSave={saveNow}
          />
          
          <BookImportDialog 
            projectId={projectId}
            onImportComplete={() => {
              loadChapters()
              toast({
                title: "Import complete",
                description: "Your chapters have been imported. Select a chapter to continue editing."
              })
            }}
            trigger={
              <Button variant="ghost" size="sm">
                <Upload className="h-4 w-4" />
              </Button>
            }
          />
          
          {/* Panel Menu */}
          <PanelMenu />

          {/* Chapter Generation Controls */}
          {currentChapterData?.status === 'planned' && (
            <Button 
              onClick={handleGenerateChapter}
              disabled={chapterGeneration.state.isGenerating}
              size="sm"
              variant="outline"
            >
              <Wand2 className="h-4 w-4 mr-2" />
              {chapterGeneration.state.isGenerating ? 'Generating...' : 'Generate Chapter'}
            </Button>
          )}

          {originalGeneratedContent && content !== originalGeneratedContent && (
            <Button 
              onClick={handleReviewChanges}
              size="sm"
              variant="outline"
            >
              <Eye className="h-4 w-4 mr-2" />
              Review Changes
            </Button>
          )}
          
          <Button 
            onClick={saveChapter}
            disabled={isSaving}
            size="sm"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </header>

      {/* Main Content with Panel Layout */}
      <PanelLayout
        projectId={projectId}
        userId={user?.id}
        chapterId={currentChapterData?.id}
        content={content}
        selectedText={selectedText}
        seriesId={project?.series_books?.[0]?.series_id}
        metadata={panelMetadata}
        className="flex-1"
      >
        <LazyMonacoEditor
          initialContent={content}
          onContentChange={setContent}
          onSave={saveChapter}
          showToolbar={true}
          showStats={false}
          showAISuggestions={true}
          focusMode={focusMode}
          onFocusModeToggle={setFocusMode}
          projectId={projectId}
          chapterNumber={currentChapterData?.chapter_number}
          chapterId={currentChapterData?.id}
          enableRealtime={isCollaborationConnected}
        />
      </PanelLayout>

      {/* Chapter Review Panel */}
      <LazyChapterReviewPanel
        isOpen={chapterGeneration.state.isReviewing}
        onClose={() => chapterGeneration.resetState()}
        originalContent={originalGeneratedContent}
        editedContent={content}
        chapterTitle={currentChapterData?.title || `Chapter ${currentChapterData?.chapter_number}`}
        chapterNumber={currentChapterData?.chapter_number || 1}
        projectId={projectId}
        chapterId={currentChapterData?.id || ''}
        onSubmitChanges={handleSubmitChanges}
        onApproveGeneration={handleApproveGeneration}
      />
    </div>
  )
}

export default function WritePage() {
  return (
    <PanelProvider initialLayout="writing">
      <ErrorBoundary>
        <WritePageContent />
      </ErrorBoundary>
    </PanelProvider>
  )
}