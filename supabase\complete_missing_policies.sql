-- ============================================================================
-- COMPLETE MISSING RLS POLICIES FOR BOOKSCRIBE AI
-- ============================================================================
-- This adds policies for all tables that currently show "No policies created yet"

-- ============================================================================
-- PROJECT-RELATED TABLES (linked via project_id)
-- ============================================================================

-- CHARACTERS: Users can manage characters in their own projects
CREATE POLICY "Users can access own project characters" ON characters
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- STORY ARCS: Users can manage story arcs in their own projects
CREATE POLICY "Users can access own project story_arcs" ON story_arcs
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- AGENT LOGS: Users can view AI agent logs for their own projects
CREATE POLICY "Users can access own project agent_logs" ON agent_logs
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- REFERENCE MATERIALS: Users can manage reference materials for their projects
CREATE POLICY "Users can access own project reference_materials" ON reference_materials
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- STORY BIBLE: Users can manage story bible entries for their projects
CREATE POLICY "Users can access own project story_bible" ON story_bible
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- AI SUGGESTIONS: Users can access AI suggestions for their own projects
CREATE POLICY "Users can access own ai_suggestions" ON ai_suggestions
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- CONTENT EMBEDDINGS: Users can access embeddings for their own projects
CREATE POLICY "Users can access own content_embeddings" ON content_embeddings
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- PROJECT SNAPSHOTS: Users can access snapshots of their own projects
CREATE POLICY "Users can access own project_snapshots" ON project_snapshots
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- COLLABORATION SESSIONS: Users can access sessions they own or participate in
CREATE POLICY "Users can access collaboration_sessions" ON collaboration_sessions
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid()) OR
    id IN (SELECT session_id FROM collaboration_participants WHERE user_id = auth.uid())
  );

-- ============================================================================
-- CHAPTER-RELATED TABLES (linked via chapter_id)
-- ============================================================================

-- CHAPTER VERSIONS: Users can access versions of chapters in their projects
CREATE POLICY "Users can access own chapter_versions" ON chapter_versions
  FOR ALL USING (
    chapter_id IN (
      SELECT id FROM chapters WHERE project_id IN (
        SELECT id FROM projects WHERE user_id = auth.uid()
      )
    )
  );

-- ============================================================================
-- USER-OWNED TABLES (linked via user_id)
-- ============================================================================

-- EDITING SESSIONS: Users can access their own editing sessions
CREATE POLICY "Users can access own editing_sessions" ON editing_sessions
  FOR ALL USING (auth.uid() = user_id);

-- NOTIFICATIONS: Users can access their own notifications
CREATE POLICY "Users can access own notifications" ON notifications
  FOR ALL USING (auth.uid() = user_id);

-- PROCESSING TASKS: Users can view their own processing tasks
CREATE POLICY "Users can access own processing_tasks" ON processing_tasks
  FOR ALL USING (auth.uid() = user_id);

-- SELECTION ANALYTICS: Users can access their own analytics
CREATE POLICY "Users can access own selection_analytics" ON selection_analytics
  FOR ALL USING (auth.uid() = user_id);

-- WRITING GOALS: Users can manage their own writing goals
CREATE POLICY "Users can access own writing_goals" ON writing_goals
  FOR ALL USING (auth.uid() = user_id);

-- WRITING SESSIONS: Users can access their own writing sessions
CREATE POLICY "Users can access own writing_sessions" ON writing_sessions
  FOR ALL USING (auth.uid() = user_id);

-- SERIES: Users can manage their own series
CREATE POLICY "Users can access own series" ON series
  FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- RELATED TABLES (linked via foreign keys)
-- ============================================================================

-- SERIES BOOKS: Users can manage books in their own series
CREATE POLICY "Users can access own series_books" ON series_books
  FOR ALL USING (
    series_id IN (SELECT id FROM series WHERE user_id = auth.uid())
  );

-- WRITING GOAL PROGRESS: Users can access their own goal progress
CREATE POLICY "Users can access own writing_goal_progress" ON writing_goal_progress
  FOR ALL USING (
    goal_id IN (SELECT id FROM writing_goals WHERE user_id = auth.uid())
  );

-- COLLABORATION PARTICIPANTS: Users can view participants in their sessions
CREATE POLICY "Users can access collaboration_participants" ON collaboration_participants
  FOR ALL USING (
    session_id IN (
      SELECT id FROM collaboration_sessions WHERE 
      project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
    ) OR auth.uid() = user_id
  );

-- ============================================================================
-- VERIFICATION QUERY
-- ============================================================================

-- Run this to see all policies that were created
SELECT 
  tablename,
  policyname,
  cmd,
  CASE 
    WHEN cmd = 'ALL' THEN 'All Operations'
    WHEN cmd = 'SELECT' THEN 'Read'
    WHEN cmd = 'INSERT' THEN 'Create'
    WHEN cmd = 'UPDATE' THEN 'Update'
    WHEN cmd = 'DELETE' THEN 'Delete'
  END as operations,
  CASE 
    WHEN qual IS NOT NULL THEN 'Has Conditions'
    ELSE 'No Conditions'
  END as security_check
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN (
    'characters', 'story_arcs', 'agent_logs', 'reference_materials', 
    'story_bible', 'ai_suggestions', 'content_embeddings', 'project_snapshots',
    'collaboration_sessions', 'chapter_versions', 'editing_sessions', 
    'notifications', 'processing_tasks', 'selection_analytics', 
    'writing_goals', 'writing_sessions', 'series', 'series_books',
    'writing_goal_progress', 'collaboration_participants'
  )
ORDER BY tablename, policyname;

-- ============================================================================
-- SUMMARY
-- ============================================================================

-- This script adds RLS policies for:
-- ✅ 20 tables that had missing policies
-- ✅ Proper security based on ownership patterns
-- ✅ Project-based access control
-- ✅ User-based access control
-- ✅ Foreign key relationship security

-- After running this, all tables should have proper RLS policies
-- and your BookScribe AI application should work without 406/403 errors!
