"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"

type ThemeProviderProps = {
  children: React.ReactNode
  attribute?: 'class' | 'data-theme' | 'data-mode'
  defaultTheme?: string
  enableSystem?: boolean
  disableTransitionOnChange?: boolean
  storageKey?: string
  themes?: string[]
  value?: { [key: string]: string }
  nonce?: string
  enableColorScheme?: boolean
}

export function ThemeProvider({
  children,
  attribute = "class",
  defaultTheme = "writers-sanctuary-light",
  enableSystem = true,
  disableTransitionOnChange = false,
  storageKey = "bookscribe-theme",
  ...props
}: ThemeProviderProps) {
  // Use your original simple theme system
  const themes = [
    'writers-sanctuary-light',
    'evening-study-dark',
    'forest-manuscript-light',
    'midnight-ink-dark',
    'system',
    'light',
    'dark'
  ]

  return (
    <NextThemesProvider
      attribute={attribute}
      defaultTheme={defaultTheme}
      enableSystem={enableSystem}
      disableTransitionOnChange={disableTransitionOnChange}
      storageKey={storageKey}
      themes={themes}
      value={{
        light: 'writers-sanctuary-light',
        dark: 'evening-study-dark',
        system: 'system'
      }}
      {...props}
    >
      {children}
    </NextThemesProvider>
  )
}