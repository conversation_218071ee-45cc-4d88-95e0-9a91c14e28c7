"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"

type ThemeProviderProps = {
  children: React.ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  // Define all available themes
  const themes = [
    'writers-sanctuary-light',
    'evening-study-dark',
    'forest-manuscript-light',
    'midnight-ink-dark'
  ]

  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="writers-sanctuary-light"
      enableSystem={false}
      disableTransitionOnChange
      storageKey="bookscribe-theme"
      themes={themes}
    >
      {children}
    </NextThemesProvider>
  )
}