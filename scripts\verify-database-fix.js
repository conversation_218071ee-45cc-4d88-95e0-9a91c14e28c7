#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceRoleKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const EXPECTED_TABLES = [
  'profiles', 'projects', 'user_subscriptions', 'usage_tracking', 'usage_events',
  'story_arcs', 'characters', 'chapters', 'agent_logs', 'selection_profiles',
  'reference_materials', 'selection_analytics', 'story_bible', 'editing_sessions',
  'chapter_versions', 'project_snapshots', 'content_embeddings', 'series',
  'series_books', 'writing_goals', 'writing_goal_progress', 'notifications',
  'writing_sessions', 'ai_suggestions', 'collaboration_sessions', 'collaboration_participants',
  'processing_tasks', 'task_progress'
];

async function verifyDatabaseFix() {
  console.log('🔍 BookScribe Database Verification Tool\n');
  console.log('='.repeat(50));

  try {
    // Test basic connection
    console.log('📡 Testing database connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('projects')
      .select('count')
      .limit(1);

    if (connectionError) {
      console.log('❌ Database connection failed:', connectionError.message);
      return false;
    }
    console.log('✅ Database connection successful');

    // Check for all expected tables by trying to query them
    console.log('\n📋 Verifying table structure...');
    
    let successCount = 0;
    let failureCount = 0;
    const missingTables = [];

    for (const tableName of EXPECTED_TABLES) {
      try {
        const { error } = await supabase
          .from(tableName)
          .select('*')
          .limit(0);

        if (error) {
          if (error.message.includes('does not exist') || error.message.includes('relation') && error.message.includes('does not exist')) {
            console.log(`  ❌ Missing: ${tableName}`);
            missingTables.push(tableName);
            failureCount++;
          } else {
            console.log(`  ✅ Present: ${tableName}`);
            successCount++;
          }
        } else {
          console.log(`  ✅ Present: ${tableName}`);
          successCount++;
        }
      } catch (error) {
        console.log(`  ❌ Error checking ${tableName}: ${error.message}`);
        missingTables.push(tableName);
        failureCount++;
      }
    }

    // Generate report
    console.log('\n' + '='.repeat(50));
    console.log('📊 VERIFICATION RESULTS');
    console.log('='.repeat(50));
    
    const totalTables = EXPECTED_TABLES.length;
    const completionPercentage = Math.round((successCount / totalTables) * 100);
    
    console.log(`📋 Tables Found: ${successCount}/${totalTables} (${completionPercentage}%)`);
    
    if (successCount === totalTables) {
      console.log('🎉 SUCCESS: All required tables are present!');
      console.log('✅ Your BookScribe database is fully configured');
      console.log('\n🚀 Next Steps:');
      console.log('  1. Restart your development server');
      console.log('  2. Test user authentication');
      console.log('  3. Create a new project');
      console.log('  4. Try AI features and analytics');
      return true;
    } else {
      console.log('⚠️  INCOMPLETE: Some tables are missing');
      console.log('\n❌ Missing Tables:');
      missingTables.forEach(table => console.log(`     - ${table}`));
      
      console.log('\n💡 To fix:');
      console.log('  1. Go to Supabase SQL Editor');
      console.log('  2. Run the consolidated_migration.sql file');
      console.log('  3. Re-run this verification script');
      return false;
    }

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    return false;
  }
}

// Additional feature tests
async function testCoreFeatures() {
  console.log('\n🧪 Testing Core Features...');
  
  const tests = [
    {
      name: 'User Profiles',
      test: async () => {
        const { error } = await supabase.from('profiles').select('id').limit(1);
        return !error;
      }
    },
    {
      name: 'Project Management',
      test: async () => {
        const { error } = await supabase.from('projects').select('id').limit(1);
        return !error;
      }
    },
    {
      name: 'AI Suggestions',
      test: async () => {
        const { error } = await supabase.from('ai_suggestions').select('id').limit(1);
        return !error;
      }
    },
    {
      name: 'Content Embeddings',
      test: async () => {
        const { error } = await supabase.from('content_embeddings').select('id').limit(1);
        return !error;
      }
    },
    {
      name: 'Subscription Management',
      test: async () => {
        const { error } = await supabase.from('user_subscriptions').select('id').limit(1);
        return !error;
      }
    }
  ];

  for (const testCase of tests) {
    try {
      const result = await testCase.test();
      console.log(`  ${result ? '✅' : '❌'} ${testCase.name}`);
    } catch (error) {
      console.log(`  ❌ ${testCase.name} - Error: ${error.message}`);
    }
  }
}

async function main() {
  const success = await verifyDatabaseFix();
  
  if (success) {
    await testCoreFeatures();
    console.log('\n🎉 Database verification completed successfully!');
  } else {
    console.log('\n🔧 Database needs additional setup. See the report above.');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { verifyDatabaseFix };