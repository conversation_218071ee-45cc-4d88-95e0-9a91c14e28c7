'use client';

import { useFirstTimeUser } from '@/hooks/use-first-time-user';
import { OnboardingFlow } from './onboarding-flow';
import { Loader2 } from 'lucide-react';

interface FirstTimeUserWrapperProps {
  children: React.ReactNode;
  hasProjects: boolean;
}

export function FirstTimeUserWrapper({ children, hasProjects }: FirstTimeUserWrapperProps) {
  const { 
    isLoading, 
    showOnboarding, 
    createFirstProject, 
    createSampleProjectAndNavigate, 
    skipOnboarding 
  } = useFirstTimeUser();

  // Show loading state while checking user status
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <Loader2 className="w-8 h-8 animate-spin mx-auto text-primary" />
          <p className="text-muted-foreground">Setting up your workspace...</p>
        </div>
      </div>
    );
  }

  // Show onboarding for first-time users
  if (showOnboarding && !hasProjects) {
    return (
      <>
        {children}
        <OnboardingFlow
          onComplete={skipOnboarding}
          onSkip={skipOnboarding}
          onCreateProject={createFirstProject}
          onExploreSample={createSampleProjectAndNavigate}
        />
      </>
    );
  }

  // Regular app for existing users
  return <>{children}</>;
}