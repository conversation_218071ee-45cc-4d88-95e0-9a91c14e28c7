/**
 * Editor Settings Section
 * Writing and editing preferences
 */

'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider as _Slider } from '@/components/ui/slider';
import { Separator } from '@/components/ui/separator';
import { 
  Edit3, 
  Save, 
  Hash, 
  WrapText,
  Zap,
  CheckCircle,
  Brain,
  RotateCcw
} from 'lucide-react';

import { useEditorSettings } from '@/lib/settings/settings-store';
import { defaultEditorSettings } from '@/lib/settings/settings-types';

const autoSaveIntervals = [
  { value: 10, label: '10 seconds' },
  { value: 30, label: '30 seconds' },
  { value: 60, label: '1 minute' },
  { value: 300, label: '5 minutes' },
  { value: 600, label: '10 minutes' },
] as const;

const tabSizeOptions = [
  { value: 2, label: '2 spaces' },
  { value: 4, label: '4 spaces' },
  { value: 8, label: '8 spaces' },
] as const;

export function EditorSettingsSection() {
  const { editor, updateEditor } = useEditorSettings();

  const resetEditor = () => {
    updateEditor(defaultEditorSettings);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Edit3 className="w-5 h-5 text-primary" />
            <h3 className="text-lg font-semibold">Editor</h3>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={resetEditor}
            className="text-xs"
          >
            <RotateCcw className="w-3 h-3 mr-1" />
            Reset
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          Configure your writing environment and editor behavior.
        </p>
      </div>

      {/* Display Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Display Options</CardTitle>
          <CardDescription>
            Control how the editor appears and behaves
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Hash className="w-4 h-4" />
                Show Line Numbers
              </Label>
              <p className="text-xs text-muted-foreground">
                Display line numbers in the editor gutter
              </p>
            </div>
            <Switch
              checked={editor.showLineNumbers}
              onCheckedChange={(checked) => updateEditor({ showLineNumbers: checked })}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <WrapText className="w-4 h-4" />
                Word Wrap
              </Label>
              <p className="text-xs text-muted-foreground">
                Wrap long lines to fit the editor width
              </p>
            </div>
            <Switch
              checked={editor.wordWrap}
              onCheckedChange={(checked) => updateEditor({ wordWrap: checked })}
            />
          </div>

          <Separator />

          <div className="space-y-3">
            <Label className="text-sm font-medium">Tab Size</Label>
            <Select
              value={editor.tabSize.toString()}
              onValueChange={(value) => updateEditor({ tabSize: parseInt(value) })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {tabSizeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Auto-Save Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Save className="w-4 h-4" />
            Auto-Save
          </CardTitle>
          <CardDescription>
            Automatically save your work to prevent data loss
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Auto-Save</Label>
              <p className="text-xs text-muted-foreground">
                Automatically save changes while you write
              </p>
            </div>
            <Switch
              checked={editor.autoSave}
              onCheckedChange={(checked) => updateEditor({ autoSave: checked })}
            />
          </div>

          {editor.autoSave && (
            <>
              <Separator />
              <div className="space-y-3">
                <Label className="text-sm font-medium">
                  Auto-Save Interval: {editor.autoSaveInterval} seconds
                </Label>
                <Select
                  value={editor.autoSaveInterval.toString()}
                  onValueChange={(value) => updateEditor({ autoSaveInterval: parseInt(value) })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {autoSaveIntervals.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  How often to automatically save your work
                </p>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Writing Assistance */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Zap className="w-4 h-4" />
            Writing Assistance
          </CardTitle>
          <CardDescription>
            Enable AI-powered writing tools and language checking
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                Spell Check
              </Label>
              <p className="text-xs text-muted-foreground">
                Highlight misspelled words as you type
              </p>
            </div>
            <Switch
              checked={editor.spellCheck}
              onCheckedChange={(checked) => updateEditor({ spellCheck: checked })}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                Grammar Check
              </Label>
              <p className="text-xs text-muted-foreground">
                Check for grammar and style issues
              </p>
            </div>
            <Switch
              checked={editor.grammarCheck}
              onCheckedChange={(checked) => updateEditor({ grammarCheck: checked })}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Brain className="w-4 h-4" />
                AI Suggestions
              </Label>
              <p className="text-xs text-muted-foreground">
                Get AI-powered writing suggestions and improvements
              </p>
            </div>
            <Switch
              checked={editor.aiSuggestions}
              onCheckedChange={(checked) => updateEditor({ aiSuggestions: checked })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Editor Preview</CardTitle>
          <CardDescription>
            See how your editor will look with current settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg p-4 bg-card font-mono text-sm">
            <div className="flex">
              {editor.showLineNumbers && (
                <div className="text-muted-foreground mr-4 select-none">
                  <div>1</div>
                  <div>2</div>
                  <div>3</div>
                  <div>4</div>
                </div>
              )}
              <div className="flex-1">
                <div>Chapter 1: The Beginning</div>
                <div></div>
                <div>
                  {editor.wordWrap 
                    ? "This is a very long line that will wrap to the next line when word wrap is enabled, making it easier to read without horizontal scrolling."
                    : "This is a very long line that extends beyond the editor width..."
                  }
                </div>
                <div style={{ paddingLeft: `${editor.tabSize * 8}px` }}>
                  Indented text (tab size: {editor.tabSize})
                </div>
              </div>
            </div>
          </div>
          
          <div className="mt-3 flex flex-wrap gap-2 text-xs">
            {editor.autoSave && (
              <div className="flex items-center gap-1 text-green-600">
                <Save className="w-3 h-3" />
                Auto-save: {editor.autoSaveInterval}s
              </div>
            )}
            {editor.spellCheck && (
              <div className="flex items-center gap-1 text-blue-600">
                <CheckCircle className="w-3 h-3" />
                Spell check
              </div>
            )}
            {editor.grammarCheck && (
              <div className="flex items-center gap-1 text-purple-600">
                <CheckCircle className="w-3 h-3" />
                Grammar check
              </div>
            )}
            {editor.aiSuggestions && (
              <div className="flex items-center gap-1 text-orange-600">
                <Brain className="w-3 h-3" />
                AI suggestions
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
