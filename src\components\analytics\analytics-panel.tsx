'use client'

import { WritingAnalyticsDashboard } from './writing-analytics-dashboard'
import { ScrollArea } from '@/components/ui/scroll-area'

interface AnalyticsPanelProps {
  projectId: string
  projectTitle?: string
  targetWordCount?: number
  targetChapters?: number
  className?: string
}

export function AnalyticsPanel({
  projectId,
  projectTitle = 'Current Project',
  targetWordCount = 80000,
  targetChapters = 20,
  className = ''
}: AnalyticsPanelProps) {
  return (
    <ScrollArea className={`h-full ${className}`}>
      <div className="p-4">
        <WritingAnalyticsDashboard
          projectId={projectId}
          projectTitle={projectTitle}
          targetWordCount={targetWordCount}
          targetChapters={targetChapters}
        />
      </div>
    </ScrollArea>
  )
}