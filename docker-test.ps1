# BookScribe AI Docker Test Script
# This script tests the Docker setup

Write-Host "Testing BookScribe AI Docker Setup..." -ForegroundColor Blue

# Check if Docker is running
Write-Host "Checking Docker installation..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "Docker found: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "Docker not found or not running" -ForegroundColor Red
    Write-Host "Please install Docker Desktop and ensure it is running" -ForegroundColor Red
    exit 1
}

# Check if docker-compose is available
Write-Host "Checking Docker Compose..." -ForegroundColor Yellow
try {
    $composeVersion = docker-compose --version
    Write-Host "Docker Compose found: $composeVersion" -ForegroundColor Green
} catch {
    Write-Host "Docker Compose not found" -ForegroundColor Red
    Write-Host "Please install Docker Compose" -ForegroundColor Red
    exit 1
}

# Check if .env.local exists
Write-Host "Checking environment configuration..." -ForegroundColor Yellow
if (Test-Path ".env.local") {
    Write-Host ".env.local file found" -ForegroundColor Green
} else {
    Write-Host ".env.local file not found" -ForegroundColor Red
    if (Test-Path ".env.example") {
        Write-Host "Creating .env.local from .env.example..." -ForegroundColor Yellow
        Copy-Item ".env.example" ".env.local"
        Write-Host ".env.local created. Please edit it with your actual values." -ForegroundColor Green
    } else {
        Write-Host ".env.example not found either" -ForegroundColor Red
        exit 1
    }
}

# Check required files
$requiredFiles = @("Dockerfile", "Dockerfile.dev", "docker-compose.yml", "docker-compose.dev.yml", ".dockerignore")
Write-Host "Checking required Docker files..." -ForegroundColor Yellow
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "$file found" -ForegroundColor Green
    } else {
        Write-Host "$file missing" -ForegroundColor Red
    }
}

# Test basic Docker functionality
Write-Host "Testing basic Docker functionality..." -ForegroundColor Yellow
try {
    docker run --rm hello-world | Out-Null
    Write-Host "Docker is working correctly" -ForegroundColor Green
} catch {
    Write-Host "Docker test failed" -ForegroundColor Red
    Write-Host "Please check your Docker installation" -ForegroundColor Red
}

Write-Host ""
Write-Host "Docker setup test completed!" -ForegroundColor Blue
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Edit .env.local with your actual API keys and configuration" -ForegroundColor White
Write-Host "2. Run: .\docker-build.ps1 to build and start the application" -ForegroundColor White
Write-Host "3. Or run: docker-compose up -d for production" -ForegroundColor White
Write-Host "4. Or run: docker-compose -f docker-compose.dev.yml up -d for development" -ForegroundColor White
