import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { AnalyticsDashboard } from '@/components/analytics/analytics-dashboard'

export default async function AnalyticsPage() {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect('/login')
  }
  
  const { data: projects } = await supabase
    .from('projects')
    .select(`
      id, 
      title, 
      primary_genre,
      series_books!series_books_project_id_fkey(
        series_id,
        book_number,
        series:series(
          id,
          title,
          universe_id,
          universe:universe_id(
            id,
            name
          )
        )
      )
    `)
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })
  
  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Writing Analytics</h1>
        <p className="text-muted-foreground">
          Deep insights into your writing patterns and productivity
        </p>
      </div>
      
      <AnalyticsDashboard 
        userId={user.id} 
        projects={projects || []}
      />
    </div>
  )
}