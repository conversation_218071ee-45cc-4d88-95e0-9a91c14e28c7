const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

const supabaseUrl = 'https://xvqeiwrpbzpiqvwuvtpj.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function runCompleteDatabaseSetup() {
  console.log('🚀 BookScribe AI Complete Database Setup')
  console.log('=' .repeat(50))
  
  try {
    // Read the SQL file
    const sqlPath = path.join(__dirname, '..', 'supabase', 'complete_database_setup.sql')
    const sqlContent = fs.readFileSync(sqlPath, 'utf8')
    
    console.log('📄 Loaded SQL file:', sqlPath)
    console.log('📏 SQL content length:', sqlContent.length, 'characters')
    
    // Split SQL into individual statements (rough split by semicolon + newline)
    const statements = sqlContent
      .split(';\n')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
      .map(stmt => stmt.endsWith(';') ? stmt : stmt + ';')
    
    console.log('📝 Found', statements.length, 'SQL statements to execute\n')
    
    let successCount = 0
    let errorCount = 0
    const errors = []
    
    // Execute statements one by one
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      
      // Skip comments and empty statements
      if (statement.startsWith('--') || statement.trim() === ';') {
        continue
      }
      
      try {
        console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`)
        
        // Use raw SQL execution
        const { data, error } = await supabase.rpc('exec', {
          sql: statement
        })
        
        if (error) {
          // Some errors are expected (like "already exists")
          if (error.message.includes('already exists') || 
              error.message.includes('does not exist') ||
              error.message.includes('relation') && error.message.includes('already exists')) {
            console.log(`  ⚠️  Expected: ${error.message.substring(0, 80)}...`)
          } else {
            console.log(`  ❌ Error: ${error.message.substring(0, 80)}...`)
            errors.push({ statement: i + 1, error: error.message })
            errorCount++
          }
        } else {
          console.log(`  ✅ Success`)
          successCount++
        }
        
        // Small delay to avoid overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100))
        
      } catch (err) {
        console.log(`  ❌ Exception: ${err.message.substring(0, 80)}...`)
        errors.push({ statement: i + 1, error: err.message })
        errorCount++
      }
    }
    
    console.log('\n' + '='.repeat(50))
    console.log('📊 EXECUTION SUMMARY')
    console.log('='.repeat(50))
    console.log(`✅ Successful statements: ${successCount}`)
    console.log(`❌ Failed statements: ${errorCount}`)
    console.log(`📝 Total statements: ${statements.length}`)
    
    if (errors.length > 0) {
      console.log('\n❌ ERRORS ENCOUNTERED:')
      errors.forEach(({ statement, error }) => {
        console.log(`  Statement ${statement}: ${error.substring(0, 100)}...`)
      })
    }
    
    // Verify the setup
    console.log('\n🔍 VERIFYING SETUP...')
    await verifyDatabaseSetup()
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message)
    process.exit(1)
  }
}

async function verifyDatabaseSetup() {
  const EXPECTED_TABLES = [
    'profiles', 'projects', 'user_subscriptions', 'usage_tracking', 'usage_events',
    'story_arcs', 'characters', 'chapters', 'agent_logs', 'selection_profiles',
    'reference_materials', 'selection_analytics', 'story_bible', 'editing_sessions',
    'chapter_versions', 'project_snapshots', 'content_embeddings', 'series',
    'series_books', 'writing_goals', 'writing_goal_progress', 'notifications',
    'writing_sessions', 'ai_suggestions', 'collaboration_sessions', 'collaboration_participants',
    'processing_tasks'
  ]
  
  let existingCount = 0
  let missingCount = 0
  
  for (const tableName of EXPECTED_TABLES) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1)
      
      if (!error) {
        console.log(`  ✅ ${tableName}`)
        existingCount++
      } else {
        console.log(`  ❌ ${tableName} - ${error.code}`)
        missingCount++
      }
    } catch (err) {
      console.log(`  ❌ ${tableName} - ${err.message}`)
      missingCount++
    }
  }
  
  console.log('\n📊 VERIFICATION RESULTS:')
  console.log(`✅ Existing tables: ${existingCount}/${EXPECTED_TABLES.length}`)
  console.log(`❌ Missing tables: ${missingCount}`)
  
  if (missingCount === 0) {
    console.log('\n🎉 SUCCESS! All tables are now available!')
    console.log('✅ Your BookScribe AI database is fully configured')
    console.log('\n🚀 NEXT STEPS:')
    console.log('  1. Refresh your BookScribe AI application')
    console.log('  2. The 406/403 errors should now be resolved')
    console.log('  3. Test creating chapters and using AI features')
  } else {
    console.log('\n⚠️  Some tables are still missing. You may need to:')
    console.log('  1. Check the Supabase dashboard for any manual fixes needed')
    console.log('  2. Run this script again')
    console.log('  3. Contact support if issues persist')
  }
}

// Run the setup
runCompleteDatabaseSetup().catch(console.error)
