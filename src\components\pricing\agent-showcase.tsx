'use client'

import { <PERSON>, <PERSON>, BookOpen, Edit3, Layout, Lightbulb } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

const agents = [
  {
    name: 'Story Architect',
    icon: Brain,
    description: 'Creates comprehensive plot structures and story arcs',
    tier: 'novelist'
  },
  {
    name: 'Character Developer',
    icon: Users,
    description: 'Builds deep, consistent character profiles and relationships',
    tier: 'novelist'
  },
  {
    name: 'Writing Agent',
    icon: BookOpen,
    description: 'Generates engaging chapter content with your style',
    tier: 'novelist'
  },
  {
    name: 'Editor Agent',
    icon: Edit3,
    description: 'Reviews for consistency, quality, and improvements',
    tier: 'professional'
  },
  {
    name: 'Chapter Planner',
    icon: Layout,
    description: 'Organizes scenes and maintains perfect pacing',
    tier: 'professional'
  },
  {
    name: 'Adaptive Planning',
    icon: Lightbulb,
    description: 'Adjusts story elements based on your changes',
    tier: 'professional'
  }
]

const tierColors = {
  novelist: 'bg-primary/10 text-primary border-primary/30',
  professional: 'bg-purple-100 text-purple-700 border-purple-300 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-700'
}

const tierLabels = {
  novelist: 'Novelist+',
  professional: 'Professional+'
}

export function AgentShowcase() {
  return (
    <div className="py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold font-literary-display mb-4">
          Your AI Writing Team
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Six specialized AI agents work together to help you craft your masterpiece.
          Plus our Dynamic Memory System maintains consistency across 100k+ words.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 max-w-7xl mx-auto">
        {agents.map((agent) => {
          const Icon = agent.icon
          return (
            <div
              key={agent.name}
              className="relative p-6 rounded-lg border bg-card hover:shadow-md transition-all"
            >
              <Badge 
                className={`absolute top-4 right-4 text-xs font-mono ${tierColors[agent.tier]}`}
                variant="outline"
              >
                {tierLabels[agent.tier]}
              </Badge>
              
              <div className="flex flex-col items-center text-center space-y-3">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <Icon className="w-6 h-6 text-primary" />
                </div>
                
                <h3 className="font-semibold text-lg">{agent.name}</h3>
                
                <p className="text-sm text-muted-foreground">
                  {agent.description}
                </p>
              </div>
            </div>
          )
        })}
        
        {/* Bonus card for Literary Master */}
        <div className="relative p-6 rounded-lg border bg-gradient-to-br from-primary/5 to-primary/10 border-primary/30">
          <Badge 
            className="absolute top-4 right-4 text-xs font-mono bg-primary text-primary-foreground"
          >
            Literary Master
          </Badge>
          
          <div className="flex flex-col items-center text-center space-y-3">
            <div className="w-12 h-12 rounded-full bg-primary flex items-center justify-center">
              <Brain className="w-6 h-6 text-primary-foreground" />
            </div>
            
            <h3 className="font-semibold text-lg">Custom AI Training</h3>
            
            <p className="text-sm text-muted-foreground">
              Train agents on your unique writing style
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}