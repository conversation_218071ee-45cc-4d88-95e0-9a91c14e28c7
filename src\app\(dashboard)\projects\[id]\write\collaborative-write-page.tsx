'use client'

import { useEffect, useState, useRef } from 'react'
import { usePara<PERSON>, useSearchParams, useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { useEditorStore } from '@/stores/editor-store'
import { CollaborativeMonacoEditor } from '@/components/editor/collaborative-monaco-editor'
import { SaveStatusIndicator, useOnlineStatus } from '@/components/version-history/save-status-indicator'
import { ErrorBoundary } from '@/components/ui/error-boundary'
import { CollaborationIndicatorEnhanced } from '@/components/collaboration/collaboration-indicator-enhanced'
import { TypingIndicator } from '@/components/collaboration/typing-indicator'
import { collaborationServiceRealtime, type CollaborationUser } from '@/lib/services/collaboration-service-realtime'
import { checkProjectAccess } from '@/lib/auth/collaboration-auth'
import { useSubscription } from '@/hooks/use-subscription'
import { useAutoSave, useUnsavedChanges } from '@/hooks/use-auto-save'
import { useChapterGeneration, UserChanges } from '@/hooks/use-chapter-generation'
import { Button } from '@/components/ui/button'
import { BookImportDialog } from '@/components/import/book-import-dialog'
import { toast } from '@/hooks/use-toast'
import { WritingSessionTracker } from '@/components/editor/writing-session-tracker'
import { AgentStatusWidget } from '@/components/agents/agent-status-widget'
import { PanelProvider, usePanelSystem, PanelLayout } from '@/lib/panels'
import { PanelMenu } from '@/components/editor/panel-menu'
import { LazyChapterReviewPanel } from '@/components/lazy'
import { logger } from '@/lib/services/logger'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu'
import { 
  Save, 
  ArrowLeft,
  Wand2,
  Eye,
  Upload,
  Globe,
  BookOpenCheck,
  ChevronDown,
  Users,
  AlertCircle,
  Lock
} from 'lucide-react'
import Link from 'next/link'
import { Database } from '@/lib/db/types'
import { User } from '@supabase/supabase-js'

type Project = Database['public']['Tables']['projects']['Row']
type Chapter = Database['public']['Tables']['chapters']['Row']

function WritePageContent() {
  const params = useParams()
  const searchParams = useSearchParams()
  const router = useRouter()
  const projectId = params.id as string
  const chapterId = searchParams.get('chapter')
  
  const {
    content,
    setContent,
    selectedText,
    currentChapter,
    setCurrentChapter,
    updateChapterList
  } = useEditorStore()

  const [project, setProject] = useState<Project | null>(null)
  const [currentChapterData, setCurrentChapterData] = useState<Chapter | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [originalGeneratedContent, setOriginalGeneratedContent] = useState<string>('')
  const [focusMode, setFocusMode] = useState(false)
  const [seriesBooks, setSeriesBooks] = useState<Array<{id: string; title: string; book_number: number}>>([])
  
  // Collaboration states
  const [collaborationEnabled, setCollaborationEnabled] = useState(true)
  const [isCollaborationConnected, setIsCollaborationConnected] = useState(false)
  const [collaborators, setCollaborators] = useState<CollaborationUser[]>([])
  const [typingUsers, setTypingUsers] = useState<any[]>([])
  const [projectPermissions, setProjectPermissions] = useState<any>(null)
  const lastTypingUpdate = useRef<number>(0)
  
  const supabase = createClient()
  const isOnline = useOnlineStatus()
  const { setUnsavedChanges } = useUnsavedChanges()
  const subscription = useSubscription()
  
  // Initialize panel system
  usePanelSystem()

  // Chapter generation hook
  const chapterGeneration = useChapterGeneration(projectId, currentChapterData?.id || '')

  // Auto-save hook
  const { saveNow } = useAutoSave(
    {
      chapterId: currentChapterData?.id || '',
      content,
      wordCount: content.trim().split(/\s+/).filter(word => word.length > 0).length
    },
    {
      enabled: !!currentChapterData?.id && isOnline && !isCollaborationConnected, // Disable auto-save during collaboration
      delay: 3000,
      onSave: (success) => {
        if (success) {
          setLastSaved(new Date())
          setHasUnsavedChanges(false)
          setUnsavedChanges(false)
        }
      },
      onError: (error) => {
        logger.error('Auto-save failed:', error);
      }
    }
  )

  useEffect(() => {
    loadData()
  }, [projectId])
  
  // Load series books when project is loaded
  useEffect(() => {
    if (project?.series_books?.[0]?.series_id) {
      loadSeriesBooks(project.series_books[0].series_id)
    }
  }, [project])

  // Setup collaboration when chapter changes
  useEffect(() => {
    if (user && currentChapterData && collaborationEnabled && subscription) {
      setupCollaboration()
    }
    
    return () => {
      if (isCollaborationConnected) {
        collaborationServiceRealtime.disconnect(
          `project:${projectId}:document:${currentChapterData?.id}`,
          user?.id || ''
        )
      }
    }
  }, [user, currentChapterData, collaborationEnabled, subscription])

  const loadData = async () => {
    try {
      // Get current user
      const { data: { user: currentUser } } = await supabase.auth.getUser()
      if (!currentUser) {
        router.push('/login')
        return
      }
      setUser(currentUser)
      
      // Check project access
      const permissions = await checkProjectAccess(currentUser.id, projectId)
      setProjectPermissions(permissions)
      
      if (!permissions.canView) {
        router.push('/projects')
        toast({
          title: "Access Denied",
          description: "You don't have permission to view this project",
          variant: "destructive"
        })
        return
      }
      
      // Load project
      await loadProject()
      await loadChapters()
    } catch (error) {
      logger.error('Error loading data:', error)
    }
  }

  const loadProject = async () => {
    const { data } = await supabase
      .from('projects')
      .select(`
        *,
        series_books!series_books_project_id_fkey(
          series_id,
          series:series(
            id,
            title
          )
        )
      `)
      .eq('id', projectId)
      .single()
    
    if (data) setProject(data)
  }

  const loadSeriesBooks = async (seriesId: string) => {
    const { data } = await supabase
      .from('series_books')
      .select(`
        book_number,
        project:projects(
          id,
          title
        )
      `)
      .eq('series_id', seriesId)
      .order('book_number')
    
    if (data) {
      setSeriesBooks(data.map(item => ({
        id: item.project.id,
        title: item.project.title,
        book_number: item.book_number
      })))
    }
  }

  const loadChapters = async () => {
    const { data } = await supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .order('chapter_number')
    
    if (data) {
      updateChapterList(data.map(ch => ({
        id: ch.id,
        number: ch.chapter_number,
        title: ch.title || `Chapter ${ch.chapter_number}`,
        status: ch.status,
        wordCount: ch.actual_word_count || 0
      })))
      
      // Load first chapter or specified chapter
      if (chapterId) {
        loadChapter(chapterId)
      } else if (data.length > 0) {
        loadChapter(data[0].id)
      }
    }
  }

  const loadChapter = async (id: string) => {
    const { data } = await supabase
      .from('chapters')
      .select('*')
      .eq('id', id)
      .single()
    
    if (data) {
      setCurrentChapterData(data)
      setContent(data.content || '')
      setCurrentChapter(data.chapter_number)
    }
  }

  const setupCollaboration = async () => {
    if (!collaborationServiceRealtime.canUseRealTimeCollaboration(subscription)) {
      return
    }
    
    try {
      const sessionId = `project:${projectId}:document:${currentChapterData?.id}`
      
      await collaborationServiceRealtime.connect(
        sessionId,
        user!.id,
        user!.user_metadata?.full_name || user!.email || 'Anonymous',
        user!.email || '',
        subscription
      )
      
      setIsCollaborationConnected(true)
      
      // Subscribe to collaboration events
      const unsubscribe = collaborationServiceRealtime.subscribe(sessionId, (event) => {
        switch (event.type) {
          case 'user.joined':
          case 'user.left':
            const users = collaborationServiceRealtime.getActiveUsers(sessionId)
            setCollaborators(users)
            break
        }
      })
      
      return () => {
        unsubscribe()
      }
    } catch (error) {
      logger.error('Failed to setup collaboration:', error)
      setIsCollaborationConnected(false)
    }
  }

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
    setHasUnsavedChanges(true)
    
    // Update typing indicator
    const now = Date.now()
    if (now - lastTypingUpdate.current > 1000) {
      lastTypingUpdate.current = now
      // This would be sent to other users
      setTypingUsers(prev => [
        ...prev.filter(u => u.id !== user?.id),
        {
          id: user?.id,
          name: user?.user_metadata?.full_name || user?.email,
          email: user?.email,
          color: '#3B82F6',
          lastTyped: now
        }
      ])
    }
  }

  const saveChapter = async () => {
    if (!currentChapterData || !projectPermissions?.canEdit) return
    
    setIsSaving(true)
    try {
      const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length
      
      const { error } = await supabase
        .from('chapters')
        .update({
          content,
          actual_word_count: wordCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', currentChapterData.id)

      if (!error) {
        setLastSaved(new Date())
        setHasUnsavedChanges(false)
        loadChapters() // Refresh chapter list
      }
    } finally {
      setIsSaving(false)
    }
  }

  const handleChapterSelect = (id: string) => {
    window.history.pushState({}, '', `/projects/${projectId}/write?chapter=${id}`)
    loadChapter(id)
  }

  const handleToggleCollaboration = async (enabled: boolean) => {
    setCollaborationEnabled(enabled)
    
    if (!enabled && isCollaborationConnected) {
      await collaborationServiceRealtime.disconnect(
        `project:${projectId}:document:${currentChapterData?.id}`,
        user?.id || ''
      )
      setIsCollaborationConnected(false)
      setCollaborators([])
    }
  }

  const handleInviteUser = () => {
    router.push(`/projects/${projectId}/team`)
  }

  // Panel metadata for context
  const panelMetadata = {
    onChapterSelect: handleChapterSelect,
    onCreateChapter: async () => {
      // Create chapter logic
    }
  }

  const canEdit = projectPermissions?.canEdit || false

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <header className="border-b px-4 py-2 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href={`/projects/${projectId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Project
            </Button>
          </Link>
          
          <div className="flex items-center gap-2">
            <h1 className="text-lg font-semibold">
              {project?.title || 'Loading...'}
            </h1>
            {currentChapterData && (
              <span className="text-sm text-muted-foreground">
                • Chapter {currentChapterData.chapter_number}
              </span>
            )}
            {!canEdit && (
              <Badge variant="secondary" className="text-xs">
                <Eye className="h-3 w-3 mr-1" />
                View Only
              </Badge>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Real-time Collaboration Indicator */}
          {currentChapterData && user && subscription && (
            <CollaborationIndicatorEnhanced
              sessionId={`project:${projectId}:document:${currentChapterData.id}`}
              isConnected={isCollaborationConnected}
              collaborators={collaborators}
              currentUserId={user.id}
              canInvite={projectPermissions?.canManageTeam}
              onToggleCollaboration={handleToggleCollaboration}
              onInviteUser={handleInviteUser}
            />
          )}
          
          {/* Team Management Link */}
          {projectPermissions?.canManageTeam && (
            <Button variant="ghost" size="sm" onClick={() => router.push(`/projects/${projectId}/team`)}>
              <Users className="h-4 w-4 mr-2" />
              Team
            </Button>
          )}
          
          {/* Agent Status */}
          <AgentStatusWidget projectId={projectId} />
          
          {/* Writing Session Display */}
          {user && (
            <WritingSessionTracker 
              userId={user.id}
              projectId={projectId}
              chapterId={currentChapterData?.id}
              variant="compact"
            />
          )}
          
          <SaveStatusIndicator 
            isSaving={isSaving}
            lastSaved={lastSaved}
            hasUnsavedChanges={hasUnsavedChanges}
            isOnline={isOnline}
            onManualSave={saveNow}
          />
          
          {/* Panel Menu */}
          <PanelMenu />
          
          {canEdit && (
            <Button 
              onClick={saveChapter}
              disabled={isSaving || !hasUnsavedChanges}
              size="sm"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save'}
            </Button>
          )}
        </div>
      </header>

      {/* Typing Indicator */}
      {typingUsers.length > 0 && (
        <div className="px-4">
          <TypingIndicator
            typingUsers={typingUsers}
            currentUserId={user?.id || ''}
            position="top"
          />
        </div>
      )}

      {/* Main Content with Panel Layout */}
      <PanelLayout
        projectId={projectId}
        userId={user?.id}
        chapterId={currentChapterData?.id}
        content={content}
        selectedText={selectedText}
        seriesId={project?.series_books?.[0]?.series_id}
        metadata={panelMetadata}
        className="flex-1"
      >
        {/* Editor */}
        {currentChapterData && user && (
          <CollaborativeMonacoEditor
            value={content}
            onChange={handleContentChange}
            projectId={projectId}
            documentId={currentChapterData.id}
            userId={user.id}
            userName={user.user_metadata?.full_name || user.email || 'Anonymous'}
            userEmail={user.email || ''}
            subscription={subscription}
            enableCollaboration={collaborationEnabled && canEdit}
            onSave={saveChapter}
            options={{
              readOnly: !canEdit,
              minimap: { enabled: !focusMode },
              lineNumbers: !focusMode ? 'on' : 'off',
              renderWhitespace: 'selection'
            }}
          />
        )}
      </PanelLayout>

      {/* Chapter Review Panel */}
      <LazyChapterReviewPanel
        isOpen={chapterGeneration.state.isReviewing}
        onClose={() => chapterGeneration.resetState()}
        originalContent={originalGeneratedContent}
        editedContent={content}
        chapterTitle={currentChapterData?.title || `Chapter ${currentChapterData?.chapter_number}`}
        chapterNumber={currentChapterData?.chapter_number || 1}
        projectId={projectId}
        chapterId={currentChapterData?.id || ''}
        onSubmitChanges={async (changes: UserChanges) => {
          // Handle changes
          return true
        }}
        onApproveGeneration={async () => {
          // Handle approval
          return true
        }}
      />
    </div>
  )
}

export default function CollaborativeWritePage() {
  return (
    <PanelProvider initialLayout="writing">
      <ErrorBoundary>
        <WritePageContent />
      </ErrorBoundary>
    </PanelProvider>
  )
}