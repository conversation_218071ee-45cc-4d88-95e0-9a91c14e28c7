'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Skeleton } from '@/components/ui/skeleton'
import { useToast } from '@/hooks/use-toast'
import { 
  Target, 
  Flame, 
  TrendingUp, 
  BookOpen, 
  Trophy,
  Plus,
  Sparkles,
  Calendar,
  BarChart3,
  Star
} from 'lucide-react'

interface RecommendedGoal {
  id: string
  type: 'word_count' | 'streak' | 'quality' | 'milestone' | 'chapter'
  title: string
  description: string
  targetValue: number
  unit: string
  difficulty: 'easy' | 'medium' | 'hard'
  estimatedDays: number
  icon: React.ElementType
  category: string
  basedOn?: string // What this recommendation is based on
}

interface RecommendedAchievementsProps {
  userId: string
  projectId?: string
  currentStats?: {
    avgDailyWords: number
    currentStreak: number
    avgQualityScore: number
    projectProgress: number
  }
  onGoalsSelected: (goals: RecommendedGoal[]) => void
}

export function RecommendedAchievements({ 
  userId, 
  projectId,
  currentStats,
  onGoalsSelected 
}: RecommendedAchievementsProps) {
  const [recommendations, setRecommendations] = useState<RecommendedGoal[]>([])
  const [selectedGoals, setSelectedGoals] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchRecommendations()
  }, [userId, projectId, currentStats])

  const fetchRecommendations = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({ userId })
      if (projectId) params.append('projectId', projectId)
      
      const response = await fetch(`/api/goals/recommendations?${params}`)
      if (response.ok) {
        const data = await response.json()
        setRecommendations(data.recommendations || generateDefaultRecommendations())
      } else {
        // Fallback to default recommendations
        setRecommendations(generateDefaultRecommendations())
      }
    } catch (error) {
      console.error('Failed to fetch recommendations:', error)
      setRecommendations(generateDefaultRecommendations())
    } finally {
      setLoading(false)
    }
  }

  const generateDefaultRecommendations = (): RecommendedGoal[] => {
    const avgWords = currentStats?.avgDailyWords || 500
    const currentStreak = currentStats?.currentStreak || 0
    const qualityScore = currentStats?.avgQualityScore || 75
    
    return [
      // Word Count Goals
      {
        id: 'wc-daily-1',
        type: 'word_count',
        title: 'Daily Writing Habit',
        description: `Write ${Math.round(avgWords * 1.5)} words daily for 7 days`,
        targetValue: Math.round(avgWords * 1.5) * 7,
        unit: 'words',
        difficulty: 'medium',
        estimatedDays: 7,
        icon: BookOpen,
        category: 'Writing Output',
        basedOn: `Currently averaging ${avgWords} words/day`
      },
      {
        id: 'wc-weekly-1',
        type: 'word_count',
        title: 'Weekly Word Count Goal',
        description: `Write ${Math.round(avgWords * 7 * 1.2)} words this week`,
        targetValue: Math.round(avgWords * 7 * 1.2),
        unit: 'words',
        difficulty: 'easy',
        estimatedDays: 7,
        icon: BarChart3,
        category: 'Writing Output',
        basedOn: 'A 20% increase from your weekly average'
      },
      {
        id: 'wc-monthly-1',
        type: 'word_count',
        title: 'Monthly Writing Challenge',
        description: `Complete 25,000 words in 30 days`,
        targetValue: 25000,
        unit: 'words',
        difficulty: 'hard',
        estimatedDays: 30,
        icon: Trophy,
        category: 'Writing Output'
      },
      
      // Streak Goals
      {
        id: 'streak-1',
        type: 'streak',
        title: currentStreak >= 7 ? 'Extend Your Streak' : 'Build a Writing Streak',
        description: currentStreak >= 7 
          ? `Reach a ${currentStreak + 7}-day writing streak`
          : 'Write for 7 consecutive days',
        targetValue: currentStreak >= 7 ? currentStreak + 7 : 7,
        unit: 'days',
        difficulty: currentStreak >= 7 ? 'medium' : 'easy',
        estimatedDays: currentStreak >= 7 ? 7 : 7 - currentStreak,
        icon: Flame,
        category: 'Consistency',
        basedOn: currentStreak > 0 ? `Current streak: ${currentStreak} days` : undefined
      },
      {
        id: 'streak-2',
        type: 'streak',
        title: 'Marathon Writer',
        description: 'Maintain a 30-day writing streak',
        targetValue: 30,
        unit: 'days',
        difficulty: 'hard',
        estimatedDays: Math.max(30 - currentStreak, 30),
        icon: Calendar,
        category: 'Consistency'
      },
      
      // Quality Goals
      {
        id: 'quality-1',
        type: 'quality',
        title: 'Quality Improvement',
        description: `Achieve ${Math.min(qualityScore + 10, 90)}% quality score on next chapter`,
        targetValue: Math.min(qualityScore + 10, 90),
        unit: 'score',
        difficulty: qualityScore >= 80 ? 'hard' : 'medium',
        estimatedDays: 7,
        icon: Star,
        category: 'Writing Quality',
        basedOn: `Current average: ${qualityScore}%`
      },
      {
        id: 'quality-2',
        type: 'quality',
        title: 'Consistent Excellence',
        description: 'Maintain 80%+ quality for 5 chapters',
        targetValue: 5,
        unit: 'chapters',
        difficulty: 'hard',
        estimatedDays: 30,
        icon: Sparkles,
        category: 'Writing Quality'
      },
      
      // Milestone Goals
      {
        id: 'milestone-1',
        type: 'milestone',
        title: 'Chapter Completion',
        description: 'Complete your current chapter',
        targetValue: 1,
        unit: 'chapter',
        difficulty: 'easy',
        estimatedDays: 5,
        icon: Target,
        category: 'Project Progress'
      },
      {
        id: 'milestone-2',
        type: 'milestone',
        title: 'Reach 50% Progress',
        description: 'Complete half of your project',
        targetValue: 50,
        unit: 'percent',
        difficulty: 'medium',
        estimatedDays: 30,
        icon: TrendingUp,
        category: 'Project Progress'
      }
    ]
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-500'
      case 'medium': return 'bg-yellow-500'
      case 'hard': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Writing Output': return BookOpen
      case 'Consistency': return Flame
      case 'Writing Quality': return Star
      case 'Project Progress': return Target
      default: return Trophy
    }
  }

  const toggleGoalSelection = (goalId: string) => {
    const newSelection = new Set(selectedGoals)
    if (newSelection.has(goalId)) {
      newSelection.delete(goalId)
    } else {
      newSelection.add(goalId)
    }
    setSelectedGoals(newSelection)
  }

  const saveSelectedGoals = async () => {
    const goalsToSave = recommendations.filter(g => selectedGoals.has(g.id))
    if (goalsToSave.length === 0) {
      toast({
        title: "No goals selected",
        description: "Please select at least one goal to track.",
        variant: "destructive"
      })
      return
    }

    setSaving(true)
    try {
      // Call parent handler
      onGoalsSelected(goalsToSave)
      
      // Save to database
      const response = await fetch('/api/goals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          goals: goalsToSave.map(goal => ({
            goal_type: goal.type,
            title: goal.title,
            description: goal.description,
            target_value: goal.targetValue,
            unit: goal.unit,
            difficulty: goal.difficulty,
            is_recommended: true,
            project_id: projectId,
            metadata: {
              category: goal.category,
              estimatedDays: goal.estimatedDays,
              basedOn: goal.basedOn
            }
          }))
        })
      })

      if (response.ok) {
        toast({
          title: "Goals created!",
          description: `${goalsToSave.length} goals have been added to your tracker.`,
        })
        setSelectedGoals(new Set())
      } else {
        throw new Error('Failed to save goals')
      }
    } catch (error) {
      toast({
        title: "Error saving goals",
        description: "Please try again later.",
        variant: "destructive"
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recommended Achievements</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <Skeleton key={i} className="h-24 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  // Group recommendations by category
  const groupedRecommendations = recommendations.reduce((groups, rec) => {
    if (!groups[rec.category]) {
      groups[rec.category] = []
    }
    groups[rec.category].push(rec)
    return groups
  }, {} as Record<string, RecommendedGoal[]>)

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Recommended Achievements
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {selectedGoals.size} selected
            </Badge>
            <Button 
              onClick={saveSelectedGoals}
              disabled={selectedGoals.size === 0 || saving}
              size="sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Selected Goals
            </Button>
          </div>
        </div>
        <p className="text-sm text-muted-foreground mt-2">
          Select achievements you'd like to track. These are personalized based on your writing patterns.
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {Object.entries(groupedRecommendations).map(([category, goals]) => {
            const CategoryIcon = getCategoryIcon(category)
            return (
              <div key={category} className="space-y-3">
                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                  <CategoryIcon className="h-4 w-4" />
                  {category}
                </div>
                <div className="space-y-3">
                  {goals.map((goal) => {
                    const Icon = goal.icon
                    return (
                      <div
                        key={goal.id}
                        className={`
                          relative border rounded-lg p-4 transition-all cursor-pointer
                          ${selectedGoals.has(goal.id) 
                            ? 'border-primary bg-primary/5' 
                            : 'border-border hover:border-primary/50'
                          }
                        `}
                        onClick={() => toggleGoalSelection(goal.id)}
                      >
                        <div className="flex items-start gap-3">
                          <Checkbox 
                            checked={selectedGoals.has(goal.id)}
                            onCheckedChange={() => toggleGoalSelection(goal.id)}
                            className="mt-0.5"
                            onClick={(e) => e.stopPropagation()}
                          />
                          <Icon className="h-5 w-5 text-muted-foreground mt-0.5" />
                          <div className="flex-1 space-y-1">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium">{goal.title}</h4>
                              <div className="flex items-center gap-2">
                                <Badge 
                                  variant="secondary" 
                                  className={`text-xs ${getDifficultyColor(goal.difficulty)} text-white`}
                                >
                                  {goal.difficulty}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  ~{goal.estimatedDays} days
                                </Badge>
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {goal.description}
                            </p>
                            {goal.basedOn && (
                              <p className="text-xs text-muted-foreground italic">
                                {goal.basedOn}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}