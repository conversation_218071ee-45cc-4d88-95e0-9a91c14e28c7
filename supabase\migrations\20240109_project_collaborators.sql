-- Project Collaborators Table for Team Management
CREATE TABLE IF NOT EXISTS project_collaborators (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  invited_by UUID NOT NULL REFERENCES profiles(id),
  role TEXT NOT NULL CHECK (role IN ('owner', 'editor', 'viewer')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'suspended')),
  permissions JSONB DEFAULT '{
    "can_write": false,
    "can_manage_team": false,
    "can_export": true,
    "can_delete": false
  }'::jsonb,
  invite_token TEXT UNIQUE,
  invite_expires_at TIMESTAMPTZ,
  joined_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(project_id, user_id)
);

-- Indexes for project_collaborators
CREATE INDEX idx_project_collaborators_project_id ON project_collaborators(project_id);
CREATE INDEX idx_project_collaborators_user_id ON project_collaborators(user_id);
CREATE INDEX idx_project_collaborators_status ON project_collaborators(status);
CREATE INDEX idx_project_collaborators_invite_token ON project_collaborators(invite_token) WHERE invite_token IS NOT NULL;

-- Enable Row Level Security
ALTER TABLE project_collaborators ENABLE ROW LEVEL SECURITY;

-- RLS Policies for project_collaborators

-- Users can view collaborators on projects they have access to
CREATE POLICY "Users can view project collaborators"
  ON project_collaborators FOR SELECT
  USING (
    -- User is a collaborator on this project
    EXISTS (
      SELECT 1 FROM project_collaborators pc
      WHERE pc.project_id = project_collaborators.project_id
      AND pc.user_id = auth.uid()
      AND pc.status = 'active'
    )
    OR
    -- User owns the project
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = project_collaborators.project_id
      AND p.user_id = auth.uid()
    )
  );

-- Only project owners and users with manage_team permission can insert collaborators
CREATE POLICY "Authorized users can add collaborators"
  ON project_collaborators FOR INSERT
  WITH CHECK (
    -- User owns the project
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = project_collaborators.project_id
      AND p.user_id = auth.uid()
    )
    OR
    -- User has manage_team permission
    EXISTS (
      SELECT 1 FROM project_collaborators pc
      WHERE pc.project_id = project_collaborators.project_id
      AND pc.user_id = auth.uid()
      AND pc.status = 'active'
      AND (pc.permissions->>'can_manage_team')::boolean = true
    )
  );

-- Only project owners and users with manage_team permission can update collaborators
CREATE POLICY "Authorized users can update collaborators"
  ON project_collaborators FOR UPDATE
  USING (
    -- User owns the project
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = project_collaborators.project_id
      AND p.user_id = auth.uid()
    )
    OR
    -- User has manage_team permission
    EXISTS (
      SELECT 1 FROM project_collaborators pc
      WHERE pc.project_id = project_collaborators.project_id
      AND pc.user_id = auth.uid()
      AND pc.status = 'active'
      AND (pc.permissions->>'can_manage_team')::boolean = true
    )
  );

-- Only project owners can delete collaborators
CREATE POLICY "Only owners can remove collaborators"
  ON project_collaborators FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = project_collaborators.project_id
      AND p.user_id = auth.uid()
    )
  );

-- Function to update permissions based on role
CREATE OR REPLACE FUNCTION update_collaborator_permissions()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.role = 'owner' THEN
    NEW.permissions = jsonb_build_object(
      'can_write', true,
      'can_manage_team', true,
      'can_export', true,
      'can_delete', true
    );
  ELSIF NEW.role = 'editor' THEN
    NEW.permissions = jsonb_build_object(
      'can_write', true,
      'can_manage_team', false,
      'can_export', true,
      'can_delete', false
    );
  ELSIF NEW.role = 'viewer' THEN
    NEW.permissions = jsonb_build_object(
      'can_write', false,
      'can_manage_team', false,
      'can_export', true,
      'can_delete', false
    );
  END IF;
  
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update permissions when role changes
CREATE TRIGGER update_collaborator_permissions_trigger
  BEFORE INSERT OR UPDATE OF role ON project_collaborators
  FOR EACH ROW
  EXECUTE FUNCTION update_collaborator_permissions();

-- Function to check collaborator limit
CREATE OR REPLACE FUNCTION check_collaborator_limit()
RETURNS TRIGGER AS $$
DECLARE
  project_owner_id UUID;
  user_tier TEXT;
  max_collaborators INTEGER;
  current_collaborators INTEGER;
BEGIN
  -- Get project owner
  SELECT user_id INTO project_owner_id
  FROM projects
  WHERE id = NEW.project_id;
  
  -- Get user's subscription tier
  SELECT tier_id INTO user_tier
  FROM user_subscriptions
  WHERE user_id = project_owner_id
  AND status = 'active'
  LIMIT 1;
  
  -- Set max collaborators based on tier
  CASE user_tier
    WHEN 'professional' THEN max_collaborators := 2;
    WHEN 'studio' THEN max_collaborators := 5;
    ELSE max_collaborators := 0;
  END CASE;
  
  -- Count current active collaborators (excluding owner)
  SELECT COUNT(*) INTO current_collaborators
  FROM project_collaborators
  WHERE project_id = NEW.project_id
  AND status = 'active'
  AND user_id != project_owner_id;
  
  -- Check limit
  IF current_collaborators >= max_collaborators THEN
    RAISE EXCEPTION 'Collaborator limit reached for this subscription tier';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to check collaborator limit
CREATE TRIGGER check_collaborator_limit_trigger
  BEFORE INSERT OR UPDATE OF status ON project_collaborators
  FOR EACH ROW
  WHEN (NEW.status = 'active')
  EXECUTE FUNCTION check_collaborator_limit();

-- Function to clean up expired invitations
CREATE OR REPLACE FUNCTION cleanup_expired_invitations()
RETURNS void AS $$
BEGIN
  DELETE FROM project_collaborators
  WHERE status = 'pending'
  AND invite_expires_at < NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enable Realtime for project_collaborators
ALTER PUBLICATION supabase_realtime ADD TABLE project_collaborators;