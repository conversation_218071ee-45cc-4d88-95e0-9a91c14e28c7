import { createClient } from '@/lib/supabase/client';
import { qualityAnalyzer } from './quality-analyzer';
import { logger } from './logger';

interface ChapterQualityAnalysis {
  chapterId: string;
  projectId: string;
  userId: string;
  content: string;
  title?: string;
  chapterNumber?: number;
}

export class ChapterQualityAnalyzer {
  private static instance: ChapterQualityAnalyzer;
  private supabase = createClient();
  private analysisQueue: ChapterQualityAnalysis[] = [];
  private isProcessing = false;

  private constructor() {
    // Process queue every 5 seconds
    setInterval(() => {
      this.processQueue();
    }, 5000);
  }

  static getInstance(): ChapterQualityAnalyzer {
    if (!ChapterQualityAnalyzer.instance) {
      ChapterQualityAnalyzer.instance = new ChapterQualityAnalyzer();
    }
    return ChapterQualityAnalyzer.instance;
  }

  /**
   * Queue a chapter for quality analysis
   */
  async queueAnalysis(params: ChapterQualityAnalysis): Promise<void> {
    // Add to queue (deduplicate by chapter ID)
    const existingIndex = this.analysisQueue.findIndex(
      item => item.chapterId === params.chapterId
    );

    if (existingIndex >= 0) {
      // Update existing entry with latest content
      this.analysisQueue[existingIndex] = params;
    } else {
      this.analysisQueue.push(params);
    }

    logger.info('Chapter queued for quality analysis', {
      chapterId: params.chapterId,
      queueLength: this.analysisQueue.length
    });
  }

  /**
   * Analyze chapter quality immediately
   */
  async analyzeChapter(params: ChapterQualityAnalysis): Promise<void> {
    try {
      // Get previous chapter content for context
      const { data: previousChapter } = await this.supabase
        .from('chapters')
        .select('content')
        .eq('project_id', params.projectId)
        .lt('chapter_number', params.chapterNumber || 0)
        .order('chapter_number', { ascending: false })
        .limit(1)
        .single();

      // Get character profiles for the project
      const { data: characters } = await this.supabase
        .from('characters')
        .select('name, personality, backstory, goals')
        .eq('project_id', params.projectId);

      // Run quality analysis
      const analysisResult = await qualityAnalyzer.analyzeContentQuality(
        params.content,
        'chapter',
        {
          previousContent: previousChapter?.content,
          characterProfiles: characters ? 
            characters.reduce((acc, char) => ({
              ...acc,
              [char.name]: {
                personality: char.personality,
                backstory: char.backstory,
                goals: char.goals
              }
            }), {}) : undefined
        }
      );

      if (!analysisResult.data) {
        logger.error('Quality analysis failed', { 
          chapterId: params.chapterId,
          error: analysisResult.error 
        });
        return;
      }

      const metrics = analysisResult.data.metrics;

      // Save quality metrics to database
      const { error: saveError } = await this.supabase
        .from('quality_metrics')
        .upsert({
          chapter_id: params.chapterId,
          project_id: params.projectId,
          user_id: params.userId,
          overall_score: metrics.overall,
          coherence: metrics.coherence,
          style: metrics.style,
          grammar: metrics.grammar,
          creativity: metrics.creativity,
          pacing: metrics.pacing,
          character_consistency: metrics.characterConsistency,
          plot_consistency: metrics.plotConsistency,
          emotional_impact: metrics.emotionalImpact,
          readability: metrics.readability,
          show_dont_tell_ratio: metrics.showDontTellRatio,
          sensory_engagement: metrics.sensoryEngagement,
          dialogue_authenticity: metrics.dialogueAuthenticity,
          hook_strength: metrics.hookStrength,
          pageturner_quality: metrics.pageturnerQuality,
          literary_merit: metrics.literaryMerit,
          market_potential: metrics.marketPotential,
          memorability: metrics.memorability,
          strengths: analysisResult.data.strengths,
          weaknesses: analysisResult.data.weaknesses,
          suggestions: analysisResult.data.suggestions,
          analyzed_at: new Date().toISOString()
        }, {
          onConflict: 'chapter_id',
          ignoreDuplicates: false
        });

      if (saveError) {
        logger.error('Failed to save quality metrics', { 
          chapterId: params.chapterId,
          error: saveError 
        });
        return;
      }

      logger.info('Chapter quality analysis completed', {
        chapterId: params.chapterId,
        overallScore: metrics.overall
      });

    } catch (error) {
      logger.error('Error analyzing chapter quality', {
        chapterId: params.chapterId,
        error
      });
    }
  }

  /**
   * Process the analysis queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.analysisQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      // Process up to 3 chapters at a time
      const batch = this.analysisQueue.splice(0, 3);
      
      await Promise.all(
        batch.map(params => this.analyzeChapter(params))
      );

    } catch (error) {
      logger.error('Error processing quality analysis queue', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Get quality trend for a project
   */
  async getProjectQualityTrend(
    projectId: string,
    limit: number = 10
  ): Promise<{
    chapterNumber: number;
    overallScore: number;
    analyzedAt: string;
  }[]> {
    const { data, error } = await this.supabase
      .from('quality_metrics')
      .select(`
        overall_score,
        analyzed_at,
        chapters!inner(chapter_number)
      `)
      .eq('project_id', projectId)
      .order('chapters.chapter_number', { ascending: true })
      .limit(limit);

    if (error) {
      logger.error('Failed to get quality trend', { projectId, error });
      return [];
    }

    return data.map(item => ({
      chapterNumber: item.chapters.chapter_number,
      overallScore: item.overall_score,
      analyzedAt: item.analyzed_at
    }));
  }

  /**
   * Get aggregated quality metrics for a project
   */
  async getProjectQualityMetrics(projectId: string): Promise<{
    avgOverallScore: number;
    avgCoherence: number;
    avgStyle: number;
    avgGrammar: number;
    avgCreativity: number;
    avgPacing: number;
    avgCharacterConsistency: number;
    avgPlotConsistency: number;
    avgEmotionalImpact: number;
    avgReadability: number;
    avgShowDontTellRatio: number;
    avgSensoryEngagement: number;
    avgDialogueAuthenticity: number;
    avgHookStrength: number;
    avgPageturnerQuality: number;
    avgLiteraryMerit: number;
    avgMarketPotential: number;
    avgMemorability: number;
    chaptersAnalyzed: number;
    lastAnalyzedAt: string | null;
  } | null> {
    const { data, error } = await this.supabase
      .from('project_quality_metrics')
      .select('*')
      .eq('project_id', projectId)
      .single();

    if (error) {
      logger.error('Failed to get project quality metrics', { projectId, error });
      return null;
    }

    if (!data) {
      return null;
    }

    return {
      avgOverallScore: data.avg_overall_score || 0,
      avgCoherence: data.avg_coherence || 0,
      avgStyle: data.avg_style || 0,
      avgGrammar: data.avg_grammar || 0,
      avgCreativity: data.avg_creativity || 0,
      avgPacing: data.avg_pacing || 0,
      avgCharacterConsistency: data.avg_character_consistency || 0,
      avgPlotConsistency: data.avg_plot_consistency || 0,
      avgEmotionalImpact: data.avg_emotional_impact || 0,
      avgReadability: data.avg_readability || 0,
      avgShowDontTellRatio: data.avg_show_dont_tell_ratio || 0,
      avgSensoryEngagement: data.avg_sensory_engagement || 0,
      avgDialogueAuthenticity: data.avg_dialogue_authenticity || 0,
      avgHookStrength: data.avg_hook_strength || 0,
      avgPageturnerQuality: data.avg_pageturner_quality || 0,
      avgLiteraryMerit: data.avg_literary_merit || 0,
      avgMarketPotential: data.avg_market_potential || 0,
      avgMemorability: data.avg_memorability || 0,
      chaptersAnalyzed: data.chapters_analyzed || 0,
      lastAnalyzedAt: data.last_analyzed_at
    };
  }
}

// Export singleton instance
export const chapterQualityAnalyzer = ChapterQualityAnalyzer.getInstance();