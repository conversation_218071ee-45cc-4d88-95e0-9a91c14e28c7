const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://xvqeiwrpbzpiqvwuvtpj.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function checkRLSPolicies() {
  console.log('🔍 Checking RLS Policies Status...\n')

  const CRITICAL_TABLES = [
    'profiles', 'projects', 'chapters', 'characters', 'story_arcs',
    'user_subscriptions', 'usage_tracking', 'usage_events'
  ]

  try {
    // Test if we can access tables without authentication (should fail if R<PERSON> is working)
    console.log('🧪 Testing RLS Security...')

    for (const tableName of CRITICAL_TABLES) {
      try {
        // Create a client without authentication to test RLS
        const publicClient = createClient(supabaseUrl, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NjAwMjYsImV4cCI6MjA2NTMzNjAyNn0.Ej8OcULEqZmmyJpan_-0o-VJKqOJJaJOhJJJJJJJJJJ')

        const { data, error } = await publicClient
          .from(tableName)
          .select('*')
          .limit(1)

        if (error) {
          if (error.code === 'PGRST301' || error.message.includes('RLS')) {
            console.log(`  ✅ ${tableName} - RLS is working (access denied)`)
          } else {
            console.log(`  ⚠️  ${tableName} - Error: ${error.code} - ${error.message}`)
          }
        } else {
          console.log(`  ❌ ${tableName} - RLS NOT WORKING (public access allowed!)`)
        }
      } catch (err) {
        console.log(`  ⚠️  ${tableName} - Exception: ${err.message}`)
      }
    }

    // Test with authenticated user
    console.log('\n🔐 Testing Authenticated Access...')

    // Test chapters specifically (the main issue)
    const { data: chapters, error: chaptersError } = await supabase
      .from('chapters')
      .select('*')
      .limit(1)

    if (chaptersError) {
      console.log(`  ❌ chapters - Service role can't access: ${chaptersError.message}`)
    } else {
      console.log(`  ✅ chapters - Service role can access (${chapters.length} records)`)
    }

    // Test projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .limit(1)

    if (projectsError) {
      console.log(`  ❌ projects - Service role can't access: ${projectsError.message}`)
    } else {
      console.log(`  ✅ projects - Service role can access (${projects.length} records)`)
    }

  } catch (error) {
    console.error('❌ Error checking RLS policies:', error)
  }
}

checkRLSPolicies()