'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import {
  Bar<PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Toolt<PERSON>,
  Legend,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts'
import {
  BookO<PERSON>,
  Users,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Clock,
  Mic,
  RefreshCw,
  Settings,
  Download,
  Shield,
  AlertCircle,
  Loader2,
  GitBranch,
  Target
} from 'lucide-react'
import { CharacterContinuityTracker } from './character-continuity-tracker'
import { VoiceTrainerEnhanced } from '@/components/voice/voice-trainer-enhanced'

interface SeriesConsistencyDashboardProps {
  seriesId: string
  seriesName: string
}

interface ConsistencyMetrics {
  overallScore: number
  characterConsistency: number
  timelineConsistency: number
  voiceConsistency: number
  worldBuildingConsistency: number
  issues: ConsistencyIssue[]
  bookCount: number
  characterCount: number
  voiceProfileCount: number
}

interface ConsistencyIssue {
  id: string
  type: 'character' | 'timeline' | 'voice' | 'world'
  severity: 'low' | 'medium' | 'high'
  bookNumber?: number
  description: string
  suggestion?: string
}

interface VoiceProfile {
  id: string
  name: string
  type: string
  confidence: number
}

export function SeriesConsistencyDashboard({ seriesId, seriesName }: SeriesConsistencyDashboardProps) {
  const [metrics, setMetrics] = useState<ConsistencyMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [voiceProfiles, setVoiceProfiles] = useState<{
    seriesProfile?: VoiceProfile
    bookProfiles: any[]
    characterProfiles: any[]
  }>({ bookProfiles: [], characterProfiles: [] })
  const [showVoiceTrainer, setShowVoiceTrainer] = useState(false)
  const [applyVoiceScope, setApplyVoiceScope] = useState<'series' | 'all_books' | 'future_books'>('future_books')
  const [selectedVoiceProfile, setSelectedVoiceProfile] = useState<string>('')
  const [availableVoiceProfiles, setAvailableVoiceProfiles] = useState<VoiceProfile[]>([])

  useEffect(() => {
    loadData()
  }, [seriesId])

  const loadData = async () => {
    setIsLoading(true)
    try {
      // Load consistency metrics
      const metricsResponse = await fetch(`/api/series/${seriesId}/continuity?type=detailed`)
      if (metricsResponse.ok) {
        const data = await metricsResponse.json()
        const overallScore = data.continuityScore || 0
        
        setMetrics({
          overallScore,
          characterConsistency: calculateCharacterScore(data),
          timelineConsistency: calculateTimelineScore(data),
          voiceConsistency: 0, // Will be calculated separately
          worldBuildingConsistency: calculateWorldScore(data),
          issues: data.issues || [],
          bookCount: data.booksAnalyzed || 0,
          characterCount: data.characterCount || 0,
          voiceProfileCount: 0
        })
      }

      // Load voice profiles
      const voiceResponse = await fetch(`/api/series/${seriesId}/apply-voice-profile`)
      if (voiceResponse.ok) {
        const data = await voiceResponse.json()
        setVoiceProfiles(data)
        
        // Calculate voice consistency
        const voiceScore = calculateVoiceConsistency(data)
        setMetrics(prev => prev ? {
          ...prev,
          voiceConsistency: voiceScore,
          voiceProfileCount: (data.seriesProfile ? 1 : 0) + 
            data.bookProfiles.filter((b: any) => b.voiceProfile).length +
            data.characterProfiles.length
        } : null)
      }

      // Load available voice profiles
      const availableResponse = await fetch(`/api/voice-profiles?seriesId=${seriesId}`)
      if (availableResponse.ok) {
        const data = await availableResponse.json()
        setAvailableVoiceProfiles(data.profiles || [])
      }
    } catch (error) {
      console.error('Error loading data:', error)
      toast.error('Failed to load consistency data')
    } finally {
      setIsLoading(false)
    }
  }

  const calculateCharacterScore = (data: any) => {
    const issues = data.issues?.filter((i: any) => i.type === 'character_role_inconsistency') || []
    return Math.max(0, 100 - (issues.length * 10))
  }

  const calculateTimelineScore = (data: any) => {
    const issues = data.issues?.filter((i: any) => i.type === 'timeline_inconsistency') || []
    return Math.max(0, 100 - (issues.length * 15))
  }

  const calculateWorldScore = (data: any) => {
    const issues = data.issues?.filter((i: any) => i.type === 'world_building_inconsistency') || []
    return Math.max(0, 100 - (issues.length * 8))
  }

  const calculateVoiceConsistency = (data: any) => {
    let score = 100
    
    // Deduct if no series-wide profile
    if (!data.seriesProfile) score -= 20
    
    // Deduct for books without profiles
    const booksWithoutProfiles = data.bookProfiles.filter((b: any) => !b.voiceProfile).length
    score -= (booksWithoutProfiles * 5)
    
    // Deduct for inconsistent character voices
    const characterWithoutProfiles = data.characterProfiles.filter((c: any) => !c.voice_profile).length
    score -= (characterWithoutProfiles * 3)
    
    return Math.max(0, score)
  }

  const runFullAnalysis = async () => {
    setIsAnalyzing(true)
    try {
      const response = await fetch(`/api/series/${seriesId}/continuity?type=ai-analysis`)
      if (!response.ok) throw new Error('Analysis failed')
      
      const data = await response.json()
      toast.success('AI analysis complete')
      loadData() // Reload to get updated data
    } catch (error) {
      console.error('Analysis error:', error)
      toast.error('Failed to run AI analysis')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const applyVoiceProfile = async () => {
    if (!selectedVoiceProfile) {
      toast.error('Please select a voice profile')
      return
    }

    try {
      const response = await fetch(`/api/series/${seriesId}/apply-voice-profile`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          voiceProfileId: selectedVoiceProfile,
          applyTo: applyVoiceScope
        })
      })

      if (!response.ok) throw new Error('Failed to apply voice profile')
      
      const data = await response.json()
      toast.success(data.message)
      loadData()
    } catch (error) {
      console.error('Error applying voice profile:', error)
      toast.error('Failed to apply voice profile')
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="h-5 w-5 text-green-600" />
    if (score >= 60) return <AlertCircle className="h-5 w-5 text-yellow-600" />
    return <AlertTriangle className="h-5 w-5 text-red-600" />
  }

  const consistencyData = metrics ? [
    { category: 'Characters', score: metrics.characterConsistency },
    { category: 'Timeline', score: metrics.timelineConsistency },
    { category: 'Voice', score: metrics.voiceConsistency },
    { category: 'World', score: metrics.worldBuildingConsistency }
  ] : []

  const issuesByType = metrics ? {
    character: metrics.issues.filter(i => i.type === 'character').length,
    timeline: metrics.issues.filter(i => i.type === 'timeline').length,
    voice: metrics.issues.filter(i => i.type === 'voice').length,
    world: metrics.issues.filter(i => i.type === 'world').length
  } : null

  const pieData = issuesByType ? [
    { name: 'Character', value: issuesByType.character, color: '#3b82f6' },
    { name: 'Timeline', value: issuesByType.timeline, color: '#f59e0b' },
    { name: 'Voice', value: issuesByType.voice, color: '#10b981' },
    { name: 'World', value: issuesByType.world, color: '#8b5cf6' }
  ].filter(d => d.value > 0) : []

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">{seriesName} - Consistency Dashboard</h2>
            <p className="text-muted-foreground">
              Monitor and maintain consistency across your series
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => loadData()}
              disabled={isLoading}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button
              onClick={runFullAnalysis}
              disabled={isAnalyzing}
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Target className="h-4 w-4 mr-2" />
                  Run AI Analysis
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Overall Score Card */}
        {metrics && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Overall Consistency Score</CardTitle>
                {getScoreIcon(metrics.overallScore)}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className={`text-4xl font-bold ${getScoreColor(metrics.overallScore)}`}>
                    {metrics.overallScore}%
                  </span>
                  <div className="text-right text-sm text-muted-foreground">
                    <p>{metrics.bookCount} Books</p>
                    <p>{metrics.characterCount} Characters</p>
                    <p>{metrics.voiceProfileCount} Voice Profiles</p>
                  </div>
                </div>
                <Progress value={metrics.overallScore} className="h-3" />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Consistency Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="characters">Characters</TabsTrigger>
            <TabsTrigger value="voice">Voice</TabsTrigger>
            <TabsTrigger value="issues">Issues</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Consistency Metrics Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Consistency by Category</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={consistencyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis domain={[0, 100]} />
                    <Tooltip />
                    <Bar dataKey="score" fill="#3b82f6">
                      {consistencyData.map((entry, index) => (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={
                            entry.score >= 80 ? '#10b981' :
                            entry.score >= 60 ? '#f59e0b' :
                            '#ef4444'
                          } 
                        />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Issues Distribution */}
            {pieData.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Issues by Type</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={250}>
                      <PieChart>
                        <Pie
                          data={pieData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {pieData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Quick Stats</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Total Issues</span>
                        <Badge variant={metrics.issues.length > 10 ? 'destructive' : 'secondary'}>
                          {metrics.issues.length}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Critical Issues</span>
                        <Badge variant="destructive">
                          {metrics.issues.filter(i => i.severity === 'high').length}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Voice Profiles</span>
                        <Badge variant="secondary">
                          {metrics.voiceProfileCount}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Characters Tracked</span>
                        <Badge variant="secondary">
                          {metrics.characterCount}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          <TabsContent value="characters">
            <CharacterContinuityTracker
              seriesId={seriesId}
              seriesName={seriesName}
              totalBooks={metrics?.bookCount || 0}
            />
          </TabsContent>

          <TabsContent value="voice" className="space-y-4">
            {/* Voice Profile Overview */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Voice Profile Management</CardTitle>
                    <CardDescription>
                      Manage voice consistency across your series
                    </CardDescription>
                  </div>
                  <Button onClick={() => setShowVoiceTrainer(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Voice Profile
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Series Voice Profile */}
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">Series Voice Profile</h4>
                    {voiceProfiles.seriesProfile ? (
                      <Badge variant="secondary">
                        <Mic className="h-3 w-3 mr-1" />
                        {voiceProfiles.seriesProfile.name}
                      </Badge>
                    ) : (
                      <Badge variant="outline">Not Set</Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Default voice profile for all books in the series
                  </p>
                </div>

                {/* Apply Voice Profile */}
                <div className="p-4 border rounded-lg space-y-4">
                  <h4 className="font-medium">Apply Voice Profile</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Select Profile</Label>
                      <Select value={selectedVoiceProfile} onValueChange={setSelectedVoiceProfile}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose a voice profile" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableVoiceProfiles.map(profile => (
                            <SelectItem key={profile.id} value={profile.id}>
                              {profile.name} ({profile.type})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Apply To</Label>
                      <Select 
                        value={applyVoiceScope} 
                        onValueChange={(value: any) => setApplyVoiceScope(value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="series">Series Default</SelectItem>
                          <SelectItem value="all_books">All Books</SelectItem>
                          <SelectItem value="future_books">Future Books Only</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <Button 
                    onClick={applyVoiceProfile}
                    disabled={!selectedVoiceProfile}
                    className="w-full"
                  >
                    Apply Voice Profile
                  </Button>
                </div>

                {/* Book Voice Profiles */}
                <div>
                  <h4 className="font-medium mb-3">Book Voice Profiles</h4>
                  <ScrollArea className="h-[200px]">
                    <div className="space-y-2">
                      {voiceProfiles.bookProfiles.map((book, idx) => (
                        <div key={idx} className="flex items-center justify-between p-3 border rounded">
                          <span className="text-sm">
                            Book {book.bookNumber}: {book.bookTitle}
                          </span>
                          {book.voiceProfile ? (
                            <Badge variant="secondary" className="text-xs">
                              {book.voiceProfile.name}
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-xs">
                              Using series default
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="issues" className="space-y-4">
            {metrics && metrics.issues.length > 0 ? (
              <ScrollArea className="h-[600px]">
                <div className="space-y-4">
                  {metrics.issues
                    .sort((a, b) => {
                      const severityOrder = { high: 0, medium: 1, low: 2 }
                      return severityOrder[a.severity] - severityOrder[b.severity]
                    })
                    .map((issue) => (
                      <Alert key={issue.id} variant={issue.severity === 'high' ? 'destructive' : 'default'}>
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle className="flex items-center gap-2">
                          {issue.type.charAt(0).toUpperCase() + issue.type.slice(1)} Issue
                          {issue.bookNumber && (
                            <Badge variant="outline" className="text-xs">
                              Book {issue.bookNumber}
                            </Badge>
                          )}
                          <Badge 
                            variant={
                              issue.severity === 'high' ? 'destructive' :
                              issue.severity === 'medium' ? 'default' :
                              'secondary'
                            }
                            className="text-xs"
                          >
                            {issue.severity}
                          </Badge>
                        </AlertTitle>
                        <AlertDescription className="mt-2">
                          <p>{issue.description}</p>
                          {issue.suggestion && (
                            <p className="mt-2 text-sm text-muted-foreground">
                              <strong>Suggestion:</strong> {issue.suggestion}
                            </p>
                          )}
                        </AlertDescription>
                      </Alert>
                    ))}
                </div>
              </ScrollArea>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <CheckCircle className="h-12 w-12 text-green-600 mb-4" />
                  <p className="text-lg font-medium">No consistency issues found!</p>
                  <p className="text-sm text-muted-foreground">
                    Your series maintains excellent consistency
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Series Consistency Settings</CardTitle>
                <CardDescription>
                  Configure how consistency is tracked and enforced
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Alert>
                  <Shield className="h-4 w-4" />
                  <AlertDescription>
                    These settings affect how AI agents maintain consistency when generating content
                  </AlertDescription>
                </Alert>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Strict Character Consistency</p>
                      <p className="text-sm text-muted-foreground">
                        Enforce character traits and voice across all books
                      </p>
                    </div>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Timeline Validation</p>
                      <p className="text-sm text-muted-foreground">
                        Check for chronological inconsistencies
                      </p>
                    </div>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">World Building Rules</p>
                      <p className="text-sm text-muted-foreground">
                        Maintain consistent world rules and settings
                      </p>
                    </div>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <Button variant="outline" className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    Export Consistency Report
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Voice Trainer Dialog */}
      <Dialog open={showVoiceTrainer} onOpenChange={setShowVoiceTrainer}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Series Voice Profile</DialogTitle>
            <DialogDescription>
              Train a voice profile to maintain consistency across your series
            </DialogDescription>
          </DialogHeader>
          <VoiceTrainerEnhanced
            seriesId={seriesId}
            onProfileCreated={() => {
              setShowVoiceTrainer(false)
              loadData()
            }}
          />
        </DialogContent>
      </Dialog>
    </>
  )
}