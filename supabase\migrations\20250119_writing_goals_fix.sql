-- Create writing_goals table if it doesn't exist
CREATE TABLE IF NOT EXISTS writing_goals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  type TEXT NOT NULL CHECK (type IN ('daily', 'weekly', 'monthly', 'project', 'custom')),
  target_value INTEGER NOT NULL,
  current_value INTEGER NOT NULL DEFAULT 0,
  unit TEXT NOT NULL DEFAULT 'words',
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused', 'failed')),
  deadline DATE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ
);

-- Indexes for writing_goals
CREATE INDEX IF NOT EXISTS idx_writing_goals_user_id ON writing_goals(user_id);
CREATE INDEX IF NOT EXISTS idx_writing_goals_project_id ON writing_goals(project_id);
CREATE INDEX IF NOT EXISTS idx_writing_goals_status ON writing_goals(status);
CREATE INDEX IF NOT EXISTS idx_writing_goals_deadline ON writing_goals(deadline);

-- Add RLS policies for writing_goals
ALTER TABLE writing_goals ENABLE ROW LEVEL SECURITY;

-- Users can view their own goals
CREATE POLICY "Users can view own goals" ON writing_goals
  FOR SELECT USING (auth.uid() = user_id);

-- Users can create their own goals
CREATE POLICY "Users can create own goals" ON writing_goals
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own goals
CREATE POLICY "Users can update own goals" ON writing_goals
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own goals
CREATE POLICY "Users can delete own goals" ON writing_goals
  FOR DELETE USING (auth.uid() = user_id);

-- Function to update goals timestamp
CREATE OR REPLACE FUNCTION update_goals_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-update timestamp
CREATE TRIGGER update_writing_goals_timestamp
  BEFORE UPDATE ON writing_goals
  FOR EACH ROW
  EXECUTE FUNCTION update_goals_timestamp();