'use client'

import { Navbar } from '@/components/layout/navbar'
import { useBreadcrumbs } from '@/hooks/use-breadcrumbs'
import { DashboardClientWrapper } from '../client-wrapper'
import { AchievementNotifier } from '@/components/achievements/achievement-notifier'

export function DashboardLayoutClient({ children }: { children: React.ReactNode }) {
  const breadcrumbs = useBreadcrumbs()

  return (
    <DashboardClientWrapper>
      <div className="min-h-screen flex flex-col bg-background text-foreground">
        <Navbar breadcrumbs={breadcrumbs} />
        <main className="flex-1">
          {children}
        </main>
        <AchievementNotifier />
      </div>
    </DashboardClientWrapper>
  )
}