'use client'

import { useState, useEffect } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import { 
  Users, 
  Wifi, 
  WifiOff, 
  Circle,
  Crown,
  Sparkles,
  Copy,
  UserPlus
} from 'lucide-react'
import type { CollaborationUser } from '@/lib/services/collaboration-service'

interface CollaborationIndicatorProps {
  sessionId: string
  isConnected: boolean
  collaborators: CollaborationUser[]
  currentUserId: string
  canInvite?: boolean
  onToggleCollaboration?: (enabled: boolean) => void
  onInviteUser?: () => void
}

export function CollaborationIndicator({
  sessionId,
  isConnected,
  collaborators,
  currentUserId,
  canInvite = false,
  onToggleCollaboration,
  onInviteUser
}: CollaborationIndicatorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [collaborationEnabled, setCollaborationEnabled] = useState(isConnected)
  const [showStatusTooltip, setShowStatusTooltip] = useState(false)
  const { toast } = useToast()
  
  useEffect(() => {
    setCollaborationEnabled(isConnected)
  }, [isConnected])
  
  // Show a brief status animation when connection state changes
  useEffect(() => {
    if (isConnected !== collaborationEnabled) {
      setShowStatusTooltip(true)
      const timer = setTimeout(() => setShowStatusTooltip(false), 3000)
      return () => clearTimeout(timer)
    }
  }, [isConnected, collaborationEnabled])
  
  const handleToggleCollaboration = (enabled: boolean) => {
    setCollaborationEnabled(enabled)
    onToggleCollaboration?.(enabled)
    
    toast({
      title: enabled ? "Collaboration Enabled" : "Collaboration Disabled",
      description: enabled 
        ? "Real-time collaboration is now active" 
        : "You're now working offline"
    })
  }
  
  const copySessionLink = async () => {
    const link = `${window.location.origin}/join-session/${sessionId}`
    await navigator.clipboard.writeText(link)
    toast({
      title: "Session Link Copied",
      description: "Share this link with collaborators"
    })
  }
  
  const getInitials = (name: string, email: string) => {
    if (name && name.trim()) {
      return name.split(' ').map(n => n[0]).join('').toUpperCase()
    }
    return email.substring(0, 2).toUpperCase()
  }
  
  const activeCollaborators = collaborators.filter(c => c.id !== currentUserId)
  
  return (
    <div className="flex items-center gap-2">
      {/* Connection Status */}
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center gap-1">
            {isConnected ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-muted-foreground" />
            )}
            <span className="text-xs text-muted-foreground">
              {isConnected ? 'Live' : 'Offline'}
            </span>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{isConnected ? 'Real-time collaboration active' : 'Working offline'}</p>
        </TooltipContent>
      </Tooltip>
      
      {/* Collaborator Avatars */}
      {activeCollaborators.length > 0 && (
        <div className="flex -space-x-2">
          {activeCollaborators.slice(0, 3).map((collaborator) => (
            <Tooltip key={collaborator.id}>
              <TooltipTrigger asChild>
                <Avatar className="h-7 w-7 border-2 border-background">
                  <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${collaborator.email}`} />
                  <AvatarFallback 
                    style={{ backgroundColor: collaborator.color }}
                    className="text-xs text-white"
                  >
                    {getInitials(collaborator.name, collaborator.email)}
                  </AvatarFallback>
                </Avatar>
              </TooltipTrigger>
              <TooltipContent>
                <div className="flex items-center gap-2">
                  <Circle 
                    className="h-2 w-2 fill-current" 
                    style={{ color: collaborator.color }}
                  />
                  <span>{collaborator.name}</span>
                </div>
              </TooltipContent>
            </Tooltip>
          ))}
          {activeCollaborators.length > 3 && (
            <Avatar className="h-7 w-7 border-2 border-background bg-muted">
              <AvatarFallback className="text-xs">
                +{activeCollaborators.length - 3}
              </AvatarFallback>
            </Avatar>
          )}
        </div>
      )}
      
      {/* Collaboration Menu */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm"
            className="gap-1"
          >
            <Users className="h-4 w-4" />
            <span className="text-xs">
              {activeCollaborators.length + 1}
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="end">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold text-sm">Real-Time Collaboration</h4>
              <Badge variant="outline" className="text-xs">
                <Crown className="h-3 w-3 mr-1" />
                Studio
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="collab-toggle" className="text-sm">
                Enable Collaboration
              </Label>
              <Switch
                id="collab-toggle"
                checked={collaborationEnabled}
                onCheckedChange={handleToggleCollaboration}
              />
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <h5 className="text-sm font-medium">Active Collaborators</h5>
              <div className="space-y-2">
                {collaborators.map((collaborator) => (
                  <div 
                    key={collaborator.id}
                    className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50"
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${collaborator.email}`} />
                      <AvatarFallback 
                        style={{ backgroundColor: collaborator.color }}
                        className="text-xs text-white"
                      >
                        {getInitials(collaborator.name, collaborator.email)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="text-sm font-medium">
                        {collaborator.name}
                        {collaborator.id === currentUserId && ' (You)'}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {collaborator.email}
                      </p>
                    </div>
                    {collaborator.cursor && (
                      <div className="text-xs text-muted-foreground">
                        Line {collaborator.cursor.line}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={copySessionLink}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Session Link
              </Button>
              
              {canInvite && (
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={onInviteUser}
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Invite Team Member
                </Button>
              )}
            </div>
            
            <div className="bg-muted/50 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <Sparkles className="h-4 w-4 text-primary mt-0.5" />
                <div className="space-y-1">
                  <p className="text-xs font-medium">Studio Feature</p>
                  <p className="text-xs text-muted-foreground">
                    Real-time collaboration allows multiple writers to work on the same document simultaneously with live cursors and selections.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}