#!/usr/bin/env node

import { readFileSync, writeFileSync } from 'fs';
import { glob } from 'glob';
import * as path from 'path';

// List of API routes that need authentication
const ROUTES_NEEDING_AUTH = [
  'src/app/api/orchestration/progress/route.ts',
  'src/app/api/timeline/validate/route.ts',
  'src/app/api/timeline/autofix/route.ts',
  'src/app/api/relationships/analyze/route.ts',
  'src/app/api/relationships/graph/route.ts',
  'src/app/api/search/index/route.ts',
  'src/app/api/search/semantic/route.ts',
  'src/app/api/search/theme/route.ts',
  'src/app/api/search/emotion/route.ts',
  'src/app/api/search/character-moments/route.ts',
  'src/app/api/search/related/route.ts',
  'src/app/api/analytics/profiles/performance/route.ts',
  'src/app/api/analytics/selections/route.ts',
  'src/app/api/analytics/selections/success-patterns/route.ts',
  'src/app/api/content-analysis/route.ts',
  'src/app/api/analysis/content/route.ts',
  'src/app/api/analysis/voice/route.ts',
  'src/app/api/profiles/[id]/clone/route.ts',
  'src/app/api/profiles/[id]/favorite/route.ts',
  'src/app/api/projects/[id]/export/route.ts',
  'src/app/api/projects/[id]/voice-profile/route.ts',
  'src/app/api/references/[id]/route.ts',
  'src/app/api/series/[id]/apply-voice-profile/route.ts',
  'src/app/api/series/[id]/character-continuity/route.ts',
  'src/app/api/series/[id]/continuity/route.ts',
  'src/app/api/series/[id]/universe/route.ts',
  'src/app/api/voice-profiles/[id]/route.ts',
  'src/app/api/voice-profiles/[id]/train/route.ts',
  'src/app/api/voice-profiles/[id]/train-from-content/route.ts',
  'src/app/api/agents/edit/route.ts',
  'src/app/api/achievements/stats/route.ts'
];

// Import statement to add
const AUTH_IMPORT = `import { authenticateUser, authenticateUserForProject } from '@/lib/api/auth-helpers';`;

// Auth check code for simple authentication
const SIMPLE_AUTH_CHECK = `  // Check authentication
  const { user, error: authError } = await authenticateUser();
  if (authError) return authError;
`;

// Auth check code for project-based authentication
const PROJECT_AUTH_CHECK = `  // Check authentication and project access
  const { user, error: authError } = await authenticateUserForProject(projectId);
  if (authError) return authError;
`;

function addAuthToRoute(filePath: string) {
  console.log(`Processing ${filePath}...`);
  
  try {
    let content = readFileSync(filePath, 'utf-8');
    
    // Check if already has authentication
    if (content.includes('authenticateUser') || content.includes('auth.getUser()')) {
      console.log(`  ✓ Already has authentication`);
      return;
    }
    
    // Add import if not present
    if (!content.includes('auth-helpers')) {
      // Find the last import statement
      const importRegex = /^import .* from .*;$/gm;
      const imports = content.match(importRegex);
      if (imports) {
        const lastImport = imports[imports.length - 1];
        content = content.replace(lastImport, `${lastImport}\n${AUTH_IMPORT}`);
      } else {
        // Add at the beginning if no imports found
        content = `${AUTH_IMPORT}\n\n${content}`;
      }
    }
    
    // Determine if this is a project-based route
    const isProjectRoute = content.includes('projectId') && 
                          (filePath.includes('/projects/') || 
                           filePath.includes('/timeline/') || 
                           filePath.includes('/search/') ||
                           filePath.includes('/orchestration/'));
    
    // Add auth check to each exported function
    const functionRegex = /export async function (GET|POST|PUT|DELETE|PATCH)\([^)]*\) {/g;
    content = content.replace(functionRegex, (match, method) => {
      const authCheck = isProjectRoute ? PROJECT_AUTH_CHECK : SIMPLE_AUTH_CHECK;
      return `${match}\n${authCheck}`;
    });
    
    // Special handling for routes that get projectId from params
    if (filePath.includes('[id]')) {
      content = content.replace(
        /const { user, error: authError } = await authenticateUserForProject\(projectId\);/g,
        `const projectId = params.id;\n  const { user, error: authError } = await authenticateUserForProject(projectId);`
      );
    }
    
    writeFileSync(filePath, content);
    console.log(`  ✓ Added authentication`);
    
  } catch (error) {
    console.error(`  ✗ Error processing file: ${error}`);
  }
}

// Process all routes
console.log('Adding authentication to API routes...\n');

ROUTES_NEEDING_AUTH.forEach(route => {
  const fullPath = path.join(process.cwd(), route);
  addAuthToRoute(fullPath);
});

console.log('\nDone! Remember to:');
console.log('1. Review the changes manually');
console.log('2. Fix any compilation errors');
console.log('3. Test the endpoints');
console.log('4. Consider adding more specific authorization checks where needed');