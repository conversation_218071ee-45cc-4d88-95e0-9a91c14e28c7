'use client'

import { useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useCachedData, projectListCache, cacheKeys, Cache } from '@/lib/cache/client'
import { createClient } from '@/lib/supabase/client'
import { Skeleton } from '@/components/ui/skeleton'
import { Button } from '@/components/ui/button'
import { RefreshCw } from 'lucide-react'

interface Project {
  id: string
  name: string
  description: string
  genre: string
  status: string
  progress: {
    wordsWritten: number
    targetWords: number
    chaptersComplete: number
    targetChapters: number
    percentComplete: number
  }
  metadata: {
    createdAt: Date
    updatedAt: Date
    targetAudience: string
    contentRating: string
    estimatedReadTime: number
  }
  [key: string]: unknown
}

interface ProjectsListProps {
  userId: string
}

export function ProjectsList({ userId }: ProjectsListProps) {
  const supabase = createClient()
  
  const fetcher = async () => {
    const response = await fetch('/api/projects')
    if (!response.ok) {
      throw new Error('Failed to fetch projects')
    }
    const data = await response.json()
    return data.projects as Project[]
  }
  
  const { data: projects, loading, error, refresh } = useCachedData<Project[]>({
    cacheKey: cacheKeys.projectList(userId),
    cache: projectListCache as Cache<Project[]>,
    fetcher,
    staleTime: 60000, // Consider data stale after 1 minute
  })

  // Subscribe to real-time updates
  useEffect(() => {
    const subscription = supabase
      .channel('projects-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'projects',
          filter: `user_id=eq.${userId}`,
        },
        () => {
          // Refresh the cache when changes occur
          refresh()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [userId, refresh, supabase])

  if (loading && !projects) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-12 w-full" />
              <div className="mt-4 flex items-center justify-between">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-24" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Card className="text-center">
        <CardContent className="py-16">
          <p className="mb-4 text-destructive">Failed to load projects.</p>
          <Button onClick={refresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (!projects || projects.length === 0) {
    return (
      <Card className="text-center">
        <CardContent className="py-16">
          <p className="mb-4 text-muted-foreground">You haven&apos;t created any projects yet.</p>
          <div className="flex gap-2 justify-center flex-wrap">
            <Link href="/templates">
              <Button variant="outline">Browse Templates</Button>
            </Link>
            <Link href="/samples">
              <Button variant="outline">Try a Sample</Button>
            </Link>
            <Link href="/projects/new">
              <Button>Create Your First Project</Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {projects.map((project) => (
        <Link key={project.id} href={`/projects/${project.id}`}>
          <Card className="h-full transition-shadow hover:shadow-lg">
            <CardHeader>
              <CardTitle>{project.name}</CardTitle>
              <CardDescription>
                {project.genre} • {project.progress.targetWords.toLocaleString()} words
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground line-clamp-3">
                {project.description || 'No description yet'}
              </p>
              <div className="mt-4 flex items-center justify-between text-sm">
                <span className="capitalize text-muted-foreground">{project.status}</span>
                <span className="text-muted-foreground">
                  {project.progress.wordsWritten.toLocaleString()} / {project.progress.targetWords.toLocaleString()}
                </span>
              </div>
              <div className="mt-2">
                <div className="h-2 w-full bg-secondary rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-primary transition-all"
                    style={{ width: `${Math.min(project.progress.percentComplete, 100)}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  )
}