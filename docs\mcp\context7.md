# Context7 MCP Server

The Context7 MCP server provides access to comprehensive documentation and code examples for thousands of libraries and frameworks, enabling instant access to implementation guides and best practices.

## Overview

Context7 MCP enables you to:
- Search library documentation across thousands of projects
- Get code examples and implementation guides
- Access up-to-date API references
- Find best practices and patterns
- Resolve library compatibility information
- Get contextual code snippets

## Configuration

### Basic Configuration

```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["@upstash/context7-mcp"],
      "env": {}
    }
  }
}
```

### Optional Environment Variables

- **CONTEXT7_API_KEY**: For enhanced rate limits (optional)
- **CONTEXT7_MAX_TOKENS**: Control response size (default: 10000)

## Available Tools

### Library Search
- **Resolve Library ID**: Find the correct library identifier
- **Search Libraries**: Find libraries by name or functionality
- **Get Library Info**: Retrieve detailed library information

### Documentation Access
- **Get Library Docs**: Fetch comprehensive documentation
- **Topic-Specific Docs**: Get focused documentation on specific topics
- **Code Snippets**: Access practical implementation examples
- **API References**: Get detailed API documentation

### Version Management
- **List Versions**: See available library versions
- **Version-Specific Docs**: Get documentation for specific versions
- **Migration Guides**: Access upgrade and migration information

## Example Usage

### Finding Libraries
```
Find React libraries for state management
```

```
Search for TypeScript validation libraries
```

### Getting Documentation
```
Get React hooks documentation with examples
```

```
Show me Next.js routing documentation and code samples
```

### Specific Topics
```
Get Supabase authentication setup guide with code examples
```

```
Show me Stripe subscription implementation patterns
```

### Version-Specific Help
```
Get React 18 migration guide and breaking changes
```

```
Show me Node.js 20 new features and examples
```

## Supported Libraries

Context7 includes documentation for thousands of libraries including:

### Frontend Frameworks
- React, Vue, Angular, Svelte
- Next.js, Nuxt, SvelteKit
- Gatsby, Remix, Vite

### Backend Technologies
- Node.js, Express, Fastify
- Python frameworks (Django, Flask, FastAPI)
- Database ORMs and clients

### UI Libraries
- Material-UI, Chakra UI, Ant Design
- Tailwind CSS, Styled Components
- Component libraries and design systems

### Development Tools
- TypeScript, ESLint, Prettier
- Testing frameworks (Jest, Cypress, Playwright)
- Build tools (Webpack, Rollup, esbuild)

### Cloud Services
- AWS SDK, Google Cloud, Azure
- Supabase, Firebase, PlanetScale
- Vercel, Netlify deployment guides

## Library ID Format

Context7 uses a specific format for library IDs:
- Format: `/org/project` or `/org/project/version`
- Examples: 
  - `/facebook/react`
  - `/vercel/next.js/v14.0.0`
  - `/supabase/supabase`

## Search and Discovery

### Finding the Right Library
1. Use natural language to describe what you need
2. Browse by category or use case
3. Filter by popularity and trust score
4. Check code snippet availability

### Quality Indicators
- **Trust Score**: Library authority and reliability (1-10)
- **Code Snippets**: Number of available examples
- **Documentation Coverage**: Completeness of docs
- **Community Support**: Activity and maintenance

## Best Practices

### Effective Searches
- Be specific about your use case
- Include technology stack context
- Mention specific features you need
- Ask for alternatives when appropriate

### Using Documentation
- Always check for the latest version
- Look for migration guides between versions
- Verify compatibility with your stack
- Review code examples before implementation

### Implementation Guidance
- Follow security best practices from docs
- Check for deprecated features
- Understand breaking changes between versions
- Review performance considerations

## Common Use Cases

### Learning New Technologies
- Get started guides for new frameworks
- Understand core concepts and patterns
- Find comprehensive tutorials
- Access best practices and conventions

### Problem Solving
- Find solutions to specific implementation challenges
- Get debugging guides and troubleshooting tips
- Access error handling patterns
- Find performance optimization techniques

### Architecture Decisions
- Compare libraries and frameworks
- Understand trade-offs and limitations
- Get integration guides between technologies
- Access scalability considerations

### Code Quality
- Find testing strategies and examples
- Access linting and formatting configurations
- Get security best practices
- Review accessibility guidelines

## Integration with Development

### Development Workflow
- Quick reference during coding
- Resolve syntax and API questions
- Find integration examples
- Get configuration templates

### Code Reviews
- Verify best practices usage
- Check for security vulnerabilities
- Validate architectural decisions
- Reference official documentation

### Team Knowledge Sharing
- Share authoritative documentation
- Provide learning resources
- Document architectural decisions
- Create development standards

## Performance Optimization

### Efficient Queries
- Be specific in your requests
- Use topic filtering when available
- Request only needed information
- Cache frequently accessed docs

### Rate Limiting
- Understand API limits
- Implement proper retry logic
- Use efficient search strategies
- Consider caching for repeated queries

## Troubleshooting

### Library Not Found
- Check spelling and exact name
- Try alternative names or aliases
- Search for organization or maintainer
- Use broader search terms

### Outdated Information
- Specify version requirements
- Check for newer versions
- Look for migration guides
- Verify current best practices

### Integration Issues
- Check compatibility requirements
- Review peer dependency information
- Look for known issues and workarounds
- Consult community discussions

## Security Considerations

### Code Safety
- Review code examples for security issues
- Follow security best practices from official docs
- Validate third-party recommendations
- Keep libraries updated

### API Usage
- Don't expose API keys in documentation requests
- Use official documentation sources
- Verify library authenticity
- Check for security advisories

## Links

- [Context7 MCP GitHub](https://github.com/upstash/context7)
- [Context7 Platform](https://context7.io/)
- [Upstash Documentation](https://docs.upstash.com/)