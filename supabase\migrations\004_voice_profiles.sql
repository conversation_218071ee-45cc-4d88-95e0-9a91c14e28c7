-- Voice Profiles and Training System
-- This migration adds comprehensive voice profile management and training functionality

-- Voice profiles table
CREATE TABLE voice_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  description TEXT,
  type VARCHAR(50) NOT NULL CHECK (type IN ('author', 'character', 'narrator')),
  
  -- Voice analysis data
  patterns JSONB NOT NULL DEFAULT '{}',
  sample_texts TEXT[] DEFAULT '{}',
  confidence DECIMAL(3,2) DEFAULT 0.50,
  
  -- Training metadata
  training_method VARCHAR(50) DEFAULT 'manual' CHECK (training_method IN ('manual', 'ai_extracted', 'hybrid')),
  training_samples_count INTEGER DEFAULT 0,
  total_words_analyzed INTEGER DEFAULT 0,
  
  -- Scope
  is_global BOOLEAN DEFAULT false,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  series_id UUID REFERENCES series(id) ON DELETE CASCADE,
  character_id UUID REFERENCES characters(id) ON DELETE CASCADE,
  
  -- Versioning
  version INTEGER DEFAULT 1,
  parent_profile_id UUID REFERENCES voice_profiles(id) ON DELETE SET NULL,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT valid_scope CHECK (
    (is_global = true AND project_id IS NULL AND series_id IS NULL AND character_id IS NULL) OR
    (is_global = false AND (project_id IS NOT NULL OR series_id IS NOT NULL OR character_id IS NOT NULL))
  )
);

-- Voice training sessions
CREATE TABLE voice_training_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  voice_profile_id UUID REFERENCES voice_profiles(id) ON DELETE CASCADE NOT NULL,
  
  -- Training data
  input_text TEXT NOT NULL,
  input_source VARCHAR(100) DEFAULT 'manual_entry' 
    CHECK (input_source IN ('manual_entry', 'file_upload', 'project_content')),
  
  -- Analysis results
  analysis_results JSONB DEFAULT '{}',
  patterns_extracted JSONB DEFAULT '{}',
  
  -- Status
  status VARCHAR(50) DEFAULT 'pending' 
    CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  error_message TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Series character continuity
CREATE TABLE series_character_continuity (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  series_id UUID REFERENCES series(id) ON DELETE CASCADE NOT NULL,
  character_name VARCHAR(255) NOT NULL,
  
  -- Character status tracking
  first_appearance_book INTEGER NOT NULL,
  last_appearance_book INTEGER,
  status VARCHAR(50) DEFAULT 'active' 
    CHECK (status IN ('active', 'inactive', 'deceased', 'written_out', 'mentioned_only')),
  status_change_book INTEGER,
  status_change_reason TEXT,
  
  -- Voice profile association
  voice_profile_id UUID REFERENCES voice_profiles(id) ON DELETE SET NULL,
  
  -- Character evolution
  character_states JSONB DEFAULT '{}',
  relationship_changes JSONB DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique character per series
  UNIQUE(series_id, character_name)
);

-- Voice consistency checks
CREATE TABLE voice_consistency_checks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE NOT NULL,
  chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
  voice_profile_id UUID REFERENCES voice_profiles(id) ON DELETE CASCADE NOT NULL,
  
  -- Analysis results
  consistency_score DECIMAL(3,2) CHECK (consistency_score >= 0 AND consistency_score <= 1),
  deviations JSONB DEFAULT '[]',
  suggestions JSONB DEFAULT '[]',
  
  -- Metadata
  content_analyzed TEXT,
  word_count INTEGER,
  analysis_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_voice_profiles_user_id ON voice_profiles(user_id);
CREATE INDEX idx_voice_profiles_project_id ON voice_profiles(project_id);
CREATE INDEX idx_voice_profiles_series_id ON voice_profiles(series_id);
CREATE INDEX idx_voice_profiles_character_id ON voice_profiles(character_id);
CREATE INDEX idx_voice_profiles_type ON voice_profiles(type);
CREATE INDEX idx_voice_profiles_is_global ON voice_profiles(is_global);

CREATE INDEX idx_voice_training_sessions_profile_id ON voice_training_sessions(voice_profile_id);
CREATE INDEX idx_voice_training_sessions_status ON voice_training_sessions(status);

CREATE INDEX idx_series_character_continuity_series_id ON series_character_continuity(series_id);
CREATE INDEX idx_series_character_continuity_status ON series_character_continuity(status);

CREATE INDEX idx_voice_consistency_checks_project_id ON voice_consistency_checks(project_id);
CREATE INDEX idx_voice_consistency_checks_chapter_id ON voice_consistency_checks(chapter_id);
CREATE INDEX idx_voice_consistency_checks_profile_id ON voice_consistency_checks(voice_profile_id);

-- Enable RLS on all tables
ALTER TABLE voice_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_training_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE series_character_continuity ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_consistency_checks ENABLE ROW LEVEL SECURITY;

-- RLS Policies for voice_profiles
CREATE POLICY "Users can view own voice profiles" ON voice_profiles
  FOR SELECT USING (
    auth.uid() = user_id OR 
    (is_global = true AND EXISTS (
      SELECT 1 FROM voice_profiles vp WHERE vp.id = voice_profiles.id AND vp.user_id = auth.uid()
    ))
  );

CREATE POLICY "Users can create own voice profiles" ON voice_profiles
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own voice profiles" ON voice_profiles
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own voice profiles" ON voice_profiles
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for voice_training_sessions
CREATE POLICY "Users can view own training sessions" ON voice_training_sessions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM voice_profiles vp 
      WHERE vp.id = voice_training_sessions.voice_profile_id 
      AND vp.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create training sessions for own profiles" ON voice_training_sessions
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM voice_profiles vp 
      WHERE vp.id = voice_training_sessions.voice_profile_id 
      AND vp.user_id = auth.uid()
    )
  );

-- RLS Policies for series_character_continuity
CREATE POLICY "Users can view own series character continuity" ON series_character_continuity
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM series s 
      WHERE s.id = series_character_continuity.series_id 
      AND s.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage own series character continuity" ON series_character_continuity
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM series s 
      WHERE s.id = series_character_continuity.series_id 
      AND s.user_id = auth.uid()
    )
  );

-- RLS Policies for voice_consistency_checks
CREATE POLICY "Users can view own consistency checks" ON voice_consistency_checks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects p 
      WHERE p.id = voice_consistency_checks.project_id 
      AND p.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create consistency checks for own projects" ON voice_consistency_checks
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects p 
      WHERE p.id = voice_consistency_checks.project_id 
      AND p.user_id = auth.uid()
    )
  );

-- Update triggers
CREATE TRIGGER update_voice_profiles_updated_at
  BEFORE UPDATE ON voice_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_series_character_continuity_updated_at
  BEFORE UPDATE ON series_character_continuity
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Add voice_profile_id to projects table for default project voice
ALTER TABLE projects ADD COLUMN IF NOT EXISTS voice_profile_id UUID REFERENCES voice_profiles(id) ON DELETE SET NULL;

-- Add voice_profile_id to series table for series-wide voice
ALTER TABLE series ADD COLUMN IF NOT EXISTS voice_profile_id UUID REFERENCES voice_profiles(id) ON DELETE SET NULL;

-- Create index for new columns
CREATE INDEX idx_projects_voice_profile_id ON projects(voice_profile_id);
CREATE INDEX idx_series_voice_profile_id ON series(voice_profile_id);