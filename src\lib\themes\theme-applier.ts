/**
 * Theme Applier - Simplified theme switching for BookScribe AI
 * Directly applies theme changes via CSS custom properties
 */

'use client';

// Simple theme configurations that match our CSS
export const themeConfigs = {
  // Light themes
  'writers-sanctuary-light': {
    id: 'writers-sanctuary-light',
    name: "Writer's Sanctuary",
    description: "Warm, literary atmosphere for focused writing",
    mode: 'light' as const,
    cssClass: 'writers-sanctuary-light',
    variables: {
      '--background': '45 50% 97%',
      '--foreground': '25 30% 10%', /* Darker for better contrast */
      '--primary': '25 75% 45%',
      '--card': '42 45% 95%',
      '--border': '40 25% 85%',
    }
  },
  'forest-manuscript-light': {
    id: 'forest-manuscript-light',
    name: "Forest Manuscript",
    description: "Natural, green-tinted writing environment",
    mode: 'light' as const,
    cssClass: 'forest-manuscript-light',
    variables: {
      '--background': '120 20% 98%',
      '--foreground': '120 25% 15%', /* Darker for better contrast */
      '--primary': '120 30% 35%',
      '--card': '120 20% 95%',
      '--border': '120 20% 88%',
    }
  },
  
  // Dark themes
  'evening-study-dark': {
    id: 'evening-study-dark',
    name: "Evening Study",
    description: "Warm dark brown for comfortable night writing",
    mode: 'dark' as const,
    cssClass: 'evening-study-dark',
    variables: {
      '--background': '25 8% 6%',
      '--foreground': '45 20% 98%', /* Brighter for better contrast */
      '--primary': '35 45% 65%',
      '--card': '25 12% 12%',
      '--border': '25 10% 18%',
    }
  },
  'midnight-ink-dark': {
    id: 'midnight-ink-dark',
    name: "Midnight Ink",
    description: "Deep blue-black for late night creativity",
    mode: 'dark' as const,
    cssClass: 'midnight-ink-dark',
    variables: {
      '--background': '220 40% 8%',
      '--foreground': '0 0% 95%', /* Brighter for better contrast */
      '--primary': '220 100% 70%',
      '--card': '220 35% 12%',
      '--border': '220 30% 18%',
    }
  }
};

export type ThemeId = keyof typeof themeConfigs;

/**
 * Apply theme to document
 */
export function applyTheme(themeId: ThemeId): void {
  if (typeof document === 'undefined') return;
  
  const theme = themeConfigs[themeId];
  if (!theme) return;
  
  const root = document.documentElement;
  
  // Remove all theme classes
  Object.values(themeConfigs).forEach(config => {
    root.classList.remove(config.cssClass);
  });
  root.classList.remove('light', 'dark');
  
  // Add new theme class and mode
  root.classList.add(theme.cssClass, theme.mode);
  
  // Apply CSS variables (optional, since CSS classes handle most styling)
  Object.entries(theme.variables).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });
  
  // Set data attribute for reference
  root.setAttribute('data-theme', themeId);
}

/**
 * Get theme by ID
 */
export function getTheme(themeId: ThemeId) {
  return themeConfigs[themeId];
}

/**
 * Get all themes
 */
export function getAllThemes() {
  return Object.values(themeConfigs);
}

/**
 * Get themes by mode
 */
export function getThemesByMode(mode: 'light' | 'dark') {
  return Object.values(themeConfigs).filter(theme => theme.mode === mode);
}

/**
 * Get current theme from document
 */
export function getCurrentTheme(): ThemeId | null {
  if (typeof document === 'undefined') return null;
  const themeId = document.documentElement.getAttribute('data-theme') as ThemeId;
  return themeConfigs[themeId] ? themeId : null;
}

/**
 * Initialize theme system
 */
export function initializeTheme(): void {
  if (typeof document === 'undefined') return;
  
  // Check for saved theme in localStorage
  const savedTheme = localStorage.getItem('bookscribe-theme') as ThemeId;
  
  if (savedTheme && themeConfigs[savedTheme]) {
    applyTheme(savedTheme);
  } else {
    // Default to light theme
    applyTheme('writers-sanctuary-light');
  }
}

/**
 * Save theme preference
 */
export function saveThemePreference(themeId: ThemeId): void {
  if (typeof localStorage === 'undefined') return;
  localStorage.setItem('bookscribe-theme', themeId);
}

/**
 * Switch theme and save preference
 */
export function switchTheme(themeId: ThemeId): void {
  applyTheme(themeId);
  saveThemePreference(themeId);
}