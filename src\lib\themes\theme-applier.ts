/**
 * Theme Applier - Simplified theme information for BookScribe AI
 * Provides theme metadata without directly manipulating the DOM
 */

'use client';

// Simple theme configurations that match our CSS
export const themeConfigs = {
  // Light themes
  'writers-sanctuary-light': {
    id: 'writers-sanctuary-light',
    name: "Writer's Sanctuary",
    description: "Warm, literary atmosphere for focused writing",
    mode: 'light' as const,
  },
  'forest-manuscript-light': {
    id: 'forest-manuscript-light',
    name: "Forest Manuscript",
    description: "Natural, green-tinted writing environment",
    mode: 'light' as const,
  },
  
  // Dark themes
  'evening-study-dark': {
    id: 'evening-study-dark',
    name: "Evening Study",
    description: "Warm dark brown for comfortable night writing",
    mode: 'dark' as const,
  },
  'midnight-ink-dark': {
    id: 'midnight-ink-dark',
    name: "Midnight Ink",
    description: "Deep blue-black for late night creativity",
    mode: 'dark' as const,
  }
};

export type ThemeId = keyof typeof themeConfigs;

/**
 * Get theme by ID
 */
export function getTheme(themeId: ThemeId) {
  return themeConfigs[themeId];
}

/**
 * Get all themes
 */
export function getAllThemes() {
  return Object.values(themeConfigs);
}

/**
 * Get themes by mode
 */
export function getThemesByMode(mode: 'light' | 'dark') {
  return Object.values(themeConfigs).filter(theme => theme.mode === mode);
}