import { useState, useCallback, useMemo } from 'react'
import { z } from 'zod'

interface ValidationError {
  field: string
  message: string
}

interface UseFormValidationOptions<T> {
  schema: z.ZodSchema<T>
  onValidData?: (data: T) => void | Promise<void>
  validateOnBlur?: boolean
  validateOnChange?: boolean
  debounceMs?: number
}

export function useFormValidation<T>({
  schema,
  onValidData,
  validateOnBlur = true,
  validateOnChange = false,
  debounceMs = 300,
}: UseFormValidationOptions<T>) {
  const [errors, setErrors] = useState<ValidationError[]>([])
  const [isValid, setIsValid] = useState(false)
  const [isValidating, setIsValidating] = useState(false)
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set())

  // Debounce helper
  const debounce = useCallback(<TArgs extends unknown[]>(func: (...args: TArgs) => void, delay: number) => {
    let timeoutId: NodeJS.Timeout
    return (...args: TArgs) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func(...args), delay)
    }
  }, [])

  const validateField = useCallback(
    (fieldName: string, value: unknown, allData: Partial<T>) => {
      try {
        // Try to validate just this field by creating a partial schema
        const partialData = { [fieldName]: value } as Partial<T>
        const result = schema.safeParse({ ...allData, ...partialData })
        
        if (result.success) {
          // Remove any existing errors for this field
          setErrors(prev => prev.filter(error => error.field !== fieldName))
        } else {
          // Check if there are errors for this specific field
          const fieldErrors = result.error.errors.filter(err => 
            err.path.length > 0 && err.path[0] === fieldName
          )
          if (fieldErrors.length > 0 && fieldErrors[0]?.message) {
            setErrors(prev => [
              ...prev.filter(err => err.field !== fieldName),
              { field: fieldName, message: fieldErrors[0]!.message }
            ])
          }
        }
      } catch {
        // Fallback error handling
        setErrors(prev => [
          ...prev.filter(err => err.field !== fieldName),
          { field: fieldName, message: 'Validation failed' }
        ])
      }
    },
    [schema]
  )

  const validateForm = useCallback(
    async (data: Partial<T>): Promise<{ isValid: boolean; errors: ValidationError[] }> => {
      setIsValidating(true)
      
      try {
        const result = schema.safeParse(data)
        
        if (result.success) {
          setErrors([])
          setIsValid(true)
          
          if (onValidData) {
            await onValidData(result.data)
          }
          
          return { isValid: true, errors: [] }
        } else {
          const validationErrors: ValidationError[] = result.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          }))
          
          setErrors(validationErrors)
          setIsValid(false)
          
          return { isValid: false, errors: validationErrors }
        }
      } catch {
        const generalError = [{ field: 'general', message: 'Validation failed' }]
        setErrors(generalError)
        setIsValid(false)
        return { isValid: false, errors: generalError }
      } finally {
        setIsValidating(false)
      }
    },
    [schema, onValidData]
  )

  const debouncedValidateForm = useMemo(
    () => debounce(validateForm, debounceMs),
    [validateForm, debounceMs, debounce]
  )

  const handleFieldChange = useCallback(
    (fieldName: string, _value: unknown, allData: Partial<T>) => {
      if (validateOnChange) {
        if (touchedFields.has(fieldName)) {
          debouncedValidateForm(allData)
        }
      }
    },
    [validateOnChange, touchedFields, debouncedValidateForm]
  )

  const handleFieldBlur = useCallback(
    (fieldName: string, value: unknown, allData: Partial<T>) => {
      setTouchedFields(prev => new Set(prev).add(fieldName))
      
      if (validateOnBlur) {
        validateField(fieldName, value, allData)
      }
    },
    [validateOnBlur, validateField]
  )

  const getFieldError = useCallback(
    (fieldName: string): string | undefined => {
      return errors.find(error => error.field === fieldName)?.message
    },
    [errors]
  )

  const hasFieldError = useCallback(
    (fieldName: string): boolean => {
      return errors.some(error => error.field === fieldName)
    },
    [errors]
  )

  const clearErrors = useCallback(() => {
    setErrors([])
    setIsValid(false)
  }, [])

  const clearFieldError = useCallback((fieldName: string) => {
    setErrors(prev => prev.filter(error => error.field !== fieldName))
  }, [])

  return {
    // State
    errors,
    isValid,
    isValidating,
    touchedFields,
    
    // Methods
    validateForm,
    validateField,
    handleFieldChange,
    handleFieldBlur,
    getFieldError,
    hasFieldError,
    clearErrors,
    clearFieldError,
    
    // Helpers
    isFieldTouched: (fieldName: string) => touchedFields.has(fieldName),
    shouldShowFieldError: (fieldName: string) => 
      touchedFields.has(fieldName) && hasFieldError(fieldName),
  }
}