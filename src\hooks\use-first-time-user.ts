'use client';

import { useState, useEffect } from 'react';
import { logger } from '@/lib/services/logger';

import { createClient } from '@/lib/supabase/client';
import { useRouter } from 'next/navigation';
import { createSampleProject } from '@/lib/data/sample-project';

interface FirstTimeUserState {
  isFirstTime: boolean;
  isLoading: boolean;
  showOnboarding: boolean;
  hasProjects: boolean;
}

export function useFirstTimeUser() {
  const [state, setState] = useState<FirstTimeUserState>({
    isFirstTime: false,
    isLoading: true,
    showOnboarding: false,
    hasProjects: false
  });
  
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    checkFirstTimeUser();
  }, []);

  const checkFirstTimeUser = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        setState(prev => ({ ...prev, isLoading: false }));
        return;
      }

      // Check if user has any projects
      const { data: projects, error } = await supabase
        .from('projects')
        .select('id')
        .eq('user_id', user.id);

      if (error) {
        logger.error('Error checking user projects:', error);
        setState(prev => ({ ...prev, isLoading: false }));
        return;
      }

      const hasProjects = projects && projects.length > 0;
      const isFirstTime = !hasProjects;

      // Check if user has seen onboarding before
      const onboardingKey = `bookscribe_onboarding_seen_${user.id}`;
      const hasSeenOnboarding = localStorage.getItem(onboardingKey) === 'true';

      setState({
        isFirstTime,
        isLoading: false,
        showOnboarding: isFirstTime && !hasSeenOnboarding,
        hasProjects
      });
    } catch (error) {
      logger.error('Error in checkFirstTimeUser:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const completeOnboarding = () => {
    const { data: { user } } = supabase.auth.getUser();
    user.then(result => {
      if (result.user) {
        const onboardingKey = `bookscribe_onboarding_seen_${result.user.id}`;
        localStorage.setItem(onboardingKey, 'true');
      }
    });
    
    setState(prev => ({ 
      ...prev, 
      showOnboarding: false 
    }));
  };

  const createFirstProject = async () => {
    try {
      completeOnboarding();
      router.push('/project-wizard');
    } catch (error) {
      logger.error('Error creating first project:', error);
    }
  };

  const createSampleProjectAndNavigate = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        logger.error('No user found');
        return;
      }

      completeOnboarding();

      // Create the sample project
      const sampleProject = await createSampleProject(user.id, supabase);
      
      if (sampleProject) {
        // Navigate to the sample project
        router.push(`/projects/${sampleProject.id}`);
      } else {
        logger.error('Failed to create sample project');
        // Fallback to project wizard
        router.push('/project-wizard');
      }
    } catch (error) {
      logger.error('Error creating sample project:', error);
      router.push('/project-wizard');
    }
  };

  const skipOnboarding = () => {
    completeOnboarding();
  };

  return {
    ...state,
    completeOnboarding,
    createFirstProject,
    createSampleProjectAndNavigate,
    skipOnboarding,
    refreshProjects: checkFirstTimeUser
  };
}