import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { db } from '@/lib/db/client';
import { aiProcessingQueue } from '@/lib/services/ai-processing-queue';
import type { ProjectSettings } from '@/lib/types/project-settings';
import { authenticateUser, handleRouteError } from '@/lib/auth';
import { z } from 'zod';
import { config } from '@/lib/config';
import { generalLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter';
import { cacheKeys, getServerCache, setServerCache } from '@/lib/cache/server';

// Validation schema for project creation
const createProjectSchema = z.object({
  settings: z.object({
    projectName: z.string().min(1, 'Project name is required'),
    description: z.string().optional(),
    primaryGenre: z.string().min(1, 'Primary genre is required'),
    subgenre: z.string().optional(),
    customGenre: z.string().optional(),
    narrativeVoice: z.string().optional(),
    tense: z.string().optional(),
    tone: z.array(z.string()).optional(),
    writingStyle: z.string().optional(),
    customStyleDescription: z.string().optional(),
    structureType: z.string().optional(),
    pacingPreference: z.string().optional(),
    chapterStructure: z.string().optional(),
    timelineComplexity: z.string().optional(),
    customStructureNotes: z.string().optional(),
    protagonistTypes: z.array(z.string()).optional(),
    antagonistTypes: z.array(z.string()).optional(),
    characterComplexity: z.string().optional(),
    characterArcTypes: z.array(z.string()).optional(),
    customCharacterConcepts: z.string().optional(),
    timePeriod: z.string().optional(),
    geographicSetting: z.string().optional(),
    worldType: z.string().optional(),
    magicTechLevel: z.string().optional(),
    customSettingDescription: z.string().optional(),
    majorThemes: z.array(z.string()).optional(),
    philosophicalThemes: z.array(z.string()).optional(),
    socialThemes: z.array(z.string()).optional(),
    customThemes: z.string().optional(),
    targetAudience: z.string().min(1, 'Target audience is required'),
    contentRating: z.string().optional(),
    initialConcept: z.string().optional(),
    contentWarnings: z.array(z.string()).optional(),
    culturalSensitivityNotes: z.string().optional(),
    projectScope: z.string().optional(),
    seriesType: z.string().optional(),
    interconnectionLevel: z.string().optional(),
    customScopeDescription: z.string().optional(),
    targetWordCount: z.number().int().positive().optional(),
    targetChapters: z.number().int().positive().optional(),
    chapterCountType: z.string().optional(),
    povCharacterCount: z.number().int().positive().optional(),
    povCharacterType: z.string().optional(),
    researchNeeds: z.array(z.string()).optional(),
    factCheckingLevel: z.string().optional(),
    customResearchNotes: z.string().optional()
  })
});

export async function GET() {
  try {
    const authResult = await authenticateUser();
    if (!authResult.success || !authResult.user) {
      return authResult.response!;
    }
    const { user } = authResult;
    const userId = user.id;

    // Check cache first
    const cacheKey = cacheKeys.projectList(userId);
    const cached = getServerCache(cacheKey);
    if (cached) {
      return NextResponse.json({ projects: cached });
    }

    // Get all projects for the user
    const projects = await db.projects.getAll(userId);

    // Get AI processing status for all projects at once (batch operation)
    const projectIds = projects.map(p => p.id);
    const allProcessingProgress = await aiProcessingQueue.getBatchProjectProgress(projectIds);

    // Map projects with their processing status
    const projectsWithProcessing = projects.map((project) => {
        const processingProgress = allProcessingProgress[project.id] || {
          projectId: project.id,
          currentTask: null,
          queuedTasks: [],
          completedTasks: [],
          totalTasks: 0,
          completedCount: 0,
          isActive: false,
          estimatedTimeRemaining: 0,
        };
        
        // Calculate progress metrics
        const chapters = project.chapters || [];
        const chapterCount = Array.isArray(chapters) ? chapters.length : chapters[0]?.count || 0;
        
        const wordsWritten = project.word_count || 0;
        const targetWords = project.target_word_count || 80000;
        const targetChapters = project.target_chapters || 20;
        
        return {
          id: project.id,
          name: project.name,
          description: project.description || '',
          genre: project.genre || 'Fiction',
          status: project.status || 'planning',
          progress: {
            wordsWritten,
            targetWords,
            chaptersComplete: chapterCount,
            targetChapters,
            percentComplete: Math.round((wordsWritten / targetWords) * 100),
          },
          aiProcessing: {
            isActive: processingProgress.isActive,
            currentTask: processingProgress.currentTask?.type,
            tasksInQueue: processingProgress.queuedTasks.length,
            lastProcessed: processingProgress.currentTask?.startedAt || 
              ((processingProgress.completedTasks && 
                Array.isArray(processingProgress.completedTasks) && 
                processingProgress.completedTasks.length > 0)
                  ? processingProgress.completedTasks[0]?.completedAt 
                  : undefined),
          },
          createdBy: 'Current User',
          metadata: {
            createdAt: new Date(project.created_at),
            updatedAt: new Date(project.updated_at),
            targetAudience: project.target_audience || 'Adult',
            contentRating: project.content_rating || 'PG-13',
            estimatedReadTime: Math.round(wordsWritten / 250), // 250 words per minute reading speed
          },
          stats: {
            dailyWords: project.daily_word_count || 0,
            weeklyWords: project.weekly_word_count || 0,
            streak: project.writing_streak || 0,
            avgWordsPerDay: project.avg_words_per_day || 0,
          },
          settings: project.project_settings?.[0]?.settings || null,
        };
      });

    // Cache the results for 5 minutes
    setServerCache(cacheKey, projectsWithProcessing, 5 * 60 * 1000);

    return NextResponse.json({ projects: projectsWithProcessing });
  } catch (error) {
    return handleRouteError(error, 'Projects GET');
  }
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for project creation (5 projects per hour)
    const clientIP = getClientIP(request);
    const rateLimitResult = generalLimiter.check(5, clientIP);
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    const authResult = await authenticateUser();
    if (!authResult.success || !authResult.user) {
      return authResult.response!;
    }
    const { user } = authResult;
    
    const body = await request.json();
    
    // Validate request body
    const validationResult = createProjectSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json({ 
        error: 'Invalid request data', 
        details: validationResult.error.errors 
      }, { status: 400 });
    }
    
    const { settings } = validationResult.data;
    const userId = user.id;

    // Create project
    const project = await db.projects.create({
      user_id: userId,
      name: settings.projectName,
      description: settings.description || '',
      genre: settings.primaryGenre,
      status: 'planning',
      target_audience: settings.targetAudience,
      content_rating: settings.contentRating || 'PG-13',
      target_word_count: settings.targetWordCount || 80000,
      target_chapters: settings.targetChapters || 20,
      word_count: 0,
      daily_word_count: 0,
      weekly_word_count: 0,
      writing_streak: 0,
      avg_words_per_day: 0,
    });

    // Save project settings
    try {
      await db.processing.createTask({
        id: `settings_${project.id}`,
        project_id: project.id,
        user_id: userId,
        type: 'save_settings',
        payload: { settings },
        estimated_duration: 1,
      });
    } catch (_settingsError) {
      // Don't fail the project creation if settings save fails
    }

    // Start advanced AI orchestration
    const orchestrationResponse = await startAdvancedOrchestration(project.id, {
      ...settings,
      description: settings.description || '',
      initialConcept: settings.initialConcept || settings.description || ''
    } as ProjectSettings);

    // Invalidate the user's project list cache
    const cacheKey = cacheKeys.projectList(userId);
    setServerCache(cacheKey, null, 0); // Clear cache

    return NextResponse.json({ 
      project: {
        id: project.id,
        name: project.name,
        description: project.description,
        genre: project.genre,
        status: project.status,
        createdAt: project.created_at,
        settings,
      },
      orchestration: orchestrationResponse
    });
  } catch (error) {
    return handleRouteError(error, 'Projects POST');
  }
}

async function startAdvancedOrchestration(projectId: string, settings: ProjectSettings) {
  try {
    // Start the advanced orchestration process
    const response = await fetch(`${config.app.url}/api/orchestration/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        projectId,
        projectSelections: settings,
        storyPrompt: settings.description || 'A compelling story based on the selected themes and preferences.',
        targetWordCount: settings.targetWordCount || 80000,
        targetChapters: settings.targetChapters || 20
      })
    });

    if (response.ok) {
      const data = await response.json();
      // Advanced orchestration started successfully
      return data;
    } else {
      // Failed to start advanced orchestration
      return { success: false, error: 'Failed to start orchestration' };
    }
  } catch (_error) {
    // Failed to start advanced orchestration
    return { success: false, error: 'Failed to start orchestration' };
  }
}