import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import ServiceManager from '@/lib/services/service-manager';
import { SemanticSearchService } from '@/lib/services/semantic-search';
import { z } from 'zod';
import { generalLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter';
import { cacheKeys, getServerCache, setServerCache } from '@/lib/cache/server';
import { authenticateUserForProject } from '@/lib/api/auth-helpers';

// Validation schemas
const searchRequestSchema = z.object({
  query: z.string().min(1).max(1000),
  projectId: z.string().uuid(),
  types: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  limit: z.number().int().positive().max(100).default(10).optional(),
  threshold: z.number().min(0).max(1).default(0.7).optional()
});

const similarContentSchema = z.object({
  contentId: z.string().uuid(),
  projectId: z.string().uuid().optional(),
  limit: z.number().int().positive().max(50).default(10).optional()
});

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for semantic search (60 searches per hour)
    const clientIP = getClientIP(request);
    const rateLimitResult = generalLimiter.check(60, clientIP);
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    const body = await request.json();
    
    // Validate request
    const validatedData = searchRequestSchema.parse(body);

    // Check authentication and project access
    const { user, error: authError } = await authenticateUserForProject(validatedData.projectId);
    if (authError) return authError;

    const serviceManager = ServiceManager.getInstance();
    await serviceManager.initialize();
    
    const searchService = await serviceManager.getService('semantic-search');

    if (!searchService) {
      return NextResponse.json(
        { error: 'Semantic search service not available' },
        { status: 503 }
      );
    }

    // Check cache for search results
    const cacheKey = cacheKeys.search(
      validatedData.query, 
      JSON.stringify({
        projectId: validatedData.projectId,
        types: validatedData.types,
        tags: validatedData.tags,
        limit: validatedData.limit,
        threshold: validatedData.threshold
      })
    );
    
    const cached = getServerCache(cacheKey) as {results: unknown[], total: number} | null;
    if (cached) {
      return NextResponse.json({
        success: true,
        results: cached.results,
        query: validatedData.query,
        total: cached.total,
        fromCache: true
      });
    }

    const searchResult = await (searchService as SemanticSearchService).search(validatedData.query, {
      projectId: validatedData.projectId,
      types: validatedData.types,
      tags: validatedData.tags,
      limit: validatedData.limit || 10,
      threshold: validatedData.threshold || 0.7
    });

    if (!searchResult.success) {
      return NextResponse.json(
        { error: searchResult.error || 'Search failed' },
        { status: 500 }
      );
    }

    const response = {
      results: searchResult.data,
      total: searchResult.data?.length || 0
    };
    
    // Cache the results for 10 minutes
    setServerCache(cacheKey, response, 10 * 60 * 1000);

    return NextResponse.json({
      success: true,
      results: searchResult.data,
      query: validatedData.query,
      total: searchResult.data?.length || 0
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid search parameters',
        details: error.errors
      }, { status: 400 })
    }
    
    console.error('Semantic search API error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Rate limiting for GET requests (100 requests per hour)
    const clientIP = getClientIP(request);
    const rateLimitResult = generalLimiter.check(100, clientIP);
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    const url = new URL(request.url);
    const action = url.searchParams.get('action');
    const contentId = url.searchParams.get('contentId');
    const projectId = url.searchParams.get('projectId');
    const limit = url.searchParams.get('limit');

    const serviceManager = ServiceManager.getInstance();
    await serviceManager.initialize();
    
    const searchService = await serviceManager.getService('semantic-search');

    if (!searchService) {
      return NextResponse.json(
        { error: 'Semantic search service not available' },
        { status: 503 }
      );
    }

    switch (action) {
      case 'similar':
        try {
          // Validate parameters
          const validatedParams = similarContentSchema.parse({
            contentId,
            projectId,
            limit: limit ? parseInt(limit) : undefined
          });

          // Check authentication and project access if projectId provided
          if (validatedParams.projectId) {
            const { user, error: authError } = await authenticateUserForProject(validatedParams.projectId);
            if (authError) return authError;
          }

          // Check cache for similar content results
          const similarCacheKey = `similar:${validatedParams.contentId}:${validatedParams.projectId || 'all'}:${validatedParams.limit || 10}`;
          const cachedSimilar = getServerCache(similarCacheKey);
          
          if (cachedSimilar) {
            return NextResponse.json({
              success: true,
              results: cachedSimilar,
              fromCache: true
            });
          }

          const similarResult = await (searchService as SemanticSearchService).similarContent(
            validatedParams.contentId, 
            {
              projectId: validatedParams.projectId,
              limit: validatedParams.limit || 10,
              excludeSelf: true
            }
          );

          // Cache similar content results for 15 minutes
          if (similarResult.success && similarResult.data) {
            setServerCache(similarCacheKey, similarResult.data, 15 * 60 * 1000);
          }

          return NextResponse.json({
            success: similarResult.success,
            results: similarResult.data,
            error: similarResult.error
          });
        } catch (error) {
          if (error instanceof z.ZodError) {
            return NextResponse.json({ 
              error: 'Invalid parameters',
              details: error.errors
            }, { status: 400 })
          }
          throw error;
        }

      case 'health':
        const healthResult = await (searchService as SemanticSearchService).healthCheck();
        return NextResponse.json(healthResult);

      default:
        return NextResponse.json({
          service: 'semantic-search',
          version: '1.0.0',
          endpoints: [
            'POST /api/search/semantic - Search content semantically',
            'GET /api/search/semantic?action=similar&contentId=X - Find similar content',
            'GET /api/search/semantic?action=health - Health check'
          ]
        });
    }

  } catch (error) {
    console.error('Semantic search API error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}