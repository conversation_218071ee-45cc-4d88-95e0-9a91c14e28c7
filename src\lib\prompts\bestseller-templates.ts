/**
 * Bestseller Writing Templates and Techniques
 * Reference patterns from successful NYT bestsellers and award-winning novels
 */

export const BESTSELLER_OPENING_HOOKS = {
  thriller: [
    "Start with a body or crime in progress (Gone Girl, The Girl on the Train)",
    "Open with the protagonist in immediate danger (The Da Vinci Code)",
    "Begin at a moment of life-changing decision (The Bourne Identity)",
    "Start with an ominous prediction coming true (The Silence of the Lambs)"
  ],
  romance: [
    "Meet-cute with instant conflict (Pride and Prejudice modern retellings)",
    "Return to hometown with unfinished business (<PERSON> pattern)",
    "Fake relationship setup (The Hating Game)",
    "Second chance with the one who got away (Me Before You elements)"
  ],
  fantasy: [
    "Ordinary world shattered by magic (<PERSON>, <PERSON>)",
    "Prophecy or chosen one revelation (Wheel of Time, Mistborn)",
    "In medias res during epic battle (The Way of Kings)",
    "Discovery of hidden heritage (Throne of Glass)"
  ],
  literary: [
    "Profound observation about life (The Goldfinch)",
    "Vivid sensory memory (In Search of Lost Time modern style)",
    "Character at a crossroads (A Little Life)",
    "Beautiful description hiding darkness (The Secret History)"
  ]
};

export const CHAPTER_ENDING_TECHNIQUES = {
  cliffhanger: {
    description: "Leave protagonist in immediate danger",
    example: "The door handle turned slowly. She held her breath, knowing that in seconds, everything would change forever."
  },
  revelation: {
    description: "Drop a shocking piece of information",
    example: "She stared at the photograph, her hands trembling. The man in the picture—the killer they'd been hunting—was her father."
  },
  emotional_gut_punch: {
    description: "End on a moment of profound emotion",
    example: "He walked away without looking back, and she knew with absolute certainty that this time, he wasn't coming home."
  },
  ominous_promise: {
    description: "Hint at dark events to come",
    example: "She had no idea that her decision to help the stranger would cost her everything she held dear."
  },
  question: {
    description: "Pose a question readers must have answered",
    example: "But if Sarah had been dead for three years, who was the woman wearing her face?"
  }
};

export const SHOW_DONT_TELL_EXAMPLES = {
  emotion: {
    tell: "She was angry.",
    show: "Her fingers curled into fists, nails biting crescents into her palms. The careful smile never left her face, but her eyes had gone flat and dangerous as a shark's."
  },
  character_trait: {
    tell: "He was a perfectionist.",
    show: "He adjusted the frame for the third time, measuring the distance from the corner with his fingertip. Still not right. The left side was perhaps a millimeter too high."
  },
  atmosphere: {
    tell: "The house was creepy.",
    show: "Shadows pooled in corners the lamplight couldn't reach. Somewhere in the walls, wood creaked like old bones, and the air tasted of dust and secrets."
  },
  relationship: {
    tell: "They had grown apart.",
    show: "They sat at opposite ends of the couch, the space between them filled with unread books and unspoken words. When their eyes met, they both looked away."
  }
};

export const DIALOGUE_EXCELLENCE_PATTERNS = {
  subtext: {
    surface: '"How was your day?" / "Fine."',
    subtext: '"How was your day?" She didn\'t look up from her phone. / "Fine." The word came out sharp enough to cut glass.',
    meaning: "Conflict simmering beneath politeness"
  },
  character_voice: {
    educated: '"The implications of this discovery are quite significant."',
    street_smart: '"This thing\'s gonna blow up in our faces, mark my words."',
    child: '"But why can\'t the rainbow stay forever?"',
    technique: "Vocabulary, rhythm, and worldview all differ"
  },
  natural_flow: {
    stilted: '"Hello, John. How are you today?" / "I am well, Sarah. Thank you for asking."',
    natural: '"Hey." / "Hey yourself. You look like hell." / "Thanks. Feel worse."',
    technique: "Interruptions, fragments, and real speech patterns"
  }
};

export const PACING_PATTERNS = {
  scene_sequel: {
    scene: "High action, conflict, immediate consequences",
    sequel: "Reaction, reflection, decision for next action",
    example: "Chase scene (SCENE) → Hero escapes, processes what happened, decides next move (SEQUEL) → Confrontation (SCENE)"
  },
  tension_escalation: {
    pattern: "Small problem → Complications → Bigger stakes → Personal cost → Everything at risk",
    example: "Missing item → Realize it's stolen → Thief is trusted friend → Friend is in danger → Must choose between friend and justice"
  },
  breathing_room: {
    pattern: "Intense scene → Character moment → Building tension → Explosive climax",
    example: "Battle → Quiet scene with mentor → Preparation montage → Final confrontation"
  }
};

export const EMOTIONAL_RESONANCE_TECHNIQUES = {
  universal_experiences: [
    "First love and heartbreak",
    "Loss of innocence",
    "Betrayal by someone trusted",
    "Finding belonging after isolation",
    "Sacrifice for loved ones",
    "Confronting mortality",
    "Discovering one's true self"
  ],
  physical_emotional_connection: {
    grief: "Chest hollow, as if someone had scooped out everything vital",
    joy: "Lightness spreading from her core, like champagne bubbles rising",
    fear: "Ice water in her veins, every muscle coiled to run",
    love: "Warmth blooming behind her ribs, dangerous and unstoppable"
  },
  earned_emotions: {
    technique: "Build through small moments before big revelation",
    example: "Show character's kindness in tiny ways throughout before their sacrifice"
  }
};

export const WORLD_BUILDING_EXCELLENCE = {
  iceberg_principle: {
    shown: "10% on the page",
    implied: "90% suggested through details",
    example: "Mention 'the Severing' casually, let readers infer the catastrophe"
  },
  sensory_world: {
    sight: "Not just what things look like, but how light behaves",
    sound: "Background noise that places us instantly",
    smell: "Most evocative sense for memory and place",
    touch: "Texture and temperature ground readers",
    taste: "Often overlooked but powerful for immersion"
  },
  cultural_details: {
    language: "Slang, idioms, and naming conventions",
    customs: "Small rituals that reveal worldview",
    values: "What's considered honorable or shameful",
    daily_life: "How people eat, work, and relate"
  }
};

export const PLOT_TWIST_PATTERNS = {
  unreliable_narrator: {
    setup: "Subtle inconsistencies in narration",
    revelation: "Narrator has been lying/delusional",
    examples: "Gone Girl, The Murder of Roger Ackroyd"
  },
  hidden_identity: {
    setup: "Character seems ordinary/helpless",
    revelation: "They're the mastermind/have hidden power",
    examples: "The Usual Suspects, Sixth Sense"
  },
  false_victory: {
    setup: "Heroes apparently win",
    revelation: "Victory was part of villain's plan",
    examples: "The Empire Strikes Back, Infinity War"
  },
  perspective_shift: {
    setup: "Story seems straightforward",
    revelation: "Everything looks different from another angle",
    examples: "Rashomon effect, Life of Pi"
  }
};

export const CHARACTER_MEMORABILITY = {
  contradiction_patterns: [
    "Killer with a code (Dexter)",
    "Genius who can't understand people (Sherlock)",
    "Strong person with specific vulnerability (Achilles' heel)",
    "Villain who's right about something important (Thanos)"
  ],
  signature_elements: [
    "Unique speech pattern or catchphrase",
    "Physical tell when lying/nervous/angry",
    "Unusual skill or interest",
    "Specific fear that humanizes them",
    "Ritual or habit that reveals character"
  ],
  arc_patterns: [
    "Coward becomes hero",
    "Idealist becomes realist without losing hope",
    "Loner learns to trust",
    "Privileged character loses everything and finds truth",
    "Ordinary person discovers extraordinary strength"
  ]
};

export const THEME_INTEGRATION = {
  subtle_reinforcement: {
    technique: "Echo theme through setting, weather, symbolism",
    example: "Theme of isolation: winter setting, island location, broken bridges"
  },
  character_embodiment: {
    technique: "Different characters represent different aspects of theme",
    example: "Justice theme: idealist lawyer, cynical cop, vigilante, corrupt judge"
  },
  plot_as_theme: {
    technique: "Story events explore theme from multiple angles",
    example: "Power corrupts: show via politics, relationships, magic system"
  }
};

export const BESTSELLER_WISDOM = {
  stephen_king: "Get the first draft done. It's a story, not a beauty contest.",
  neil_gaiman: "Remember: when people tell you something's wrong, they're usually right. When they tell you how to fix it, they're usually wrong.",
  margaret_atwood: "A word after a word after a word is power.",
  ray_bradbury: "Your intuition knows what to write, so get out of the way.",
  toni_morrison: "If there's a book that you want to read, but it hasn't been written yet, then you must write it."
};

// Export function to get relevant templates for a genre
export function getBestsellerTemplates(genre: string) {
  return {
    openingHooks: BESTSELLER_OPENING_HOOKS[genre as keyof typeof BESTSELLER_OPENING_HOOKS] || BESTSELLER_OPENING_HOOKS.thriller,
    chapterEndings: CHAPTER_ENDING_TECHNIQUES,
    showDontTell: SHOW_DONT_TELL_EXAMPLES,
    dialogue: DIALOGUE_EXCELLENCE_PATTERNS,
    pacing: PACING_PATTERNS,
    emotional: EMOTIONAL_RESONANCE_TECHNIQUES,
    worldBuilding: WORLD_BUILDING_EXCELLENCE,
    plotTwists: PLOT_TWIST_PATTERNS,
    characters: CHARACTER_MEMORABILITY,
    themes: THEME_INTEGRATION,
    wisdom: BESTSELLER_WISDOM
  };
}