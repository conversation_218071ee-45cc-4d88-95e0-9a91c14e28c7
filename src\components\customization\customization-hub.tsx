/**
 * Customization Hub Component
 * Comprehensive visual customization interface combining theme settings and showcase
 */

'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Palette,
  Sun,
  Moon,
  Monitor,
  Check,
  Sparkles,
  Settings,
  Type,
  Plus,
  Edit,
  Save,
  Trash2
} from 'lucide-react';

import { useTheme } from '@/hooks/use-theme';
import { useThemeSettings, useTypographySettings } from '@/lib/settings/settings-store';
import { textSizeMap, fontFamilyMap, type TypographySettings } from '@/lib/settings/settings-types';
import { useAuth } from '@/contexts/auth-context';
import { useCustomThemes, determineThemeMode, type CustomTheme } from '@/lib/themes/custom-themes-store';
import { CustomizationTest } from './customization-test';

interface ThemePreviewCardProps {
  themeId: string;
  themeName: string;
  themeDescription: string;
  themeMode: 'light' | 'dark';
  colors: {
    background: string;
    text: string;
    accent: string;
    card: string;
  };
  isActive: boolean;
  onSelect: () => void;
}

function ThemePreviewCard({ 
  themeId: _themeId, 
  themeName, 
  themeDescription, 
  themeMode, 
  colors, 
  isActive, 
  onSelect 
}: ThemePreviewCardProps) {
  return (
    <Card 
      className={`cursor-pointer hover:shadow-lg ${
        isActive ? 'ring-2 ring-primary shadow-lg' : 'hover:ring-1 hover:ring-border'
      }`}
      onClick={onSelect}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {themeMode === 'light' ? (
              <Sun className="h-4 w-4 text-amber-600" />
            ) : (
              <Moon className="h-4 w-4 text-blue-400" />
            )}
            <CardTitle className="text-sm font-medium">{themeName}</CardTitle>
          </div>
          {isActive && (
            <Check className="h-4 w-4 text-primary" />
          )}
        </div>
        <CardDescription className="text-xs">{themeDescription}</CardDescription>
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* Color Preview */}
        <div 
          className="rounded-lg p-4 mb-3 border"
          style={{ 
            backgroundColor: colors.background,
            borderColor: colors.accent + '40'
          }}
        >
          <div 
            className="text-xs font-medium mb-2"
            style={{ color: colors.text }}
          >
            Sample Text
          </div>
          <div className="flex gap-1">
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: colors.accent }}
            />
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: colors.card }}
            />
            <div 
              className="w-3 h-3 rounded-full opacity-60"
              style={{ backgroundColor: colors.text }}
            />
          </div>
        </div>
        
        <Badge variant={themeMode === 'light' ? 'default' : 'secondary'} className="text-xs">
          {themeMode}
        </Badge>
      </CardContent>
    </Card>
  );
}

export function CustomizationHub() {
  const { setTheme, isThemeActive, mounted, themes } = useTheme();
  const { theme: themeSettings, updateTheme } = useThemeSettings();
  const { typography, updateTypography } = useTypographySettings();
  // Use optional auth since this component can be used in marketing pages
  let user = null;
  try {
    const auth = useAuth();
    user = auth.user;
  } catch (_error) {
    // Auth context not available, which is fine for marketing pages
    user = null;
  }

  const { customThemes, addCustomTheme, removeCustomTheme } = useCustomThemes();
  const [customSize, setCustomSize] = useState(typography.customTextSize || 14);
  const [activeTab, setActiveTab] = useState('themes');
  const [customThemeName, setCustomThemeName] = useState('');

  // Check if user is a paying user (placeholder logic)
  const isPayingUser = false; // TODO: Implement proper subscription checking

  // Don't show loading skeleton to prevent flicker
  // The theme data is available immediately from the configs

  const lightThemes = themes.filter(t => t.mode === 'light');
  const darkThemes = themes.filter(t => t.mode === 'dark');

  // Get custom themes from the custom themes store (already destructured above)
  const customLightThemes = customThemes.filter(theme => theme.mode === 'light');
  const customDarkThemes = customThemes.filter(theme => theme.mode === 'dark');

  const handleThemeModeChange = (mode: 'light' | 'dark' | 'system') => {
    updateTheme({ themeMode: mode });
    
    if (mode === 'system') {
      // Use system preference
      const systemMode = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      const defaultTheme = systemMode === 'light' ? lightThemes[0] : darkThemes[0];
      if (defaultTheme) {
        setTheme(defaultTheme.id);
      }
    } else {
      // Switch to default theme for the selected mode
      const defaultTheme = mode === 'light' ? lightThemes[0] : darkThemes[0];
      if (defaultTheme) {
        setTheme(defaultTheme.id);
      }
    }
  };

  const textSizeOptions = [
    { value: 'small', label: 'Small', description: 'Compact text' },
    { value: 'medium', label: 'Medium', description: 'Standard size' },
    { value: 'large', label: 'Large', description: 'Larger text' },
    { value: 'extra-large', label: 'Extra Large', description: 'Maximum size' },
    { value: 'custom', label: 'Custom', description: 'Set your own' },
  ] as const;

  const handleCustomSizeChange = (value: number[]) => {
    const newSize = value[0];
    if (newSize !== undefined) {
      setCustomSize(newSize);
      updateTypography({
        textSize: 'custom',
        customTextSize: newSize
      });
    }
  };

  const applyCustomTheme = (customTheme: CustomTheme) => {
    // Apply the base theme first
    setTheme(customTheme.settings.baseTheme);
    updateTheme({
      currentTheme: customTheme.settings.baseTheme,
      themeMode: customTheme.settings.themeMode as 'light' | 'dark' | 'system'
    });

    // Apply typography settings
    updateTypography({
      textSize: customTheme.settings.textSize as TypographySettings['textSize'],
      customTextSize: customTheme.settings.customTextSize,
      editorFont: customTheme.settings.editorFont as TypographySettings['editorFont'],
      uiFont: customTheme.settings.uiFont as TypographySettings['uiFont'],
      readingFont: customTheme.settings.readingFont as TypographySettings['readingFont'],
    });
  };

  return (
    <div className="space-y-8 min-h-[600px]">
      {/* Customization Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="themes" className="flex items-center gap-2">
            <Palette className="w-4 h-4" />
            Themes
          </TabsTrigger>
          <TabsTrigger value="custom" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Custom
          </TabsTrigger>
        </TabsList>

        <TabsContent value="themes" className="space-y-8 mt-6">
          {/* Theme Mode Selection */}
          <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Theme Mode Preference
          </CardTitle>
          <CardDescription>
            Choose how BookScribe AI should determine which theme to use
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={themeSettings.themeMode}
            onValueChange={handleThemeModeChange}
            className="grid grid-cols-1 md:grid-cols-3 gap-4"
          >
            <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-accent/50 transition-colors">
              <RadioGroupItem value="light" id="light" />
              <Label htmlFor="light" className="flex items-center gap-2 cursor-pointer flex-1">
                <Sun className="w-4 h-4 text-amber-600" />
                <div>
                  <div className="font-medium">Light Mode</div>
                  <div className="text-xs text-muted-foreground">Always use light themes</div>
                </div>
              </Label>
            </div>
            <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-accent/50 transition-colors">
              <RadioGroupItem value="dark" id="dark" />
              <Label htmlFor="dark" className="flex items-center gap-2 cursor-pointer flex-1">
                <Moon className="w-4 h-4 text-blue-400" />
                <div>
                  <div className="font-medium">Dark Mode</div>
                  <div className="text-xs text-muted-foreground">Always use dark themes</div>
                </div>
              </Label>
            </div>
            <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-accent/50 transition-colors">
              <RadioGroupItem value="system" id="system" />
              <Label htmlFor="system" className="flex items-center gap-2 cursor-pointer flex-1">
                <Monitor className="w-4 h-4" />
                <div>
                  <div className="font-medium">System</div>
                  <div className="text-xs text-muted-foreground">Follow system preference</div>
                </div>
              </Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      <Separator />

      {/* Theme Gallery Header */}
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Palette className="h-5 w-5 text-primary" />
          <h2 className="text-2xl font-bold">Theme Gallery</h2>
        </div>
        <p className="text-muted-foreground">
          Choose from our collection of carefully crafted writing themes
        </p>
      </div>

      {/* Light Themes */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Sun className="h-4 w-4 text-amber-600" />
          <h3 className="text-lg font-semibold">Light Themes</h3>
          <Badge variant="outline">{lightThemes.length + customLightThemes.length}</Badge>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {lightThemes.map((themeData) => (
            <ThemePreviewCard
              key={themeData.id}
              themeId={themeData.id}
              themeName={themeData.name}
              themeDescription={themeData.description}
              themeMode={themeData.mode}
              colors={{
                background: themeData.id === 'writers-sanctuary-light' ? '#fcfcfc' : '#f8f5f0',
                text: themeData.id === 'writers-sanctuary-light' ? '#2d2520' : '#3d3028',
                accent: themeData.id === 'writers-sanctuary-light' ? '#d4a574' : '#7a9471',
                card: themeData.id === 'writers-sanctuary-light' ? '#f9f7f4' : '#f0ede8'
              }}
              isActive={mounted && isThemeActive(themeData.id)}
              onSelect={() => {
                setTheme(themeData.id);
                updateTheme({ currentTheme: themeData.id, themeMode: 'light' });
              }}
            />
          ))}

          {/* Custom Light Themes */}
          {customLightThemes.map((customTheme) => (
            <Card
              key={customTheme.id}
              className="cursor-pointer transition-all hover:shadow-md relative group"
              onClick={() => applyCustomTheme(customTheme)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{customTheme.name}</h4>
                    <Badge variant="secondary" className="text-xs">
                      Custom
                    </Badge>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="opacity-0 group-hover:opacity-100 transition-opacity p-1 h-auto"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeCustomTheme(customTheme.id);
                      }}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  Created {new Date(customTheme.createdAt).toLocaleDateString()}
                </p>
                <div className="flex gap-2">
                  <div
                    className="w-6 h-6 rounded-full border border-border"
                    style={{ backgroundColor: '#8B4513' }}
                    title="primary"
                  />
                  <div
                    className="w-6 h-6 rounded-full border border-border"
                    style={{ backgroundColor: '#D2B48C' }}
                    title="secondary"
                  />
                  <div
                    className="w-6 h-6 rounded-full border border-border"
                    style={{ backgroundColor: '#FEFEFE' }}
                    title="background"
                  />
                  <div
                    className="w-6 h-6 rounded-full border border-border"
                    style={{ backgroundColor: '#2C2C2C' }}
                    title="foreground"
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <Separator />

      {/* Dark Themes */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Moon className="h-4 w-4 text-blue-400" />
          <h3 className="text-lg font-semibold">Dark Themes</h3>
          <Badge variant="outline">{darkThemes.length + customDarkThemes.length}</Badge>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {darkThemes.map((themeData) => (
            <ThemePreviewCard
              key={themeData.id}
              themeId={themeData.id}
              themeName={themeData.name}
              themeDescription={themeData.description}
              themeMode={themeData.mode}
              colors={{
                background: themeData.id === 'evening-study-dark' ? '#1a1a1a' : '#0f1419',
                text: themeData.id === 'evening-study-dark' ? '#e0e0e0' : '#c9d1d9',
                accent: themeData.id === 'evening-study-dark' ? '#d4a574' : '#6699ff',
                card: themeData.id === 'evening-study-dark' ? '#242424' : '#161b22'
              }}
              isActive={mounted && isThemeActive(themeData.id)}
              onSelect={() => {
                setTheme(themeData.id);
                updateTheme({ currentTheme: themeData.id, themeMode: 'dark' });
              }}
            />
          ))}

          {/* Custom Dark Themes */}
          {customDarkThemes.map((customTheme) => (
            <Card
              key={customTheme.id}
              className="cursor-pointer transition-all hover:shadow-md relative group"
              onClick={() => applyCustomTheme(customTheme)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{customTheme.name}</h4>
                    <Badge variant="secondary" className="text-xs">
                      Custom
                    </Badge>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="opacity-0 group-hover:opacity-100 transition-opacity p-1 h-auto"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeCustomTheme(customTheme.id);
                      }}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  Created {new Date(customTheme.createdAt).toLocaleDateString()}
                </p>
                <div className="flex gap-2">
                  <div
                    className="w-6 h-6 rounded-full border border-border"
                    style={{ backgroundColor: '#8B4513' }}
                    title="primary"
                  />
                  <div
                    className="w-6 h-6 rounded-full border border-border"
                    style={{ backgroundColor: '#D2B48C' }}
                    title="secondary"
                  />
                  <div
                    className="w-6 h-6 rounded-full border border-border"
                    style={{ backgroundColor: '#1A1A1A' }}
                    title="background"
                  />
                  <div
                    className="w-6 h-6 rounded-full border border-border"
                    style={{ backgroundColor: '#E5E5E5' }}
                    title="foreground"
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Sparkles className="w-4 h-4" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Quickly switch between popular theme combinations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setTheme('writers-sanctuary-light');
                updateTheme({ currentTheme: 'writers-sanctuary-light', themeMode: 'light' });
              }}
            >
              <Sun className="w-3 h-3 mr-1" />
              Default Light
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setTheme('evening-study-dark');
                updateTheme({ currentTheme: 'evening-study-dark', themeMode: 'dark' });
              }}
            >
              <Moon className="w-3 h-3 mr-1" />
              Default Dark
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                handleThemeModeChange('system');
              }}
            >
              <Monitor className="w-3 h-3 mr-1" />
              Auto
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* User's Custom Themes */}
      {user && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Edit className="w-4 h-4" />
              My Custom Themes
            </CardTitle>
            <CardDescription>
              Your saved custom themes
            </CardDescription>
          </CardHeader>
          <CardContent>
            {!isPayingUser ? (
              <div className="text-center py-6">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-3">
                  <Sparkles className="w-6 h-6 text-primary" />
                </div>
                <h4 className="font-medium mb-2">Premium Feature</h4>
                <p className="text-sm text-muted-foreground mb-3">
                  Upgrade to create and save unlimited custom themes.
                </p>
                <Button size="sm" variant="outline">
                  Upgrade to Premium
                </Button>
              </div>
            ) : customThemes.length === 0 ? (
              <div className="text-center py-6">
                <div className="w-12 h-12 rounded-full bg-muted flex items-center justify-center mx-auto mb-3">
                  <Plus className="w-6 h-6 text-muted-foreground" />
                </div>
                <h4 className="font-medium mb-2">No Custom Themes Yet</h4>
                <p className="text-sm text-muted-foreground mb-3">
                  Create your first custom theme in the Custom tab.
                </p>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setActiveTab('custom')}
                >
                  Create Theme
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {customThemes.length} custom theme{customThemes.length !== 1 ? 's' : ''}
                  </span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setActiveTab('custom')}
                  >
                    <Plus className="w-3 h-3 mr-1" />
                    Create New
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {customThemes.slice(0, 4).map((customTheme) => (
                    <Card
                      key={customTheme.id}
                      className="cursor-pointer transition-all hover:shadow-sm"
                      onClick={() => applyCustomTheme(customTheme)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="font-medium text-sm">{customTheme.name}</h5>
                          <Badge variant="outline" className="text-xs">
                            {customTheme.mode}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Created {new Date(customTheme.createdAt).toLocaleDateString()}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                {customThemes.length > 4 && (
                  <p className="text-xs text-muted-foreground text-center">
                    And {customThemes.length - 4} more in the gallery above
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
        </TabsContent>

        <TabsContent value="custom" className="space-y-6 mt-6">
          {/* Text Size */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Type className="w-5 h-5" />
                Text Size
              </CardTitle>
              <CardDescription>
                Adjust the size of text throughout the application
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <RadioGroup
                value={typography.textSize}
                onValueChange={(value) => updateTypography({ textSize: value as keyof typeof textSizeMap })}
                className="grid grid-cols-2 md:grid-cols-3 gap-4"
              >
                {textSizeOptions.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <RadioGroupItem value={option.value} id={option.value} />
                    <Label htmlFor={option.value} className="cursor-pointer flex-1">
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-muted-foreground">
                        {option.description}
                      </div>
                    </Label>
                  </div>
                ))}
              </RadioGroup>

              {typography.textSize === 'custom' && (
                <div className="space-y-3 p-4 border rounded-lg bg-muted/30">
                  <Label className="text-sm font-medium">Custom Size: {customSize}px</Label>
                  <Slider
                    value={[customSize]}
                    onValueChange={handleCustomSizeChange}
                    min={10}
                    max={24}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>10px</span>
                    <span>24px</span>
                  </div>
                </div>
              )}

              {/* Preview */}
              <div className="p-4 border rounded-lg bg-card">
                <div className="text-xs text-muted-foreground mb-2">Preview:</div>
                <div
                  className="space-y-2"
                  style={{
                    fontSize: typography.textSize === 'custom'
                      ? `${customSize}px`
                      : textSizeMap[typography.textSize]?.ui || '14px'
                  }}
                >
                  <div>This is how your UI text will appear.</div>
                  <div className="text-muted-foreground">
                    Secondary text and descriptions will look like this.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Font Families */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Font Families</CardTitle>
              <CardDescription>
                Choose fonts for different contexts in your writing environment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Editor Font */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Editor Font</Label>
                  <Select
                    value={typography.editorFont}
                    onValueChange={(value) => updateTypography({ editorFont: value as TypographySettings['editorFont'] })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="jetbrains-mono">JetBrains Mono</SelectItem>
                      <SelectItem value="fira-code">Fira Code</SelectItem>
                      <SelectItem value="source-code-pro">Source Code Pro</SelectItem>
                      <SelectItem value="consolas">Consolas</SelectItem>
                      <SelectItem value="monaco">Monaco</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* UI Font */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Interface Font</Label>
                  <Select
                    value={typography.uiFont}
                    onValueChange={(value) => updateTypography({ uiFont: value as TypographySettings['uiFont'] })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="inter">Inter</SelectItem>
                      <SelectItem value="roboto">Roboto</SelectItem>
                      <SelectItem value="open-sans">Open Sans</SelectItem>
                      <SelectItem value="system-ui">System UI</SelectItem>
                      <SelectItem value="segoe-ui">Segoe UI</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Reading Font */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Reading Font</Label>
                  <Select
                    value={typography.readingFont}
                    onValueChange={(value) => updateTypography({ readingFont: value as TypographySettings['readingFont'] })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="crimson-text">Crimson Text</SelectItem>
                      <SelectItem value="georgia">Georgia</SelectItem>
                      <SelectItem value="times-new-roman">Times New Roman</SelectItem>
                      <SelectItem value="libre-baskerville">Libre Baskerville</SelectItem>
                      <SelectItem value="merriweather">Merriweather</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Font Preview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="p-3 border rounded bg-card">
                  <div className="text-xs text-muted-foreground mb-1">Editor:</div>
                  <div
                    className="font-mono text-sm"
                    style={{ fontFamily: fontFamilyMap[typography.editorFont] }}
                  >
                    function write() {'{'}
                    <br />
                    &nbsp;&nbsp;return &ldquo;Hello&rdquo;;
                    <br />
                    {'}'}
                  </div>
                </div>

                <div className="p-3 border rounded bg-card">
                  <div className="text-xs text-muted-foreground mb-1">Interface:</div>
                  <div
                    className="text-sm"
                    style={{ fontFamily: fontFamilyMap[typography.uiFont] }}
                  >
                    <div className="font-semibold">Menu Item</div>
                    <div>Settings and options</div>
                  </div>
                </div>

                <div className="p-3 border rounded bg-card">
                  <div className="text-xs text-muted-foreground mb-1">Reading:</div>
                  <div
                    className="text-sm"
                    style={{ fontFamily: fontFamilyMap[typography.readingFont] }}
                  >
                    <div className="font-semibold">Chapter One</div>
                    <div>Once upon a time...</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Save Custom Theme */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Save className="w-5 h-5" />
                Save Custom Theme
              </CardTitle>
              <CardDescription>
                Save your current settings as a custom theme
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {!user ? (
                <div className="text-center py-6">
                  <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
                    <Save className="w-8 h-8 text-muted-foreground" />
                  </div>
                  <h4 className="font-semibold mb-2">Sign In Required</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Sign in to save your custom themes and access them across devices.
                  </p>
                  <Button variant="outline" size="sm">
                    Sign In
                  </Button>
                </div>
              ) : !isPayingUser ? (
                <div className="text-center py-6">
                  <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                    <Sparkles className="w-8 h-8 text-primary" />
                  </div>
                  <h4 className="font-semibold mb-2">Premium Feature</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Upgrade to save unlimited custom themes and access advanced customization features.
                  </p>
                  <Button size="sm">
                    Upgrade to Premium
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="theme-name">Theme Name</Label>
                    <Input
                      id="theme-name"
                      placeholder="My Custom Theme"
                      value={customThemeName}
                      onChange={(e) => setCustomThemeName(e.target.value)}
                    />
                  </div>

                  <div className="p-4 border rounded-lg bg-muted/30">
                    <div className="text-sm font-medium mb-2">Current Settings:</div>
                    <div className="text-xs text-muted-foreground space-y-1">
                      <div>• Text Size: {typography.textSize === 'custom' ? `${customSize}px` : typography.textSize}</div>
                      <div>• Editor Font: {typography.editorFont}</div>
                      <div>• UI Font: {typography.uiFont}</div>
                      <div>• Reading Font: {typography.readingFont}</div>
                      <div>• Theme Mode: {themeSettings.themeMode}</div>
                      <div>• Current Theme: {themeSettings.currentTheme}</div>
                    </div>
                  </div>

                  <Button
                    className="w-full"
                    disabled={!customThemeName.trim()}
                    onClick={() => {
                      const themeMode = determineThemeMode(themeSettings.currentTheme, themeSettings.themeMode);

                      addCustomTheme({
                        name: customThemeName,
                        mode: themeMode,
                        settings: {
                          textSize: typography.textSize,
                          customTextSize: typography.customTextSize,
                          editorFont: typography.editorFont,
                          uiFont: typography.uiFont,
                          readingFont: typography.readingFont,
                          baseTheme: themeSettings.currentTheme,
                          themeMode: themeSettings.themeMode,
                        },
                      });

                      // Clear the input and show success
                      setCustomThemeName('');

                      // Switch to themes tab to show the new theme
                      setActiveTab('themes');
                    }}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Save Theme
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Live Preview */}
      <CustomizationTest />
    </div>
  );
}
