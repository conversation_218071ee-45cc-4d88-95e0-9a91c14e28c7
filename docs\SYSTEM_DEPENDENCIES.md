# System Dependencies for BookScribe AI

This document outlines the system dependencies required for BookScribe AI, particularly for PDF generation, headless browser operations, and export functionality.

## Overview

BookScribe AI uses several technologies that require system-level dependencies:

- **Puppeteer**: For PDF generation and headless browser operations
- **PDF Export**: For generating high-quality PDF documents
- **Document Processing**: For importing and processing various document formats
- **Image Processing**: For handling cover images and graphics

## Required System Dependencies

### Linux (Ubuntu/Debian)

```bash
sudo apt-get update && sudo apt-get install -y \
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libgtk-3-0 \
    libgbm1 \
    libasound2 \
    libxss1 \
    libgconf-2-4 \
    libxrandr2 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxtst6 \
    libatspi2.0-0 \
    libx11-xcb1 \
    libxcb-dri3-0
```

### Linux (RHEL/CentOS/Fedora)

```bash
# For yum-based systems
sudo yum install -y \
    nss \
    atk \
    at-spi2-atk \
    gtk3 \
    libdrm \
    libXScrnSaver \
    libXrandr \
    GConf2 \
    alsa-lib

# For dnf-based systems (newer Fedora)
sudo dnf install -y \
    nss \
    atk \
    at-spi2-atk \
    gtk3 \
    libdrm \
    libXScrnSaver \
    libXrandr \
    GConf2 \
    alsa-lib
```

### macOS

```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Chrome (recommended for Puppeteer)
brew install --cask google-chrome
```

### Windows

**✅ No additional dependencies required!**

On Windows, Puppeteer works out of the box with its bundled Chromium browser. The system dependencies listed above are only needed for Linux environments.

```powershell
# Windows users: Nothing to install!
# Puppeteer includes everything needed for PDF generation

# Optional: Only if you specifically want to use Chrome instead of Chromium
# Set environment variable: INSTALL_CHROME=true
# Then run: npm run install:system-deps
```

## Automated Installation

### Using npm script

```bash
npm run install:system-deps
```

This script will automatically detect your platform and install the appropriate dependencies.

### Using Docker

For production deployments or if you prefer containerized environments:

```bash
# Build with system dependencies
npm run docker:build:deps

# Run with system dependencies
npm run docker:run:deps
```

## Manual Installation

If the automated installation fails, you can install dependencies manually:

### 1. Install Node.js Dependencies

```bash
npm install puppeteer
```

### 2. Install System Dependencies

Follow the platform-specific instructions above.

### 3. Verify Installation

```bash
# Test Puppeteer installation
node -e "const puppeteer = require('puppeteer'); console.log('Puppeteer installed successfully');"
```

## Troubleshooting

### Common Issues

#### 1. "Chrome not found" error

**Solution**: Install Google Chrome or ensure Chromium is available:

```bash
# Linux
sudo apt-get install chromium-browser

# macOS
brew install --cask google-chrome

# Windows
# Download and install from https://www.google.com/chrome/
```

#### 2. Permission errors on Linux

**Solution**: Ensure proper permissions and run with sudo if necessary:

```bash
sudo npm run install:system-deps
```

#### 3. Missing shared libraries

**Solution**: Install additional development packages:

```bash
# Ubuntu/Debian
sudo apt-get install build-essential

# RHEL/CentOS/Fedora
sudo yum groupinstall "Development Tools"
```

### Environment Variables

You can configure Puppeteer behavior with environment variables:

```bash
# Skip Chromium download (if you have Chrome installed)
export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

# Use custom Chrome executable
export PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome

# Set custom cache directory
export PUPPETEER_CACHE_DIR=/path/to/cache
```

## Docker Support

### Development with Dependencies

```bash
# Build development image with system dependencies
docker build -f Dockerfile.deps -t bookscribe-ai:deps .

# Run with dependencies
docker-compose -f docker-compose.deps.yml up
```

### Production Deployment

The `Dockerfile.deps` includes all necessary system dependencies for production deployment:

```dockerfile
# Install system dependencies
RUN apt-get update && apt-get install -y \
    libnss3 libatk-bridge2.0-0 libdrm2 libgtk-3-0 libgbm1 \
    # ... other dependencies
```

## CI/CD Integration

For continuous integration environments:

```yaml
# GitHub Actions example
- name: Install system dependencies
  run: |
    sudo apt-get update
    sudo apt-get install -y libnss3 libatk-bridge2.0-0 libdrm2 libgtk-3-0 libgbm1
    npm run install:system-deps
```

## Performance Considerations

- **Memory Usage**: Headless browsers can be memory-intensive
- **CPU Usage**: PDF generation requires significant CPU resources
- **Disk Space**: Chrome/Chromium installation requires ~200MB

## Security Notes

- System dependencies run with elevated privileges
- Ensure your system is updated before installation
- Use containerized deployments for better security isolation

## Support

If you encounter issues with system dependencies:

1. Check the [troubleshooting section](#troubleshooting)
2. Review the logs: `npm run docker:logs:deps`
3. Create an issue with your platform details and error messages
