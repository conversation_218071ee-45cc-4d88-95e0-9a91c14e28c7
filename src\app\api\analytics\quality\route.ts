import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { authenticateUser, handleRouteError } from '@/lib/auth'
import { vercelAIClient } from '@/lib/ai/vercel-ai-client'
import { AI_MODELS } from '@/lib/config/ai-settings'
import { z } from 'zod'
import { ConsistencyValidator } from '@/lib/services/consistency-validator'

const QualityRequestSchema = z.object({
  projectId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  content: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    const supabase = await createClient()

    // Get quality metrics from database
    let query = supabase
      .from('quality_metrics')
      .select('*')
      .eq('user_id', authResult.user.id)

    if (projectId) {
      query = query.eq('project_id', projectId)
    }

    if (startDate) {
      query = query.gte('created_at', startDate)
    }

    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    const { data: qualityMetrics, error } = await query.order('created_at', { ascending: false })

    if (error) {
      throw error
    }

    // Calculate aggregate quality scores
    const aggregateQuality = qualityMetrics.length > 0 ? {
      overallScore: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.overall_score || 0), 0) / qualityMetrics.length),
      readability: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_readability || 0), 0) / qualityMetrics.length),
      consistency: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_consistency || 0), 0) / qualityMetrics.length),
      pacing: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_pacing || 0), 0) / qualityMetrics.length),
      engagement: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_engagement || 0), 0) / qualityMetrics.length),
      dialogue: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_dialogue_authenticity || 0), 0) / qualityMetrics.length),
      description: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_creativity || 0), 0) / qualityMetrics.length),
    } : {
      overallScore: 0,
      readability: 0,
      consistency: 0,
      pacing: 0,
      engagement: 0,
      dialogue: 0,
      description: 0,
    }

    return NextResponse.json({
      metrics: qualityMetrics,
      aggregate: aggregateQuality,
      count: qualityMetrics.length
    })

  } catch (error) {
    return handleRouteError(error, 'Quality Analytics')
  }
}

export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const body = await request.json()
    const { projectId, content } = QualityRequestSchema.parse(body)

    if (!content) {
      return NextResponse.json({ error: 'Content is required for quality analysis' }, { status: 400 })
    }

    const supabase = await createClient()

    // Generate quality analysis using AI
    const qualityPrompt = `Analyze the following writing sample and provide quality metrics on a scale of 0-100:

Content:
${content}

Please evaluate:
1. Readability - How clear and easy to read is the text?
2. Consistency - How consistent are character voices, tone, and style?
3. Pacing - How well does the text maintain reader interest and flow?
4. Engagement - How compelling and engaging is the content?
5. Dialogue - How natural and authentic is the dialogue (if present)?
6. Description - How vivid and effective are the descriptions?

Provide an overall quality score and specific scores for each dimension.`

    const qualityAnalysis = await vercelAIClient.generateObjectWithFallback(
      qualityPrompt,
      z.object({
        overall_score: z.number().min(0).max(100),
        readability: z.number().min(0).max(100),
        consistency: z.number().min(0).max(100),
        pacing: z.number().min(0).max(100),
        engagement: z.number().min(0).max(100),
        dialogue: z.number().min(0).max(100),
        description: z.number().min(0).max(100),
        feedback: z.string(),
        improvement_suggestions: z.array(z.string()),
      }),
      {
        model: AI_MODELS.FAST,
        temperature: 0.3,
        systemPrompt: 'You are an expert writing quality analyst. Provide detailed, constructive feedback on writing quality.'
      }
    )

    // Save quality metrics to database
    const { data: qualityRecord, error: insertError } = await supabase
      .from('quality_metrics')
      .insert({
        user_id: authResult.user.id,
        project_id: projectId,
        overall_score: qualityAnalysis.overall_score,
        avg_readability: qualityAnalysis.readability,
        avg_consistency: qualityAnalysis.consistency,
        avg_pacing: qualityAnalysis.pacing,
        avg_engagement: qualityAnalysis.engagement,
        avg_dialogue_authenticity: qualityAnalysis.dialogue,
        avg_creativity: qualityAnalysis.description,
        feedback: qualityAnalysis.feedback,
        improvement_suggestions: qualityAnalysis.improvement_suggestions,
      })
      .select()
      .single()

    if (insertError) {
      throw insertError
    }

    return NextResponse.json({
      qualityMetrics: qualityRecord,
      analysis: qualityAnalysis
    })

  } catch (error) {
    return handleRouteError(error, 'Quality Analysis')
  }
}