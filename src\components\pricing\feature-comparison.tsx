'use client'

import { Check, X, Minus } from 'lucide-react'
import { SUBSCRIPTION_TIERS } from '@/lib/subscription'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

type FeatureCategory = {
  name: string
  features: {
    name: string
    description?: string
    tiers: {
      [key: string]: boolean | string | number
    }
  }[]
}

const featureCategories: FeatureCategory[] = [
  {
    name: 'Writing Tools',
    features: [
      {
        name: 'Active Projects',
        tiers: {
          starter: '1',
          writer: '3',
          author: '10',
          professional: 'Unlimited',
          studio: 'Unlimited'
        }
      },
      {
        name: 'Writing Editor',
        tiers: {
          starter: true,
          writer: true,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Story Bible',
        description: 'Characters, Locations, Timeline, Plot Threads',
        tiers: {
          starter: false,
          writer: true,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Series Management',
        description: 'Link books, track continuity',
        tiers: {
          starter: false,
          writer: '1 series',
          author: '3 series',
          professional: 'Unlimited',
          studio: 'Unlimited'
        }
      },
      {
        name: 'Character Sharing',
        description: 'Use characters across books/series',
        tiers: {
          starter: false,
          writer: false,
          author: false,
          professional: true,
          studio: true
        }
      }
    ]
  },
  {
    name: 'AI Capabilities',
    features: [
      {
        name: 'AI Words/month',
        tiers: {
          starter: '10,000',
          writer: '50,000',
          author: '150,000',
          professional: '300,000',
          studio: '600,000'
        }
      },
      {
        name: 'Story Architect',
        description: 'Creates comprehensive plot structures and story arcs',
        tiers: {
          starter: true,
          writer: true,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Writing Agent',
        description: 'Generates engaging chapter content with your style',
        tiers: {
          starter: true,
          writer: true,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Character Developer',
        description: 'Builds deep character profiles and relationships',
        tiers: {
          starter: false,
          writer: true,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Chapter Planner',
        description: 'Organizes scenes and maintains pacing',
        tiers: {
          starter: false,
          writer: true,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Adaptive Planning',
        description: 'Adjusts story elements based on changes',
        tiers: {
          starter: false,
          writer: true,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Editor Agent',
        description: 'Reviews for quality and consistency',
        tiers: {
          starter: false,
          writer: false,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'AI Intelligence Level',
        description: 'Adaptive AI that scales with your story complexity',
        tiers: {
          starter: 'Essential AI for drafts & basic writing',
          writer: 'Enhanced AI for story structure & characters',
          author: 'Premium AI for complex narratives',
          professional: 'Advanced AI for all writing tasks',
          studio: 'Ultimate AI with priority intelligence'
        }
      },
      {
        name: 'AI Response Quality',
        description: 'Quality and sophistication of AI-generated content',
        tiers: {
          starter: 'Good - Clear and functional',
          writer: 'Better - Nuanced and contextual',
          author: 'Best - Creative and insightful',
          professional: 'Premium - Exceptional depth',
          studio: 'Ultimate - Unmatched sophistication'
        }
      },
      {
        name: 'AI Context Memory',
        description: 'How much of your story the AI remembers and understands',
        tiers: {
          starter: 'Current chapter',
          writer: 'Whole book',
          author: 'Whole series',
          professional: 'Whole series',
          studio: 'Whole universe'
        }
      },
      {
        name: 'Voice Consistency',
        description: 'AI analyzes and maintains your writing voice',
        tiers: {
          starter: false,
          writer: 'Basic',
          author: 'Standard',
          professional: 'Advanced',
          studio: 'Advanced'
        }
      },
    ]
  },
  {
    name: 'Analytics & Insights',
    features: [
      {
        name: 'Writing Statistics',
        description: 'Word count, progress tracking',
        tiers: {
          starter: 'Basic',
          writer: 'Standard',
          author: 'Detailed',
          professional: 'Advanced',
          studio: 'Advanced'
        }
      },
      {
        name: 'Character Analytics',
        description: 'Appearance frequency, arc tracking',
        tiers: {
          starter: false,
          writer: false,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Pacing Analysis',
        description: 'Chapter pacing, scene flow',
        tiers: {
          starter: false,
          writer: false,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Continuity Reports',
        description: 'Plot threads, timeline consistency',
        tiers: {
          starter: false,
          writer: false,
          author: true,
          professional: true,
          studio: true
        }
      }
    ]
  },
  {
    name: 'Export & Sharing',
    features: [
      {
        name: 'Export Formats',
        tiers: {
          starter: 'PDF with watermark',
          writer: 'TXT, Markdown, DOCX, PDF',
          author: 'TXT, Markdown, DOCX, PDF',
          professional: 'All formats + EPUB',
          studio: 'All formats + EPUB'
        }
      },
      {
        name: 'Batch Export',
        description: 'Export multiple chapters/books at once',
        tiers: {
          starter: false,
          writer: false,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Export Templates',
        description: 'Custom formatting for exports',
        tiers: {
          starter: false,
          writer: 'Basic',
          author: 'Standard',
          professional: 'Advanced',
          studio: 'Custom'
        }
      }
    ]
  },
  {
    name: 'Collaboration & Team Features',
    features: [
      {
        name: 'Team Members',
        description: 'Invite collaborators to your projects',
        tiers: {
          starter: '0',
          writer: '0',
          author: '0',
          professional: '2',
          studio: '5'
        }
      },
      {
        name: 'Real-time Collaboration',
        description: 'Edit documents simultaneously with team members',
        tiers: {
          starter: false,
          writer: false,
          author: false,
          professional: false,
          studio: true
        }
      },
      {
        name: 'Shared Universe',
        description: 'Multiple series in one universe',
        tiers: {
          starter: false,
          writer: false,
          author: false,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Cross-Series Characters',
        description: 'Characters appear in multiple series',
        tiers: {
          starter: false,
          writer: false,
          author: false,
          professional: false,
          studio: true
        }
      },
      {
        name: 'Version Control',
        description: 'Track changes, restore versions',
        tiers: {
          starter: false,
          writer: 'Basic',
          author: 'Standard',
          professional: 'Advanced',
          studio: 'Advanced'
        }
      }
    ]
  }
]

function renderFeatureValue(value: boolean | string | number) {
  if (typeof value === 'boolean') {
    return value ? (
      <Check className="h-5 w-5 text-primary mx-auto" />
    ) : (
      <X className="h-5 w-5 text-muted-foreground/50 mx-auto" />
    )
  }
  
  if (typeof value === 'string' && (value === 'Read-only' || value.includes('+'))) {
    return <span className="text-xs font-mono text-primary">{value}</span>
  }
  
  return <span className="text-sm font-mono">{value}</span>
}

export function FeatureComparison() {
  return (
    <div className="w-full overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[250px] sticky left-0 bg-background z-10">Feature</TableHead>
            {SUBSCRIPTION_TIERS.map((tier) => (
              <TableHead 
                key={tier.id} 
                className="text-center min-w-[120px]"
              >
                <div>
                  <div className="font-semibold">{tier.name}</div>
                  <div className="text-xs text-muted-foreground font-mono">
                    ${tier.price}/mo
                  </div>
                </div>
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {featureCategories.map((category) => (
            <>
              <TableRow key={category.name} className="bg-muted/50">
                <TableCell 
                  colSpan={SUBSCRIPTION_TIERS.length + 1} 
                  className="font-semibold text-sm"
                >
                  {category.name}
                </TableCell>
              </TableRow>
              {category.features.map((feature) => (
                <TableRow key={feature.name}>
                  <TableCell className="sticky left-0 bg-background z-10">
                    <div>
                      <div className="font-medium">{feature.name}</div>
                      {feature.description && (
                        <div className="text-xs text-muted-foreground">
                          {feature.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  {SUBSCRIPTION_TIERS.map((tier) => (
                    <TableCell key={tier.id} className="text-center">
                      {renderFeatureValue(feature.tiers[tier.id])}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}