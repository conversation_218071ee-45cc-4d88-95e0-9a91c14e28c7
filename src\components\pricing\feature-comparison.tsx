'use client'

import { Check, X, Minus } from 'lucide-react'
import { SUBSCRIPTION_TIERS } from '@/lib/subscription'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

type FeatureCategory = {
  name: string
  features: {
    name: string
    description?: string
    tiers: {
      [key: string]: boolean | string | number
    }
  }[]
}

const featureCategories: FeatureCategory[] = [
  {
    name: 'Writing Tools',
    features: [
      {
        name: 'Active Projects',
        tiers: {
          starter: '1',
          writer: '3',
          author: '10',
          professional: 'Unlimited',
          studio: 'Unlimited'
        }
      },
      {
        name: 'Writing Editor',
        tiers: {
          starter: true,
          writer: true,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Story Bible',
        description: 'Characters, Locations, Timeline, Plot Threads',
        tiers: {
          starter: false,
          writer: true,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Series Management',
        description: 'Link books, track continuity',
        tiers: {
          starter: false,
          writer: '1 series',
          author: '3 series',
          professional: 'Unlimited',
          studio: 'Unlimited'
        }
      },
      {
        name: 'Character Sharing',
        description: 'Use characters across books/series',
        tiers: {
          starter: false,
          writer: false,
          author: false,
          professional: true,
          studio: true
        }
      }
    ]
  },
  {
    name: 'AI Capabilities',
    features: [
      {
        name: 'AI Generations/month',
        tiers: {
          starter: '20',
          writer: '100',
          author: '300',
          professional: '600',
          studio: '1,200'
        }
      },
      {
        name: 'AI Agents',
        description: 'Story Architect, Character Developer, Writing, Editor, Chapter Planner, Adaptive Planning',
        tiers: {
          starter: '2 agents',
          writer: '5 agents',
          author: 'All 6 agents',
          professional: 'All 6 agents',
          studio: 'All 6 agents'
        }
      },
      {
        name: 'AI Model Quality',
        description: 'GPT-4o mini vs GPT-4.1 usage ratio',
        tiers: {
          starter: 'GPT-4o mini only',
          writer: '80% mini, 20% GPT-4.1',
          author: '50% mini, 50% GPT-4.1',
          professional: '30% mini, 70% GPT-4.1',
          studio: '20% mini, 80% GPT-4.1'
        }
      },
      {
        name: 'AI Context Memory',
        description: 'Maintains consistency across 100k+ words',
        tiers: {
          starter: 'Basic',
          writer: 'Standard',
          author: 'Advanced',
          professional: 'Advanced',
          studio: 'Priority'
        }
      },
      {
        name: 'Voice Consistency',
        description: 'AI analyzes and maintains your writing voice',
        tiers: {
          starter: false,
          writer: 'Basic',
          author: 'Standard',
          professional: 'Advanced',
          studio: 'Advanced'
        }
      },
      {
        name: 'AI Agent Priority',
        description: 'Faster processing during peak times',
        tiers: {
          starter: false,
          writer: false,
          author: false,
          professional: false,
          studio: true
        }
      }
    ]
  },
  {
    name: 'Analytics & Insights',
    features: [
      {
        name: 'Writing Statistics',
        description: 'Word count, progress tracking',
        tiers: {
          starter: 'Basic',
          writer: 'Standard',
          author: 'Detailed',
          professional: 'Advanced',
          studio: 'Advanced'
        }
      },
      {
        name: 'Character Analytics',
        description: 'Appearance frequency, arc tracking',
        tiers: {
          starter: false,
          writer: false,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Pacing Analysis',
        description: 'Chapter pacing, scene flow',
        tiers: {
          starter: false,
          writer: false,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Continuity Reports',
        description: 'Plot threads, timeline consistency',
        tiers: {
          starter: false,
          writer: false,
          author: true,
          professional: true,
          studio: true
        }
      }
    ]
  },
  {
    name: 'Export & Sharing',
    features: [
      {
        name: 'Export Formats',
        tiers: {
          starter: 'TXT, Markdown',
          writer: 'TXT, Markdown, DOCX',
          author: 'TXT, Markdown, DOCX, PDF',
          professional: 'All formats + EPUB',
          studio: 'All formats + EPUB'
        }
      },
      {
        name: 'Batch Export',
        description: 'Export multiple chapters/books at once',
        tiers: {
          starter: false,
          writer: false,
          author: true,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Export Templates',
        description: 'Custom formatting for exports',
        tiers: {
          starter: false,
          writer: 'Basic',
          author: 'Standard',
          professional: 'Advanced',
          studio: 'Custom'
        }
      }
    ]
  },
  {
    name: 'Universe & Collaboration',
    features: [
      {
        name: 'Shared Universe',
        description: 'Multiple series in one universe',
        tiers: {
          starter: false,
          writer: false,
          author: false,
          professional: true,
          studio: true
        }
      },
      {
        name: 'Cross-Series Characters',
        description: 'Characters appear in multiple series',
        tiers: {
          starter: false,
          writer: false,
          author: false,
          professional: false,
          studio: true
        }
      },
      {
        name: 'Team Members',
        tiers: {
          starter: '0',
          writer: '0',
          author: '2',
          professional: '5',
          studio: 'Unlimited'
        }
      },
      {
        name: 'Real-Time Collaboration',
        description: 'Live editing with team members',
        tiers: {
          starter: false,
          writer: false,
          author: false,
          professional: false,
          studio: true
        }
      },
      {
        name: 'Version Control',
        description: 'Track changes, restore versions',
        tiers: {
          starter: false,
          writer: 'Basic',
          author: 'Standard',
          professional: 'Advanced',
          studio: 'Advanced'
        }
      }
    ]
  },
  {
    name: 'Support & Resources',
    features: [
      {
        name: 'Support',
        tiers: {
          starter: 'Community',
          writer: 'Priority Email',
          author: 'Priority Email',
          professional: 'Priority Chat',
          studio: 'White-glove'
        }
      },
      {
        name: 'Storage',
        tiers: {
          starter: '500MB',
          writer: '10GB',
          author: '25GB',
          professional: '100GB',
          studio: '500GB'
        }
      },
      {
        name: 'Early Access',
        description: 'Get new features first',
        tiers: {
          starter: false,
          writer: false,
          author: false,
          professional: false,
          studio: true
        }
      },
      {
        name: 'Training Resources',
        description: 'Video tutorials, guides',
        tiers: {
          starter: 'Basic',
          writer: 'Standard',
          author: 'Full',
          professional: 'Full',
          studio: 'Full + 1-on-1'
        }
      }
    ]
  }
]

function renderFeatureValue(value: boolean | string | number) {
  if (typeof value === 'boolean') {
    return value ? (
      <Check className="h-5 w-5 text-primary mx-auto" />
    ) : (
      <X className="h-5 w-5 text-muted-foreground/50 mx-auto" />
    )
  }
  
  if (typeof value === 'string' && (value === 'Read-only' || value.includes('+'))) {
    return <span className="text-xs font-mono text-primary">{value}</span>
  }
  
  return <span className="text-sm font-mono">{value}</span>
}

export function FeatureComparison() {
  return (
    <div className="w-full overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[250px] sticky left-0 bg-background z-10">Feature</TableHead>
            {SUBSCRIPTION_TIERS.map((tier) => (
              <TableHead 
                key={tier.id} 
                className="text-center min-w-[120px]"
              >
                <div>
                  <div className="font-semibold">{tier.name}</div>
                  <div className="text-xs text-muted-foreground font-mono">
                    ${tier.price}/mo
                  </div>
                </div>
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {featureCategories.map((category) => (
            <>
              <TableRow key={category.name} className="bg-muted/50">
                <TableCell 
                  colSpan={SUBSCRIPTION_TIERS.length + 1} 
                  className="font-semibold text-sm"
                >
                  {category.name}
                </TableCell>
              </TableRow>
              {category.features.map((feature) => (
                <TableRow key={feature.name}>
                  <TableCell className="sticky left-0 bg-background z-10">
                    <div>
                      <div className="font-medium">{feature.name}</div>
                      {feature.description && (
                        <div className="text-xs text-muted-foreground">
                          {feature.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  {SUBSCRIPTION_TIERS.map((tier) => (
                    <TableCell key={tier.id} className="text-center">
                      {renderFeatureValue(feature.tiers[tier.id])}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}