'use client'

import { useState } from 'react'
import { logger } from '@/lib/services/logger';

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { getUserTier, type UserSubscription } from '@/lib/subscription'
import { useToast } from '@/hooks/use-toast'

interface SubscriptionCardProps {
  subscription: UserSubscription
}

export function SubscriptionCard({ subscription }: SubscriptionCardProps) {
  const [isLoading, setIsLoading] = useState(false)
  const tier = getUserTier(subscription)
  const { toast } = useToast()

  const handleUpgrade = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/subscription', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tierId: tier.id === 'starter' ? 'writer' : 'author' })
      })

      if (response.ok) {
        const { url } = await response.json()
        window.location.href = url
      } else {
        throw new Error('Failed to create checkout session')
      }
    } catch (error) {
      logger.error('Upgrade error:', error);
      toast({
        title: 'Upgrade Failed',
        description: 'Failed to initiate upgrade. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleManageBilling = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/subscription/portal', {
        method: 'POST'
      })

      if (response.ok) {
        const { url } = await response.json()
        window.location.href = url
      } else {
        throw new Error('Failed to create portal session')
      }
    } catch (error) {
      logger.error('Billing portal error:', error);
      toast({
        title: 'Portal Error',
        description: 'Failed to open billing portal. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (date: string | Date) => {
    return (date instanceof Date ? date : new Date(date)).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Subscription</CardTitle>
          <Badge variant={subscription?.status === 'active' ? 'default' : 'secondary'}>
            {tier.name}
          </Badge>
        </div>
        <CardDescription>
          Manage your subscription and billing
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Plan</span>
            <span className="font-medium">{tier.name}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Price</span>
            <span className="font-medium">
              {tier.price === 0 ? 'Free' : `$${tier.price}/month`}
            </span>
          </div>
          {subscription?.status === 'active' && (
            <>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Status</span>
                <Badge variant="default" className="text-xs">
                  {subscription.cancelAtPeriodEnd ? 'Cancelling' : 'Active'}
                </Badge>
              </div>
              {subscription.currentPeriodEnd && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">
                    {subscription.cancelAtPeriodEnd ? 'Ends' : 'Renews'}
                  </span>
                  <span className="font-medium">
                    {formatDate(subscription.currentPeriodEnd)}
                  </span>
                </div>
              )}
            </>
          )}
        </div>

        <Separator />

        <div className="space-y-2">
          <h4 className="font-medium text-sm">Plan Features</h4>
          <div className="space-y-1 text-sm text-muted-foreground">
            <div className="flex justify-between">
              <span>AI Words/month</span>
              <span>{tier.limits.monthlyWords === -1 ? 'Unlimited' : `${(tier.limits.monthlyWords / 1000).toFixed(0)}k`}</span>
            </div>
            <div className="flex justify-between">
              <span>Projects</span>
              <span>{tier.limits.projects === -1 ? 'Unlimited' : tier.limits.projects}</span>
            </div>
            <div className="flex justify-between">
              <span>Storage</span>
              <span>{tier.limits.storageGB === -1 ? 'Unlimited' : `${tier.limits.storageGB}GB`}</span>
            </div>
            <div className="flex justify-between">
              <span>Export Formats</span>
              <span>{tier.limits.exportFormats.join(', ')}</span>
            </div>
          </div>
        </div>

        <Separator />

        <div className="space-y-2">
          {tier.id === 'starter' ? (
            <Button onClick={handleUpgrade} disabled={isLoading} className="w-full">
              {isLoading ? 'Loading...' : 'Upgrade to Writer'}
            </Button>
          ) : tier.id === 'writer' ? (
            <Button onClick={handleUpgrade} disabled={isLoading} className="w-full">
              {isLoading ? 'Loading...' : 'Upgrade to Author'}
            </Button>
          ) : tier.id === 'author' ? (
            <Button onClick={handleUpgrade} disabled={isLoading} className="w-full">
              {isLoading ? 'Loading...' : 'Upgrade to Professional'}
            </Button>
          ) : tier.id === 'professional' ? (
            <Button onClick={handleUpgrade} disabled={isLoading} className="w-full">
              {isLoading ? 'Loading...' : 'Upgrade to Studio'}
            </Button>
          ) : (
            <Button 
              variant="outline" 
              onClick={handleManageBilling} 
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? 'Loading...' : 'Manage Billing'}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}