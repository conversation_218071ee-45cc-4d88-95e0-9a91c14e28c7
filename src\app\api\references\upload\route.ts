import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { supabase } from '@/lib/db/client';
import { authenticateUser, handleRouteError } from '@/lib/auth';
import { generalLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter';
import { 
  validateFileComprehensive, 
  sanitizeFileName, 
  sanitizeTextInput, 
  generateSecureFilename,
  isProcessableFileType,
  SECURITY_HEADERS 
} from '@/lib/file-upload-security';
import { logger } from '@/lib/services/logger';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for file uploads
    const clientIP = getClientIP(request);
    const rateLimitResult = generalLimiter.check(20, clientIP); // 20 uploads per hour
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    // Authentication required for file uploads
    const authResult = await authenticateUser();
    if (!authResult.success) {
      return authResult.response!;
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const projectId = formData.get('projectId') as string;
    const userId = formData.get('userId') as string;
    const title = formData.get('title') as string;
    const description = formData.get('description') as string;
    const tagsJson = formData.get('tags') as string;

    // Basic input validation
    if (!file || !projectId || !userId || !title) {
      return NextResponse.json({ error: 'Required fields missing' }, { status: 400 });
    }

    // Validate user matches authenticated user
    if (userId !== authResult.user!.id) {
      return NextResponse.json({ error: 'User ID mismatch' }, { status: 403 });
    }

    let tags: string[] = [];
    try {
      tags = tagsJson ? JSON.parse(tagsJson) : [];
    } catch {
      logger.warn('Invalid tags JSON:', tagsJson);
    }

    // Comprehensive file validation (includes signature checking)
    const fileValidation = await validateFileComprehensive(file);
    if (!fileValidation.isValid) {
      return NextResponse.json({ error: fileValidation.error }, { status: 400 });
    }

    // Sanitize all text inputs
    const sanitizedTitle = sanitizeTextInput(title, 100);
    const sanitizedDescription = description ? sanitizeTextInput(description, 1000) : null;

    // Determine material type based on file validation category
    const materialType = fileValidation.category || 'document';

    // Generate secure filename using utility
    const secureFileName = generateSecureFilename(file.name, 'ref');
    const filePath = `reference-materials/${projectId}/${secureFileName}`;

    // Read file buffer for upload (reuse from validation if possible)
    const fileBuffer = await file.arrayBuffer();
    
    const { error: uploadError } = await supabase.storage
      .from('project-files')
      .upload(filePath, fileBuffer, {
        contentType: file.type,
        duplex: 'half'
      });

    if (uploadError) {
      logger.error('Upload error:', uploadError);
      return NextResponse.json({ error: 'File upload failed' }, { status: 500 });
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('project-files')
      .getPublicUrl(filePath);

    // Create database record with sanitized data
    const materialData = {
      project_id: projectId,
      user_id: userId,
      type: materialType,
      title: sanitizedTitle,
      description: sanitizedDescription,
      file_url: publicUrl,
      file_size: file.size,
      mime_type: file.type,
      tags: tags || [],
      original_filename: sanitizeFileName(file.name), // Store original filename safely
    };

    const { data: material, error: dbError } = await supabase
      .from('reference_materials')
      .insert(materialData)
      .select()
      .single();

    if (dbError) {
      // Clean up uploaded file if database insert fails
      await supabase.storage
        .from('project-files')
        .remove([filePath]);
      
      throw dbError;
    }

    // Transform response to match frontend interface
    const formattedMaterial = {
      id: material.id,
      projectId: material.project_id,
      type: material.type,
      title: material.title,
      description: material.description,
      fileUrl: material.file_url,
      fileSize: material.file_size,
      mimeType: material.mime_type,
      content: material.content,
      tags: material.tags || [],
      aiSummary: material.ai_summary,
      createdAt: new Date(material.created_at),
      updatedAt: new Date(material.updated_at),
    };

    // Queue AI processing for text extraction and summarization
    try {
      await queueMaterialProcessing(material.id, file.type);
    } catch (processingError) {
      logger.warn('Failed to queue material processing:', processingError);
      // Don't fail the request if processing queue fails
    }

    // Return success response with security headers
    const response = NextResponse.json({ material: formattedMaterial });
    
    // Add security headers
    Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    return response;
  } catch (error) {
    return handleRouteError(error, 'File Upload');
  }
}

async function queueMaterialProcessing(materialId: string, mimeType: string) {
  // Use the utility function to check if file is processable
  if (!isProcessableFileType(mimeType)) {
    return;
  }

  // Queue processing task for text extraction and AI summarization
  const { error } = await supabase
    .from('processing_tasks')
    .insert({
      id: `material_process_${materialId}`,
      project_id: '', // Will be updated by processing system
      user_id: '', // Will be updated by processing system
      type: 'material_processing',
      status: 'pending',
      priority: 'low',
      payload: {
        materialId,
        tasks: ['text_extraction', 'ai_summary']
      },
      estimated_duration: 30, // 30 seconds estimated
    });

  if (error) {
    logger.error('Failed to queue material processing:', error);
  }
}