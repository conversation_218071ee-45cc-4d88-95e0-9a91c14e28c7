'use client'

import { useRef, useEffect, useState } from 'react'
import { useTheme } from 'next-themes'
import type { editor as MonacoEditor } from 'monaco-editor'
import { LazyMonacoEditor } from './lazy-monaco-editor'
import { CollaborationIndicator } from '@/components/collaboration/collaboration-indicator'
import { useMonacoCollaboration } from '@/hooks/use-monaco-collaboration'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import { cn } from '@/lib/utils'
import type { UserSubscription } from '@/lib/subscription'

interface CollaborativeMonacoEditorProps {
  value: string
  onChange: (value: string) => void
  projectId: string
  documentId: string
  userId: string
  userName: string
  userEmail: string
  subscription: UserSubscription | null
  enableCollaboration?: boolean
  onSave?: () => void
  className?: string
  height?: string
  language?: string
  theme?: string
  options?: any
}

export function CollaborativeMonacoEditor({
  value,
  onChange,
  projectId,
  documentId,
  userId,
  userName,
  userEmail,
  subscription,
  enableCollaboration = true,
  onSave,
  className,
  height = '100%',
  language = 'markdown',
  theme: customTheme,
  options = {}
}: CollaborativeMonacoEditorProps) {
  const editorRef = useRef<MonacoEditor.IStandaloneCodeEditor | null>(null)
  const [isCollaborationEnabled, setIsCollaborationEnabled] = useState(enableCollaboration)
  const { theme } = useTheme()
  const { toast } = useToast()
  
  // Use collaboration hook
  const {
    isConnected,
    collaborators,
    error: collaborationError
  } = useMonacoCollaboration(editorRef.current, {
    sessionId: `project:${projectId}:document:${documentId}`,
    userId,
    userName,
    userEmail,
    subscription,
    enabled: isCollaborationEnabled && enableCollaboration,
    onChange: (newValue) => {
      // Handle remote changes
      onChange(newValue)
    }
  })

  // Handle collaboration errors
  useEffect(() => {
    if (collaborationError) {
      logger.error('Collaboration error:', collaborationError)
      toast({
        title: 'Collaboration Error',
        description: collaborationError.message,
        variant: 'destructive'
      })
    }
  }, [collaborationError, toast])

  const handleEditorMount = (editor: MonacoEditor.IStandaloneCodeEditor) => {
    editorRef.current = editor
    
    // Configure editor for collaboration
    if (enableCollaboration) {
      // Set up cursor style
      editor.updateOptions({
        cursorStyle: 'line',
        cursorBlinking: 'smooth',
        cursorSmoothCaretAnimation: true,
        renderWhitespace: 'selection',
        ...options
      })
    }
  }

  const toggleCollaboration = async (enabled: boolean) => {
    setIsCollaborationEnabled(enabled)
    if (!enabled) {
      // Clear all collaboration decorations when disabled
      if (editorRef.current) {
        const model = editorRef.current.getModel()
        if (model) {
          // This will be handled by the hook
        }
      }
    }
  }

  return (
    <div className={cn('relative flex flex-col', className)} style={{ height }}>
      {/* Collaboration Toolbar */}
      {enableCollaboration && subscription && (
        <div className="absolute top-2 right-2 z-50">
          <CollaborationIndicator
            sessionId={`project:${projectId}:document:${documentId}`}
            isConnected={isConnected}
            collaborators={collaborators}
            currentUserId={userId}
            canInvite={subscription.tierId === 'studio' || subscription.tierId === 'professional'}
            onToggleCollaboration={toggleCollaboration}
          />
        </div>
      )}
      
      {/* Monaco Editor */}
      <LazyMonacoEditor
        initialContent={value}
        onContentChange={onChange}
        onSave={onSave}
        onEditorMount={handleEditorMount}
        language={language}
        theme={customTheme || (theme === 'dark' ? 'vs-dark' : 'vs')}
        options={{
          ...options,
          readOnly: !isCollaborationEnabled && enableCollaboration
        }}
      />
    </div>
  )
}