declare module 'pdf-parse' {
  interface PDFInfo {
    PDFFormatVersion: string;
    IsAcroFormPresent: boolean;
    IsXFAPresent: boolean;
    Title?: string;
    Author?: string;
    Subject?: string;
    Keywords?: string;
    Creator?: string;
    Producer?: string;
    CreationDate?: Date;
    ModDate?: Date;
  }

  interface PDFMetadata {
    [key: string]: string | number | boolean | Date | undefined;
  }

  interface PDFPage {
    pageIndex: number;
    text: string;
  }

  interface PDFData {
    numpages: number;
    numrender: number;
    info: PDFInfo;
    metadata: PDFMetadata | null;
    text: string;
    version: string;
  }

  interface PDFOptions {
    pagerender?: (pageData: PDFPage) => string;
    max?: number;
    version?: string;
  }

  function parse(buffer: Buffer, options?: PDFOptions): Promise<PDFData>;
  function parse(buffer: Uint8Array, options?: PDFOptions): Promise<PDFData>;

  export = parse;
}