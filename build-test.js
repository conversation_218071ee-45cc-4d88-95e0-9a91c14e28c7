const { execSync } = require('child_process');

console.log('Testing production build...');

try {
  // Run type check first
  console.log('1. Running TypeScript check...');
  execSync('npx tsc --noEmit', { stdio: 'inherit' });
  console.log('✅ TypeScript check passed');

  // Run ESLint check
  console.log('2. Running ESLint check...');
  execSync('npm run lint', { stdio: 'inherit' });
  console.log('✅ ESLint check passed');

  console.log('3. Starting production build...');
  execSync('npm run build', { stdio: 'inherit', timeout: 300000 }); // 5 minute timeout
  console.log('✅ Build completed successfully!');

} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}