import { createClient } from '@/lib/supabase/server';
import type { BookContext, StoryStructure, CharacterProfiles, ChapterOutlines, StoryBible } from '@/lib/agents/types';
import type { Database } from '@/types/database';

type Project = Database['public']['Tables']['projects']['Row'];
type Character = Database['public']['Tables']['characters']['Row'];
type Chapter = Database['public']['Tables']['chapters']['Row'];
type StoryArc = Database['public']['Tables']['story_arcs']['Row'];
type StoryBibleEntry = Database['public']['Tables']['story_bible']['Row'];

export interface BuildContextOptions {
  includeCompletedChapters?: boolean;
  includeStoryBible?: boolean;
  includeCharacters?: boolean;
  includeStoryArcs?: boolean;
}

export class BookContextBuilder {
  private supabase = createClient();

  async buildFromProjectId(
    projectId: string, 
    userId: string,
    options: BuildContextOptions = {}
  ): Promise<BookContext> {
    const {
      includeCompletedChapters = true,
      includeStoryBible = true,
      includeCharacters = true,
      includeStoryArcs = true
    } = options;

    // Fetch project details
    const { data: project, error: projectError } = await this.supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .eq('user_id', userId)
      .single();

    if (projectError || !project) {
      throw new Error(`Project not found: ${projectError?.message || 'Unknown error'}`);
    }

    // Build the context
    const context: BookContext = {
      projectId,
      targetWordCount: project.target_word_count,
      targetChapters: project.target_chapters,
      settings: this.buildProjectSettings(project)
    };

    // Fetch and include story arcs if requested
    if (includeStoryArcs) {
      const storyStructure = await this.buildStoryStructure(projectId, project);
      if (storyStructure) {
        context.storyStructure = storyStructure;
      }
    }

    // Fetch and include characters if requested
    if (includeCharacters) {
      const characters = await this.buildCharacterProfiles(projectId);
      if (characters) {
        context.characters = characters;
      }
    }

    // Fetch and include completed chapters if requested
    if (includeCompletedChapters) {
      const chapters = await this.buildCompletedChapters(projectId);
      if (chapters.length > 0) {
        context.completedChapters = chapters;
      }
    }

    // Fetch and include story bible if requested
    if (includeStoryBible) {
      const storyBible = await this.buildStoryBible(projectId, project);
      if (storyBible) {
        context.storyBible = storyBible;
      }
    }

    // Add metadata
    context.metadata = {
      totalWordCount: context.completedChapters?.reduce((sum, ch) => sum + ch.wordCount, 0) || 0,
      chaptersCompleted: context.completedChapters?.length || 0,
      lastUpdated: new Date().toISOString()
    };

    return context;
  }

  private buildProjectSettings(project: Project) {
    return {
      genre: project.primary_genre,
      subGenres: project.sub_genres || [],
      povType: project.pov_type || 'third_limited',
      narrativeVoice: project.narrative_voice || 'standard',
      tense: project.tense || 'past',
      worldType: project.world_type || 'real_world',
      timePeriod: project.time_period || 'contemporary',
      magicTechLevel: project.magic_tech_level || 'none',
      violenceLevel: project.violence_level || 'moderate',
      romanceLevel: project.romance_level || 'minimal',
      humorLevel: project.humor_level || 'moderate',
      tone: project.tone || ['serious'],
      pacing: project.pacing || 'moderate',
      dialogueDensity: project.dialogue_density || 'moderate',
      descriptiveDensity: project.descriptive_density || 'moderate',
      narrativeDistance: project.narrative_distance || 'medium',
      chapterStructure: project.chapter_structure || 'standard',
      sceneDensity: project.scene_density || '3-5',
      conflictTypes: project.conflict_types || ['internal', 'external'],
      endings: project.endings || ['conclusive'],
      writingStyle: project.writing_style || 'standard',
      storyThemes: []
    };
  }

  private async buildStoryStructure(projectId: string, project: Project): Promise<StoryStructure | undefined> {
    const { data: storyArcs } = await this.supabase
      .from('story_arcs')
      .select('*')
      .eq('project_id', projectId)
      .order('act_number');

    const { data: storyBible } = await this.supabase
      .from('story_bible')
      .select('*')
      .eq('project_id', projectId);

    if (!storyArcs || storyArcs.length === 0) {
      return undefined;
    }

    return {
      title: project.title,
      premise: project.description || '',
      genre: project.primary_genre,
      themes: [],
      acts: storyArcs.map(arc => ({
        number: arc.act_number,
        title: `Act ${arc.act_number}`,
        description: arc.description,
        wordCountTarget: Math.round(project.target_word_count / 3),
        wordCount: 0,
        chapters: [],
        keyEvents: arc.key_events || [],
        themes: [],
        characterArcs: []
      })),
      conflicts: [],
      timeline: storyBible?.filter(entry => entry.entry_type === 'timeline_event')
        .map(entry => ({
          id: entry.entry_key,
          title: entry.entry_data.title || entry.entry_data.event || '',
          description: entry.entry_data.description || entry.entry_data.event || '',
          event: entry.entry_data.event || entry.entry_data.title || '',
          timestamp: entry.entry_data.timestamp || '',
          actNumber: entry.entry_data.actNumber || 1,
          chapterNumber: entry.entry_data.chapterNumber || entry.entry_data.chapter,
          chapter: entry.entry_data.chapter || entry.entry_data.chapterNumber,
          importance: (entry.entry_data.importance as 'critical' | 'major' | 'minor') || 'major'
        })) || [],
      worldBuilding: {
        setting: {
          timeForPeriod: project.time_period,
          locations: [],
          culture: '',
          technology: project.magic_tech_level,
          magic: undefined
        },
        rules: storyBible?.filter(entry => entry.entry_type === 'world_rule')
          .map(entry => entry.entry_data.value) || [],
        history: []
      },
      plotPoints: []
    };
  }

  private async buildCharacterProfiles(projectId: string): Promise<CharacterProfiles | undefined> {
    const { data: characters } = await this.supabase
      .from('characters')
      .select('*')
      .eq('project_id', projectId);

    if (!characters || characters.length === 0) {
      return undefined;
    }

    const mapCharacter = (char: Character) => ({
      id: char.id,
      name: char.name,
      role: char.role,
      appearance: char.physical_description || '',
      description: char.description,
      personality: {
        traits: char.personality_traits?.traits || [],
        strengths: char.personality_traits?.strengths || [],
        weaknesses: char.personality_traits?.weaknesses || [],
        fears: char.personality_traits?.fears || [],
        desires: char.personality_traits?.desires || []
      },
      backstory: char.backstory,
      motivation: char.personality_traits?.motivation || '',
      arc: char.character_arc || {
        type: char.role === 'protagonist' ? 'positive_change' : 
              char.role === 'antagonist' ? 'negative_change' : 'flat_arc',
        startingPoint: '',
        endingPoint: '',
        keyMoments: [],
        internalConflict: '',
        externalConflict: ''
      },
      voice: {
        speakingStyle: '',
        vocabulary: '',
        mannerisms: []
      },
      relationships: char.relationships || []
    });

    return {
      protagonists: characters.filter(c => c.role === 'protagonist').map(mapCharacter),
      antagonists: characters.filter(c => c.role === 'antagonist').map(mapCharacter),
      supporting: characters.filter(c => ['supporting', 'minor'].includes(c.role)).map(mapCharacter),
      relationships: []
    };
  }

  private async buildCompletedChapters(projectId: string) {
    const { data: chapters } = await this.supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .not('content', 'is', null)
      .order('chapter_number');

    return chapters?.map(ch => ({
      chapterNumber: ch.chapter_number,
      title: ch.title,
      content: ch.content,
      wordCount: ch.actual_word_count,
      scenes: [],
      characterVoices: [],
      themes: [],
      continuityNotes: []
    })) || [];
  }

  private async buildStoryBible(projectId: string, project: Project): Promise<StoryBible | undefined> {
    const { data: storyBibleEntries } = await this.supabase
      .from('story_bible')
      .select('*')
      .eq('project_id', projectId);

    if (!storyBibleEntries || storyBibleEntries.length === 0) {
      return undefined;
    }

    const storyStructure = await this.buildStoryStructure(projectId, project);
    const characters = await this.buildCharacterProfiles(projectId);

    return {
      projectId,
      lastUpdated: new Date(),
      structure: storyStructure || {
        title: project.title,
        premise: project.description || '',
        genre: project.primary_genre,
        themes: [],
        acts: [],
        conflicts: [],
        timeline: [],
        worldBuilding: {
          setting: {
            timeForPeriod: project.time_period,
            locations: [],
            culture: '',
            technology: project.magic_tech_level
          },
          rules: [],
          history: []
        },
        plotPoints: []
      },
      characters: characters || {
        protagonists: [],
        antagonists: [],
        supporting: [],
        relationships: []
      },
      worldRules: storyBibleEntries.filter(e => e.entry_type === 'world_rule')
        .map(e => e.entry_data.value),
      culturalNotes: storyBibleEntries.filter(e => e.entry_type === 'cultural_note')
        .map(e => e.entry_data.value),
      customEntries: storyBibleEntries.filter(e => e.entry_type === 'custom')
        .reduce((acc, entry) => {
          acc[entry.entry_key] = entry.entry_data;
          return acc;
        }, {} as Record<string, Record<string, unknown>>)
    };
  }
}

// Singleton instance for convenience
export const bookContextBuilder = new BookContextBuilder();