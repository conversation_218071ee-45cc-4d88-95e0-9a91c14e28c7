const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://xvqeiwrpbzpiqvwuvtpj.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

const EXPECTED_TABLES = [
  'profiles', 'projects', 'user_subscriptions', 'usage_tracking', 'usage_events',
  'story_arcs', 'characters', 'chapters', 'agent_logs', 'selection_profiles',
  'reference_materials', 'selection_analytics', 'story_bible', 'editing_sessions',
  'chapter_versions', 'project_snapshots', 'content_embeddings', 'series',
  'series_books', 'writing_goals', 'writing_goal_progress', 'notifications',
  'writing_sessions', 'ai_suggestions', 'collaboration_sessions', 'collaboration_participants',
  'processing_tasks'
]

async function checkCurrentTables() {
  console.log('🔍 Checking current database tables...\n')
  
  try {
    // Try to get all tables using direct queries
    const existingTables = []
    
    for (const tableName of EXPECTED_TABLES) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(1)
        
        if (!error) {
          existingTables.push(tableName)
          console.log(`✅ ${tableName} - EXISTS`)
        } else {
          console.log(`❌ ${tableName} - MISSING (${error.code})`)
        }
      } catch (err) {
        console.log(`❌ ${tableName} - MISSING (${err.message})`)
      }
    }
    
    console.log(`\n📊 Summary:`)
    console.log(`✅ Existing tables: ${existingTables.length}/${EXPECTED_TABLES.length}`)
    console.log(`❌ Missing tables: ${EXPECTED_TABLES.length - existingTables.length}`)
    
    const missingTables = EXPECTED_TABLES.filter(table => !existingTables.includes(table))
    
    if (missingTables.length > 0) {
      console.log(`\n❌ Missing tables:`)
      missingTables.forEach(table => console.log(`   - ${table}`))
    }
    
    if (existingTables.length > 0) {
      console.log(`\n✅ Existing tables:`)
      existingTables.forEach(table => console.log(`   - ${table}`))
    }
    
    return { existingTables, missingTables }
    
  } catch (error) {
    console.error('❌ Error checking tables:', error)
    return { existingTables: [], missingTables: EXPECTED_TABLES }
  }
}

checkCurrentTables()
