import { BaseAgent } from './base-agent'
import { logger } from '@/lib/services/logger';

import { BookContext } from './types'
import { 
  CharacterDeveloperInput, 
  CharacterProfile, 
  AgentResponse 
} from '../types/agents'
import { getAIConfig } from '../config/ai-settings'
import { charactersResponseSchema } from '../schemas/agent-schemas'

export class CharacterDeveloperAgent extends BaseAgent {
  constructor(context: BookContext) {
    super(context);
  }

  private getConfig() {
    return getAIConfig('CHARACTER_DEVELOPMENT');
  }

  async generateCharacters(input: CharacterDeveloperInput): Promise<AgentResponse<CharacterProfile[]>> {
    const startTime = Date.now()
    
    try {
      const systemPrompt = this.buildSystemPrompt(input)
      const userPrompt = this.buildUserPrompt(input)
      const config = this.getConfig();

      // Use structured completion from base class
      const result = await this.createStructuredCompletion(
        [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        charactersResponseSchema,
        'characters_response',
        config.model,
        config.temperature,
        config.max_tokens
      );

      if (!result.success) {
        // If refusal occurred, include it in the error message
        const errorMessage = result.refusal 
          ? `Model refused to generate content: ${result.refusal}`
          : result.error || 'Failed to generate characters';
        
        return {
          success: false,
          error: errorMessage,
          executionTime: Date.now() - startTime
        };
      }

      return {
        success: true,
        data: result.data!.characters,
        executionTime: Date.now() - startTime,
        // Note: token usage would need to be tracked separately
      }
    } catch (error) {
      logger.error('Character generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime
      }
    }
  }

  private buildSystemPrompt(input: CharacterDeveloperInput): string {
    return `You are a master Character Developer AI agent with the character-crafting genius of George R.R. Martin, the psychological depth of Gillian Flynn, and the relatability of John Green. Your characters must be so vivid and compelling that readers will think about them long after finishing the book.

BESTSELLER CHARACTER STANDARDS:
- Create characters that could join the pantheon of unforgettable literary figures
- Design personalities as complex as Tyrion Lannister or Amy Dunne
- Build backstories as haunting as Jay Gatsby's or Severus Snape's
- Craft character voices as distinctive as Holden Caulfield's or Bridget Jones's
- Ensure each character could carry their own spin-off novel

CHARACTER PSYCHOLOGY & DEPTH:
- Layer contradictions that make characters fascinatingly human
- Hide secrets that will shock readers when revealed
- Create moral ambiguity that sparks heated discussions
- Design flaws that readers will both hate and understand
- Build in vulnerabilities that make readers protective of characters

MEMORABLE CHARACTER TECHNIQUES for ${input.storyStructure.genre}:
- Opening Impressions: Introduce each character unforgettably
- Signature Traits: Give each a unique quirk, habit, or catchphrase
- Hidden Depths: Every character has surprising layers
- Relatable Struggles: Universal human experiences in specific contexts
- Growth Potential: Clear paths from who they are to who they'll become

CHARACTER DYNAMICS:
- Chemistry: Relationships that crackle with tension (romantic or otherwise)
- Conflict: Every interaction should advance plot or reveal character
- Subtext: What characters don't say matters as much as what they do
- Power Dynamics: Shifting balances that create ongoing tension

DIALOGUE MASTERY:
- Each character's speech patterns must be instantly recognizable
- Subtext and implied meanings in every conversation
- Wit, humor, or intensity appropriate to each personality
- Dialogue that readers will quote and remember

PHYSICAL PRESENCE:
- Descriptions that use unexpected, memorable details
- Body language that reveals inner states
- Physical traits that become symbolic or meaningful
- Appearances that subvert or play with reader expectations

Remember: You're creating people readers will love, hate, fear for, and dream about. These aren't just characters—they're potential cultural icons.`
  }

  private buildUserPrompt(input: CharacterDeveloperInput): string {
    const storyStructure = input.storyStructure
    const selections = input.projectSelections
    
    return `Please create a comprehensive cast of characters for this story:

STORY STRUCTURE:
Title: ${storyStructure.title}
Genre: ${storyStructure.genre}
Themes: ${storyStructure.themes.join(', ')}

STORY ACTS:
${storyStructure.acts.map(act => `Act ${act.number}: ${act.title} - ${act.description}`).join('\n')}

CONFLICTS:
${storyStructure.conflicts.map(conflict => `${conflict.type}: ${conflict.description}`).join('\n')}

PROJECT SELECTIONS:
- Character Complexity: ${selections.characterComplexity}
- Protagonist Types: ${selections.protagonistTypes?.join(', ') || 'Not specified'}
- Antagonist Types: ${selections.antagonistTypes?.join(', ') || 'Not specified'}
- Character Arc Types: ${selections.characterArcTypes?.join(', ') || 'Not specified'}
- Narrative Voice: ${selections.narrativeVoice}
- Target Audience: ${selections.targetAudience}
- Content Rating: ${selections.contentRating}

CUSTOM CHARACTER CONCEPTS:
${selections.customCharacterConcepts || 'None specified'}

CHARACTER REQUIREMENTS:
${input.characterRequirements ? `
- Protagonists needed: ${input.characterRequirements.protagonistCount}
- Antagonists needed: ${input.characterRequirements.antagonistCount}  
- Supporting characters needed: ${input.characterRequirements.supportingCount}
` : 'Create an appropriate number of characters for the story scope.'}

SETTING CONTEXT:
- Time Period: ${selections.timePeriod}
- Geographic Setting: ${selections.geographicSetting}
- World Type: ${selections.worldType}
- Magic/Tech Level: ${selections.magicTechLevel}

Create characters that will bring this story to life, ensuring each serves a specific purpose in the narrative while being compelling individuals in their own right.`
  }
}