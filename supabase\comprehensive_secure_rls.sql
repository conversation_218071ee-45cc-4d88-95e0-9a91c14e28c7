-- ============================================================================
-- COMPREHENSIVE & SECURE RLS POLICIES FOR BOOKSCRIBE AI
-- ============================================================================
-- This replaces broad "ALL" policies with specific, secure policies
-- Following security best practices with granular permissions

-- ============================================================================
-- 1. DROP ALL EXISTING BROAD POLICIES
-- ============================================================================

-- Drop overly broad "ALL" policies that were created
DROP POLICY IF EXISTS "Users can access own project characters" ON characters;
DROP POLICY IF EXISTS "Users can access own project story_arcs" ON story_arcs;
DROP POLICY IF EXISTS "Users can access own project reference_materials" ON reference_materials;
DROP POLICY IF EXISTS "Users can access own project story_bible" ON story_bible;
DROP POLICY IF EXISTS "Users can access own content_embeddings" ON content_embeddings;
DROP POLICY IF EXISTS "Users can access own editing_sessions" ON editing_sessions;
DROP POLICY IF EXISTS "Users can access own notifications" ON notifications;
DROP POLICY IF EXISTS "Users can access own writing_goals" ON writing_goals;
DROP POLICY IF EXISTS "Users can access own writing_sessions" ON writing_sessions;
DROP POLICY IF EXISTS "Users can access own series" ON series;
DROP POLICY IF EXISTS "Users can manage own selection profiles" ON selection_profiles;

-- ============================================================================
-- 2. CHARACTERS - Granular Permissions
-- ============================================================================

CREATE POLICY "Users can view own project characters" ON characters
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can create characters in own projects" ON characters
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can update own project characters" ON characters
  FOR UPDATE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can delete own project characters" ON characters
  FOR DELETE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- ============================================================================
-- 3. STORY ARCS - Granular Permissions
-- ============================================================================

CREATE POLICY "Users can view own project story_arcs" ON story_arcs
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can create story_arcs in own projects" ON story_arcs
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can update own project story_arcs" ON story_arcs
  FOR UPDATE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can delete own project story_arcs" ON story_arcs
  FOR DELETE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- ============================================================================
-- 4. REFERENCE MATERIALS - Granular Permissions
-- ============================================================================

CREATE POLICY "Users can view own project reference_materials" ON reference_materials
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can upload reference_materials to own projects" ON reference_materials
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can update own project reference_materials" ON reference_materials
  FOR UPDATE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can delete own project reference_materials" ON reference_materials
  FOR DELETE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- ============================================================================
-- 5. STORY BIBLE - Granular Permissions
-- ============================================================================

CREATE POLICY "Users can view own project story_bible" ON story_bible
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can create story_bible entries in own projects" ON story_bible
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can update own project story_bible" ON story_bible
  FOR UPDATE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can delete own project story_bible" ON story_bible
  FOR DELETE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- ============================================================================
-- 6. CONTENT EMBEDDINGS - Read-Only for Users, System Managed
-- ============================================================================

CREATE POLICY "Users can view own project content_embeddings" ON content_embeddings
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- Note: INSERT/UPDATE/DELETE for embeddings should be handled by system/service role
-- Users don't directly manipulate embeddings

-- ============================================================================
-- 7. EDITING SESSIONS - User-Owned with Time Restrictions
-- ============================================================================

CREATE POLICY "Users can view own editing_sessions" ON editing_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own editing_sessions" ON editing_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update recent editing_sessions" ON editing_sessions
  FOR UPDATE USING (
    auth.uid() = user_id AND 
    created_at > NOW() - INTERVAL '24 hours'
  );

-- Users cannot delete editing sessions (audit trail)

-- ============================================================================
-- 8. NOTIFICATIONS - User-Owned with Read/Update Only
-- ============================================================================

CREATE POLICY "Users can view own notifications" ON notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can mark own notifications as read" ON notifications
  FOR UPDATE USING (
    auth.uid() = user_id AND 
    OLD.read = false AND NEW.read = true
  );

-- Users cannot create or delete notifications (system managed)

-- ============================================================================
-- 9. WRITING GOALS - User-Owned Full Access
-- ============================================================================

CREATE POLICY "Users can view own writing_goals" ON writing_goals
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own writing_goals" ON writing_goals
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own writing_goals" ON writing_goals
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own writing_goals" ON writing_goals
  FOR DELETE USING (auth.uid() = user_id);

-- ============================================================================
-- 10. WRITING SESSIONS - User-Owned with Validation
-- ============================================================================

CREATE POLICY "Users can view own writing_sessions" ON writing_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own writing_sessions" ON writing_sessions
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can update own recent writing_sessions" ON writing_sessions
  FOR UPDATE USING (
    auth.uid() = user_id AND 
    created_at > NOW() - INTERVAL '7 days'
  );

-- Users cannot delete writing sessions (analytics data)

-- ============================================================================
-- 11. SERIES - User-Owned Full Access
-- ============================================================================

CREATE POLICY "Users can view own series" ON series
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own series" ON series
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own series" ON series
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own series" ON series
  FOR DELETE USING (auth.uid() = user_id);

-- ============================================================================
-- 12. SELECTION PROFILES - Public Read, Owner Write
-- ============================================================================

CREATE POLICY "Users can view public and own selection_profiles" ON selection_profiles
  FOR SELECT USING (
    is_public = true OR auth.uid() = user_id
  );

CREATE POLICY "Users can create own selection_profiles" ON selection_profiles
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own selection_profiles" ON selection_profiles
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own selection_profiles" ON selection_profiles
  FOR DELETE USING (auth.uid() = user_id);

-- ============================================================================
-- 13. MISSING TABLES - Comprehensive Policies
-- ============================================================================

-- AGENT LOGS - Read-only for users (system managed)
CREATE POLICY "Users can view own project agent_logs" ON agent_logs
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- AI SUGGESTIONS - Read and feedback only
CREATE POLICY "Users can view own project ai_suggestions" ON ai_suggestions
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can update ai_suggestions feedback" ON ai_suggestions
  FOR UPDATE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid()) AND
    (OLD.applied != NEW.applied OR OLD.feedback != NEW.feedback)
  );

-- CHAPTER VERSIONS - Read-only (system managed)
CREATE POLICY "Users can view own chapter_versions" ON chapter_versions
  FOR SELECT USING (
    chapter_id IN (
      SELECT id FROM chapters WHERE project_id IN (
        SELECT id FROM projects WHERE user_id = auth.uid()
      )
    )
  );

-- PROJECT SNAPSHOTS - Read and create only
CREATE POLICY "Users can view own project_snapshots" ON project_snapshots
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can create project_snapshots for own projects" ON project_snapshots
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- PROCESSING TASKS - User-owned read-only (system updates)
CREATE POLICY "Users can view own processing_tasks" ON processing_tasks
  FOR SELECT USING (auth.uid() = user_id);

-- SELECTION ANALYTICS - User-owned read-only (system managed)
CREATE POLICY "Users can view own selection_analytics" ON selection_analytics
  FOR SELECT USING (auth.uid() = user_id);

-- SERIES BOOKS - Linked to user's series
CREATE POLICY "Users can view own series_books" ON series_books
  FOR SELECT USING (
    series_id IN (SELECT id FROM series WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can add books to own series" ON series_books
  FOR INSERT WITH CHECK (
    series_id IN (SELECT id FROM series WHERE user_id = auth.uid()) AND
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can remove books from own series" ON series_books
  FOR DELETE USING (
    series_id IN (SELECT id FROM series WHERE user_id = auth.uid())
  );

-- WRITING GOAL PROGRESS - Linked to user's goals
CREATE POLICY "Users can view own writing_goal_progress" ON writing_goal_progress
  FOR SELECT USING (
    goal_id IN (SELECT id FROM writing_goals WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can create progress for own goals" ON writing_goal_progress
  FOR INSERT WITH CHECK (
    goal_id IN (SELECT id FROM writing_goals WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can update recent progress entries" ON writing_goal_progress
  FOR UPDATE USING (
    goal_id IN (SELECT id FROM writing_goals WHERE user_id = auth.uid()) AND
    created_at > NOW() - INTERVAL '7 days'
  );

-- COLLABORATION SESSIONS - Project owner and participants
CREATE POLICY "Users can view collaboration_sessions they own or participate in" ON collaboration_sessions
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid()) OR
    id IN (SELECT session_id FROM collaboration_participants WHERE user_id = auth.uid())
  );

CREATE POLICY "Project owners can create collaboration_sessions" ON collaboration_sessions
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid()) AND
    auth.uid() = created_by
  );

CREATE POLICY "Session creators can update collaboration_sessions" ON collaboration_sessions
  FOR UPDATE USING (auth.uid() = created_by);

-- COLLABORATION PARTICIPANTS - Session access control
CREATE POLICY "Users can view participants in accessible sessions" ON collaboration_participants
  FOR SELECT USING (
    session_id IN (
      SELECT id FROM collaboration_sessions WHERE 
      project_id IN (SELECT id FROM projects WHERE user_id = auth.uid()) OR
      id IN (SELECT session_id FROM collaboration_participants WHERE user_id = auth.uid())
    )
  );

CREATE POLICY "Session owners can manage participants" ON collaboration_participants
  FOR ALL USING (
    session_id IN (
      SELECT id FROM collaboration_sessions WHERE 
      project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
    )
  );

-- ============================================================================
-- VERIFICATION
-- ============================================================================

SELECT 
  tablename,
  COUNT(*) as policy_count,
  STRING_AGG(DISTINCT cmd, ', ') as operations
FROM pg_policies 
WHERE schemaname = 'public'
GROUP BY tablename
ORDER BY tablename;
