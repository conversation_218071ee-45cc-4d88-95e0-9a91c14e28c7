'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogDescription } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import {
  Globe,
  Plus,
  Edit,
  Trash2,
  BookOpen,
  Users,
  Calendar,
  Settings,
  Link2,
  Loader2,
  MapPin,
  Sparkles,
  Save,
  X
} from 'lucide-react'

interface Universe {
  id: string
  name: string
  description?: string
  rules: Record<string, unknown>
  created_at: string
  created_by: string
  series?: Array<{
    id: string
    title: string
    description?: string
  }>
  timeline_events?: Array<{
    id: string
    event_name: string
    description: string
    event_date?: string
    relative_date?: string
    event_type: string
    importance: string
  }>
}

interface UniverseRule {
  id: string
  category: string
  title: string
  description: string
}

interface TimelineEvent {
  id?: string
  universe_id?: string
  event_name: string
  description: string
  event_date?: string
  relative_date?: string
  event_type: 'historical' | 'cataclysm' | 'political' | 'discovery' | 'character' | 'other'
  importance: 'universe-changing' | 'major' | 'minor' | 'personal'
}

interface UniverseManagerProps {
  userId: string
  onUniverseSelect?: (universe: Universe) => void
}

export function UniverseManager({ userId, onUniverseSelect }: UniverseManagerProps) {
  const [universes, setUniverses] = useState<Universe[]>([])
  const [selectedUniverse, setSelectedUniverse] = useState<Universe | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const { toast } = useToast()

  const [newUniverse, setNewUniverse] = useState({
    name: '',
    description: ''
  })
  
  const [editingUniverse, setEditingUniverse] = useState<Universe | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState<string | null>(null)
  const [showTimelineDialog, setShowTimelineDialog] = useState(false)
  const [showRulesDialog, setShowRulesDialog] = useState(false)
  
  const [newTimelineEvent, setNewTimelineEvent] = useState<TimelineEvent>({
    event_name: '',
    description: '',
    event_type: 'historical',
    importance: 'major',
    relative_date: ''
  })
  
  const [universeRules, setUniverseRules] = useState<UniverseRule[]>([])
  const [newRule, setNewRule] = useState({
    category: '',
    title: '',
    description: ''
  })

  useEffect(() => {
    loadUniverses()
  }, [])

  const loadUniverses = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/universes')
      if (!response.ok) throw new Error('Failed to load universes')
      
      const data = await response.json()
      setUniverses(data.universes || [])
    } catch (error) {
      logger.error('Error loading universes:', error)
      toast({
        title: "Error",
        description: "Failed to load universes",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const createUniverse = async () => {
    if (!newUniverse.name.trim()) {
      toast({
        title: "Error",
        description: "Universe name is required",
        variant: "destructive"
      })
      return
    }

    try {
      const response = await fetch('/api/universes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newUniverse)
      })

      if (!response.ok) throw new Error('Failed to create universe')
      
      const data = await response.json()
      setUniverses([data.universe, ...universes])
      setNewUniverse({ name: '', description: '' })
      setIsCreating(false)
      
      toast({
        title: "Success",
        description: "Universe created successfully"
      })
    } catch (error) {
      logger.error('Error creating universe:', error)
      toast({
        title: "Error",
        description: "Failed to create universe",
        variant: "destructive"
      })
    }
  }

  const updateUniverse = async () => {
    if (!editingUniverse) return
    
    try {
      const response = await fetch(`/api/universes/${editingUniverse.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: editingUniverse.name,
          description: editingUniverse.description,
          rules: editingUniverse.rules
        })
      })

      if (!response.ok) throw new Error('Failed to update universe')
      
      const data = await response.json()
      setUniverses(universes.map(u => 
        u.id === editingUniverse.id ? data.universe : u
      ))
      
      if (selectedUniverse?.id === editingUniverse.id) {
        setSelectedUniverse(data.universe)
      }
      
      setIsEditing(false)
      setEditingUniverse(null)
      
      toast({
        title: "Success",
        description: "Universe updated successfully"
      })
    } catch (error) {
      logger.error('Error updating universe:', error)
      toast({
        title: "Error",
        description: "Failed to update universe",
        variant: "destructive"
      })
    }
  }

  const deleteUniverse = async (universeId: string) => {
    try {
      const response = await fetch(`/api/universes/${universeId}`, {
        method: 'DELETE'
      })

      if (!response.ok) throw new Error('Failed to delete universe')
      
      setUniverses(universes.filter(u => u.id !== universeId))
      if (selectedUniverse?.id === universeId) {
        setSelectedUniverse(null)
      }
      
      setShowDeleteDialog(null)
      
      toast({
        title: "Success",
        description: "Universe deleted successfully"
      })
    } catch (error) {
      logger.error('Error deleting universe:', error)
      toast({
        title: "Error",
        description: "Failed to delete universe",
        variant: "destructive"
      })
    }
  }

  const selectUniverse = async (universe: Universe) => {
    try {
      const response = await fetch(`/api/universes/${universe.id}`)
      if (!response.ok) throw new Error('Failed to load universe details')
      
      const data = await response.json()
      setSelectedUniverse(data.universe)
      onUniverseSelect?.(data.universe)
      
      // Load universe rules
      if (data.universe.rules) {
        const rulesArray = Object.entries(data.universe.rules).map(([category, rules]) => {
          if (Array.isArray(rules)) {
            return rules.map((rule: any) => ({
              id: rule.id || Math.random().toString(36).substr(2, 9),
              category,
              title: rule.title || '',
              description: rule.description || ''
            }))
          }
          return []
        }).flat()
        setUniverseRules(rulesArray)
      }
    } catch (error) {
      logger.error('Error loading universe details:', error)
      toast({
        title: "Error",
        description: "Failed to load universe details",
        variant: "destructive"
      })
    }
  }
  
  const addTimelineEvent = async () => {
    if (!selectedUniverse || !newTimelineEvent.event_name) return
    
    try {
      const response = await fetch('/api/universes/timeline-events', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...newTimelineEvent,
          universe_id: selectedUniverse.id
        })
      })
      
      if (!response.ok) throw new Error('Failed to add timeline event')
      
      const data = await response.json()
      
      // Reload universe to get updated timeline
      await selectUniverse(selectedUniverse)
      
      setNewTimelineEvent({
        event_name: '',
        description: '',
        event_type: 'historical',
        importance: 'major',
        relative_date: ''
      })
      setShowTimelineDialog(false)
      
      toast({
        title: "Success",
        description: "Timeline event added successfully"
      })
    } catch (error) {
      logger.error('Error adding timeline event:', error)
      toast({
        title: "Error",
        description: "Failed to add timeline event",
        variant: "destructive"
      })
    }
  }
  
  const addRule = () => {
    if (!newRule.category || !newRule.title) return
    
    const rule: UniverseRule = {
      id: Math.random().toString(36).substr(2, 9),
      ...newRule
    }
    
    setUniverseRules([...universeRules, rule])
    setNewRule({ category: '', title: '', description: '' })
  }
  
  const removeRule = (ruleId: string) => {
    setUniverseRules(universeRules.filter(r => r.id !== ruleId))
  }
  
  const saveRules = async () => {
    if (!selectedUniverse) return
    
    try {
      // Group rules by category
      const rulesObject = universeRules.reduce((acc, rule) => {
        if (!acc[rule.category]) {
          acc[rule.category] = []
        }
        acc[rule.category].push({
          id: rule.id,
          title: rule.title,
          description: rule.description
        })
        return acc
      }, {} as Record<string, any[]>)
      
      const response = await fetch(`/api/universes/${selectedUniverse.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          rules: rulesObject
        })
      })
      
      if (!response.ok) throw new Error('Failed to save rules')
      
      await selectUniverse(selectedUniverse)
      setShowRulesDialog(false)
      
      toast({
        title: "Success",
        description: "Universe rules saved successfully"
      })
    } catch (error) {
      logger.error('Error saving rules:', error)
      toast({
        title: "Error",
        description: "Failed to save rules",
        variant: "destructive"
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Shared Universes</h2>
          <p className="text-sm text-muted-foreground">
            Create interconnected worlds for your series
          </p>
        </div>
        
        <Dialog open={isCreating} onOpenChange={setIsCreating}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Universe
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Universe</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 mt-4">
              <div>
                <Label htmlFor="name">Universe Name</Label>
                <Input
                  id="name"
                  value={newUniverse.name}
                  onChange={(e) => setNewUniverse({ ...newUniverse, name: e.target.value })}
                  placeholder="e.g., Marvel Universe, Middle-earth"
                />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newUniverse.description}
                  onChange={(e) => setNewUniverse({ ...newUniverse, description: e.target.value })}
                  placeholder="Describe the universe and its key characteristics..."
                  rows={4}
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={createUniverse}>Create Universe</Button>
                <Button variant="outline" onClick={() => setIsCreating(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Universes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {universes.map((universe) => (
          <Card 
            key={universe.id} 
            className={`cursor-pointer transition-all hover:shadow-md ${
              selectedUniverse?.id === universe.id ? 'ring-2 ring-primary' : ''
            }`}
            onClick={() => selectUniverse(universe)}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    {universe.name}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {universe.description || 'No description'}
                  </CardDescription>
                </div>
                {universe.created_by === userId && (
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        setEditingUniverse(universe)
                        setIsEditing(true)
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        setShowDeleteDialog(universe.id)
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <BookOpen className="h-4 w-4" />
                  {universe.series?.length || 0} series
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  {universe.timeline_events?.length || 0} events
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Edit Universe Dialog */}
      <Dialog open={isEditing} onOpenChange={(open) => {
        setIsEditing(open)
        if (!open) setEditingUniverse(null)
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Universe</DialogTitle>
            <DialogDescription>
              Update the universe details
            </DialogDescription>
          </DialogHeader>
          {editingUniverse && (
            <div className="space-y-4 mt-4">
              <div>
                <Label htmlFor="edit-name">Universe Name</Label>
                <Input
                  id="edit-name"
                  value={editingUniverse.name}
                  onChange={(e) => setEditingUniverse({ ...editingUniverse, name: e.target.value })}
                  placeholder="e.g., Marvel Universe, Middle-earth"
                />
              </div>
              <div>
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  value={editingUniverse.description || ''}
                  onChange={(e) => setEditingUniverse({ ...editingUniverse, description: e.target.value })}
                  placeholder="Describe the universe and its key characteristics..."
                  rows={4}
                />
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsEditing(false)}>
                  Cancel
                </Button>
                <Button onClick={updateUniverse}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!showDeleteDialog} onOpenChange={() => setShowDeleteDialog(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the universe
              and remove all connections to series using this universe.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => showDeleteDialog && deleteUniverse(showDeleteDialog)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Universe
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Selected Universe Details */}
      {selectedUniverse && (
        <Card className="mt-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Universe Details: {selectedUniverse.name}</CardTitle>
              {selectedUniverse.created_by === userId && (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setEditingUniverse(selectedUniverse)
                      setIsEditing(true)
                    }}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Universe
                  </Button>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="overview">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="series">Series</TabsTrigger>
                <TabsTrigger value="timeline">Timeline</TabsTrigger>
                <TabsTrigger value="rules">Rules</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2">Description</h3>
                    <p className="text-muted-foreground">
                      {selectedUniverse.description || 'No description provided'}
                    </p>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Statistics</h3>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-muted rounded">
                        <div className="text-2xl font-bold">
                          {selectedUniverse.series?.length || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Series</div>
                      </div>
                      <div className="text-center p-4 bg-muted rounded">
                        <div className="text-2xl font-bold">
                          {selectedUniverse.timeline_events?.length || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Events</div>
                      </div>
                      <div className="text-center p-4 bg-muted rounded">
                        <div className="text-2xl font-bold">
                          {Object.keys(selectedUniverse.rules || {}).length}
                        </div>
                        <div className="text-sm text-muted-foreground">Rules</div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="series" className="mt-4">
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {selectedUniverse.series?.map((series) => (
                      <Card key={series.id}>
                        <CardHeader className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-semibold">{series.title}</h4>
                              <p className="text-sm text-muted-foreground">
                                {series.description || 'No description'}
                              </p>
                            </div>
                            <Link2 className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </CardHeader>
                      </Card>
                    ))}
                    {(!selectedUniverse.series || selectedUniverse.series.length === 0) && (
                      <p className="text-center text-muted-foreground py-8">
                        No series connected to this universe yet
                      </p>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="timeline" className="mt-4">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-muted-foreground">
                      Major events that shape this universe
                    </p>
                    {selectedUniverse.created_by === userId && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowTimelineDialog(true)}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Event
                      </Button>
                    )}
                  </div>
                  <ScrollArea className="h-64">
                    <div className="space-y-2">
                      {selectedUniverse.timeline_events?.map((event) => (
                        <Card key={event.id}>
                          <CardHeader className="p-4">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h4 className="font-semibold">{event.event_name}</h4>
                                <p className="text-sm text-muted-foreground mt-1">
                                  {event.description}
                                </p>
                                <div className="flex items-center gap-2 mt-2">
                                  <Badge variant="outline">
                                    {event.event_type}
                                  </Badge>
                                  <Badge variant={
                                    event.importance === 'universe-changing' ? 'destructive' :
                                    event.importance === 'major' ? 'default' : 'secondary'
                                  }>
                                    {event.importance}
                                  </Badge>
                                  {event.relative_date && (
                                    <span className="text-xs text-muted-foreground">
                                      {event.relative_date}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </CardHeader>
                        </Card>
                      ))}
                      {(!selectedUniverse.timeline_events || selectedUniverse.timeline_events.length === 0) && (
                        <p className="text-center text-muted-foreground py-8">
                          No timeline events yet
                        </p>
                      )}
                    </div>
                  </ScrollArea>
                </div>
              </TabsContent>

              <TabsContent value="rules" className="mt-4">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-muted-foreground">
                      Define the fundamental rules that govern this universe
                    </p>
                    {selectedUniverse.created_by === userId && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowRulesDialog(true)}
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Manage Rules
                      </Button>
                    )}
                  </div>
                  {universeRules.length > 0 ? (
                    <div className="space-y-2">
                      {Object.entries(
                        universeRules.reduce((acc, rule) => {
                          if (!acc[rule.category]) acc[rule.category] = []
                          acc[rule.category].push(rule)
                          return acc
                        }, {} as Record<string, UniverseRule[]>)
                      ).map(([category, rules]) => (
                        <Card key={category}>
                          <CardHeader className="p-4">
                            <h4 className="font-semibold capitalize">{category}</h4>
                            <div className="space-y-2 mt-2">
                              {rules.map((rule) => (
                                <div key={rule.id} className="border-l-2 pl-3">
                                  <h5 className="font-medium text-sm">{rule.title}</h5>
                                  {rule.description && (
                                    <p className="text-sm text-muted-foreground">
                                      {rule.description}
                                    </p>
                                  )}
                                </div>
                              ))}
                            </div>
                          </CardHeader>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-muted-foreground py-8">
                      No rules defined yet
                    </p>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
      
      {/* Add Timeline Event Dialog */}
      <Dialog open={showTimelineDialog} onOpenChange={setShowTimelineDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Timeline Event</DialogTitle>
            <DialogDescription>
              Add a significant event to this universe's timeline
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 mt-4">
            <div>
              <Label htmlFor="event-name">Event Name</Label>
              <Input
                id="event-name"
                value={newTimelineEvent.event_name}
                onChange={(e) => setNewTimelineEvent({ ...newTimelineEvent, event_name: e.target.value })}
                placeholder="e.g., The Great War, First Contact"
              />
            </div>
            <div>
              <Label htmlFor="event-description">Description</Label>
              <Textarea
                id="event-description"
                value={newTimelineEvent.description}
                onChange={(e) => setNewTimelineEvent({ ...newTimelineEvent, description: e.target.value })}
                placeholder="Describe what happened and its impact..."
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="relative-date">Date/Time</Label>
              <Input
                id="relative-date"
                value={newTimelineEvent.relative_date || ''}
                onChange={(e) => setNewTimelineEvent({ ...newTimelineEvent, relative_date: e.target.value })}
                placeholder="e.g., Year 1247, The Third Age"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="event-type">Event Type</Label>
                <Select
                  value={newTimelineEvent.event_type}
                  onValueChange={(value) => setNewTimelineEvent({ ...newTimelineEvent, event_type: value as any })}
                >
                  <SelectTrigger id="event-type">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="historical">Historical</SelectItem>
                    <SelectItem value="cataclysm">Cataclysm</SelectItem>
                    <SelectItem value="political">Political</SelectItem>
                    <SelectItem value="discovery">Discovery</SelectItem>
                    <SelectItem value="character">Character</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="importance">Importance</Label>
                <Select
                  value={newTimelineEvent.importance}
                  onValueChange={(value) => setNewTimelineEvent({ ...newTimelineEvent, importance: value as any })}
                >
                  <SelectTrigger id="importance">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="universe-changing">Universe-Changing</SelectItem>
                    <SelectItem value="major">Major</SelectItem>
                    <SelectItem value="minor">Minor</SelectItem>
                    <SelectItem value="personal">Personal</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowTimelineDialog(false)}>
                Cancel
              </Button>
              <Button onClick={addTimelineEvent}>
                <Plus className="h-4 w-4 mr-2" />
                Add Event
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* Manage Rules Dialog */}
      <Dialog open={showRulesDialog} onOpenChange={setShowRulesDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Manage Universe Rules</DialogTitle>
            <DialogDescription>
              Define the fundamental laws and principles of your universe
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 mt-4">
            <div className="grid grid-cols-[1fr_2fr_auto] gap-2 items-end">
              <div>
                <Label htmlFor="rule-category">Category</Label>
                <Input
                  id="rule-category"
                  value={newRule.category}
                  onChange={(e) => setNewRule({ ...newRule, category: e.target.value })}
                  placeholder="e.g., Magic, Physics"
                />
              </div>
              <div>
                <Label htmlFor="rule-title">Rule</Label>
                <Input
                  id="rule-title"
                  value={newRule.title}
                  onChange={(e) => setNewRule({ ...newRule, title: e.target.value })}
                  placeholder="e.g., Conservation of Magic"
                />
              </div>
              <Button onClick={addRule} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div>
              <Label htmlFor="rule-description">Description (Optional)</Label>
              <Textarea
                id="rule-description"
                value={newRule.description}
                onChange={(e) => setNewRule({ ...newRule, description: e.target.value })}
                placeholder="Explain how this rule works..."
                rows={2}
              />
            </div>
            
            <ScrollArea className="h-64 border rounded-md p-4">
              {universeRules.length > 0 ? (
                <div className="space-y-4">
                  {Object.entries(
                    universeRules.reduce((acc, rule) => {
                      if (!acc[rule.category]) acc[rule.category] = []
                      acc[rule.category].push(rule)
                      return acc
                    }, {} as Record<string, UniverseRule[]>)
                  ).map(([category, rules]) => (
                    <div key={category}>
                      <h4 className="font-semibold capitalize mb-2">{category}</h4>
                      <div className="space-y-2">
                        {rules.map((rule) => (
                          <div key={rule.id} className="flex items-start gap-2 p-2 bg-muted/50 rounded">
                            <div className="flex-1">
                              <h5 className="font-medium text-sm">{rule.title}</h5>
                              {rule.description && (
                                <p className="text-sm text-muted-foreground">
                                  {rule.description}
                                </p>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeRule(rule.id)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-muted-foreground py-8">
                  No rules added yet
                </p>
              )}
            </ScrollArea>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowRulesDialog(false)}>
                Cancel
              </Button>
              <Button onClick={saveRules}>
                <Save className="h-4 w-4 mr-2" />
                Save Rules
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}