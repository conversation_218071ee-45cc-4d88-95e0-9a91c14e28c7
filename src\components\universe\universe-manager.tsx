'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import {
  Globe,
  Plus,
  Edit,
  Trash2,
  BookOpen,
  Users,
  Calendar,
  Settings,
  Link2,
  Loader2
} from 'lucide-react'

interface Universe {
  id: string
  name: string
  description?: string
  rules: Record<string, unknown>
  created_at: string
  series?: Array<{
    id: string
    title: string
    description?: string
  }>
  timeline_events?: Array<{
    id: string
    event_name: string
    description: string
    event_date?: string
    relative_date?: string
    event_type: string
    importance: string
  }>
}

interface UniverseManagerProps {
  userId: string
  onUniverseSelect?: (universe: Universe) => void
}

export function UniverseManager({ userId, onUniverseSelect }: UniverseManagerProps) {
  const [universes, setUniverses] = useState<Universe[]>([])
  const [selectedUniverse, setSelectedUniverse] = useState<Universe | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const { toast } = useToast()

  const [newUniverse, setNewUniverse] = useState({
    name: '',
    description: ''
  })

  useEffect(() => {
    loadUniverses()
  }, [])

  const loadUniverses = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/universes')
      if (!response.ok) throw new Error('Failed to load universes')
      
      const data = await response.json()
      setUniverses(data.universes || [])
    } catch (error) {
      logger.error('Error loading universes:', error)
      toast({
        title: "Error",
        description: "Failed to load universes",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const createUniverse = async () => {
    if (!newUniverse.name.trim()) {
      toast({
        title: "Error",
        description: "Universe name is required",
        variant: "destructive"
      })
      return
    }

    try {
      const response = await fetch('/api/universes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newUniverse)
      })

      if (!response.ok) throw new Error('Failed to create universe')
      
      const data = await response.json()
      setUniverses([data.universe, ...universes])
      setNewUniverse({ name: '', description: '' })
      setIsCreating(false)
      
      toast({
        title: "Success",
        description: "Universe created successfully"
      })
    } catch (error) {
      logger.error('Error creating universe:', error)
      toast({
        title: "Error",
        description: "Failed to create universe",
        variant: "destructive"
      })
    }
  }

  const updateUniverse = async (universe: Universe) => {
    try {
      const response = await fetch(`/api/universes/${universe.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: universe.name,
          description: universe.description,
          rules: universe.rules
        })
      })

      if (!response.ok) throw new Error('Failed to update universe')
      
      const data = await response.json()
      setUniverses(universes.map(u => 
        u.id === universe.id ? data.universe : u
      ))
      
      if (selectedUniverse?.id === universe.id) {
        setSelectedUniverse(data.universe)
      }
      
      toast({
        title: "Success",
        description: "Universe updated successfully"
      })
    } catch (error) {
      logger.error('Error updating universe:', error)
      toast({
        title: "Error",
        description: "Failed to update universe",
        variant: "destructive"
      })
    }
  }

  const deleteUniverse = async (universeId: string) => {
    if (!confirm('Are you sure you want to delete this universe? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/universes/${universeId}`, {
        method: 'DELETE'
      })

      if (!response.ok) throw new Error('Failed to delete universe')
      
      setUniverses(universes.filter(u => u.id !== universeId))
      if (selectedUniverse?.id === universeId) {
        setSelectedUniverse(null)
      }
      
      toast({
        title: "Success",
        description: "Universe deleted successfully"
      })
    } catch (error) {
      logger.error('Error deleting universe:', error)
      toast({
        title: "Error",
        description: "Failed to delete universe",
        variant: "destructive"
      })
    }
  }

  const selectUniverse = async (universe: Universe) => {
    try {
      const response = await fetch(`/api/universes/${universe.id}`)
      if (!response.ok) throw new Error('Failed to load universe details')
      
      const data = await response.json()
      setSelectedUniverse(data.universe)
      onUniverseSelect?.(data.universe)
    } catch (error) {
      logger.error('Error loading universe details:', error)
      toast({
        title: "Error",
        description: "Failed to load universe details",
        variant: "destructive"
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Shared Universes</h2>
          <p className="text-sm text-muted-foreground">
            Create interconnected worlds for your series
          </p>
        </div>
        
        <Dialog open={isCreating} onOpenChange={setIsCreating}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Universe
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Universe</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 mt-4">
              <div>
                <Label htmlFor="name">Universe Name</Label>
                <Input
                  id="name"
                  value={newUniverse.name}
                  onChange={(e) => setNewUniverse({ ...newUniverse, name: e.target.value })}
                  placeholder="e.g., Marvel Universe, Middle-earth"
                />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newUniverse.description}
                  onChange={(e) => setNewUniverse({ ...newUniverse, description: e.target.value })}
                  placeholder="Describe the universe and its key characteristics..."
                  rows={4}
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={createUniverse}>Create Universe</Button>
                <Button variant="outline" onClick={() => setIsCreating(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Universes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {universes.map((universe) => (
          <Card 
            key={universe.id} 
            className={`cursor-pointer transition-all hover:shadow-md ${
              selectedUniverse?.id === universe.id ? 'ring-2 ring-primary' : ''
            }`}
            onClick={() => selectUniverse(universe)}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    {universe.name}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {universe.description || 'No description'}
                  </CardDescription>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      setIsEditing(true)
                      setSelectedUniverse(universe)
                    }}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      deleteUniverse(universe.id)
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <BookOpen className="h-4 w-4" />
                  {universe.series?.length || 0} series
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  {universe.timeline_events?.length || 0} events
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Selected Universe Details */}
      {selectedUniverse && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Universe Details: {selectedUniverse.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="overview">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="series">Series</TabsTrigger>
                <TabsTrigger value="timeline">Timeline</TabsTrigger>
                <TabsTrigger value="rules">Rules</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2">Description</h3>
                    <p className="text-muted-foreground">
                      {selectedUniverse.description || 'No description provided'}
                    </p>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Statistics</h3>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-muted rounded">
                        <div className="text-2xl font-bold">
                          {selectedUniverse.series?.length || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Series</div>
                      </div>
                      <div className="text-center p-4 bg-muted rounded">
                        <div className="text-2xl font-bold">
                          {selectedUniverse.timeline_events?.length || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Events</div>
                      </div>
                      <div className="text-center p-4 bg-muted rounded">
                        <div className="text-2xl font-bold">
                          {Object.keys(selectedUniverse.rules || {}).length}
                        </div>
                        <div className="text-sm text-muted-foreground">Rules</div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="series" className="mt-4">
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {selectedUniverse.series?.map((series) => (
                      <Card key={series.id}>
                        <CardHeader className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-semibold">{series.title}</h4>
                              <p className="text-sm text-muted-foreground">
                                {series.description || 'No description'}
                              </p>
                            </div>
                            <Link2 className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </CardHeader>
                      </Card>
                    ))}
                    {(!selectedUniverse.series || selectedUniverse.series.length === 0) && (
                      <p className="text-center text-muted-foreground py-8">
                        No series connected to this universe yet
                      </p>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="timeline" className="mt-4">
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {selectedUniverse.timeline_events?.map((event) => (
                      <Card key={event.id}>
                        <CardHeader className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h4 className="font-semibold">{event.event_name}</h4>
                              <p className="text-sm text-muted-foreground mt-1">
                                {event.description}
                              </p>
                              <div className="flex items-center gap-2 mt-2">
                                <Badge variant="outline">
                                  {event.event_type}
                                </Badge>
                                <Badge variant={
                                  event.importance === 'universe-changing' ? 'destructive' :
                                  event.importance === 'major' ? 'default' : 'secondary'
                                }>
                                  {event.importance}
                                </Badge>
                                {event.relative_date && (
                                  <span className="text-xs text-muted-foreground">
                                    {event.relative_date}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </CardHeader>
                      </Card>
                    ))}
                    {(!selectedUniverse.timeline_events || selectedUniverse.timeline_events.length === 0) && (
                      <p className="text-center text-muted-foreground py-8">
                        No timeline events yet
                      </p>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="rules" className="mt-4">
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Define the fundamental rules that govern this universe
                  </p>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    Manage Rules
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  )
}