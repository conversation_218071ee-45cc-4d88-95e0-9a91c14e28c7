import { NextResponse } from 'next/server'
import { logger } from '@/lib/services/logger';

import { ZodError } from 'zod'

// Standard error response format
export interface ApiErrorResponse {
  error: string
  message: string
  details?: unknown
  code?: string
  timestamp: string
  path?: string
}

// Error codes for consistent error handling
export const ErrorCodes = {
  // Authentication errors
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  INVALID_TOKEN: 'INVALID_TOKEN',
  EXPIRED_TOKEN: 'EXPIRED_TOKEN',
  
  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELDS: 'MISSING_REQUIRED_FIELDS',
  
  // Resource errors
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  CONFLICT: 'CONFLICT',
  
  // Rate limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  
  // Server errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  
  // Business logic errors
  INSUFFICIENT_CREDITS: 'INSUFFICIENT_CREDITS',
  SUBSCRIPTION_REQUIRED: 'SUBSCRIPTION_REQUIRED',
  FEATURE_DISABLED: 'FEATURE_DISABLED',
} as const

export type ErrorCode = typeof ErrorCodes[keyof typeof ErrorCodes]

// Helper to create standardized error responses
export function createErrorResponse(
  error: string,
  message: string,
  status: number,
  options?: {
    code?: ErrorCode
    details?: unknown
    path?: string
  }
): NextResponse<ApiErrorResponse> {
  const response: ApiErrorResponse = {
    error,
    message,
    timestamp: new Date().toISOString(),
    ...(options?.code ? { code: options.code } : {}),
    ...(options?.details ? { details: options.details } : {}),
    ...(options?.path ? { path: options.path } : {}),
  }
  
  return NextResponse.json(response, { status })
}

// Specific error response creators
export const ErrorResponses = {
  // 400 Bad Request
  badRequest: (message: string, details?: unknown) =>
    createErrorResponse('Bad Request', message, 400, {
      code: ErrorCodes.INVALID_INPUT,
      details
    }),
  
  validationError: (errors: ZodError | unknown[]) =>
    createErrorResponse('Validation Error', 'Invalid request data', 400, {
      code: ErrorCodes.VALIDATION_ERROR,
      details: errors instanceof ZodError ? errors.errors : errors
    }),
  
  missingRequiredFields: (fields: string[]) =>
    createErrorResponse('Missing Required Fields', `Required fields: ${fields.join(', ')}`, 400, {
      code: ErrorCodes.MISSING_REQUIRED_FIELDS,
      details: { requiredFields: fields }
    }),
  
  // 401 Unauthorized
  unauthorized: (message = 'Authentication required') =>
    createErrorResponse('Unauthorized', message, 401, {
      code: ErrorCodes.UNAUTHORIZED
    }),
  
  invalidToken: () =>
    createErrorResponse('Invalid Token', 'The provided authentication token is invalid', 401, {
      code: ErrorCodes.INVALID_TOKEN
    }),
  
  expiredToken: () =>
    createErrorResponse('Expired Token', 'The authentication token has expired', 401, {
      code: ErrorCodes.EXPIRED_TOKEN
    }),
  
  // 403 Forbidden
  forbidden: (message = 'Access denied') =>
    createErrorResponse('Forbidden', message, 403, {
      code: ErrorCodes.FORBIDDEN
    }),
  
  insufficientCredits: (required: number, available: number) =>
    createErrorResponse('Insufficient Credits', 'You do not have enough credits for this operation', 403, {
      code: ErrorCodes.INSUFFICIENT_CREDITS,
      details: { required, available }
    }),
  
  subscriptionRequired: (feature: string) =>
    createErrorResponse('Subscription Required', `This feature requires an active subscription: ${feature}`, 403, {
      code: ErrorCodes.SUBSCRIPTION_REQUIRED,
      details: { feature }
    }),
  
  // 404 Not Found
  notFound: (resource: string) =>
    createErrorResponse('Not Found', `${resource} not found`, 404, {
      code: ErrorCodes.NOT_FOUND,
      details: { resource }
    }),
  
  // 409 Conflict
  conflict: (message: string, details?: unknown) =>
    createErrorResponse('Conflict', message, 409, {
      code: ErrorCodes.CONFLICT,
      details
    }),
  
  alreadyExists: (resource: string, identifier?: string) =>
    createErrorResponse('Already Exists', `${resource} already exists`, 409, {
      code: ErrorCodes.ALREADY_EXISTS,
      details: { resource, identifier }
    }),
  
  // 429 Too Many Requests
  rateLimitExceeded: (retryAfter: number, limit?: number) =>
    createErrorResponse('Too Many Requests', 'Rate limit exceeded', 429, {
      code: ErrorCodes.RATE_LIMIT_EXCEEDED,
      details: { retryAfter, limit }
    }),
  
  quotaExceeded: (resource: string, limit: number) =>
    createErrorResponse('Quota Exceeded', `${resource} quota exceeded`, 429, {
      code: ErrorCodes.QUOTA_EXCEEDED,
      details: { resource, limit }
    }),
  
  // 500 Internal Server Error
  internalError: (message = 'An unexpected error occurred') =>
    createErrorResponse('Internal Server Error', message, 500, {
      code: ErrorCodes.INTERNAL_ERROR
    }),
  
  databaseError: (operation?: string) =>
    createErrorResponse('Database Error', 'A database error occurred', 500, {
      code: ErrorCodes.DATABASE_ERROR,
      details: { operation }
    }),
  
  externalServiceError: (service: string, details?: unknown) =>
    createErrorResponse('External Service Error', `Error communicating with ${service}`, 502, {
      code: ErrorCodes.EXTERNAL_SERVICE_ERROR,
      details: { service, ...(details && typeof details === 'object' ? details : {}) }
    }),
}

// Helper to handle common error scenarios
export function handleApiError(error: unknown, context?: string): NextResponse<ApiErrorResponse> {
  logger.error(`API Error${context ? ` in ${context}` : ''}:`, error);
  
  // Handle Zod validation errors
  if (error instanceof ZodError) {
    return ErrorResponses.validationError(error)
  }
  
  // Handle known error types
  if (error instanceof Error) {
    // Check for specific error messages
    if (error.message.includes('not found')) {
      return ErrorResponses.notFound(context || 'Resource')
    }
    
    if (error.message.includes('already exists')) {
      return ErrorResponses.alreadyExists(context || 'Resource')
    }
    
    if (error.message.includes('unauthorized') || error.message.includes('not authenticated')) {
      return ErrorResponses.unauthorized()
    }
    
    if (error.message.includes('forbidden') || error.message.includes('access denied')) {
      return ErrorResponses.forbidden()
    }
    
    // Default to internal error with message
    return ErrorResponses.internalError(error.message)
  }
  
  // Unknown error type
  return ErrorResponses.internalError()
}