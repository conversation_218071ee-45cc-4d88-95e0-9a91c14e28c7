import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'

const progressUpdateSchema = z.object({
  goalId: z.string().uuid(),
  value: z.number(),
  notes: z.string().optional()
})

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validation = progressUpdateSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json({ error: validation.error.issues }, { status: 400 })
    }

    const { goalId, value, notes } = validation.data

    // Call the stored procedure to update progress
    const { error } = await supabase.rpc('update_goal_progress', {
      p_goal_id: goalId,
      p_value: value,
      p_notes: notes
    })

    if (error) {
      console.error('Error updating goal progress:', error)
      return NextResponse.json({ error: 'Failed to update progress' }, { status: 500 })
    }

    // Fetch updated goal
    const { data: goal } = await supabase
      .from('writing_goals')
      .select('*')
      .eq('id', goalId)
      .single()

    return NextResponse.json({ goal })
  } catch (error) {
    console.error('Error in goal progress POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const searchParams = request.nextUrl.searchParams
    const goalId = searchParams.get('goalId')
    const days = parseInt(searchParams.get('days') || '30')

    if (!goalId) {
      return NextResponse.json({ error: 'Goal ID required' }, { status: 400 })
    }

    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const { data: progress, error } = await supabase
      .from('writing_goal_progress')
      .select('*')
      .eq('goal_id', goalId)
      .eq('user_id', user.id)
      .gte('progress_date', startDate.toISOString())
      .order('progress_date', { ascending: true })

    if (error) {
      console.error('Error fetching goal progress:', error)
      return NextResponse.json({ error: 'Failed to fetch progress' }, { status: 500 })
    }

    return NextResponse.json({ progress })
  } catch (error) {
    console.error('Error in goal progress GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}