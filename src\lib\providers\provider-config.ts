import { logger } from '@/lib/services/logger';

// Provider configuration types
export interface ProviderConfig {
  theme: {
    enabled: boolean
    defaultTheme?: string
    enableSystem?: boolean
    storageKey?: string
  }
  auth: {
    enabled: boolean
    redirectTo?: string
    autoRefresh?: boolean
  }
  settings: {
    enabled: boolean
    persistToStorage?: boolean
    storageKey?: string
  }
  notifications: {
    enabled: boolean
    position?: string
    duration?: number
  }
  keyboard: {
    enabled: boolean
    shortcuts?: Record<string, () => void>
  }
  errorHandling: {
    enabled: boolean
    reportToSentry?: boolean
    showErrorUI?: boolean
  }
}

// Environment-based configuration
export const getProviderConfig = (): ProviderConfig => {
  const isDevelopment = process.env.NODE_ENV === 'development'
  const isProduction = process.env.NODE_ENV === 'production'
  
  return {
    theme: {
      enabled: true,
      defaultTheme: 'light',
      enableSystem: false, // Disabled to prevent hydration issues
      storageKey: 'bookscribe-theme'
    },
    auth: {
      enabled: false, // Enable per route as needed
      redirectTo: '/login',
      autoRefresh: true
    },
    settings: {
      enabled: false, // Enable per route as needed
      persistToStorage: true,
      storageKey: 'bookscribe-settings'
    },
    notifications: {
      enabled: true,
      position: 'top-right',
      duration: 5000
    },
    keyboard: {
      enabled: false, // Enable per route as needed
      shortcuts: {
        'ctrl+k': () => logger.info('Search shortcut'),
        'ctrl+/': () => logger.info('Help shortcut'),
        'ctrl+s': () => logger.info('Save shortcut')
      }
    },
    errorHandling: {
      enabled: true,
      reportToSentry: isProduction,
      showErrorUI: isDevelopment
    }
  }
}

// Route-specific configurations
export const routeConfigs = {
  marketing: {
    theme: { enabled: true },
    notifications: { enabled: true },
    auth: { enabled: false },
    settings: { enabled: false },
    keyboard: { enabled: false },
    errorHandling: { enabled: true, showErrorUI: false }
  },
  
  dashboard: {
    theme: { enabled: true },
    notifications: { enabled: true },
    auth: { enabled: true },
    settings: { enabled: true },
    keyboard: { enabled: true },
    errorHandling: { enabled: true }
  },
  
  auth: {
    theme: { enabled: true },
    notifications: { enabled: true },
    auth: { enabled: true },
    settings: { enabled: false },
    keyboard: { enabled: false },
    errorHandling: { enabled: true }
  },
  
  demo: {
    theme: { enabled: true },
    notifications: { enabled: true },
    auth: { enabled: false },
    settings: { enabled: true }, // For demo customization
    keyboard: { enabled: true }, // For demo shortcuts
    errorHandling: { enabled: true, showErrorUI: false }
  }
} as const

export type RouteConfigKey = keyof typeof routeConfigs

export function getRouteConfig(route: RouteConfigKey): Partial<ProviderConfig> {
  return routeConfigs[route]
}

// Feature flags for gradual rollout
export const featureFlags = {
  enableAdvancedThemes: process.env.NEXT_PUBLIC_ENABLE_ADVANCED_THEMES === 'true',
  enableKeyboardShortcuts: process.env.NEXT_PUBLIC_ENABLE_KEYBOARD_SHORTCUTS === 'true',
  enableRealTimeCollaboration: process.env.NEXT_PUBLIC_ENABLE_REALTIME_COLLAB === 'true',
  enableAIFeatures: process.env.NEXT_PUBLIC_ENABLE_AI_FEATURES === 'true',
  enableAnalytics: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true'
}

// Performance monitoring configuration
export const performanceConfig = {
  enableBundleAnalysis: process.env.ANALYZE === 'true',
  enableWebVitals: process.env.NEXT_PUBLIC_ENABLE_WEB_VITALS === 'true',
  maxProviderLoadTime: 5000, // 5 seconds
  enableLazyLoading: true,
  enableCodeSplitting: true
}

// Error handling configuration
export const errorConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  enableErrorReporting: process.env.NODE_ENV === 'production',
  enableConsoleLogging: process.env.NODE_ENV === 'development',
  enableUserNotifications: true,
  fallbackToMinimalMode: true
}
