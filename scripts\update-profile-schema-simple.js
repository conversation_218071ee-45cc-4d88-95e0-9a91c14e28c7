/**
 * Simple script to update the profiles table with enhanced fields
 */

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateProfileSchema() {
  console.log('🚀 BookScribe AI - Profile Schema Update');
  console.log('=====================================\n');
  
  try {
    console.log('🔄 Adding new fields to profiles table...');
    
    const queries = [
      `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS username VARCHAR(50) UNIQUE;`,
      `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS bio TEXT;`,
      `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS location VARCHAR(255);`,
      `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS website VARCHAR(500);`,
      `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS writing_goals JSONB DEFAULT '{"daily_words": 1000, "weekly_hours": 10, "genre_focus": "Fiction"}';`,
      `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{"public_profile": true, "email_notifications": true, "writing_reminders": true, "beta_features": false}';`,
      `CREATE INDEX IF NOT EXISTS idx_profiles_username ON profiles(username);`
    ];
    
    let successCount = 0;
    for (const [index, query] of queries.entries()) {
      console.log(`📝 Executing query ${index + 1}/${queries.length}...`);
      const { error } = await supabase.rpc('exec_sql', { sql: query });
      if (error) {
        console.warn(`⚠️ Query ${index + 1} warning:`, error.message);
      } else {
        successCount++;
      }
    }
    
    console.log(`\n✅ Schema update completed! (${successCount}/${queries.length} successful)`);
    console.log('\n📋 Added fields:');
    console.log('- username (VARCHAR(50), unique)');
    console.log('- bio (TEXT)');
    console.log('- location (VARCHAR(255))');
    console.log('- website (VARCHAR(500))');
    console.log('- writing_goals (JSONB)');
    console.log('- preferences (JSONB)');
    
    // Test the schema
    console.log('\n🧪 Testing profile functionality...');
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('id, email, full_name, username, bio, writing_goals, preferences')
      .limit(1);
    
    if (testError) {
      console.warn('⚠️ Warning: Could not test profile functionality:', testError.message);
    } else {
      console.log('✅ Profile schema test passed!');
      if (testData && testData.length > 0) {
        console.log('📊 Sample profile data structure confirmed');
      }
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
    console.log('\n💡 Manual alternative:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Run the SQL from: supabase/migrations/002_profile_enhancements.sql');
  }
}

updateProfileSchema();