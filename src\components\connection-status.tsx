'use client'

import { useSupabaseConnection } from '@/hooks/use-supabase-connection'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { WifiOff, RefreshCw, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

export function ConnectionStatus() {
  const { state, error, retry, isConnecting } = useSupabaseConnection()

  if (state === 'connected') {
    return null // Don't show anything when connected
  }

  return (
    <Alert
      className={cn(
        'fixed bottom-4 left-4 w-auto max-w-sm shadow-lg z-50',
        state === 'error' && 'border-destructive',
        state === 'disconnected' && 'border-amber-600'
      )}
    >
      <div className="flex items-start gap-3">
        {state === 'disconnected' && <WifiOff className="h-4 w-4 mt-0.5" />}
        {state === 'error' && <AlertCircle className="h-4 w-4 mt-0.5" />}
        {state === 'connecting' && (
          <RefreshCw className="h-4 w-4 mt-0.5 animate-spin" />
        )}
        
        <div className="flex-1">
          <AlertDescription>
            {state === 'disconnected' && 'No network connection'}
            {state === 'error' && (error || 'Unable to connect to server')}
            {state === 'connecting' && 'Connecting...'}
          </AlertDescription>
          
          {state === 'error' && (
            <Button
              size="sm"
              variant="outline"
              onClick={retry}
              disabled={isConnecting}
              className="mt-2"
              aria-label="Retry connection"
            >
              <RefreshCw className={cn('mr-2 h-3 w-3', isConnecting && 'animate-spin')} />
              Retry
            </Button>
          )}
        </div>
      </div>
    </Alert>
  )
}

// Minimal connection indicator for navbar
export function ConnectionIndicator() {
  const { state } = useSupabaseConnection()

  if (state === 'connected') {
    return null
  }

  return (
    <div className="flex items-center">
      {state === 'disconnected' && (
        <div className="flex items-center gap-1.5 text-amber-600">
          <WifiOff className="h-4 w-4" />
          <span className="text-xs font-medium">Offline</span>
        </div>
      )}
      {state === 'error' && (
        <div className="flex items-center gap-1.5 text-destructive">
          <AlertCircle className="h-4 w-4" />
          <span className="text-xs font-medium">Connection Error</span>
        </div>
      )}
      {state === 'connecting' && (
        <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
      )}
    </div>
  )
}