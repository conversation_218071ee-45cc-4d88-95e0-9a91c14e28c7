import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { authenticateUser, handleRouteError } from '@/lib/auth'

interface ParsedChapter {
  title: string
  content: string
  wordCount: number
  chapterNumber: number
}

export async function POST(request: NextRequest) {
  try {
    // Authentication
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const projectId = formData.get('projectId') as string
    const parseChapters = formData.get('parseChapters') === 'true'
    const startPage = parseInt(formData.get('startPage') as string) || 1
    const endPage = parseInt(formData.get('endPage') as string) || undefined

    if (!file || !projectId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Parse PDF options
    const options: { max?: number } = {}
    if (endPage) {
      options.max = endPage
    }

    // Extract text from PDF (server-side only)
    if (typeof window !== 'undefined') {
      throw new Error('PDF parsing is only available on server-side')
    }
    const pdf = (await import('pdf-parse')).default
    const data = await pdf(buffer, options)
    
    // Get only the pages we want
    let text = data.text
    if (startPage > 1) {
      // Simple page splitting - this is approximate
      const pages = text.split('\n\n\n')
      text = pages.slice(startPage - 1, endPage).join('\n\n\n')
    }

    // Parse chapters if requested
    let chapters: ParsedChapter[] = []
    
    if (parseChapters) {
      chapters = parseChaptersFromText(text)
    } else {
      // Treat entire document as one chapter
      chapters = [{
        title: file.name.replace('.pdf', ''),
        content: text,
        wordCount: text.split(/\s+/).filter((word: string) => word.length > 0).length,
        chapterNumber: 1
      }]
    }

    // Create chapters in database
    const supabase = await createClient()
    const createdChapters = []

    for (const chapter of chapters) {
      const { data, error } = await supabase
        .from('chapters')
        .insert({
          project_id: projectId,
          chapter_number: chapter.chapterNumber,
          title: chapter.title,
          content: chapter.content,
          actual_word_count: chapter.wordCount,
          status: 'imported',
          created_by: authResult.user!.id
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating chapter:', error)
        continue
      }

      createdChapters.push(data)
    }

    // Update project word count
    const totalWordCount = chapters.reduce((sum, ch) => sum + ch.wordCount, 0)
    await supabase
      .from('projects')
      .update({
        word_count: totalWordCount,
        chapters_count: chapters.length,
        updated_at: new Date().toISOString()
      })
      .eq('id', projectId)

    // Store PDF metadata
    const metadata = {
      pageCount: data.numpages,
      info: data.info,
      importedPages: `${startPage}-${endPage || data.numpages}`
    }

    return NextResponse.json({
      success: true,
      chaptersImported: createdChapters.length,
      totalWordCount,
      metadata,
      chapters: createdChapters.map(ch => ({
        id: ch.id,
        title: ch.title,
        chapterNumber: ch.chapter_number,
        wordCount: ch.actual_word_count
      }))
    })

  } catch (error) {
    return handleRouteError(error, 'PDF Import')
  }
}

function parseChaptersFromText(text: string): ParsedChapter[] {
  const chapters: ParsedChapter[] = []
  
  // Common chapter patterns - same as DOCX
  const chapterPatterns = [
    /^Chapter\s+(\d+)(?:\s*[-:]\s*(.*))?$/im,
    /^CHAPTER\s+(\d+)(?:\s*[-:]\s*(.*))?$/im,
    /^Chapter\s+([A-Z]+)(?:\s*[-:]\s*(.*))?$/im,
    /^(\d+)\.?\s+(.*)$/m,
    /^Part\s+(\d+)(?:\s*[-:]\s*(.*))?$/im
  ]

  // Split text by chapter markers
  let currentChapter: ParsedChapter | null = null
  const lines = text.split('\n')
  let chapterNumber = 0

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]?.trim() || ''
    let isChapterStart = false
    let title = ''

    // Check if line matches any chapter pattern
    for (const pattern of chapterPatterns) {
      const match = line.match(pattern)
      if (match) {
        isChapterStart = true
        chapterNumber++
        title = match[2] || `Chapter ${chapterNumber}`
        break
      }
    }

    // Also check for lines that are all caps and short (likely chapter titles)
    if (!isChapterStart && line.length > 0 && line.length < 50 && line === line.toUpperCase()) {
      isChapterStart = true
      chapterNumber++
      title = line
    }

    if (isChapterStart) {
      // Save previous chapter if exists
      if (currentChapter && currentChapter.content.trim()) {
        chapters.push(currentChapter)
      }

      // Start new chapter
      currentChapter = {
        title: title.trim(),
        content: '',
        wordCount: 0,
        chapterNumber
      }
    } else if (currentChapter) {
      // Add line to current chapter
      currentChapter.content += line + '\n'
    } else if (!currentChapter && line.trim()) {
      // No chapter marker found yet, create a default first chapter
      chapterNumber = 1
      currentChapter = {
        title: 'Chapter 1',
        content: line + '\n',
        wordCount: 0,
        chapterNumber
      }
    }
  }

  // Add the last chapter
  if (currentChapter && currentChapter.content.trim()) {
    chapters.push(currentChapter)
  }

  // Calculate word counts and clean up
  for (const chapter of chapters) {
    chapter.content = chapter.content.trim()
    chapter.wordCount = chapter.content.split(/\s+/).filter(word => word.length > 0).length
  }

  // If no chapters found, return the entire text as one chapter
  if (chapters.length === 0) {
    chapters.push({
      title: 'Imported Content',
      content: text,
      wordCount: text.split(/\s+/).filter(word => word.length > 0).length,
      chapterNumber: 1
    })
  }

  return chapters
}