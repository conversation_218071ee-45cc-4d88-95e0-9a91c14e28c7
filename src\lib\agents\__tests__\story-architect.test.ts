import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { StoryArchitect } from '../story-architect';
import type { BookContext, StoryStructure } from '../types';
import OpenAI from 'openai';

// Mock OpenAI
jest.mock('openai');

describe('StoryArchitect', () => {
  let architect: StoryArchitect;
  let mockContext: BookContext;
  let mockOpenAI: jest.Mocked<OpenAI>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockContext = {
      projectId: 'test-project-id',
      storyPrompt: 'A young wizard discovers their powers',
      settings: {
        primaryGenre: 'fantasy',
        secondaryGenres: ['adventure', 'coming-of-age'],
        targetAudience: 'young-adult',
        writingStyle: 'descriptive',
        narrativeVoice: 'third-person',
        tense: 'past',
        pacing: 'medium',
        violenceLevel: 'moderate',
        romanceLevel: 'low',
        profanityLevel: 'mild',
      },
      targetWordCount: 80000,
      targetChapters: 20,
    };

    mockOpenAI = {
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    } as unknown as jest.Mocked<OpenAI>;

    (OpenAI as jest.MockedClass<typeof OpenAI>).mockImplementation(() => mockOpenAI);
    
    architect = new StoryArchitect(mockContext);
  });

  describe('execute', () => {
    it('should generate a complete story structure', async () => {
      const mockStructure: StoryStructure = {
        title: 'The Awakening Mage',
        premise: 'A young wizard must master their newfound powers to save their kingdom from an ancient evil.',
        genre: 'fantasy',
        themes: ['self-discovery', 'power and responsibility', 'good vs evil'],
        acts: [
          {
            number: 1,
            title: 'Discovery',
            description: 'The protagonist discovers their magical abilities',
            wordCountTarget: 20000,
            wordCount: 0,
            chapters: [1, 2, 3, 4, 5],
            keyEvents: ['Power awakening', 'Meeting the mentor'],
            themes: ['self-discovery'],
            characterArcs: [],
          },
        ],
        conflicts: [
          {
            type: 'internal',
            description: 'Fear of their own power',
          },
          {
            type: 'external',
            description: 'Ancient evil threatening the kingdom',
          },
        ],
        timeline: [
          {
            id: '1',
            title: 'Power Awakening',
            description: 'First manifestation of magic',
            event: 'Accidental magic during village festival',
            timestamp: 'Day 1',
            actNumber: 1,
            chapterNumber: 1,
            importance: 'critical',
          },
        ],
        worldBuilding: {
          setting: {
            timeForPeriod: 'Medieval fantasy',
            locations: [
              {
                name: 'Eldoria',
                description: 'A magical kingdom',
                importance: 'primary',
                atmosphere: 'Mystical and ancient',
                keyFeatures: ['Crystal towers', 'Floating gardens'],
              },
            ],
            culture: 'Magic-based society with strict hierarchies',
            technology: 'Medieval with magical enhancements',
            magic: {
              name: 'The Weave',
              description: 'Elemental magic system',
              rules: ['Requires focus crystals', 'Drains life force'],
              limitations: ['Cannot resurrect the dead', 'Limited by user stamina'],
              practitioners: ['Mages', 'Sorcerers', 'Witches'],
            },
          },
          rules: [
            'Magic requires sacrifice',
            'Ancient evils cannot be destroyed, only contained',
          ],
          history: [
            {
              name: 'The Great Sealing',
              description: 'When the ancient evil was first contained',
              timestamp: '1000 years ago',
              impact: 'Created the current magical order',
            },
          ],
        },
        plotPoints: [
          {
            id: '1',
            title: 'The Call',
            description: 'Discovery of magical powers',
            actNumber: 1,
            chapterNumber: 1,
            type: 'inciting_incident',
            consequences: ['Draws attention of dark forces', 'Must leave home'],
          },
        ],
      };

      const mockResponse = {
        choices: [{
          message: {
            tool_calls: [{
              function: {
                arguments: JSON.stringify({
                  title: mockStructure.title,
                  premise: mockStructure.premise,
                  genre: mockStructure.genre,
                  themes: mockStructure.themes,
                  acts: mockStructure.acts,
                  conflicts: mockStructure.conflicts,
                  timeline: mockStructure.timeline,
                  worldBuilding: mockStructure.worldBuilding,
                  plotPoints: mockStructure.plotPoints,
                }),
              },
            }],
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const result = await architect.execute();

      expect(result).toEqual(mockStructure);
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({
          model: expect.any(String),
          messages: expect.arrayContaining([
            expect.objectContaining({
              role: 'system',
              content: expect.stringContaining('Story Architect'),
            }),
          ]),
          tools: expect.arrayContaining([
            expect.objectContaining({
              function: expect.objectContaining({
                name: 'create_story_structure',
              }),
            }),
          ]),
        })
      );
    });

    it('should throw error when context is incomplete', async () => {
      const incompleteContext = { ...mockContext };
      delete incompleteContext.storyPrompt;
      
      architect = new StoryArchitect(incompleteContext);
      
      await expect(architect.execute()).rejects.toThrow('Story Architect requires story prompt');
    });

    it('should throw error when no tool calls in response', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Some text response',
            tool_calls: null,
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      await expect(architect.execute()).rejects.toThrow('Story Architect failed to generate story structure');
    });

    it('should handle API errors gracefully', async () => {
      const error = new Error('OpenAI API error');
      mockOpenAI.chat.completions.create.mockRejectedValue(error);

      await expect(architect.execute()).rejects.toThrow('OpenAI API error');
    });

    it('should respect word count and chapter settings', async () => {
      const mockResponse = {
        choices: [{
          message: {
            tool_calls: [{
              function: {
                arguments: JSON.stringify({
                  title: 'Test',
                  premise: 'Test premise',
                  genre: 'fantasy',
                  themes: ['test'],
                  acts: [
                    {
                      number: 1,
                      title: 'Act 1',
                      description: 'First act',
                      wordCountTarget: 40000,
                      chapters: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
                    },
                    {
                      number: 2,
                      title: 'Act 2', 
                      description: 'Second act',
                      wordCountTarget: 40000,
                      chapters: [11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
                    },
                  ],
                  conflicts: [],
                  timeline: [],
                  worldBuilding: {
                    setting: { locations: [] },
                    rules: [],
                    history: [],
                  },
                  plotPoints: [],
                }),
              },
            }],
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const result = await architect.execute();

      // Check that total chapters match target
      const totalChapters = result.acts.reduce((sum, act) => sum + act.chapters.length, 0);
      expect(totalChapters).toBe(20);

      // Check that word count targets sum to total target
      const totalWordCount = result.acts.reduce((sum, act) => sum + act.wordCountTarget, 0);
      expect(totalWordCount).toBe(80000);
    });
  });

  describe('story structure generation', () => {
    it('should include all required story elements', async () => {
      const mockResponse = {
        choices: [{
          message: {
            tool_calls: [{
              function: {
                arguments: JSON.stringify({
                  title: 'Epic Fantasy',
                  premise: 'A grand adventure',
                  genre: 'fantasy',
                  themes: ['heroism', 'sacrifice'],
                  acts: [{
                    number: 1,
                    title: 'Beginning',
                    description: 'The start',
                    wordCountTarget: 20000,
                    chapters: [1, 2, 3, 4, 5],
                    keyEvents: ['Event 1'],
                    themes: ['heroism'],
                  }],
                  conflicts: [{
                    type: 'external',
                    description: 'Battle against evil',
                  }],
                  timeline: [{
                    id: '1',
                    title: 'Start',
                    event: 'The beginning',
                    timestamp: 'Day 1',
                    actNumber: 1,
                    importance: 'critical',
                  }],
                  worldBuilding: {
                    setting: {
                      timeForPeriod: 'Medieval',
                      locations: [{
                        name: 'Kingdom',
                        description: 'A vast realm',
                        importance: 'primary',
                        atmosphere: 'Grand',
                        keyFeatures: ['Castle'],
                      }],
                      culture: 'Feudal',
                      technology: 'Medieval',
                    },
                    rules: ['Rule 1'],
                    history: [{
                      name: 'The Founding',
                      description: 'How it began',
                      timestamp: 'Long ago',
                      impact: 'Created the kingdom',
                    }],
                  },
                  plotPoints: [{
                    id: '1',
                    title: 'Inciting Incident',
                    description: 'The call to adventure',
                    actNumber: 1,
                    chapterNumber: 1,
                    type: 'inciting_incident',
                    consequences: ['Must leave home'],
                  }],
                }),
              },
            }],
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const result = await architect.execute();

      // Verify all required fields are present
      expect(result.title).toBeDefined();
      expect(result.premise).toBeDefined();
      expect(result.genre).toBeDefined();
      expect(result.themes).toBeInstanceOf(Array);
      expect(result.acts).toBeInstanceOf(Array);
      expect(result.conflicts).toBeInstanceOf(Array);
      expect(result.timeline).toBeInstanceOf(Array);
      expect(result.worldBuilding).toBeDefined();
      expect(result.plotPoints).toBeInstanceOf(Array);

      // Verify structure depth
      expect(result.worldBuilding.setting).toBeDefined();
      expect(result.worldBuilding.rules).toBeInstanceOf(Array);
      expect(result.worldBuilding.history).toBeInstanceOf(Array);
    });
  });
});