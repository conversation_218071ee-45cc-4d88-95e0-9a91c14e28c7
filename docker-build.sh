#!/bin/bash

# BookScribe AI Docker Build Script
# This script helps build and manage Docker containers for BookScribe AI

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="production"
BUILD_ONLY=false
PUSH=false
TAG="latest"
REGISTRY=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --env ENVIRONMENT    Set environment (development|production) [default: production]"
    echo "  -t, --tag TAG           Set Docker image tag [default: latest]"
    echo "  -r, --registry REGISTRY Set Docker registry URL"
    echo "  -p, --push              Push image to registry after build"
    echo "  -b, --build-only        Only build, don't run"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                      # Build and run production image"
    echo "  $0 -e development       # Build and run development image"
    echo "  $0 -t v1.0.0 -p         # Build with tag v1.0.0 and push to registry"
    echo "  $0 -r myregistry.com -t v1.0.0 -p  # Build, tag, and push to custom registry"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -p|--push)
            PUSH=true
            shift
            ;;
        -b|--build-only)
            BUILD_ONLY=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Environment must be 'development' or 'production'"
    exit 1
fi

# Set image name
IMAGE_NAME="bookscribe-ai"
if [[ -n "$REGISTRY" ]]; then
    FULL_IMAGE_NAME="$REGISTRY/$IMAGE_NAME:$TAG"
else
    FULL_IMAGE_NAME="$IMAGE_NAME:$TAG"
fi

# Check if .env.local exists
if [[ ! -f ".env.local" ]]; then
    print_warning ".env.local file not found. Creating from .env.example..."
    if [[ -f ".env.example" ]]; then
        cp .env.example .env.local
        print_warning "Please edit .env.local with your actual environment variables before running the container"
    else
        print_error ".env.example file not found. Please create .env.local manually"
        exit 1
    fi
fi

print_status "Building BookScribe AI Docker image..."
print_status "Environment: $ENVIRONMENT"
print_status "Image: $FULL_IMAGE_NAME"

# Build the appropriate Docker image
if [[ "$ENVIRONMENT" == "development" ]]; then
    print_status "Building development image..."
    docker build -f Dockerfile.dev -t "$FULL_IMAGE_NAME" .
else
    print_status "Building production image..."
    docker build -t "$FULL_IMAGE_NAME" .
fi

print_success "Docker image built successfully: $FULL_IMAGE_NAME"

# Push to registry if requested
if [[ "$PUSH" == true ]]; then
    if [[ -z "$REGISTRY" ]]; then
        print_error "Registry URL required for push operation"
        exit 1
    fi
    
    print_status "Pushing image to registry..."
    docker push "$FULL_IMAGE_NAME"
    print_success "Image pushed successfully to $REGISTRY"
fi

# Run the container if not build-only
if [[ "$BUILD_ONLY" == false ]]; then
    print_status "Starting container..."
    
    # Stop existing container if running
    if docker ps -q -f name=bookscribe-app | grep -q .; then
        print_status "Stopping existing container..."
        docker stop bookscribe-app
        docker rm bookscribe-app
    fi
    
    if [[ "$ENVIRONMENT" == "development" ]]; then
        print_status "Starting development container with docker-compose..."
        docker-compose -f docker-compose.dev.yml up -d
    else
        print_status "Starting production container..."
        docker run -d \
            --name bookscribe-app \
            -p 3000:3000 \
            --env-file .env.local \
            --restart unless-stopped \
            "$FULL_IMAGE_NAME"
    fi
    
    print_success "Container started successfully!"
    print_status "Application will be available at: http://localhost:3000"
    print_status "Use 'docker logs bookscribe-app' to view logs"
fi

print_success "Build process completed!"
