# BookScribe AI Docker Build Script (PowerShell)
# This script helps build and manage Docker containers for BookScribe AI

param(
    [string]$Environment = "production",
    [string]$Tag = "latest",
    [string]$Registry = "",
    [switch]$Push = $false,
    [switch]$BuildOnly = $false,
    [switch]$Help = $false
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Function to show usage
function Show-Usage {
    Write-Host "Usage: .\docker-build.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Environment ENVIRONMENT  Set environment (development|production) [default: production]"
    Write-Host "  -Tag TAG                 Set Docker image tag [default: latest]"
    Write-Host "  -Registry REGISTRY       Set Docker registry URL"
    Write-Host "  -Push                    Push image to registry after build"
    Write-Host "  -BuildOnly               Only build, don't run"
    Write-Host "  -Help                    Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\docker-build.ps1                           # Build and run production image"
    Write-Host "  .\docker-build.ps1 -Environment development  # Build and run development image"
    Write-Host "  .\docker-build.ps1 -Tag v1.0.0 -Push        # Build with tag v1.0.0 and push to registry"
}

# Show help if requested
if ($Help) {
    Show-Usage
    exit 0
}

# Validate environment
if ($Environment -ne "development" -and $Environment -ne "production") {
    Write-Error "Environment must be 'development' or 'production'"
    exit 1
}

# Set image name
$ImageName = "bookscribe-ai"
if ($Registry) {
    $FullImageName = "$Registry/$ImageName`:$Tag"
} else {
    $FullImageName = "$ImageName`:$Tag"
}

# Check if .env.local exists
if (-not (Test-Path ".env.local")) {
    Write-Warning ".env.local file not found. Creating from .env.example..."
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env.local"
        Write-Warning "Please edit .env.local with your actual environment variables before running the container"
    } else {
        Write-Error ".env.example file not found. Please create .env.local manually"
        exit 1
    }
}

Write-Status "Building BookScribe AI Docker image..."
Write-Status "Environment: $Environment"
Write-Status "Image: $FullImageName"

# Build the appropriate Docker image
try {
    if ($Environment -eq "development") {
        Write-Status "Building development image..."
        docker build -f Dockerfile.dev -t $FullImageName .
    } else {
        Write-Status "Building production image..."
        docker build -t $FullImageName .
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "Docker build failed"
    }
    
    Write-Success "Docker image built successfully: $FullImageName"
} catch {
    Write-Error "Failed to build Docker image: $_"
    exit 1
}

# Push to registry if requested
if ($Push) {
    if (-not $Registry) {
        Write-Error "Registry URL required for push operation"
        exit 1
    }
    
    try {
        Write-Status "Pushing image to registry..."
        docker push $FullImageName
        
        if ($LASTEXITCODE -ne 0) {
            throw "Docker push failed"
        }
        
        Write-Success "Image pushed successfully to $Registry"
    } catch {
        Write-Error "Failed to push image: $_"
        exit 1
    }
}

# Run the container if not build-only
if (-not $BuildOnly) {
    Write-Status "Starting container..."
    
    # Stop existing container if running
    $existingContainer = docker ps -q -f name=bookscribe-app
    if ($existingContainer) {
        Write-Status "Stopping existing container..."
        docker stop bookscribe-app
        docker rm bookscribe-app
    }
    
    try {
        if ($Environment -eq "development") {
            Write-Status "Starting development container with docker-compose..."
            docker-compose -f docker-compose.dev.yml up -d
        } else {
            Write-Status "Starting production container..."
            docker run -d --name bookscribe-app -p 3000:3000 --env-file .env.local --restart unless-stopped $FullImageName
        }
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to start container"
        }
        
        Write-Success "Container started successfully!"
        Write-Status "Application will be available at: http://localhost:3000"
        Write-Status "Use 'docker logs bookscribe-app' to view logs"
    } catch {
        Write-Error "Failed to start container: $_"
        exit 1
    }
}

Write-Success "Build process completed!"
