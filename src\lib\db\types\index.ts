// Re-export all types from modular files
export * from './json'
export * from './story'
export * from './character'
export * from './enums'
export * from './tables'

// Re-export specific types from the main types file that are not duplicated
export type { 
  Database,
  Json,
  StoryStructure,
  ChapterScenes,
  PlotAdvancement,
  AiAnalysis,
  QualityScore,
  
  // Table types
  Tables,
  TablesInsert,
  TablesUpdate,
  
  // Specific table types  
  Profile,
  UserSubscription,
  UsageTracking,
  UsageEvent,
  Project,
  StoryArc,
  Chapter,
  Character,
  StoryBible,
  StoryBibles,
  ReferenceMaterial,
  AgentLog,
  SelectionProfile,
  SelectionAnalytics,
  WritingSession,
  ChapterVersion,
  EditingSession,
  ProcessingTask,
  ContentEmbedding,
  
  // Insert types
  ProfileInsert,
  ProjectInsert,
  ChapterInsert,
  CharacterInsert,
  SelectionProfileInsert,
  WritingSessionInsert,
  AgentLogInsert,
  ReferenceMaterialInsert,
  StoryBibleInsert,
  ProcessingTaskInsert,
  ContentEmbeddingInsert,
  
  // Update types
  ProjectUpdate,
  ChapterUpdate,
  CharacterUpdate,
  SelectionProfileUpdate,
  ProcessingTaskUpdate,
  ContentEmbeddingUpdate,
  
  // Status and enum types
  SubscriptionStatus,
  ProjectStatus,
  ChapterStatus,
  CharacterRole,
  ContentRating,
  ProjectScope,
  WritingStyle,
  NarrativeVoice,
  Tense,
  AgentStatus,
  SessionType,
  ActionType,
  EntryType,
  FileType,
  ProcessingStatus,
  ChapterCountType,
  CreatedBy,
  EventType,
  ContentType,
  
  // Data structure aliases
  StoryStructureData,
  CharacterDataMap,
  ChapterScenesData,
  CharacterStatesData,
  PlotAdvancementData,
  AiAnalysisData,
  VoiceDataStructure,
  PersonalityTraitsData,
  CharacterArcData,
  RelationshipsData,
  QualityScoreData
} from '../types'