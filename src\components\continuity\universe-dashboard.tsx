'use client';

import { useState, useEffect, useCallback } from 'react';
import { logger } from '@/lib/services/logger';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Globe, 
  Users, 
  AlertTriangle, 
  CheckCircle, 
  BookOpen,
  Link,
  Clock,
  Settings
} from 'lucide-react';

interface SharedUniverse {
  id: string;
  name: string;
  description: string;
  projects: string[];
  sharedElements: SharedElement[];
  continuityIssues: number;
}

interface SharedElement {
  id: string;
  type: string;
  name: string;
  conflicts: number;
}

interface ContinuityIssue {
  id: string;
  description: string;
  severity: 'critical' | 'major' | 'minor';
}

interface UniverseDashboardProps {
  projectId: string;
  onSelectUniverse?: (universeId: string) => void;
}

export function UniverseDashboard({ projectId, onSelectUniverse }: UniverseDashboardProps) {
  const [universe, setUniverse] = useState<SharedUniverse | null>(null);
  const [continuityIssues, setContinuityIssues] = useState<ContinuityIssue[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUniverseData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/universe?projectId=${projectId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch universe data');
      }
      
      const data = await response.json();
      
      if (data.universe) {
        setUniverse(data.universe);
        setContinuityIssues(data.continuityIssues || []);
      } else {
        setUniverse(null);
        setContinuityIssues([]);
      }
    } catch (error) {
      logger.error('Error fetching universe data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load universe data');
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    fetchUniverseData();
  }, [fetchUniverseData]);

  const getElementIcon = (type: string) => {
    switch (type) {
      case 'character': return <Users className="w-4 h-4" />;
      case 'location': return <Globe className="w-4 h-4" />;
      case 'event': return <Clock className="w-4 h-4" />;
      default: return <BookOpen className="w-4 h-4" />;
    }
  };

  const getIssueSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'outline';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-32">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
            <p className="text-gray-500">Loading universe data...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-32">
          <Alert variant="destructive">
            <AlertTriangle className="w-4 h-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!universe) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-32">
          <div className="text-center">
            <Globe className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p className="text-gray-500">No shared universe detected</p>
            <Button size="sm" className="mt-2" onClick={() => onSelectUniverse?.('')}>
              Create Universe
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Universe Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Globe className="w-5 h-5 text-blue-600" />
              <div>
                <CardTitle>{universe.name}</CardTitle>
                <CardDescription>{universe.description}</CardDescription>
              </div>
            </div>
            <Badge variant={universe.continuityIssues > 0 ? "destructive" : "default"}>
              {universe.continuityIssues} issues
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{universe.projects.length}</div>
              <div className="text-sm text-gray-500">Connected Projects</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{universe.sharedElements.length}</div>
              <div className="text-sm text-gray-500">Shared Elements</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {universe.sharedElements.reduce((sum, el) => sum + el.conflicts, 0)}
              </div>
              <div className="text-sm text-gray-500">Active Conflicts</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Continuity Issues */}
      {continuityIssues.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-yellow-600" />
              <span>Continuity Issues</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {continuityIssues.map(issue => (
              <Alert key={issue.id}>
                <AlertTriangle className="w-4 h-4" />
                <AlertDescription className="flex items-center justify-between">
                  <span>{issue.description}</span>
                  <Badge variant={getIssueSeverityColor(issue.severity) as 'default' | 'secondary' | 'destructive' | 'outline'}>
                    {issue.severity}
                  </Badge>
                </AlertDescription>
              </Alert>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Shared Elements */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Link className="w-5 h-5 text-green-600" />
            <span>Shared Elements</span>
          </CardTitle>
          <CardDescription>
            Elements shared across projects in this universe
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {universe.sharedElements.map(element => (
              <div key={element.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  {getElementIcon(element.type)}
                  <div>
                    <div className="font-medium">{element.name}</div>
                    <div className="text-sm text-gray-500 capitalize">{element.type}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {element.conflicts > 0 ? (
                    <Badge variant="destructive">{element.conflicts} conflicts</Badge>
                  ) : (
                    <Badge variant="default">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Synced
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="w-5 h-5" />
            <span>Universe Management</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button size="sm" variant="outline">
              Add Shared Element
            </Button>
            <Button size="sm" variant="outline">
              Create Continuity Rule
            </Button>
            <Button size="sm" variant="outline">
              Sync Timeline
            </Button>
            <Button size="sm" variant="outline">
              Resolve Conflicts
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}