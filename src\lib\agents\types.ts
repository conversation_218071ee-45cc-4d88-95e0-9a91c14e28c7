import type { ProjectSettings } from '../types/project-settings';
import type { UserSubscription } from '../subscription';

export interface BookContext {
  projectId: string;
  userId?: string;
  subscription?: UserSubscription | null;
  settings?: ProjectSettings;
  projectSelections?: ProjectSettings;
  storyPrompt?: string;
  targetWordCount?: number;
  targetChapters?: number;
  storyStructure?: StoryStructure;
  characters?: CharacterProfiles;
  chapterOutlines?: ChapterOutlines;
  storyBible?: StoryBible;
  completedChapters?: ChapterContent[];
  currentChapter?: number;
  currentChapterContent?: ChapterContent;
  currentProgress?: {
    chaptersWritten: number;
    wordsWritten: number;
  };
  metadata?: {
    totalWordCount: number;
    chaptersCompleted: number;
    lastUpdated: string;
    qualityMetrics?: Record<string, unknown>;
  };
  customTaskResults?: Record<string, Record<string, unknown>>;
}

export interface StoryStructure {
  title: string;
  premise: string;
  genre: string;
  themes: string[];
  acts: Act[];
  conflicts: {
    type: 'internal' | 'external' | 'relational';
    description: string;
    resolution?: string;
  }[];
  timeline: TimelineEvent[];
  worldBuilding: WorldBuilding;
  plotPoints: PlotPoint[];
}

export interface Act {
  number: number;
  title: string;
  description: string;
  wordCountTarget: number;
  wordCount: number;
  chapters: number[];
  keyEvents: string[];
  themes: string[];
  characterArcs: CharacterArcProgress[];
}

export interface TimelineEvent {
  id: string;
  title: string;
  description: string;
  event: string;
  timestamp: string;
  actNumber: number;
  chapterNumber?: number;
  chapter?: number;
  importance: 'critical' | 'major' | 'minor';
}

export interface WorldBuilding {
  setting: {
    timeForPeriod: string;
    locations: Location[];
    culture: string;
    technology: string;
    magic?: MagicSystem;
  };
  rules: string[];
  history: HistoricalEvent[];
}

export interface Location {
  name: string;
  description: string;
  importance: 'primary' | 'secondary' | 'minor';
  atmosphere: string;
  keyFeatures: string[];
}

export interface MagicSystem {
  name: string;
  description: string;
  rules: string[];
  limitations: string[];
  practitioners: string[];
}

export interface HistoricalEvent {
  name: string;
  description: string;
  timestamp: string;
  impact: string;
}

export interface PlotPoint {
  id: string;
  title: string;
  description: string;
  actNumber: number;
  chapterNumber?: number;
  type: 'inciting_incident' | 'plot_point_1' | 'midpoint' | 'plot_point_2' | 'climax' | 'resolution' | 'other';
  consequences: string[];
}

export interface CharacterProfiles {
  protagonists: Character[];
  antagonists: Character[];
  supporting: Character[];
  relationships: Relationship[];
}

export interface Character {
  id: string;
  name: string;
  role: 'protagonist' | 'antagonist' | 'supporting' | 'minor';
  age?: number;
  appearance: string;
  description?: string;
  personality: {
    traits: string[];
    strengths: string[];
    weaknesses: string[];
    fears: string[];
    desires: string[];
    motivations?: string[];
    goals?: string[];
  };
  backstory: string;
  background?: string;
  motivation: string;
  motivations?: string[];
  arc: CharacterArc;
  voice: {
    speakingStyle: string;
    vocabulary: string;
    mannerisms: string[];
  };
  voiceCharacteristics?: string[];
  relationships: string[];
  physicalDescription?: string;
}

export interface CharacterArc {
  type: 'positive_change' | 'negative_change' | 'flat_arc' | 'corruption' | 'redemption';
  startingPoint: string;
  endingPoint: string;
  transformation?: string;
  keyMoments: CharacterArcMoment[];
  internalConflict: string;
  externalConflict: string;
}

export interface CharacterArcMoment {
  chapterNumber: number;
  description: string;
  emotionalState: string;
  growth: string;
}

export interface CharacterArcProgress {
  characterId: string;
  currentState: string;
  nextMilestone: string;
}

export interface Relationship {
  character1Id: string;
  character2Id: string;
  type: 'family' | 'romantic' | 'friendship' | 'mentor_mentee' | 'rivalry' | 'enemy' | 'professional';
  description: string;
  dynamics: string;
  evolution: RelationshipEvolution[];
}

export interface RelationshipEvolution {
  chapterNumber: number;
  change: string;
  reason: string;
}

export interface ChapterOutlines {
  chapters: ChapterOutline[];
  totalWordCount: number;
  estimatedReadingTime: number;
}

export interface AgentResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  context?: Partial<BookContext>;
  executionTime?: number;
  tokensUsed?: number;
}

export interface ChapterOutline {
  number: number;
  title: string;
  summary: string;
  wordCountTarget: number;
  scenes: Scene[];
  povCharacter: string;
  objectives: string[];
  conflicts: string[];
  resolutions: string[];
  cliffhanger?: string;
  characterStates: CharacterState[];
  plotAdvancement: string[];
}

export interface Scene {
  id: string;
  title: string;
  description: string;
  wordCountTarget: number;
  setting: string;
  characters: string[];
  purpose: string;
  conflict: string;
  outcome: string;
}

export interface CharacterState {
  characterId: string;
  emotionalState: string;
  physicalState: string;
  knowledge: string[];
  relationships: string;
}

export interface ChapterContent {
  chapterNumber: number;
  title: string;
  content: string;
  wordCount: number;
  scenes: SceneContent[];
  characterVoices: string[];
  themes: string[];
  continuityNotes: string[];
  continuityWarnings?: string[];
}

export interface SceneContent {
  id: string;
  content: string;
  wordCount: number;
  purpose: string;
}

export interface EditorialReview {
  chapterNumber: number;
  overallScore: number;
  consistency: ConsistencyCheck;
  quality: QualityAssessment;
  suggestions: EditorialSuggestion[];
  revisions: Revision[];
}

export interface ConsistencyCheck {
  characterVoices: boolean;
  plotContinuity: boolean;
  worldBuilding: boolean;
  timeline: boolean;
  relationships: boolean;
  issues: ConsistencyIssue[];
}

export interface ConsistencyIssue {
  type: 'character' | 'plot' | 'world' | 'timeline' | 'relationship';
  description: string;
  severity: 'low' | 'medium' | 'high';
  suggestion: string;
}

export interface QualityAssessment {
  prose: number;
  dialogue: number;
  pacing: number;
  description: number;
  characterization: number;
  overallEngagement: number;
}

export interface EditorialSuggestion {
  type: 'improvement' | 'correction' | 'enhancement';
  description: string;
  location: string;
  priority: 'low' | 'medium' | 'high';
}

export interface Revision {
  original: string;
  revised: string;
  reason: string;
  approved: boolean;
}

export interface StoryBible {
  projectId: string;
  lastUpdated: Date;
  
  structure: StoryStructure;
  characters: CharacterProfiles;
  world: WorldBuilding;
  timeline: TimelineEvent[];
  themes: ThemeTracker[];
  continuity: ContinuityTracker;
  style: StyleGuide;
}

export interface ThemeTracker {
  theme: string;
  occurrences: ThemeOccurrence[];
  development: string;
}

export interface ThemeOccurrence {
  chapterNumber: number;
  description: string;
  treatment: string;
}

export interface ContinuityTracker {
  characterStates: Map<string, CharacterState>;
  plotThreads: PlotThread[];
  establishedFacts: EstablishedFact[];
}

export interface PlotThread {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'resolved' | 'abandoned';
  introduction: number;
  resolution?: number;
  relatedCharacters: string[];
}

export interface EstablishedFact {
  id: string;
  description: string;
  chapterEstablished: number;
  category: 'character' | 'world' | 'plot' | 'relationship';
  importance: 'critical' | 'major' | 'minor';
}

export interface StyleGuide {
  voice: string;
  tone: string[];
  vocabulary: string;
  sentenceStructure: string;
  paragraphLength: string;
  dialogueStyle: string;
  descriptionStyle: string;
}