import { BaseService } from './base-service';
import { logger } from '@/lib/services/logger';
import { ServiceResponse } from './types';
import type { BookContext, CharacterProfile, TimelineEvent, ChapterContent } from '../agents/types';
import { qualityAnalyzer } from './quality-analyzer';
import { vercelAIClient } from '@/lib/ai/vercel-ai-client';
import { z } from 'zod';
import { config } from '@/lib/config';
import { getAIConfig } from '../config/ai-settings';
import { withRetry, ErrorContext } from '../services/error-handler';
import { withCircuitBreaker, CIRCUIT_BREAKER_PRESETS } from '../services/circuit-breaker';

interface ConsistencyIssue {
  type: 'character' | 'timeline' | 'plot' | 'world' | 'relationship' | 'style';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  location: string;
  suggestion: string;
}

interface ConsistencyReport {
  overallScore: number;
  issues: ConsistencyIssue[];
  characterConsistency: number;
  timelineConsistency: number;
  plotConsistency: number;
  worldConsistency: number;
  styleConsistency: number;
}

interface CharacterState {
  location: string;
  emotionalState: string;
  physicalState: string;
  relationships: Map<string, string>;
  knowledge: Set<string>;
  lastSeenChapter: number;
}

interface PlotThread {
  id: string;
  description: string;
  introduced: number;
  resolved?: number;
  status: 'active' | 'resolved' | 'abandoned';
  dependencies: string[];
}

export class ConsistencyValidator extends BaseService {
  private aiClient = vercelAIClient;
  private characterStates: Map<string, CharacterState> = new Map();
  private plotThreads: Map<string, PlotThread> = new Map();
  private worldRules: Set<string> = new Set();
  private establishedFacts: Map<string, { fact: string; chapter: number }> = new Map();

  constructor() {
    super({
      name: 'consistency-validator',
      version: '1.0.0',
      status: 'inactive',
      endpoints: ['/api/consistency/validate', '/api/consistency/check'],
      dependencies: ['quality-analyzer'],
      healthCheck: '/api/consistency/health'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, {
      status: `Tracking ${this.characterStates.size} characters, ${this.plotThreads.size} plot threads`
    });
  }

  async shutdown(): Promise<void> {
    this.characterStates.clear();
    this.plotThreads.clear();
    this.worldRules.clear();
    this.establishedFacts.clear();
    this.setStatus('inactive');
  }

  /**
   * Validate consistency across the entire book context
   */
  async validateBookConsistency(context: BookContext): Promise<ServiceResponse<ConsistencyReport>> {
    return this.withErrorHandling(async () => {
      // Initialize tracking structures
      this.initializeFromContext(context);

      const issues: ConsistencyIssue[] = [];

      // Validate character consistency
      const characterIssues = await this.validateCharacterConsistency(context);
      issues.push(...characterIssues);

      // Validate timeline consistency
      const timelineIssues = await this.validateTimelineConsistency(context);
      issues.push(...timelineIssues);

      // Validate plot consistency
      const plotIssues = await this.validatePlotConsistency(context);
      issues.push(...plotIssues);

      // Validate world consistency
      const worldIssues = await this.validateWorldConsistency(context);
      issues.push(...worldIssues);

      // Validate style consistency
      const styleIssues = await this.validateStyleConsistency(context);
      issues.push(...styleIssues);

      // Calculate scores
      const report: ConsistencyReport = {
        overallScore: this.calculateOverallScore(issues),
        issues: issues.sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity)),
        characterConsistency: this.calculateCategoryScore(issues, 'character'),
        timelineConsistency: this.calculateCategoryScore(issues, 'timeline'),
        plotConsistency: this.calculateCategoryScore(issues, 'plot'),
        worldConsistency: this.calculateCategoryScore(issues, 'world'),
        styleConsistency: this.calculateCategoryScore(issues, 'style')
      };

      return report;
    });
  }

  /**
   * Check consistency for new content against existing context
   */
  async checkNewContent(
    newContent: string,
    contentType: 'chapter' | 'scene' | 'dialogue',
    context: BookContext
  ): Promise<ServiceResponse<ConsistencyIssue[]>> {
    return this.withErrorHandling(async () => {
      this.initializeFromContext(context);
      const issues: ConsistencyIssue[] = [];

      // Use AI to analyze the new content for consistency
      const aiConfig = getAIConfig('ANALYSIS');
      
      const errorContext: ErrorContext = {
        operation: 'ConsistencyValidator.checkNewContent',
        projectId: context.projectId,
        metadata: {
          contentType,
          contentLength: newContent.length,
          model: aiConfig.model
        }
      };
      
      // Define schema for consistency issues
      const consistencyIssuesSchema = z.object({
        issues: z.array(z.object({
          type: z.enum(['character', 'timeline', 'plot', 'world', 'relationship', 'style']),
          severity: z.enum(['low', 'medium', 'high', 'critical']),
          description: z.string(),
          location: z.string(),
          suggestion: z.string()
        }))
      });
      
      const systemPrompt = 'You are an expert editor checking for consistency issues in a novel.';
      const userPrompt = this.buildConsistencyCheckPrompt(newContent, contentType, context);
      
      try {
        const result = await this.aiClient.generateObjectWithFallback(
          userPrompt,
          consistencyIssuesSchema,
          {
            model: aiConfig.model,
            temperature: aiConfig.temperature,
            maxTokens: aiConfig.max_tokens,
            systemPrompt
          },
          errorContext
        );
        
        issues.push(...result.issues);
      } catch (error) {
        logger.error('Consistency check failed:', error);
        // Continue with other checks even if AI fails
      }

      // Additional specific checks
      const characterIssues = this.checkCharacterConsistencyInContent(newContent, context);
      issues.push(...characterIssues);

      const timelineIssues = this.checkTimelineConsistencyInContent(newContent, context);
      issues.push(...timelineIssues);

      return issues;
    });
  }

  /**
   * Initialize tracking structures from context
   */
  private initializeFromContext(context: BookContext): void {
    // Initialize character states
    if (context.characters) {
      const allCharacters = [
        ...context.characters.protagonists,
        ...context.characters.antagonists,
        ...context.characters.supporting
      ];

      allCharacters.forEach(character => {
        this.characterStates.set(character.id, {
          location: '',
          emotionalState: character.personality.traits.join(', '),
          physicalState: 'healthy',
          relationships: new Map(),
          knowledge: new Set(),
          lastSeenChapter: 0
        });
      });
    }

    // Initialize world rules
    if (context.storyBible?.worldRules) {
      context.storyBible.worldRules.forEach(rule => this.worldRules.add(rule));
    }

    // Initialize plot threads from story structure
    if (context.storyStructure?.plotPoints) {
      context.storyStructure.plotPoints.forEach(point => {
        this.plotThreads.set(point.id, {
          id: point.id,
          description: point.description,
          introduced: point.chapterNumber || 1,
          status: 'active',
          dependencies: []
        });
      });
    }
  }

  /**
   * Validate character consistency
   */
  private async validateCharacterConsistency(context: BookContext): Promise<ConsistencyIssue[]> {
    const issues: ConsistencyIssue[] = [];

    if (!context.characters || !context.completedChapters) return issues;

    const allCharacters = [
      ...context.characters.protagonists,
      ...context.characters.antagonists,
      ...context.characters.supporting
    ];

    for (const character of allCharacters) {
      const characterIssues = await this.analyzeCharacterArc(character, context.completedChapters);
      issues.push(...characterIssues);
    }

    return issues;
  }

  /**
   * Analyze character arc for consistency
   */
  private async analyzeCharacterArc(
    character: CharacterProfile,
    chapters: ChapterContent[]
  ): Promise<ConsistencyIssue[]> {
    const issues: ConsistencyIssue[] = [];
    const characterMentions: { chapter: number; context: string }[] = [];

    // Find all mentions of the character
    chapters.forEach(chapter => {
      if (chapter.content.toLowerCase().includes(character.name.toLowerCase())) {
        characterMentions.push({
          chapter: chapter.chapterNumber,
          context: this.extractCharacterContext(character.name, chapter.content)
        });
      }
    });

    // Check for personality consistency
    if (characterMentions.length > 1) {
      for (let i = 1; i < characterMentions.length; i++) {
        const currentMention = characterMentions[i];
        const previousMention = characterMentions[i - 1];

        // Check if character behavior matches personality
        const behaviorIssues = this.checkCharacterBehavior(
          character,
          currentMention.context,
          currentMention.chapter
        );
        issues.push(...behaviorIssues);
      }
    }

    // Check for arc progression
    if (character.arc && characterMentions.length > 0) {
      const firstMention = characterMentions[0];
      const lastMention = characterMentions[characterMentions.length - 1];

      if (!this.isArcProgressing(character.arc, firstMention.context, lastMention.context)) {
        issues.push({
          type: 'character',
          severity: 'medium',
          description: `${character.name}'s character arc shows no progression`,
          location: `Chapters ${firstMention.chapter} to ${lastMention.chapter}`,
          suggestion: `Show gradual development from ${character.arc.startingPoint} to ${character.arc.endingPoint}`
        });
      }
    }

    return issues;
  }

  /**
   * Check character behavior against personality
   */
  private checkCharacterBehavior(
    character: CharacterProfile,
    context: string,
    chapter: number
  ): ConsistencyIssue[] {
    const issues: ConsistencyIssue[] = [];
    const contextLower = context.toLowerCase();

    // Check for contradictory behaviors
    character.personality.traits.forEach(trait => {
      const traitLower = trait.toLowerCase();
      
      // Simple contradiction checks
      const contradictions: { [key: string]: string[] } = {
        'shy': ['boldly', 'confidently', 'assertively'],
        'brave': ['cowered', 'fled', 'trembled'],
        'honest': ['lied', 'deceived', 'misled'],
        'kind': ['cruel', 'harsh', 'callous'],
        'intelligent': ['foolishly', 'stupidly', 'ignorantly']
      };

      if (contradictions[traitLower]) {
        contradictions[traitLower].forEach(contradiction => {
          if (contextLower.includes(contradiction)) {
            issues.push({
              type: 'character',
              severity: 'medium',
              description: `${character.name} is described as ${trait} but acts ${contradiction}`,
              location: `Chapter ${chapter}`,
              suggestion: `Ensure ${character.name}'s actions align with their ${trait} personality trait`
            });
          }
        });
      }
    });

    return issues;
  }

  /**
   * Validate timeline consistency
   */
  private async validateTimelineConsistency(context: BookContext): Promise<ConsistencyIssue[]> {
    const issues: ConsistencyIssue[] = [];

    if (!context.storyStructure?.timeline || !context.completedChapters) return issues;

    const timeline = context.storyStructure.timeline;
    const sortedEvents = [...timeline].sort((a, b) => {
      const chapterA = a.chapterNumber || a.chapter || 0;
      const chapterB = b.chapterNumber || b.chapter || 0;
      return chapterA - chapterB;
    });

    // Check for temporal inconsistencies
    for (let i = 1; i < sortedEvents.length; i++) {
      const currentEvent = sortedEvents[i];
      const previousEvent = sortedEvents[i - 1];

      // Check if events are in wrong order
      if (this.isTemporalInconsistency(previousEvent, currentEvent)) {
        issues.push({
          type: 'timeline',
          severity: 'high',
          description: `Timeline inconsistency: "${currentEvent.title}" appears to happen before "${previousEvent.title}"`,
          location: `Chapter ${currentEvent.chapterNumber || currentEvent.chapter}`,
          suggestion: 'Reorder events or adjust temporal references to maintain chronological consistency'
        });
      }
    }

    // Check for missing timeline references in chapters
    context.completedChapters.forEach(chapter => {
      const expectedEvents = timeline.filter(event => 
        (event.chapterNumber || event.chapter) === chapter.chapterNumber
      );

      expectedEvents.forEach(event => {
        if (!chapter.content.toLowerCase().includes(event.event.toLowerCase().substring(0, 20))) {
          issues.push({
            type: 'timeline',
            severity: 'low',
            description: `Expected timeline event "${event.title}" not clearly referenced`,
            location: `Chapter ${chapter.chapterNumber}`,
            suggestion: `Include reference to "${event.event}" in the chapter`
          });
        }
      });
    });

    return issues;
  }

  /**
   * Validate plot consistency
   */
  private async validatePlotConsistency(context: BookContext): Promise<ConsistencyIssue[]> {
    const issues: ConsistencyIssue[] = [];

    if (!context.storyStructure?.plotPoints || !context.completedChapters) return issues;

    // Track plot thread resolution
    const plotStatus = new Map<string, { introduced: boolean; resolved: boolean; lastMention: number }>();

    context.storyStructure.plotPoints.forEach(point => {
      plotStatus.set(point.id, { introduced: false, resolved: false, lastMention: 0 });
    });

    // Analyze chapters for plot thread mentions
    context.completedChapters.forEach(chapter => {
      context.storyStructure!.plotPoints.forEach(point => {
        if (this.isPlotPointMentioned(point, chapter.content)) {
          const status = plotStatus.get(point.id)!;
          status.introduced = true;
          status.lastMention = chapter.chapterNumber;

          if (point.resolution && chapter.content.toLowerCase().includes(point.resolution.toLowerCase())) {
            status.resolved = true;
          }
        }
      });
    });

    // Check for unresolved plot threads
    plotStatus.forEach((status, plotId) => {
      const plotPoint = context.storyStructure!.plotPoints.find(p => p.id === plotId)!;

      if (status.introduced && !status.resolved && plotPoint.resolution) {
        issues.push({
          type: 'plot',
          severity: 'medium',
          description: `Plot thread "${plotPoint.title}" introduced but not resolved`,
          location: `Last mentioned in Chapter ${status.lastMention}`,
          suggestion: `Resolve the plot thread or plan for resolution in upcoming chapters`
        });
      }

      if (!status.introduced && plotPoint.type === 'major') {
        issues.push({
          type: 'plot',
          severity: 'high',
          description: `Major plot point "${plotPoint.title}" not introduced`,
          location: `Expected by Chapter ${plotPoint.chapterNumber || 'unknown'}`,
          suggestion: `Introduce this plot point as it's crucial to the story`
        });
      }
    });

    return issues;
  }

  /**
   * Validate world consistency
   */
  private async validateWorldConsistency(context: BookContext): Promise<ConsistencyIssue[]> {
    const issues: ConsistencyIssue[] = [];

    if (!context.storyBible?.worldRules || !context.completedChapters) return issues;

    // Check each chapter against world rules
    for (const chapter of context.completedChapters) {
      for (const rule of context.storyBible.worldRules) {
        const violations = this.checkWorldRuleViolation(rule, chapter.content);
        violations.forEach(violation => {
          issues.push({
            type: 'world',
            severity: 'medium',
            description: `World rule violation: ${violation}`,
            location: `Chapter ${chapter.chapterNumber}`,
            suggestion: `Ensure content adheres to established world rule: "${rule}"`
          });
        });
      }
    }

    return issues;
  }

  /**
   * Validate style consistency
   */
  private async validateStyleConsistency(context: BookContext): Promise<ConsistencyIssue[]> {
    const issues: ConsistencyIssue[] = [];

    if (!context.completedChapters || context.completedChapters.length < 2) return issues;

    // Compare style between chapters
    for (let i = 1; i < context.completedChapters.length; i++) {
      const currentChapter = context.completedChapters[i];
      const previousChapter = context.completedChapters[i - 1];

      // Use quality analyzer to compare style
      if (qualityAnalyzer.getStatus() === 'active') {
        const comparison = await qualityAnalyzer.compareContent(
          previousChapter.content.substring(0, 2000),
          currentChapter.content.substring(0, 2000),
          'style'
        );

        if (comparison.success && comparison.data) {
          if (comparison.data.styleDifference > 30) {
            issues.push({
              type: 'style',
              severity: 'low',
              description: `Significant style difference detected between chapters`,
              location: `Chapters ${previousChapter.chapterNumber} and ${currentChapter.chapterNumber}`,
              suggestion: 'Review and adjust writing style for consistency'
            });
          }

          comparison.data.issues.forEach(issue => {
            issues.push({
              type: 'style',
              severity: 'low',
              description: issue,
              location: `Chapter ${currentChapter.chapterNumber}`,
              suggestion: 'Maintain consistent narrative voice and style'
            });
          });
        }
      }
    }

    return issues;
  }

  // Helper methods

  private extractCharacterContext(characterName: string, content: string): string {
    const sentences = content.split(/[.!?]+/);
    const relevantSentences = sentences.filter(s => 
      s.toLowerCase().includes(characterName.toLowerCase())
    );
    return relevantSentences.slice(0, 3).join('. ');
  }

  private isArcProgressing(
    arc: CharacterProfile['arc'],
    firstContext: string,
    lastContext: string
  ): boolean {
    // Simple check - would be more sophisticated in production
    const startKeywords = arc.startingPoint.toLowerCase().split(' ');
    const endKeywords = arc.endingPoint.toLowerCase().split(' ');

    const firstHasStart = startKeywords.some(keyword => firstContext.toLowerCase().includes(keyword));
    const lastHasEnd = endKeywords.some(keyword => lastContext.toLowerCase().includes(keyword));

    return firstHasStart || lastHasEnd;
  }

  private isTemporalInconsistency(event1: TimelineEvent, event2: TimelineEvent): boolean {
    // Check for temporal keywords that indicate order
    const beforeKeywords = ['before', 'prior to', 'earlier than', 'preceded'];
    const afterKeywords = ['after', 'following', 'later than', 'succeeded'];

    const desc1Lower = event1.description.toLowerCase();
    const desc2Lower = event2.description.toLowerCase();

    // Check if event2 claims to be before event1
    return beforeKeywords.some(keyword => 
      desc2Lower.includes(keyword) && desc2Lower.includes(event1.title.toLowerCase())
    );
  }

  private isPlotPointMentioned(plotPoint: { title: string; description: string }, content: string): boolean {
    const contentLower = content.toLowerCase();
    const keywords = plotPoint.description.toLowerCase().split(' ')
      .filter(word => word.length > 4); // Only check meaningful words

    // Check if at least 30% of keywords are mentioned
    const mentionedKeywords = keywords.filter(keyword => contentLower.includes(keyword));
    return mentionedKeywords.length >= keywords.length * 0.3;
  }

  private checkWorldRuleViolation(rule: string, content: string): string[] {
    const violations: string[] = [];
    const ruleLower = rule.toLowerCase();
    const contentLower = content.toLowerCase();

    // Simple rule checking - would be more sophisticated in production
    if (ruleLower.includes('no magic') && contentLower.match(/\b(magic|spell|enchant|wizard|sorcerer)\b/)) {
      violations.push('Content mentions magic in a non-magic world');
    }

    if (ruleLower.includes('medieval') && contentLower.match(/\b(computer|internet|phone|car|electricity)\b/)) {
      violations.push('Modern technology mentioned in medieval setting');
    }

    if (ruleLower.includes('peaceful') && contentLower.match(/\b(war|battle|fight|violence|blood)\b/)) {
      violations.push('Violence mentioned in peaceful world');
    }

    return violations;
  }

  private checkCharacterConsistencyInContent(content: string, context: BookContext): ConsistencyIssue[] {
    const issues: ConsistencyIssue[] = [];

    if (!context.characters) return issues;

    const allCharacters = [
      ...context.characters.protagonists,
      ...context.characters.antagonists,
      ...context.characters.supporting
    ];

    // Check for character name consistency
    allCharacters.forEach(character => {
      const nameParts = character.name.split(' ');
      const firstName = nameParts[0];
      const lastName = nameParts[nameParts.length - 1];

      // Check for misspellings
      const possibleMisspellings = this.generateMisspellings(character.name);
      possibleMisspellings.forEach(misspelling => {
        if (content.includes(misspelling)) {
          issues.push({
            type: 'character',
            severity: 'low',
            description: `Possible misspelling of character name "${character.name}" as "${misspelling}"`,
            location: 'New content',
            suggestion: `Use correct spelling: "${character.name}"`
          });
        }
      });
    });

    return issues;
  }

  private checkTimelineConsistencyInContent(content: string, context: BookContext): ConsistencyIssue[] {
    const issues: ConsistencyIssue[] = [];

    // Check for anachronisms based on time period
    if (context.settings?.timePeriod) {
      const anachronisms = this.checkAnachronisms(content, context.settings.timePeriod);
      anachronisms.forEach(anachronism => {
        issues.push({
          type: 'timeline',
          severity: 'medium',
          description: `Anachronism detected: "${anachronism}" in ${context.settings!.timePeriod} setting`,
          location: 'New content',
          suggestion: 'Remove or replace with period-appropriate alternative'
        });
      });
    }

    return issues;
  }

  private generateMisspellings(name: string): string[] {
    const misspellings: string[] = [];
    
    // Common typos
    for (let i = 0; i < name.length - 1; i++) {
      // Transposition
      const transposed = name.slice(0, i) + name[i + 1] + name[i] + name.slice(i + 2);
      if (transposed !== name) misspellings.push(transposed);
    }

    // Missing letters
    for (let i = 0; i < name.length; i++) {
      const missing = name.slice(0, i) + name.slice(i + 1);
      misspellings.push(missing);
    }

    return misspellings;
  }

  private checkAnachronisms(content: string, timePeriod: string): string[] {
    const anachronisms: string[] = [];
    const contentLower = content.toLowerCase();

    const modernTerms = [
      'computer', 'internet', 'smartphone', 'email', 'website',
      'television', 'microwave', 'refrigerator', 'airplane', 'helicopter'
    ];

    const periodRestrictions: { [key: string]: string[] } = {
      'medieval': modernTerms,
      'victorian': [...modernTerms, 'television', 'computer'],
      'ancient': modernTerms,
      'renaissance': modernTerms
    };

    const restrictions = periodRestrictions[timePeriod.toLowerCase()] || [];
    
    restrictions.forEach(term => {
      if (contentLower.includes(term)) {
        anachronisms.push(term);
      }
    });

    return anachronisms;
  }

  private buildConsistencyCheckPrompt(
    newContent: string,
    contentType: string,
    context: BookContext
  ): string {
    return `Check this new ${contentType} for consistency issues:

${newContent}

Context:
- Characters: ${JSON.stringify(context.characters?.protagonists.map(c => c.name))}
- Timeline: ${JSON.stringify(context.storyStructure?.timeline.slice(0, 5))}
- World Rules: ${JSON.stringify(context.storyBible?.worldRules?.slice(0, 5))}
- Setting: ${context.settings?.timePeriod}, ${context.settings?.worldType}

Look for:
1. Character behavior inconsistent with established personality
2. Timeline contradictions or anachronisms
3. Plot holes or unresolved threads
4. World rule violations
5. Relationship inconsistencies
6. Style or tone shifts`;
  }

  private getSeverityWeight(severity: ConsistencyIssue['severity']): number {
    const weights = { critical: 4, high: 3, medium: 2, low: 1 };
    return weights[severity];
  }

  private calculateOverallScore(issues: ConsistencyIssue[]): number {
    if (issues.length === 0) return 100;

    const totalWeight = issues.reduce((sum, issue) => sum + this.getSeverityWeight(issue.severity), 0);
    const maxPossibleWeight = issues.length * 4; // All critical
    
    return Math.max(0, Math.round(100 - (totalWeight / maxPossibleWeight) * 100));
  }

  private calculateCategoryScore(issues: ConsistencyIssue[], category: ConsistencyIssue['type']): number {
    const categoryIssues = issues.filter(issue => issue.type === category);
    return this.calculateOverallScore(categoryIssues);
  }
}

// Singleton instance
export const consistencyValidator = new ConsistencyValidator();