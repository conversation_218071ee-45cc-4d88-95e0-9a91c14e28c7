# Stripe Sync Engine Implementation

This document describes the implementation of the Supabase Stripe Sync Engine in BookScribe, which automatically syncs Stripe webhook data to your Postgres database for better performance and analytics.

## Overview

The Stripe Sync Engine is a webhook listener that transforms Stripe webhooks into structured Postgres inserts/updates. It provides:

- **Lower latency**: Avoid round-trips to the Stripe API
- **Better joins**: Query subscriptions, invoices, and charges together
- **Custom logic**: Build fraud checks, billing dashboards, and dunning workflows

## Installation & Setup

### 1. Install Dependencies

The required packages are already installed:
- `@supabase/stripe-sync-engine` - The sync engine library
- `dotenv` - Environment variable loading
- `tsx` - TypeScript execution

### 2. Environment Variables

Add these to your `.env.local` file:

```env
# Stripe Configuration (already exists)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Supabase Configuration (already exists)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 3. Run Database Setup

Initialize the Stripe schema and tables:

```bash
npm run stripe:setup
```

This will:
- Create a `stripe` schema in your database
- Set up all necessary tables for Stripe data
- Configure proper indexes and relationships

## Components

### 1. Webhook Handler (`/api/stripe/webhook`)

**File**: `src/app/api/stripe/webhook/route.ts`

Processes incoming Stripe webhooks and syncs data to the database:

```typescript
// Automatically processes webhooks and syncs to database
await stripeSync.processWebhook(rawBody, stripeSignature)
```

### 2. Sync Service (`StripeSyncService`)

**File**: `src/lib/stripe/sync-service.ts`

Provides utilities for querying synced Stripe data:

```typescript
// Get customer by email
const customer = await StripeSyncService.getCustomerByEmail('<EMAIL>')

// Get active subscription
const subscription = await StripeSyncService.getActiveSubscription(customerId)

// Get customer invoices
const invoices = await StripeSyncService.getCustomerInvoices(customerId)
```

### 3. React Hooks

**File**: `src/hooks/use-stripe-sync.ts`

React hooks for accessing synced data in components:

```typescript
// Use in components
const { customer, loading } = useStripeCustomer(userEmail)
const { subscription } = useActiveSubscription(customer?.id)
const { invoices } = useCustomerInvoices(customer?.id)
```

### 4. Admin Dashboard

**File**: `src/components/admin/stripe-dashboard.tsx`

Admin interface for monitoring Stripe data:

- Overview metrics
- Failed payments monitoring
- Subscription management
- Customer insights

## Stripe Webhook Configuration

### 1. Create Webhook Endpoint

In your Stripe Dashboard:

1. Go to **Developers** → **Webhooks**
2. Click **Add endpoint**
3. Set URL to: `https://your-domain.com/api/stripe/webhook`
4. Select events to listen for (recommended):
   - `customer.created`
   - `customer.updated`
   - `customer.deleted`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.created`
   - `invoice.updated`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`

### 2. Configure Webhook Secret

1. Copy the webhook signing secret from Stripe Dashboard
2. Add it to your `.env.local` as `STRIPE_WEBHOOK_SECRET`

## Database Schema

The sync engine creates tables in the `stripe` schema:

- `stripe.customers` - Customer data
- `stripe.subscriptions` - Subscription information
- `stripe.invoices` - Invoice records
- `stripe.payment_intents` - Payment attempts
- `stripe.charges` - Charge records
- And more...

## Usage Examples

### Check User Subscription Status

```typescript
import { useStripeCustomer, useActiveSubscription } from '@/hooks/use-stripe-sync'

function UserSubscriptionStatus({ userEmail }: { userEmail: string }) {
  const { customer } = useStripeCustomer(userEmail)
  const { subscription } = useActiveSubscription(customer?.id)
  
  if (!subscription) {
    return <div>No active subscription</div>
  }
  
  return (
    <div>
      <p>Status: {subscription.status}</p>
      <p>Next billing: {new Date(subscription.current_period_end * 1000).toLocaleDateString()}</p>
    </div>
  )
}
```

### Monitor Failed Payments

```typescript
import { useFailedPayments } from '@/hooks/use-stripe-sync'

function FailedPaymentsAlert() {
  const { failedPayments } = useFailedPayments(10)
  
  if (failedPayments.length === 0) return null
  
  return (
    <div className="alert alert-warning">
      {failedPayments.length} failed payments require attention
    </div>
  )
}
```

### Custom Analytics Query

```typescript
import { StripeSyncService } from '@/lib/stripe/sync-service'

// Custom service method for analytics
export async function getMonthlyRevenue() {
  const { data } = await supabase
    .from('stripe.invoices')
    .select('amount_paid, created')
    .eq('status', 'paid')
    .gte('created', Math.floor(Date.now() / 1000) - 30 * 24 * 60 * 60) // Last 30 days
  
  return data?.reduce((total, invoice) => total + invoice.amount_paid, 0) || 0
}
```

## Benefits

### 1. Performance
- No API rate limits
- Instant data access
- Complex queries with joins

### 2. Reliability
- Automatic retry logic
- Webhook verification
- Data consistency

### 3. Analytics
- Historical data analysis
- Custom reporting
- Real-time dashboards

## Monitoring

### Health Checks

Monitor webhook processing:

```typescript
// Check recent webhook processing
const recentWebhooks = await supabase
  .from('stripe.events')
  .select('*')
  .order('created', { ascending: false })
  .limit(10)
```

### Error Handling

The webhook handler includes comprehensive error handling:

- Signature verification
- Duplicate event handling
- Database transaction safety
- Detailed logging

## Security

- Webhook signature verification
- Service role key for database access
- Schema isolation (`stripe` schema)
- Input validation and sanitization

## Troubleshooting

### Common Issues

1. **Webhook signature verification fails**
   - Check `STRIPE_WEBHOOK_SECRET` is correct
   - Ensure raw body is passed to verification

2. **Database connection errors**
   - Verify `SUPABASE_SERVICE_ROLE_KEY` is set
   - Check database URL construction

3. **Missing webhook events**
   - Verify webhook endpoint is configured in Stripe
   - Check webhook event selection

### Debugging

Enable debug logging by setting:

```env
DEBUG=stripe-sync:*
```

## Next Steps

1. Run the setup script: `npm run stripe:setup`
2. Configure Stripe webhooks
3. Test with a few webhook events
4. Monitor the admin dashboard
5. Implement custom analytics as needed

The Stripe Sync Engine provides a solid foundation for building advanced billing features and analytics in BookScribe.
