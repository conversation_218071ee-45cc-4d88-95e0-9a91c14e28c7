# BookScribe AI - Docker Implementation Guide

This guide covers how to build, deploy, and manage BookScribe AI using Docker containers.

## Quick Start

### Prerequisites
- Docker installed and running
- Docker Compose (included with Docker Desktop)
- `.env.local` file with your environment variables

### 1. Setup Environment Variables
```bash
# Copy the example environment file
cp .env.example .env.local

# Edit .env.local with your actual API keys and configuration
# Required variables:
# - OPENAI_API_KEY
# - NEXT_PUBLIC_SUPABASE_URL
# - NEXT_PUBLIC_SUPABASE_ANON_KEY
# - SUPABASE_SERVICE_ROLE_KEY
# - STRIPE_SECRET_KEY
# - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
```

### 2. Build and Run (Production)
```bash
# Using the build script (recommended)
./docker-build.sh

# Or manually with docker-compose
docker-compose up -d

# Or manually with docker
docker build -t bookscribe-ai .
docker run -d --name bookscribe-app -p 3000:3000 --env-file .env.local bookscribe-ai
```

### 3. Build and Run (Development)
```bash
# Using the build script
./docker-build.sh -e development

# Or manually with docker-compose
docker-compose -f docker-compose.dev.yml up -d
```

## Docker Files Overview

### Core Files
- `Dockerfile` - Multi-stage production build
- `Dockerfile.dev` - Development environment
- `docker-compose.yml` - Production orchestration
- `docker-compose.dev.yml` - Development orchestration
- `.dockerignore` - Files to exclude from build context

### Build Scripts
- `docker-build.sh` - Linux/macOS build script
- `docker-build.ps1` - Windows PowerShell build script

## Production Deployment

### Building for Production
```bash
# Build production image
docker build -t bookscribe-ai:latest .

# Build with specific tag
docker build -t bookscribe-ai:v1.0.0 .

# Build and push to registry
./docker-build.sh -t v1.0.0 -r your-registry.com -p
```

### Running in Production
```bash
# Using docker-compose (recommended)
docker-compose up -d

# Using docker run
docker run -d \
  --name bookscribe-app \
  -p 3000:3000 \
  --env-file .env.local \
  --restart unless-stopped \
  bookscribe-ai:latest
```

### Health Checks
The container includes built-in health checks:
```bash
# Check container health
docker ps

# View health check logs
docker inspect bookscribe-app | grep -A 10 Health
```

## Development Workflow

### Development Container
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop development environment
docker-compose -f docker-compose.dev.yml down
```

### Hot Reloading
The development container supports hot reloading:
- Source code is mounted as a volume
- Changes are reflected immediately
- Node modules are cached for performance

## Environment Variables

### Required Variables
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Stripe
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Optional Variables
```env
# Development
NEXT_PUBLIC_DEV_BYPASS_AUTH=false
DEV_USER_EMAIL=<EMAIL>

# Admin
ADMIN_EMAILS=<EMAIL>

# Webhooks
SUPABASE_WEBHOOK_SECRET=your_webhook_secret
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
```

## Container Management

### Useful Commands
```bash
# View running containers
docker ps

# View all containers
docker ps -a

# View container logs
docker logs bookscribe-app

# Follow logs in real-time
docker logs -f bookscribe-app

# Execute commands in container
docker exec -it bookscribe-app sh

# Stop container
docker stop bookscribe-app

# Remove container
docker rm bookscribe-app

# View container resource usage
docker stats bookscribe-app
```

### Docker Compose Commands
```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f

# Rebuild and restart
docker-compose up -d --build

# Scale services (if needed)
docker-compose up -d --scale bookscribe=3
```

## Troubleshooting

### Common Issues

#### 1. Container Won't Start
```bash
# Check logs for errors
docker logs bookscribe-app

# Common causes:
# - Missing environment variables
# - Port already in use
# - Invalid configuration
```

#### 2. Health Check Failing
```bash
# Test health endpoint manually
curl http://localhost:3000/api/health?basic=true

# Check if application is responding
docker exec bookscribe-app curl -f http://localhost:3000/api/health?basic=true
```

#### 3. Build Failures
```bash
# Clear Docker cache
docker system prune -a

# Rebuild without cache
docker build --no-cache -t bookscribe-ai .

# Check disk space
docker system df
```

#### 4. Performance Issues
```bash
# Monitor resource usage
docker stats

# Check container limits
docker inspect bookscribe-app | grep -A 10 Resources

# Optimize image size
docker images | grep bookscribe-ai
```

### Debugging
```bash
# Access container shell
docker exec -it bookscribe-app sh

# Check environment variables
docker exec bookscribe-app env

# Check file system
docker exec bookscribe-app ls -la

# Check processes
docker exec bookscribe-app ps aux
```

## Security Considerations

### Best Practices
1. **Never include secrets in images**
   - Use environment variables or secrets management
   - Add sensitive files to `.dockerignore`

2. **Run as non-root user**
   - Images use `nextjs` user (UID 1001)
   - Follows security best practices

3. **Keep images updated**
   - Regularly rebuild with latest base images
   - Monitor for security vulnerabilities

4. **Network security**
   - Use Docker networks for service communication
   - Limit exposed ports

### Production Security
```bash
# Scan image for vulnerabilities
docker scan bookscribe-ai:latest

# Use specific tags, not 'latest'
docker build -t bookscribe-ai:v1.0.0 .

# Limit container resources
docker run --memory=1g --cpus=1 bookscribe-ai:v1.0.0
```

## Monitoring and Logging

### Application Logs
```bash
# View application logs
docker logs bookscribe-app

# Follow logs with timestamps
docker logs -f -t bookscribe-app

# View last 100 lines
docker logs --tail 100 bookscribe-app
```

### Health Monitoring
```bash
# Check health status
docker inspect bookscribe-app | grep -A 5 Health

# Monitor with docker-compose
docker-compose ps
```

## Deployment Platforms

### Docker Hub
```bash
# Tag for Docker Hub
docker tag bookscribe-ai:latest username/bookscribe-ai:latest

# Push to Docker Hub
docker push username/bookscribe-ai:latest
```

### AWS ECR
```bash
# Login to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-east-1.amazonaws.com

# Tag for ECR
docker tag bookscribe-ai:latest 123456789012.dkr.ecr.us-east-1.amazonaws.com/bookscribe-ai:latest

# Push to ECR
docker push 123456789012.dkr.ecr.us-east-1.amazonaws.com/bookscribe-ai:latest
```

### Google Container Registry
```bash
# Configure Docker for GCR
gcloud auth configure-docker

# Tag for GCR
docker tag bookscribe-ai:latest gcr.io/project-id/bookscribe-ai:latest

# Push to GCR
docker push gcr.io/project-id/bookscribe-ai:latest
```

## Performance Optimization

### Image Size Optimization
- Multi-stage builds reduce final image size
- `.dockerignore` excludes unnecessary files
- Alpine Linux base image is lightweight

### Runtime Optimization
- Node.js production mode
- Standalone Next.js output
- Optimized webpack configuration

### Caching
- Docker layer caching
- npm cache optimization
- Next.js build cache

## Support

For issues related to Docker deployment:
1. Check the troubleshooting section above
2. Review container logs
3. Verify environment configuration
4. Test health endpoints

For application-specific issues, refer to the main README.md file.
