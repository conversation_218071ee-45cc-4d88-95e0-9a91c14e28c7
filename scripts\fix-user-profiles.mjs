import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { persistSession: false }
});

async function fixUserProfiles() {
  console.log('🔧 Fixing user profiles...\n');

  try {
    // 1. Get all users from auth.users
    console.log('📋 Fetching all authenticated users...');
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('❌ Error fetching auth users:', authError.message);
      return;
    }

    console.log(`✅ Found ${authUsers.users.length} authenticated users\n`);

    // 2. Get existing profiles
    console.log('📋 Fetching existing profiles...');
    const { data: existingProfiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id');
    
    if (profilesError) {
      console.error('❌ Error fetching profiles:', profilesError.message);
      return;
    }

    const existingProfileIds = new Set(existingProfiles?.map(p => p.id) || []);
    console.log(`✅ Found ${existingProfiles?.length || 0} existing profiles\n`);

    // 3. Create missing profiles
    const missingUsers = authUsers.users.filter(user => !existingProfileIds.has(user.id));
    
    if (missingUsers.length === 0) {
      console.log('✅ All users already have profiles!');
      return;
    }

    console.log(`🔨 Creating profiles for ${missingUsers.length} users...\n`);

    for (const user of missingUsers) {
      console.log(`Creating profile for user: ${user.email} (${user.id})`);
      
      const profileData = {
        id: user.id,
        email: user.email,
        full_name: user.user_metadata?.full_name || user.user_metadata?.name || '',
        avatar_url: user.user_metadata?.avatar_url || '',
        writing_goals: {
          daily_words: 1000,
          weekly_hours: 10,
          genre_focus: 'Fiction'
        },
        preferences: {
          public_profile: true,
          email_notifications: true,
          writing_reminders: true,
          beta_features: false
        }
      };

      const { error: insertError } = await supabase
        .from('profiles')
        .insert(profileData);

      if (insertError) {
        console.error(`❌ Error creating profile for ${user.email}:`, insertError.message);
      } else {
        console.log(`✅ Created profile for ${user.email}`);
      }
    }

    console.log('\n🎉 Profile creation complete!');

    // 4. Verify the specific user from the error
    const problemUserId = 'ac3a6991-ee87-4b5f-b461-b133b9410687';
    console.log(`\n🔍 Checking specific user: ${problemUserId}`);
    
    const { data: specificProfile, error: specificError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', problemUserId)
      .single();

    if (specificError) {
      console.error('❌ Specific user profile check failed:', specificError.message);
    } else {
      console.log('✅ Specific user profile exists:', {
        id: specificProfile.id,
        email: specificProfile.email,
        full_name: specificProfile.full_name
      });
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

fixUserProfiles();
