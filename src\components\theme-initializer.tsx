'use client'

import { useEffect } from 'react'

export function ThemeInitializer() {
  useEffect(() => {
    // Ensure theme is applied on mount
    const initTheme = () => {
      const savedTheme = localStorage.getItem('bookscribe-theme')
      const htmlElement = document.documentElement
      
      // Remove any existing theme classes
      htmlElement.className = htmlElement.className
        .replace(/writers-sanctuary-light|forest-manuscript-light|evening-study-dark|midnight-ink-dark|light|dark/g, '')
        .trim()
      
      if (savedTheme) {
        // Apply saved theme
        htmlElement.classList.add(savedTheme)
        if (savedTheme.includes('dark')) {
          htmlElement.classList.add('dark')
        }
      } else {
        // Apply default theme
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        if (prefersDark) {
          htmlElement.classList.add('evening-study-dark', 'dark')
          localStorage.setItem('bookscribe-theme', 'evening-study-dark')
        } else {
          htmlElement.classList.add('writers-sanctuary-light')
          localStorage.setItem('bookscribe-theme', 'writers-sanctuary-light')
        }
      }
      
      // Also set data-theme attribute for compatibility
      htmlElement.setAttribute('data-theme', savedTheme || (prefersDark ? 'evening-study-dark' : 'writers-sanctuary-light'))
    }
    
    // Initialize immediately
    initTheme()
    
    // Also initialize on storage changes (for multi-tab sync)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'bookscribe-theme') {
        initTheme()
      }
    }
    
    window.addEventListener('storage', handleStorageChange)
    
    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])
  
  return null
}