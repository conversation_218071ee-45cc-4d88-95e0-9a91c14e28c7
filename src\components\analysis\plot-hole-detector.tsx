'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  ChevronDown, 
  ChevronUp,
  Check,
  X,
  ExternalLink
} from 'lucide-react';
import type { PlotHole } from '@/lib/types/analysis';

interface PlotHoleDetectorProps {
  plotHoles: PlotHole[];
  projectId: string;
  className?: string;
}

// Utility functions for plot hole analysis
const getSeverityIcon = (severity: string) => {
  switch (severity) {
    case 'high':
      return <AlertTriangle className="w-4 h-4 text-red-500" />;
    case 'medium':
      return <AlertCircle className="w-4 h-4 text-yellow-500" />;
    case 'low':
      return <Info className="w-4 h-4 text-blue-500" />;
    default:
      return <Info className="w-4 h-4 text-gray-500" />;
  }
};

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case 'high':
      return 'destructive';
    case 'medium':
      return 'secondary';
    case 'low':
      return 'outline';
    default:
      return 'outline';
  }
};

const getTypeDescription = (type: string) => {
  switch (type) {
    case 'character_inconsistency':
      return 'Character behavior or traits conflict with previous descriptions';
    case 'timeline_error':
      return 'Events occur in an impossible or illogical sequence';
    case 'plot_contradiction':
      return 'Plot elements contradict each other';
    case 'world_building_error':
      return 'Inconsistencies in the story world or setting';
    case 'missing_setup':
      return 'Plot elements lack proper setup or foreshadowing';
    case 'logical_gap':
      return 'Logical inconsistencies in cause and effect';
    default:
      return 'Potential inconsistency detected';
  }
};

export function PlotHoleDetector({ plotHoles, projectId, className }: PlotHoleDetectorProps) {
  const [expandedHoles, setExpandedHoles] = useState<Set<string>>(new Set());
  const [resolvedHoles, setResolvedHoles] = useState<Set<string>>(new Set());
  const [dismissedHoles, setDismissedHoles] = useState<Set<string>>(new Set());




  const toggleExpanded = (holeId: string) => {
    const newExpanded = new Set(expandedHoles);
    if (newExpanded.has(holeId)) {
      newExpanded.delete(holeId);
    } else {
      newExpanded.add(holeId);
    }
    setExpandedHoles(newExpanded);
  };

  const markAsResolved = (holeId: string) => {
    setResolvedHoles(prev => new Set([...prev, holeId]));
  };

  const dismissHole = (holeId: string) => {
    setDismissedHoles(prev => new Set([...prev, holeId]));
  };


  const visibleHoles = plotHoles.filter(hole => 
    !resolvedHoles.has(hole.id) && !dismissedHoles.has(hole.id)
  );

  const criticalHoles = visibleHoles.filter(hole => hole.severity === 'critical');
  const majorHoles = visibleHoles.filter(hole => hole.severity === 'major');
  const minorHoles = visibleHoles.filter(hole => hole.severity === 'minor');

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2" />
            Plot Hole Detection
          </CardTitle>
          <CardDescription>
            AI-powered analysis identifying logical inconsistencies and plot issues
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-red-50 dark:bg-red-950/20 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{criticalHoles.length}</div>
              <div className="text-sm text-red-600">Critical Issues</div>
            </div>
            <div className="text-center p-4 bg-orange-50 dark:bg-orange-950/20 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{majorHoles.length}</div>
              <div className="text-sm text-orange-600">Major Issues</div>
            </div>
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{minorHoles.length}</div>
              <div className="text-sm text-blue-600">Minor Issues</div>
            </div>
          </div>

          {visibleHoles.length === 0 && (
            <Alert className="mt-4">
              <Check className="h-4 w-4" />
              <AlertDescription>
                <strong>Excellent!</strong> No significant plot holes detected in your story.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Critical Issues - Always Visible */}
      {criticalHoles.length > 0 && (
        <Card className="border-red-200 dark:border-red-800">
          <CardHeader className="bg-red-50 dark:bg-red-950/20">
            <CardTitle className="text-red-800 dark:text-red-200 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2" />
              Critical Plot Issues ({criticalHoles.length})
            </CardTitle>
            <CardDescription className="text-red-700 dark:text-red-300">
              These issues require immediate attention as they may confuse readers
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="space-y-4">
              {criticalHoles.map((hole) => (
                <PlotHoleCard
                  key={hole.id}
                  hole={hole}
                  isExpanded={expandedHoles.has(hole.id)}
                  onToggleExpanded={() => toggleExpanded(hole.id)}
                  onMarkResolved={() => markAsResolved(hole.id)}
                  onDismiss={() => dismissHole(hole.id)}
                  projectId={projectId}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Major Issues */}
      {majorHoles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertCircle className="w-5 h-5 mr-2" />
              Major Issues ({majorHoles.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {majorHoles.map((hole) => (
                <PlotHoleCard
                  key={hole.id}
                  hole={hole}
                  isExpanded={expandedHoles.has(hole.id)}
                  onToggleExpanded={() => toggleExpanded(hole.id)}
                  onMarkResolved={() => markAsResolved(hole.id)}
                  onDismiss={() => dismissHole(hole.id)}
                  projectId={projectId}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Minor Issues */}
      {minorHoles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Info className="w-5 h-5 mr-2" />
              Minor Issues ({minorHoles.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {minorHoles.map((hole) => (
                <PlotHoleCard
                  key={hole.id}
                  hole={hole}
                  isExpanded={expandedHoles.has(hole.id)}
                  onToggleExpanded={() => toggleExpanded(hole.id)}
                  onMarkResolved={() => markAsResolved(hole.id)}
                  onDismiss={() => dismissHole(hole.id)}
                  projectId={projectId}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

interface PlotHoleCardProps {
  hole: PlotHole;
  isExpanded: boolean;
  onToggleExpanded: () => void;
  onMarkResolved: () => void;
  onDismiss: () => void;
  projectId: string;
}

function PlotHoleCard({ 
  hole, 
  isExpanded, 
  onToggleExpanded, 
  onMarkResolved, 
  onDismiss,
  projectId 
}: PlotHoleCardProps) {



  const handleJumpToLocation = () => {
    // Navigate to chapter and highlight the issue
    window.open(`/projects/${projectId}/write?chapter=${hole.location.chapter}&paragraph=${hole.location.paragraph}`, '_blank');
  };

  return (
    <div className="border rounded-lg p-4 bg-slate-50 dark:bg-slate-800">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            {getSeverityIcon(hole.severity)}
            <Badge variant={getSeverityColor(hole.severity) as "destructive" | "secondary" | "outline"} className="text-xs">
              {hole.type.replace('_', ' ')}
            </Badge>
            <Badge variant="outline" className="text-xs">
              Chapter {hole.location.chapter}
            </Badge>
            <Badge variant="outline" className="text-xs">
              {Math.round(hole.confidence)}% confidence
            </Badge>
          </div>
          
          <h4 className="font-medium mb-1">{hole.description}</h4>
          <p className="text-sm text-slate-600 dark:text-slate-400 mb-2">
            {getTypeDescription(hole.type)}
          </p>

          <Collapsible open={isExpanded} onOpenChange={onToggleExpanded}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="p-0 h-auto">
                <span className="text-sm text-blue-600 hover:text-blue-800">
                  {isExpanded ? 'Show less' : 'Show details'}
                </span>
                {isExpanded ? (
                  <ChevronUp className="w-3 h-3 ml-1" />
                ) : (
                  <ChevronDown className="w-3 h-3 ml-1" />
                )}
              </Button>
            </CollapsibleTrigger>
            
            <CollapsibleContent className="mt-3 space-y-3">
              <div className="p-3 bg-green-50 dark:bg-green-950/20 rounded border">
                <h5 className="text-sm font-medium text-green-800 dark:text-green-200 mb-1">
                  Suggested Fix:
                </h5>
                <p className="text-sm text-green-700 dark:text-green-300">
                  {hole.suggestion}
                </p>
              </div>

              {hole.relatedElements.length > 0 && (
                <div>
                  <h5 className="text-sm font-medium mb-1">Related Elements:</h5>
                  <div className="flex flex-wrap gap-1">
                    {hole.relatedElements.map((element, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {element}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>
        </div>

        <div className="flex items-center space-x-1 ml-3">
          <Button
            size="sm"
            variant="outline"
            onClick={handleJumpToLocation}
            title="Jump to location"
          >
            <ExternalLink className="w-3 h-3" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onMarkResolved}
            title="Mark as resolved"
            className="text-green-600 hover:text-green-700"
          >
            <Check className="w-3 h-3" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={onDismiss}
            title="Dismiss"
          >
            <X className="w-3 h-3" />
          </Button>
        </div>
      </div>
    </div>
  );
}