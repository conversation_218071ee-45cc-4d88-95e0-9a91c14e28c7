import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { logger } from '@/lib/services/logger';

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all universes the user has access to
    const { data: universes, error } = await supabase
      .from('universes')
      .select(`
        *,
        series:series!universe_id(
          id,
          title,
          description
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('Error fetching universes:', error);
      return NextResponse.json({ error: 'Failed to fetch universes' }, { status: 500 });
    }

    return NextResponse.json({ universes });
  } catch (error) {
    logger.error('Error in GET /api/universes:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, rules = {} } = body;

    if (!name?.trim()) {
      return NextResponse.json({ error: 'Universe name is required' }, { status: 400 });
    }

    const { data: universe, error } = await supabase
      .from('universes')
      .insert({
        name: name.trim(),
        description: description?.trim(),
        rules,
        created_by: user.user.id
      })
      .select()
      .single();

    if (error) {
      logger.error('Error creating universe:', error);
      return NextResponse.json({ error: 'Failed to create universe' }, { status: 500 });
    }

    return NextResponse.json({ universe });
  } catch (error) {
    logger.error('Error in POST /api/universes:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}