import type { editor } from 'monaco-editor';

/**
 * Converts HSL CSS variable to hex color
 */
function hslToHex(hsl: string): string {
  // Parse HSL values from string like "25 30% 10%"
  const parts = hsl.split(' ');
  if (parts.length !== 3) return '#000000';
  
  const h = parseFloat(parts[0]);
  const s = parseFloat(parts[1]) / 100;
  const l = parseFloat(parts[2]) / 100;
  
  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h / 60) % 2 - 1));
  const m = l - c / 2;
  
  let r = 0, g = 0, b = 0;
  
  if (h >= 0 && h < 60) {
    r = c; g = x; b = 0;
  } else if (h >= 60 && h < 120) {
    r = x; g = c; b = 0;
  } else if (h >= 120 && h < 180) {
    r = 0; g = c; b = x;
  } else if (h >= 180 && h < 240) {
    r = 0; g = x; b = c;
  } else if (h >= 240 && h < 300) {
    r = x; g = 0; b = c;
  } else if (h >= 300 && h < 360) {
    r = c; g = 0; b = x;
  }
  
  // Convert to hex
  const toHex = (n: number) => {
    const hex = Math.round((n + m) * 255).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };
  
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

/**
 * Gets CSS variable value from computed styles
 */
function getCSSVariable(varName: string): string {
  if (typeof window === 'undefined') return '';
  
  const styles = getComputedStyle(document.documentElement);
  return styles.getPropertyValue(varName).trim();
}

/**
 * Generates Monaco theme from current CSS variables
 */
export function generateMonacoTheme(isDark: boolean = false): editor.IStandaloneThemeData {
  // Get theme colors from CSS variables
  const background = getCSSVariable('--background');
  const foreground = getCSSVariable('--foreground');
  const primary = getCSSVariable('--primary');
  const secondary = getCSSVariable('--secondary');
  const muted = getCSSVariable('--muted');
  const mutedForeground = getCSSVariable('--muted-foreground');
  const accent = getCSSVariable('--accent');
  const border = getCSSVariable('--border');
  
  // Convert to hex colors
  const bgHex = hslToHex(background);
  const fgHex = hslToHex(foreground);
  const primaryHex = hslToHex(primary);
  const secondaryHex = hslToHex(secondary);
  const mutedHex = hslToHex(muted);
  const mutedFgHex = hslToHex(mutedForeground);
  const accentHex = hslToHex(accent);
  const borderHex = hslToHex(border);
  
  // Calculate derived colors
  const lineHighlight = isDark 
    ? hslToHex(background.replace(/(\d+)%$/, (match, p1) => `${parseInt(p1) + 5}%`))
    : hslToHex(background.replace(/(\d+)%$/, (match, p1) => `${parseInt(p1) - 3}%`));
    
  const selectionBg = primaryHex + '40'; // 25% opacity
  const selectionHighlight = primaryHex + '20'; // 12.5% opacity
  
  return {
    base: isDark ? 'vs-dark' : 'vs',
    inherit: true,
    rules: [
      { token: '', foreground: fgHex.substring(1) },
      { token: 'string', foreground: accentHex.substring(1) },
      { token: 'comment', foreground: mutedFgHex.substring(1), fontStyle: 'italic' },
      { token: 'keyword', foreground: primaryHex.substring(1) },
      { token: 'number', foreground: accentHex.substring(1) },
      { token: 'type', foreground: secondaryHex.substring(1) },
      { token: 'delimiter', foreground: primaryHex.substring(1) },
      { token: 'emphasis', fontStyle: 'italic' },
      { token: 'strong', fontStyle: 'bold' }
    ],
    colors: {
      'editor.background': bgHex,
      'editor.foreground': fgHex,
      'editor.lineHighlightBackground': lineHighlight,
      'editor.selectionBackground': selectionBg,
      'editor.selectionHighlightBackground': selectionHighlight,
      'editor.wordHighlightBackground': selectionHighlight,
      'editor.wordHighlightStrongBackground': selectionBg,
      'editorCursor.foreground': primaryHex,
      'editorWhitespace.foreground': mutedHex,
      'editorIndentGuide.background': mutedHex,
      'editorIndentGuide.activeBackground': mutedFgHex,
      'editorLineNumber.foreground': mutedFgHex,
      'editorLineNumber.activeForeground': fgHex,
      'editor.findMatchBackground': selectionBg,
      'editor.findMatchHighlightBackground': selectionHighlight,
      'editorBracketMatch.background': selectionHighlight,
      'editorBracketMatch.border': primaryHex,
      'scrollbar.shadow': '#00000033',
      'scrollbarSlider.background': mutedHex + '66',
      'scrollbarSlider.hoverBackground': mutedHex + '99',
      'scrollbarSlider.activeBackground': mutedHex + 'cc',
      'editorOverviewRuler.border': borderHex
    }
  };
}

/**
 * Updates Monaco editor theme based on current CSS variables
 */
export function updateMonacoTheme(monaco: any, themeName: string = 'bookscribe-dynamic') {
  if (typeof window === 'undefined') return;
  
  // Determine if dark mode
  const isDark = document.documentElement.classList.contains('dark') || 
                 document.documentElement.classList.contains('evening-study-dark') ||
                 document.documentElement.classList.contains('midnight-ink-dark');
  
  // Generate and define theme
  const themeData = generateMonacoTheme(isDark);
  monaco.editor.defineTheme(themeName, themeData);
  
  // Apply theme
  monaco.editor.setTheme(themeName);
}