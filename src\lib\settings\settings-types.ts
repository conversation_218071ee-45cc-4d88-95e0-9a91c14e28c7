/**
 * Settings Types for BookScribe AI
 * Defines all user customizable settings
 */

export interface TypographySettings {
  // Text size settings
  textSize: 'small' | 'medium' | 'large' | 'extra-large' | 'custom';
  customTextSize?: number; // in px, only used when textSize is 'custom'
  
  // Font family settings
  editorFont: 'jetbrains-mono' | 'fira-code' | 'source-code-pro' | 'consolas' | 'monaco';
  uiFont: 'inter' | 'roboto' | 'open-sans' | 'system-ui' | 'segoe-ui';
  readingFont: 'crimson-text' | 'georgia' | 'times-new-roman' | 'libre-baskerville' | 'merriweather';
  
  // Line height settings
  lineHeight: 'compact' | 'normal' | 'relaxed' | 'loose';
  
  // Letter spacing
  letterSpacing: 'tight' | 'normal' | 'wide';
}

export interface ThemeSettings {
  // Current theme ID
  currentTheme: string;
  
  // Theme mode preference
  themeMode: 'light' | 'dark' | 'system';
  
  // Custom theme overrides (for future extensibility)
  customColors?: Record<string, string>;
}

export interface EditorSettings {
  // Editor-specific typography
  showLineNumbers: boolean;
  wordWrap: boolean;
  tabSize: number;
  
  // Editor behavior
  autoSave: boolean;
  autoSaveInterval: number; // in seconds
  
  // Writing assistance
  spellCheck: boolean;
  grammarCheck: boolean;
  aiSuggestions: boolean;
}

export interface AccessibilitySettings {
  // High contrast mode
  highContrast: boolean;
  
  // Reduced motion
  reducedMotion: boolean;
  
  // Focus indicators
  enhancedFocus: boolean;
  
  // Screen reader optimizations
  screenReaderOptimized: boolean;
}

export interface UserSettings {
  // Core settings sections
  typography: TypographySettings;
  theme: ThemeSettings;
  editor: EditorSettings;
  accessibility: AccessibilitySettings;
  
  // Metadata
  version: string;
  lastUpdated: string;
}

// Default settings
export const defaultTypographySettings: TypographySettings = {
  textSize: 'medium',
  editorFont: 'jetbrains-mono',
  uiFont: 'inter',
  readingFont: 'crimson-text',
  lineHeight: 'normal',
  letterSpacing: 'normal',
};

export const defaultThemeSettings: ThemeSettings = {
  currentTheme: 'writers-sanctuary-light',
  themeMode: 'light',
};

export const defaultEditorSettings: EditorSettings = {
  showLineNumbers: true,
  wordWrap: true,
  tabSize: 2,
  autoSave: true,
  autoSaveInterval: 30,
  spellCheck: true,
  grammarCheck: true,
  aiSuggestions: true,
};

export const defaultAccessibilitySettings: AccessibilitySettings = {
  highContrast: false,
  reducedMotion: false,
  enhancedFocus: false,
  screenReaderOptimized: false,
};

export const defaultUserSettings: UserSettings = {
  typography: defaultTypographySettings,
  theme: defaultThemeSettings,
  editor: defaultEditorSettings,
  accessibility: defaultAccessibilitySettings,
  version: '1.0.0',
  lastUpdated: new Date().toISOString(),
};

// Text size mappings
export const textSizeMap = {
  small: {
    editor: '12px',
    ui: '13px',
    reading: '14px',
    scale: 0.875,
  },
  medium: {
    editor: '14px',
    ui: '14px',
    reading: '16px',
    scale: 1,
  },
  large: {
    editor: '16px',
    ui: '15px',
    reading: '18px',
    scale: 1.125,
  },
  'extra-large': {
    editor: '18px',
    ui: '16px',
    reading: '20px',
    scale: 1.25,
  },
} as const;

// Font family mappings
export const fontFamilyMap = {
  // Editor fonts (monospace)
  'jetbrains-mono': "'JetBrains Mono', 'Courier New', monospace",
  'fira-code': "'Fira Code', 'Courier New', monospace",
  'source-code-pro': "'Source Code Pro', 'Courier New', monospace",
  'consolas': "'Consolas', 'Courier New', monospace",
  'monaco': "'Monaco', 'Courier New', monospace",
  
  // UI fonts (sans-serif)
  'inter': "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
  'roboto': "'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
  'open-sans': "'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
  'system-ui': "system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
  'segoe-ui': "'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif",
  
  // Reading fonts (serif)
  'crimson-text': "'Crimson Text', Georgia, serif",
  'georgia': "Georgia, serif",
  'times-new-roman': "'Times New Roman', Times, serif",
  'libre-baskerville': "'Libre Baskerville', Georgia, serif",
  'merriweather': "'Merriweather', Georgia, serif",
} as const;

// Line height mappings
export const lineHeightMap = {
  compact: 1.3,
  normal: 1.5,
  relaxed: 1.6,
  loose: 1.8,
} as const;

// Letter spacing mappings
export const letterSpacingMap = {
  tight: '-0.025em',
  normal: '0em',
  wide: '0.025em',
} as const;
