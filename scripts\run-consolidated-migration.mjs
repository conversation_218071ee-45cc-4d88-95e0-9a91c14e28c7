#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// Supabase configuration
const SUPABASE_URL = 'https://xvqeiwrpbzpiqvwuvtpj.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko';

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function runConsolidatedMigration() {
  try {
    console.log('🚀 Starting consolidated Supabase migration...\n');
    
    const migrationPath = join(projectRoot, 'supabase/consolidated_migration.sql');
    const sql = readFileSync(migrationPath, 'utf8');
    
    console.log('📝 Migration file loaded successfully');
    console.log(`📊 File size: ${(sql.length / 1024).toFixed(2)} KB\n`);
    
    // Split SQL into individual statements
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`🔍 Found ${statements.length} SQL statements to execute\n`);
    
    let successCount = 0;
    let warningCount = 0;
    let errorCount = 0;
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.length === 0) continue;
      
      const statementPreview = statement.substring(0, 60).replace(/\s+/g, ' ') + '...';
      process.stdout.write(`[${i + 1}/${statements.length}] ${statementPreview}`);
      
      try {
        // Use the REST API directly for better error handling
        const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/exec_sql`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
            'apikey': SUPABASE_SERVICE_ROLE_KEY
          },
          body: JSON.stringify({ sql_query: statement + ';' })
        });
        
        if (response.ok) {
          console.log(' ✅');
          successCount++;
        } else {
          const errorData = await response.text();
          if (errorData.includes('already exists') || errorData.includes('does not exist')) {
            console.log(' ⚠️  (already exists/expected)');
            warningCount++;
          } else {
            console.log(' ❌');
            console.log(`    Error: ${errorData}`);
            errorCount++;
          }
        }
      } catch (err) {
        console.log(' ❌');
        console.log(`    Error: ${err.message}`);
        errorCount++;
      }
      
      // Small delay to avoid rate limiting
      if (i % 10 === 0 && i > 0) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    console.log('\n📊 Migration Summary:');
    console.log(`   ✅ Successful: ${successCount}`);
    console.log(`   ⚠️  Warnings: ${warningCount}`);
    console.log(`   ❌ Errors: ${errorCount}`);
    console.log(`   📁 Total: ${statements.length}`);
    
    if (errorCount === 0) {
      console.log('\n🎉 Migration completed successfully!');
      console.log('🔗 Your database is now ready for BookScribe AI');
    } else {
      console.log('\n⚠️  Migration completed with some errors.');
      console.log('   Most errors are likely due to existing objects or expected conflicts.');
    }
    
    // Test basic connectivity
    console.log('\n🔍 Testing database connectivity...');
    const { data, error } = await supabase
      .from('projects')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('❌ Database connectivity test failed:', error.message);
    } else {
      console.log('✅ Database connectivity test passed');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Run the migration
runConsolidatedMigration().catch(console.error);
