# Playwright MCP Server

The Playwright MCP server provides browser automation capabilities, enabling web scraping, testing, and interaction with web applications directly within Claude.

## Overview

Playwright MCP enables you to:
- Navigate to websites and web applications
- Take screenshots and capture page content
- Fill forms and interact with page elements
- Execute JavaScript in browser context
- Monitor network requests and responses
- Test web applications across multiple browsers

## Configuration

### Basic Configuration

```json
{
  "mcpServers": {
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp"],
      "env": {
        "PLAYWRIGHT_BROWSER": "chromium",
        "PLAYWRIGHT_HEADLESS": "true"
      }
    }
  }
}
```

### Environment Variables

- **PLAYWRIGHT_BROWSER**: <PERSON><PERSON><PERSON> to use (`chromium`, `firefox`, `webkit`)
- **PLAYWRIGHT_HEADLESS**: Run in headless mode (`true`/`false`)
- **PLAYWRIGHT_TIMEOUT**: Default timeout in milliseconds
- **PLAYWRIGHT_VIEWPORT**: Default viewport size (e.g., `1280x720`)

## Available Tools

### Navigation
- **Navigate**: Go to specific URLs
- **Go Back/Forward**: Browser navigation
- **Reload Page**: Refresh current page
- **Wait for Load**: Wait for page load events

### Page Interaction
- **Click Elements**: Click buttons, links, and interactive elements
- **Fill Forms**: Enter text in input fields
- **Select Options**: Choose from dropdown menus
- **Hover Elements**: Trigger hover effects
- **Scroll Page**: Scroll to specific elements or positions

### Content Extraction
- **Take Screenshots**: Capture full page or specific elements
- **Get Page Content**: Extract HTML, text, or structured data
- **Get Element Text**: Retrieve text from specific elements
- **Get Attributes**: Extract element attributes and properties

### JavaScript Execution
- **Execute Script**: Run custom JavaScript in page context
- **Evaluate Functions**: Execute functions with parameters
- **Inject Scripts**: Add JavaScript libraries to pages

### Network Monitoring
- **Monitor Requests**: Track HTTP requests and responses
- **Block Resources**: Block images, CSS, or other resources
- **Set Headers**: Add custom HTTP headers
- **Handle Authentication**: Manage login flows

### Multi-Browser Support
- **Browser Context**: Manage multiple browser sessions
- **Incognito Mode**: Use private browsing contexts
- **Mobile Emulation**: Simulate mobile devices
- **Geolocation**: Set custom location data

## Example Usage

### Web Scraping
```
Navigate to https://example.com and extract all article titles from the homepage
```

```
Take a screenshot of the pricing page and get the text of all pricing plans
```

### Form Testing
```
Go to the contact form, fill in name "John Doe", email "<EMAIL>", and submit
```

```
Test the login form with invalid credentials and capture any error messages
```

### Performance Testing
```
Navigate to the dashboard and measure how long it takes to load all content
```

```
Monitor network requests while loading the checkout page and identify slow API calls
```

### Visual Testing
```
Take screenshots of the homepage on desktop and mobile viewports
```

```
Compare the current design with a reference screenshot
```

## Security Considerations

### Safe Browsing
- Only navigate to trusted websites
- Be cautious with form submissions
- Avoid entering real credentials
- Use test environments when possible

### Data Privacy
- Don't extract sensitive user data
- Respect robots.txt and rate limits
- Follow website terms of service
- Use appropriate delays between requests

### Resource Management
- Close browser contexts when done
- Limit concurrent browser instances
- Set reasonable timeouts
- Monitor memory usage

## Common Use Cases

### Quality Assurance
- Automated UI testing
- Cross-browser compatibility testing
- Regression testing
- Visual regression testing

### Web Scraping
- Extract product information
- Monitor competitor pricing
- Collect market research data
- Archive web content

### SEO & Marketing
- Test page load performance
- Verify meta tags and structured data
- Check mobile responsiveness
- Monitor search result appearances

### Integration Testing
- Test API integrations in browser
- Verify third-party widget functionality
- Test payment flows
- Validate analytics tracking

## Performance Optimization

### Browser Configuration
- Use headless mode for better performance
- Disable unnecessary features (images, CSS)
- Set smaller viewport sizes
- Use faster browser engines

### Request Optimization
- Block unnecessary resources
- Use network interception
- Cache static resources
- Minimize DOM queries

### Parallelization
- Run tests in parallel
- Use multiple browser contexts
- Batch similar operations
- Implement proper cleanup

## Error Handling

### Common Error Types
- **Navigation**: Page not found or timeout
- **Selector**: Element not found or not visible
- **Network**: Connection issues or slow responses
- **JavaScript**: Runtime errors in page context

### Retry Strategies
- Implement wait conditions
- Use retry logic for flaky elements
- Handle dynamic content loading
- Set appropriate timeouts

### Debugging
- Enable verbose logging
- Take screenshots on failures
- Capture console messages
- Monitor network activity

## Browser Support

### Chromium
- Best compatibility
- Fastest performance
- Most features supported
- Default choice for most use cases

### Firefox
- Good for cross-browser testing
- Different rendering engine
- Useful for compatibility validation

### WebKit (Safari)
- iOS/macOS compatibility testing
- Different JavaScript engine
- Required for Apple ecosystem testing

## Advanced Features

### Mobile Testing
- Device emulation
- Touch interactions
- Orientation changes
- Network throttling

### Accessibility Testing
- Screen reader simulation
- Keyboard navigation testing
- Color contrast validation
- ARIA attribute verification

### Custom Extensions
- Install browser extensions
- Test extension functionality
- Simulate extension interactions

## Troubleshooting

### Installation Issues
- Ensure browsers are properly installed
- Check system dependencies
- Verify PATH configuration
- Update browser versions

### Performance Issues
- Reduce viewport size
- Disable images and CSS
- Use headless mode
- Limit concurrent instances

### Element Selection
- Use stable selectors
- Implement wait strategies
- Handle dynamic content
- Use xpath when needed

## Links

- [Playwright MCP GitHub](https://github.com/microsoft/playwright-mcp)
- [Playwright Documentation](https://playwright.dev/)
- [Browser Automation Best Practices](https://playwright.dev/docs/best-practices)