import { useState, useCallback, useRef, useEffect } from 'react';
import { logger } from '@/lib/services/logger';

interface AsyncState<T> {
  data: T | null;
  error: Error | null;
  loading: boolean;
  isIdle: boolean;
  isSuccess: boolean;
  isError: boolean;
}

interface UseAsyncStateOptions {
  onSuccess?: <T>(data: T) => void;
  onError?: (error: Error) => void;
  resetOnUnmount?: boolean;
}

export function useAsyncState<T = unknown>(
  initialData: T | null = null,
  options: UseAsyncStateOptions = {}
): [
  AsyncState<T>,
  {
    execute: (asyncFunction: () => Promise<T>) => Promise<void>;
    reset: () => void;
    setData: (data: T | null) => void;
    setError: (error: Error | null) => void;
  }
] {
  const [state, setState] = useState<AsyncState<T>>({
    data: initialData,
    error: null,
    loading: false,
    isIdle: true,
    isSuccess: false,
    isError: false,
  });

  const isMountedRef = useRef(true);
  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (options.resetOnUnmount) {
        setState({
          data: null,
          error: null,
          loading: false,
          isIdle: true,
          isSuccess: false,
          isError: false,
        });
      }
    };
  }, [options.resetOnUnmount]);

  const execute = useCallback(
    async (asyncFunction: () => Promise<T>) => {
      // Cancel any pending request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller
      abortControllerRef.current = new AbortController();

      setState(prev => ({
        ...prev,
        loading: true,
        isIdle: false,
        error: null,
      }));

      try {
        const result = await asyncFunction();
        
        if (!isMountedRef.current) return;

        setState({
          data: result,
          error: null,
          loading: false,
          isIdle: false,
          isSuccess: true,
          isError: false,
        });

        options.onSuccess?.(result);
      } catch (error) {
        if (!isMountedRef.current) return;
        
        // Ignore abort errors
        if (error instanceof Error && error.name === 'AbortError') {
          return;
        }

        const errorObj = error instanceof Error ? error : new Error('Unknown error occurred');
        
        setState({
          data: null,
          error: errorObj,
          loading: false,
          isIdle: false,
          isSuccess: false,
          isError: true,
        });

        logger.error('Async operation failed:', errorObj);
        options.onError?.(errorObj);
      }
    },
    [options]
  );

  const reset = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    setState({
      data: initialData,
      error: null,
      loading: false,
      isIdle: true,
      isSuccess: false,
      isError: false,
    });
  }, [initialData]);

  const setData = useCallback((data: T | null) => {
    setState(prev => ({
      ...prev,
      data,
      error: null,
      isSuccess: true,
      isError: false,
    }));
  }, []);

  const setError = useCallback((error: Error | null) => {
    setState(prev => ({
      ...prev,
      error,
      data: null,
      isSuccess: false,
      isError: true,
    }));
  }, []);

  return [
    state,
    {
      execute,
      reset,
      setData,
      setError,
    },
  ];
}

// Hook for managing multiple async operations
export function useAsyncOperations() {
  const operations = useRef<Map<string, AbortController>>(new Map());

  const execute = useCallback(
    async <T,>(
      operationId: string,
      asyncFunction: () => Promise<T>,
      options?: {
        onSuccess?: (data: T) => void;
        onError?: (error: Error) => void;
      }
    ): Promise<T | null> => {
      // Cancel any existing operation with the same ID
      const existingController = operations.current.get(operationId);
      if (existingController) {
        existingController.abort();
      }

      // Create new abort controller
      const controller = new AbortController();
      operations.current.set(operationId, controller);

      try {
        const result = await asyncFunction();
        options?.onSuccess?.(result);
        operations.current.delete(operationId);
        return result;
      } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') {
          return null;
        }
        
        const errorObj = error instanceof Error ? error : new Error('Unknown error occurred');
        logger.error(`Async operation ${operationId} failed:`, errorObj);
        options?.onError?.(errorObj);
        operations.current.delete(operationId);
        throw errorObj;
      }
    },
    []
  );

  const cancel = useCallback((operationId: string) => {
    const controller = operations.current.get(operationId);
    if (controller) {
      controller.abort();
      operations.current.delete(operationId);
    }
  }, []);

  const cancelAll = useCallback(() => {
    operations.current.forEach(controller => controller.abort());
    operations.current.clear();
  }, []);

  useEffect(() => {
    return () => {
      cancelAll();
    };
  }, [cancelAll]);

  return {
    execute,
    cancel,
    cancelAll,
  };
}