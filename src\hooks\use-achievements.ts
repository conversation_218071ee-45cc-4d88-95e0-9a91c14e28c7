import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'

interface Achievement {
  id: string
  name: string
  description: string
  category: string
  tier: string
  points: number
  icon?: string
  unlockedAt?: string
  progress?: number
  requirement?: number
}

interface AchievementStats {
  totalUnlocked: number
  totalAchievements: number
  totalPoints: number
  currentStreak: number
  rarestAchievement?: string
  rarestAchievementRarity?: number
}

export function useAchievements(userId: string) {
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [stats, setStats] = useState<AchievementStats>({
    totalUnlocked: 0,
    totalAchievements: 0,
    totalPoints: 0,
    currentStreak: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()

  useEffect(() => {
    if (userId) {
      fetchAchievements()
      checkForNewAchievements()
    }
  }, [userId])

  const fetchAchievements = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/achievements?userId=${userId}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch achievements')
      }

      const data = await response.json()
      setAchievements(data.achievements || [])
      
      // Calculate stats
      const unlocked = data.achievements.filter((a: Achievement) => a.unlockedAt)
      const totalPoints = unlocked.reduce((sum: number, a: Achievement) => sum + a.points, 0)
      
      setStats({
        totalUnlocked: unlocked.length,
        totalAchievements: data.achievements.length,
        totalPoints,
        currentStreak: await getCurrentStreak(),
        rarestAchievement: data.rarestAchievement?.name,
        rarestAchievementRarity: data.rarestAchievement?.rarity
      })
    } catch (err) {
      console.error('Error fetching achievements:', err)
      setError(err instanceof Error ? err.message : 'Failed to load achievements')
    } finally {
      setLoading(false)
    }
  }

  const getCurrentStreak = async () => {
    try {
      const { data } = await supabase
        .from('writing_sessions')
        .select('created_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(100)

      if (!data || data.length === 0) return 0

      let streak = 1
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      for (let i = 1; i < data.length; i++) {
        const currentDate = new Date(data[i].created_at)
        const prevDate = new Date(data[i - 1].created_at)
        
        currentDate.setHours(0, 0, 0, 0)
        prevDate.setHours(0, 0, 0, 0)
        
        const dayDiff = (prevDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)
        
        if (dayDiff === 1) {
          streak++
        } else {
          break
        }
      }

      return streak
    } catch {
      return 0
    }
  }

  const checkForNewAchievements = async () => {
    try {
      const response = await fetch('/api/achievements/check', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.newAchievements?.length > 0) {
          // Refresh achievements to show newly unlocked ones
          await fetchAchievements()
        }
      }
    } catch (err) {
      console.error('Error checking achievements:', err)
    }
  }

  const refreshAchievements = () => {
    fetchAchievements()
    checkForNewAchievements()
  }

  return {
    achievements,
    stats,
    loading,
    error,
    refreshAchievements
  }
}