'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Sparkles, 
  BookOpen, 
  Users, 
  BarChart3, 
  Wand2,
  ChevronRight,
  CheckCircle,
  Play,
  X
} from 'lucide-react';

interface OnboardingFlowProps {
  onComplete: () => void;
  onSkip?: () => void;
  onCreateProject?: () => void;
  onExploreSample?: () => void;
}

const steps = [
  {
    id: 'welcome',
    title: 'Welcome to BookScribe AI',
    description: 'Your AI-powered writing companion',
    icon: BookOpen,
    content: (
      <div className="space-y-4">
        <p className="text-lg">
          BookScribe helps you write novels with AI agents that maintain consistency across your entire story.
        </p>
        <div className="grid gap-3">
          <div className="flex items-start gap-3">
            <Sparkles className="w-5 h-5 text-primary mt-0.5" />
            <div>
              <p className="font-medium">AI-Powered Writing</p>
              <p className="text-sm text-muted-foreground">Generate chapters, develop characters, and maintain plot consistency</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <Users className="w-5 h-5 text-primary mt-0.5" />
            <div>
              <p className="font-medium">Character Development</p>
              <p className="text-sm text-muted-foreground">Create deep, consistent characters with unique voices</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <BarChart3 className="w-5 h-5 text-primary mt-0.5" />
            <div>
              <p className="font-medium">Progress Tracking</p>
              <p className="text-sm text-muted-foreground">Monitor your writing progress with detailed analytics</p>
            </div>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'ai-agents',
    title: 'Meet Your AI Writing Team',
    description: 'Specialized agents work together on your novel',
    icon: Users,
    content: (
      <div className="space-y-4">
        <div className="grid gap-3">
          <Card className="border-primary/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Story Architect</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">Creates comprehensive story structures and plot outlines</p>
            </CardContent>
          </Card>
          <Card className="border-primary/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Character Developer</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">Develops detailed character profiles and relationship dynamics</p>
            </CardContent>
          </Card>
          <Card className="border-primary/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Writing Agent</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">Generates chapter content while maintaining your style</p>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  },
  {
    id: 'features',
    title: 'Key Features',
    description: 'Everything you need to write your novel',
    icon: Sparkles,
    content: (
      <div className="space-y-4">
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
              <Wand2 className="w-4 h-4 text-primary" />
            </div>
            <div className="flex-1">
              <p className="font-medium">One-Click Chapter Generation</p>
              <p className="text-sm text-muted-foreground">Generate complete chapters based on your outline</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
              <CheckCircle className="w-4 h-4 text-primary" />
            </div>
            <div className="flex-1">
              <p className="font-medium">Quality Analysis</p>
              <p className="text-sm text-muted-foreground">Real-time feedback on writing quality and consistency</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
              <BarChart3 className="w-4 h-4 text-primary" />
            </div>
            <div className="flex-1">
              <p className="font-medium">Progress Analytics</p>
              <p className="text-sm text-muted-foreground">Track your writing habits and productivity</p>
            </div>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'get-started',
    title: 'Ready to Write?',
    description: 'Choose how to begin your journey',
    icon: Play,
    content: (
      <div className="space-y-4">
        <p className="text-lg text-center mb-6">
          Start writing your novel in seconds with our quick-start options
        </p>
        <div className="space-y-3" id="onboarding-actions">
          {/* These buttons will be made functional by parent component */}
        </div>
      </div>
    )
  }
];

export function OnboardingFlow({ onComplete, onSkip, onCreateProject, onExploreSample }: OnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const progress = ((currentStep + 1) / steps.length) * 100;

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleComplete = () => {
    setIsVisible(false);
    setTimeout(() => {
      onComplete();
    }, 300);
  };

  const handleSkip = () => {
    if (onSkip) {
      setIsVisible(false);
      setTimeout(() => {
        onSkip();
      }, 300);
    }
  };

  const currentStepData = steps[currentStep];
  const Icon = currentStepData.icon;

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex items-center justify-center p-4"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          transition={{ type: "spring", duration: 0.5 }}
          className="w-full max-w-2xl"
        >
          <Card className="border-2">
            <CardHeader>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <Icon className="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <CardTitle>{currentStepData.title}</CardTitle>
                    <CardDescription>{currentStepData.description}</CardDescription>
                  </div>
                </div>
                {onSkip && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleSkip}
                    className="h-8 w-8"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <Progress value={progress} className="h-2" />
            </CardHeader>
            <CardContent className="space-y-6">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                {currentStepData.content}
                {currentStep === steps.length - 1 && (
                  <div className="space-y-3">
                    <Button 
                      className="w-full justify-start" 
                      size="lg" 
                      variant="default"
                      onClick={() => {
                        handleComplete();
                        onCreateProject?.();
                      }}
                    >
                      <Wand2 className="w-4 h-4 mr-2" />
                      Create Your First Project
                    </Button>
                    <Button 
                      className="w-full justify-start" 
                      size="lg" 
                      variant="outline"
                      onClick={() => {
                        handleComplete();
                        onExploreSample?.();
                      }}
                    >
                      <BookOpen className="w-4 h-4 mr-2" />
                      Explore Sample Project
                    </Button>
                    <Button 
                      className="w-full justify-start" 
                      size="lg" 
                      variant="outline"
                      onClick={() => window.open('https://docs.bookscribe.ai/tutorial', '_blank')}
                    >
                      <Play className="w-4 h-4 mr-2" />
                      Watch Video Tutorial
                    </Button>
                  </div>
                )}
              </motion.div>

              <div className="flex items-center justify-between pt-4">
                <div className="flex items-center gap-2">
                  {steps.map((_, index) => (
                    <div
                      key={index}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentStep
                          ? 'bg-primary'
                          : index < currentStep
                          ? 'bg-primary/50'
                          : 'bg-muted'
                      }`}
                    />
                  ))}
                </div>

                <Button onClick={handleNext}>
                  {currentStep === steps.length - 1 ? 'Get Started' : 'Next'}
                  <ChevronRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}