'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Trophy,
  Lock,
  Sparkles,
  TrendingUp,
  Award,
  Star,
  ChevronRight,
  RefreshCw
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'
import { motion, AnimatePresence } from 'framer-motion'

interface Achievement {
  id: string
  title: string
  description: string
  category: 'writing' | 'consistency' | 'quality' | 'milestones' | 'collaboration' | 'special'
  tier: 'bronze' | 'silver' | 'gold' | 'platinum'
  icon: string
  current_value: number
  target_value: number
  unlocked_at: string | null
  progress: number
}

interface AchievementDisplayProps {
  userId: string
  showUnlocked?: boolean
  showProgress?: boolean
  maxDisplay?: number
  onAchievementClick?: (achievement: Achievement) => void
}

export function AchievementDisplay({
  userId,
  showUnlocked = true,
  showProgress = true,
  maxDisplay,
  onAchievementClick
}: AchievementDisplayProps) {
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(true)
  const [newlyUnlocked, setNewlyUnlocked] = useState<string[]>([])
  const { toast } = useToast()

  useEffect(() => {
    fetchAchievements()
  }, [userId])

  const fetchAchievements = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/achievements?userId=${userId}`)
      if (response.ok) {
        const data = await response.json()
        const achievementsWithProgress = data.achievements.map((a: Achievement) => ({
          ...a,
          progress: a.target_value > 0 ? Math.round((a.current_value / a.target_value) * 100) : 0
        }))
        setAchievements(achievementsWithProgress)
        
        // Check for newly unlocked achievements
        const recentlyUnlocked = achievementsWithProgress
          .filter((a: Achievement) => {
            if (!a.unlocked_at) return false
            const unlockedTime = new Date(a.unlocked_at).getTime()
            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000
            return unlockedTime > fiveMinutesAgo
          })
          .map((a: Achievement) => a.id)
        
        setNewlyUnlocked(recentlyUnlocked)
      }
    } catch (error) {
      console.error('Error fetching achievements:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const checkForNewAchievements = async () => {
    try {
      const response = await fetch('/api/achievements/check', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.newlyUnlocked && data.newlyUnlocked.length > 0) {
          // Show toast for each new achievement
          data.newlyUnlocked.forEach((achievementId: string) => {
            const achievement = achievements.find(a => a.id === achievementId)
            if (achievement) {
              toast({
                title: '🎉 Achievement Unlocked!',
                description: achievement.title,
                duration: 5000,
              })
            }
          })
          
          // Refresh achievements
          fetchAchievements()
        }
      }
    } catch (error) {
      console.error('Error checking achievements:', error)
    }
  }

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'bronze': return 'border-orange-600 bg-orange-50 dark:bg-orange-900/20'
      case 'silver': return 'border-gray-400 bg-gray-50 dark:bg-gray-900/20'
      case 'gold': return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
      case 'platinum': return 'border-purple-600 bg-purple-50 dark:bg-purple-900/20'
      default: return 'border-border'
    }
  }

  const getTierBadgeColor = (tier: string) => {
    switch (tier) {
      case 'bronze': return 'bg-orange-600 text-white'
      case 'silver': return 'bg-gray-400 text-white'
      case 'gold': return 'bg-yellow-500 text-white'
      case 'platinum': return 'bg-purple-600 text-white'
      default: return 'bg-muted'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'writing': return TrendingUp
      case 'consistency': return Star
      case 'quality': return Award
      case 'milestones': return Trophy
      case 'special': return Sparkles
      default: return Trophy
    }
  }

  const categories = [
    { id: 'all', label: 'All', count: achievements.length },
    { id: 'writing', label: 'Writing', count: achievements.filter(a => a.category === 'writing').length },
    { id: 'consistency', label: 'Consistency', count: achievements.filter(a => a.category === 'consistency').length },
    { id: 'quality', label: 'Quality', count: achievements.filter(a => a.category === 'quality').length },
    { id: 'milestones', label: 'Milestones', count: achievements.filter(a => a.category === 'milestones').length },
    { id: 'special', label: 'Special', count: achievements.filter(a => a.category === 'special').length },
  ]

  const filteredAchievements = achievements.filter(a => {
    if (selectedCategory !== 'all' && a.category !== selectedCategory) return false
    if (!showUnlocked && a.unlocked_at) return false
    if (!showProgress && !a.unlocked_at) return false
    return true
  })

  const displayAchievements = maxDisplay 
    ? filteredAchievements.slice(0, maxDisplay)
    : filteredAchievements

  const unlockedCount = achievements.filter(a => a.unlocked_at).length
  const totalCount = achievements.length
  const overallProgress = totalCount > 0 ? Math.round((unlockedCount / totalCount) * 100) : 0

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Achievements
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <Skeleton key={i} className="h-20 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Achievements
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {unlockedCount} / {totalCount}
            </Badge>
            <Button
              size="sm"
              variant="ghost"
              onClick={checkForNewAchievements}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Overall Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Overall Progress</span>
              <span className="font-medium">{overallProgress}%</span>
            </div>
            <Progress value={overallProgress} className="h-2" />
          </div>

          {/* Category Tabs */}
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
              {categories.map(cat => (
                <TabsTrigger 
                  key={cat.id} 
                  value={cat.id}
                  className="text-xs"
                >
                  {cat.label}
                  {cat.count > 0 && (
                    <Badge variant="secondary" className="ml-1 h-4 px-1 text-[10px]">
                      {cat.count}
                    </Badge>
                  )}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>

          {/* Achievements List */}
          <ScrollArea className="h-[400px] w-full pr-4">
            <div className="space-y-3">
              <AnimatePresence>
                {displayAchievements.map((achievement, idx) => {
                  const Icon = getCategoryIcon(achievement.category)
                  const isUnlocked = !!achievement.unlocked_at
                  const isNew = newlyUnlocked.includes(achievement.id)
                  
                  return (
                    <motion.div
                      key={achievement.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: idx * 0.05 }}
                    >
                      <div
                        className={cn(
                          "relative p-4 rounded-lg border-2 transition-all cursor-pointer",
                          isUnlocked ? getTierColor(achievement.tier) : "border-muted bg-muted/10",
                          "hover:shadow-md",
                          isNew && "ring-2 ring-primary ring-offset-2"
                        )}
                        onClick={() => onAchievementClick?.(achievement)}
                      >
                        <div className="flex items-start gap-3">
                          {/* Icon */}
                          <div className={cn(
                            "p-2 rounded-full",
                            isUnlocked ? "bg-background" : "bg-muted"
                          )}>
                            {isUnlocked ? (
                              <span className="text-2xl">{achievement.icon}</span>
                            ) : (
                              <Lock className="h-6 w-6 text-muted-foreground" />
                            )}
                          </div>

                          {/* Content */}
                          <div className="flex-1 space-y-1">
                            <div className="flex items-center gap-2">
                              <h4 className={cn(
                                "font-medium",
                                !isUnlocked && "text-muted-foreground"
                              )}>
                                {achievement.title}
                              </h4>
                              <Badge 
                                className={cn(
                                  "text-xs",
                                  isUnlocked ? getTierBadgeColor(achievement.tier) : "bg-muted"
                                )}
                              >
                                {achievement.tier}
                              </Badge>
                              {isNew && (
                                <Badge variant="default" className="text-xs animate-pulse">
                                  NEW!
                                </Badge>
                              )}
                            </div>
                            <p className={cn(
                              "text-sm",
                              isUnlocked ? "text-muted-foreground" : "text-muted-foreground/60"
                            )}>
                              {achievement.description}
                            </p>
                            
                            {/* Progress */}
                            {!isUnlocked && showProgress && (
                              <div className="space-y-1 mt-2">
                                <div className="flex items-center justify-between text-xs">
                                  <span className="text-muted-foreground">
                                    Progress: {achievement.current_value} / {achievement.target_value}
                                  </span>
                                  <span className="font-medium">{achievement.progress}%</span>
                                </div>
                                <Progress value={achievement.progress} className="h-1.5" />
                              </div>
                            )}
                            
                            {/* Unlocked date */}
                            {isUnlocked && achievement.unlocked_at && (
                              <p className="text-xs text-muted-foreground mt-1">
                                Unlocked {new Date(achievement.unlocked_at).toLocaleDateString()}
                              </p>
                            )}
                          </div>

                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </AnimatePresence>
            </div>
          </ScrollArea>

          {/* View All Button */}
          {maxDisplay && filteredAchievements.length > maxDisplay && (
            <Button variant="outline" className="w-full">
              View All Achievements ({filteredAchievements.length})
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}