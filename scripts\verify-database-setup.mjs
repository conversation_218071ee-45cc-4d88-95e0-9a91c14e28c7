#!/usr/bin/env node

/**
 * BookScribe AI Database Verification Script
 * 
 * This script verifies that all required database tables are properly set up
 * and provides detailed information about the database state.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://127.0.0.1:54321';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko';

// Expected tables for BookScribe AI
const EXPECTED_TABLES = [
  'projects',
  'profiles', 
  'user_subscriptions',
  'usage_tracking',
  'usage_events',
  'story_arcs',
  'characters',
  'chapters',
  'agent_logs',
  'selection_profiles',
  'reference_materials',
  'selection_analytics',
  'story_bible',
  'editing_sessions',
  'chapter_versions',
  'project_snapshots',
  'content_embeddings',
  'series',
  'series_books',
  'writing_goals',
  'writing_goal_progress',
  'notifications',
  'writing_sessions',
  'ai_suggestions',
  'collaboration_sessions',
  'collaboration_participants'
];

// Expected extensions
const EXPECTED_EXTENSIONS = [
  'uuid-ossp',
  'vector'
];

class DatabaseVerifier {
  constructor() {
    this.supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
    this.results = {
      connection: false,
      extensions: {},
      tables: {},
      policies: {},
      functions: {},
      triggers: {},
      indexes: {},
      issues: [],
      recommendations: []
    };
  }

  async verify() {
    console.log('🔍 Starting BookScribe AI Database Verification...\n');
    
    try {
      await this.checkConnection();
      await this.checkExtensions();
      await this.checkTables();
      await this.checkRLSPolicies();
      await this.checkFunctions();
      await this.checkTriggers();
      await this.checkIndexes();
      
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Verification failed:', error.message);
      process.exit(1);
    }
  }

  async checkConnection() {
    console.log('📡 Checking database connection...');
    
    try {
      const { data, error } = await this.supabase
        .from('information_schema.tables')
        .select('table_name')
        .limit(1);
        
      if (error) throw error;
      
      this.results.connection = true;
      console.log('✅ Database connection successful\n');
      
    } catch (error) {
      console.log('❌ Database connection failed:', error.message);
      this.results.issues.push('Database connection failed');
      throw error;
    }
  }

  async checkExtensions() {
    console.log('🔌 Checking required extensions...');
    
    for (const extension of EXPECTED_EXTENSIONS) {
      try {
        const { data, error } = await this.supabase.rpc('check_extension', {
          extension_name: extension
        });
        
        if (error) {
          // Try alternative method
          const { data: altData, error: altError } = await this.supabase
            .from('pg_extension')
            .select('extname')
            .eq('extname', extension);
            
          this.results.extensions[extension] = !altError && altData?.length > 0;
        } else {
          this.results.extensions[extension] = data;
        }
        
        if (this.results.extensions[extension]) {
          console.log(`  ✅ ${extension} extension is installed`);
        } else {
          console.log(`  ❌ ${extension} extension is missing`);
          this.results.issues.push(`Missing extension: ${extension}`);
        }
        
      } catch (error) {
        console.log(`  ⚠️  Could not check ${extension} extension:`, error.message);
        this.results.extensions[extension] = false;
      }
    }
    console.log();
  }

  async checkTables() {
    console.log('📋 Checking required tables...');
    
    try {
      const { data: tables, error } = await this.supabase
        .from('information_schema.tables')
        .select('table_name, table_schema')
        .eq('table_schema', 'public');
        
      if (error) throw error;
      
      const existingTables = tables.map(t => t.table_name);
      
      for (const table of EXPECTED_TABLES) {
        const exists = existingTables.includes(table);
        this.results.tables[table] = exists;
        
        if (exists) {
          console.log(`  ✅ ${table} table exists`);
        } else {
          console.log(`  ❌ ${table} table is missing`);
          this.results.issues.push(`Missing table: ${table}`);
        }
      }
      
      // Check for unexpected tables
      const unexpectedTables = existingTables.filter(t => !EXPECTED_TABLES.includes(t));
      if (unexpectedTables.length > 0) {
        console.log(`  ℹ️  Additional tables found: ${unexpectedTables.join(', ')}`);
      }
      
    } catch (error) {
      console.log('❌ Failed to check tables:', error.message);
      this.results.issues.push('Failed to check tables');
    }
    console.log();
  }

  async checkRLSPolicies() {
    console.log('🔒 Checking Row Level Security policies...');
    
    try {
      const { data: policies, error } = await this.supabase
        .from('pg_policies')
        .select('tablename, policyname, permissive, roles, cmd, qual');
        
      if (error) throw error;
      
      const tablesPolicies = {};
      policies.forEach(policy => {
        if (!tablesPolicies[policy.tablename]) {
          tablesPolicies[policy.tablename] = [];
        }
        tablesPolicies[policy.tablename].push(policy.policyname);
      });
      
      this.results.policies = tablesPolicies;
      
      for (const table of EXPECTED_TABLES) {
        const policyCount = tablesPolicies[table]?.length || 0;
        if (policyCount > 0) {
          console.log(`  ✅ ${table} has ${policyCount} RLS policies`);
        } else {
          console.log(`  ⚠️  ${table} has no RLS policies`);
          this.results.recommendations.push(`Consider adding RLS policies for ${table}`);
        }
      }
      
    } catch (error) {
      console.log('❌ Failed to check RLS policies:', error.message);
      this.results.issues.push('Failed to check RLS policies');
    }
    console.log();
  }

  async checkFunctions() {
    console.log('⚙️  Checking database functions...');
    
    const expectedFunctions = [
      'update_updated_at_column',
      'handle_new_user',
      'search_similar_content',
      'update_goal_progress',
      'create_chapter_version'
    ];
    
    try {
      const { data: functions, error } = await this.supabase
        .from('information_schema.routines')
        .select('routine_name, routine_type')
        .eq('routine_schema', 'public');
        
      if (error) throw error;
      
      const existingFunctions = functions.map(f => f.routine_name);
      
      for (const func of expectedFunctions) {
        const exists = existingFunctions.includes(func);
        this.results.functions[func] = exists;
        
        if (exists) {
          console.log(`  ✅ ${func} function exists`);
        } else {
          console.log(`  ❌ ${func} function is missing`);
          this.results.issues.push(`Missing function: ${func}`);
        }
      }
      
    } catch (error) {
      console.log('❌ Failed to check functions:', error.message);
      this.results.issues.push('Failed to check functions');
    }
    console.log();
  }

  async checkTriggers() {
    console.log('🎯 Checking database triggers...');
    
    try {
      const { data: triggers, error } = await this.supabase
        .from('information_schema.triggers')
        .select('trigger_name, event_object_table')
        .eq('trigger_schema', 'public');
        
      if (error) throw error;
      
      const triggersByTable = {};
      triggers.forEach(trigger => {
        if (!triggersByTable[trigger.event_object_table]) {
          triggersByTable[trigger.event_object_table] = [];
        }
        triggersByTable[trigger.event_object_table].push(trigger.trigger_name);
      });
      
      this.results.triggers = triggersByTable;
      
      const tablesWithTriggers = Object.keys(triggersByTable);
      console.log(`  ✅ Found triggers on ${tablesWithTriggers.length} tables`);
      
      // Check for updated_at triggers
      const tablesNeedingUpdatedAt = EXPECTED_TABLES.filter(table => 
        !triggersByTable[table]?.some(trigger => trigger.includes('updated_at'))
      );
      
      if (tablesNeedingUpdatedAt.length > 0) {
        console.log(`  ⚠️  Tables missing updated_at triggers: ${tablesNeedingUpdatedAt.join(', ')}`);
      }
      
    } catch (error) {
      console.log('❌ Failed to check triggers:', error.message);
      this.results.issues.push('Failed to check triggers');
    }
    console.log();
  }

  async checkIndexes() {
    console.log('📊 Checking database indexes...');
    
    try {
      const { data: indexes, error } = await this.supabase
        .from('pg_indexes')
        .select('indexname, tablename')
        .eq('schemaname', 'public');
        
      if (error) throw error;
      
      const indexesByTable = {};
      indexes.forEach(index => {
        if (!indexesByTable[index.tablename]) {
          indexesByTable[index.tablename] = [];
        }
        indexesByTable[index.tablename].push(index.indexname);
      });
      
      this.results.indexes = indexesByTable;
      
      const totalIndexes = indexes.length;
      console.log(`  ✅ Found ${totalIndexes} indexes across all tables`);
      
      // Check for performance-critical indexes
      const criticalIndexes = [
        'idx_projects_user_id',
        'idx_chapters_project_id',
        'idx_characters_project_id',
        'idx_content_embeddings_embedding'
      ];
      
      const missingCriticalIndexes = criticalIndexes.filter(idx => 
        !indexes.some(index => index.indexname === idx)
      );
      
      if (missingCriticalIndexes.length > 0) {
        console.log(`  ⚠️  Missing critical indexes: ${missingCriticalIndexes.join(', ')}`);
        this.results.recommendations.push('Add missing performance indexes');
      }
      
    } catch (error) {
      console.log('❌ Failed to check indexes:', error.message);
      this.results.issues.push('Failed to check indexes');
    }
    console.log();
  }

  generateReport() {
    console.log('📋 VERIFICATION REPORT');
    console.log('='.repeat(50));
    
    // Summary
    const tablesOk = Object.values(this.results.tables).filter(Boolean).length;
    const totalTables = EXPECTED_TABLES.length;
    const extensionsOk = Object.values(this.results.extensions).filter(Boolean).length;
    const totalExtensions = EXPECTED_EXTENSIONS.length;
    
    console.log(`\n📊 SUMMARY:`);
    console.log(`  Database Connection: ${this.results.connection ? '✅' : '❌'}`);
    console.log(`  Extensions: ${extensionsOk}/${totalExtensions} installed`);
    console.log(`  Tables: ${tablesOk}/${totalTables} present`);
    console.log(`  Issues Found: ${this.results.issues.length}`);
    console.log(`  Recommendations: ${this.results.recommendations.length}`);
    
    // Issues
    if (this.results.issues.length > 0) {
      console.log(`\n❌ ISSUES FOUND:`);
      this.results.issues.forEach((issue, i) => {
        console.log(`  ${i + 1}. ${issue}`);
      });
    }
    
    // Recommendations
    if (this.results.recommendations.length > 0) {
      console.log(`\n💡 RECOMMENDATIONS:`);
      this.results.recommendations.forEach((rec, i) => {
        console.log(`  ${i + 1}. ${rec}`);
      });
    }
    
    // Next steps
    console.log(`\n🚀 NEXT STEPS:`);
    if (this.results.issues.length > 0) {
      console.log(`  1. Run the consolidated migration to fix missing tables/functions`);
      console.log(`  2. Execute: npx supabase db reset --local`);
      console.log(`  3. Or manually run: node scripts/run-consolidated-migration.mjs`);
    } else {
      console.log(`  ✅ Database is properly configured!`);
      console.log(`  🎉 Your BookScribe AI application is ready to use.`);
    }
    
    console.log('\n' + '='.repeat(50));
  }
}

// Run verification
const verifier = new DatabaseVerifier();
verifier.verify().catch(console.error);
