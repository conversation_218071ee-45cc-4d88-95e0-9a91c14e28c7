import { ServiceManager } from './service-manager';
import { logger } from '@/lib/services/logger';

import { NextResponse } from 'next/server';

/**
 * Middleware helper to ensure services are initialized before handling requests
 * This provides a consistent pattern for all API routes that depend on services
 */
export async function withServiceManager<T extends (...args: unknown[]) => unknown>(
  handler: T
): Promise<T> {
  return (async (...args: Parameters<T>) => {
    try {
      // Ensure services are initialized
      const manager = ServiceManager.getInstance();
      
      // The service manager handles lazy initialization internally
      // We just need to trigger it by attempting to get a service
      await manager.getAIOrchestrator();
      
      // Call the actual handler
      return handler(...args);
    } catch (error) {
      logger.error('[ServiceMiddleware] Error initializing services:', error);
      
      // Return error response if this is an API route
      if (args[0] && typeof args[0] === 'object' && 'url' in args[0]) {
        return NextResponse.json(
          { error: 'Service initialization failed', details: error instanceof Error ? error.message : 'Unknown error' },
          { status: 503 }
        );
      }
      
      // Otherwise re-throw for other handlers
      throw error;
    }
  }) as T;
}

/**
 * Helper to check if services are available
 * Useful for health checks and debugging
 */
export async function checkServicesHealth(): Promise<{
  initialized: boolean;
  services: Record<string, boolean>;
  errors: string[];
}> {
  const manager = ServiceManager.getInstance();
  const errors: string[] = [];
  
  // Check each service
  const services: Record<string, boolean> = {
    'ai-orchestrator': !!(await manager.getAIOrchestrator()),
    'content-generator': !!(await manager.getContentGenerator()),
    'context-manager': !!(await manager.getContextManager()),
    'analytics-engine': !!(await manager.getAnalyticsEngine()),
    'collaboration-hub': !!(await manager.getCollaborationHub()),
    'semantic-search': !!(await manager.getSemanticSearch()),
  };
  
  // Collect errors
  Object.entries(services).forEach(([name, available]) => {
    if (!available) {
      errors.push(`Service '${name}' is not available`);
    }
  });
  
  return {
    initialized: Object.values(services).some(v => v), // At least one service is available
    services,
    errors,
  };
}