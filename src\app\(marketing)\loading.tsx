import { Skeleton } from '@/components/ui/skeleton'

export default function MarketingLoading() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Paper texture background */}
      <div className="fixed inset-0 paper-texture opacity-30" />
      {/* Header */}
      <header className="relative z-50 border-b border-border bg-background/95 backdrop-blur-xl">
        <div className="container flex h-20 items-center justify-between">
          <div className="flex items-center space-x-3">
            <Skeleton className="w-10 h-10 rounded-lg" />
            <Skeleton className="h-8 w-32" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-9 w-20" />
            <Skeleton className="h-9 w-32" />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="container max-w-6xl mx-auto text-center">
          <Skeleton className="h-6 w-96 mx-auto mb-8" />
          <div className="space-y-4 mb-8">
            <Skeleton className="h-16 w-full max-w-4xl mx-auto" />
            <Skeleton className="h-12 w-3/4 mx-auto" />
          </div>
          <Skeleton className="h-6 w-full max-w-3xl mx-auto mb-12" />
          
          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Skeleton className="h-12 w-48" />
            <Skeleton className="h-12 w-48" />
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="text-center p-8 bg-card rounded-xl border border-border">
                <Skeleton className="h-8 w-16 mx-auto mb-2" />
                <Skeleton className="h-4 w-32 mx-auto" />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="relative z-10 py-20 px-4 border-t border-border">
        <div className="container max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <Skeleton className="h-12 w-64 mx-auto mb-4" />
            <Skeleton className="h-6 w-96 mx-auto" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="rounded-2xl border border-border bg-card p-6">
                <Skeleton className="w-16 h-16 rounded-xl mb-4" />
                <Skeleton className="h-6 w-32 mb-2" />
                <Skeleton className="h-16 w-full" />
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}