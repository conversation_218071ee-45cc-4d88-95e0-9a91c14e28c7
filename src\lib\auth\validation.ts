import { createClient } from '@/lib/supabase/server'
import { logger } from '@/lib/services/logger';

import { OwnershipResult, AUTH_ERRORS } from './types'
import { createErrorResponse } from './server'

/**
 * Validate user ownership of a resource
 */
export async function validateUserOwnership(
  supabase: Awaited<ReturnType<typeof createClient>>,
  userId: string,
  table: string,
  resourceId: string,
  userColumn: string = 'user_id'
): Promise<OwnershipResult> {
  try {
    const { data, error } = await supabase
      .from(table)
      .select('*')
      .eq('id', resourceId)
      .eq(userColumn, userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return {
          success: false,
          response: createErrorResponse(AUTH_ERRORS.NOT_FOUND)
        }
      }
      logger.error(`Ownership validation error for ${table}:`, error);
      return {
        success: false,
        response: createErrorResponse(AUTH_ERRORS.SERVER_ERROR)
      }
    }

    return {
      success: true,
      data
    }
  } catch (error) {
    logger.error(`Ownership validation error for ${table}:`, error);
    return {
      success: false,
      response: createErrorResponse(AUTH_ERRORS.SERVER_ERROR)
    }
  }
}

/**
 * Validate user ownership through joined table (e.g., chapters through projects)
 */
export async function validateUserOwnershipViaJoin(
  supabase: Awaited<ReturnType<typeof createClient>>,
  userId: string,
  primaryTable: string,
  resourceId: string,
  joinTable: string,
  _joinColumn: string,
  userColumn: string = 'user_id'
): Promise<OwnershipResult> {
  try {
    const { data, error } = await supabase
      .from(primaryTable)
      .select(`*, ${joinTable}!inner(${userColumn})`)
      .eq('id', resourceId)
      .eq(`${joinTable}.${userColumn}`, userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return {
          success: false,
          response: createErrorResponse(AUTH_ERRORS.NOT_FOUND)
        }
      }
      logger.error(`Joined ownership validation error for ${primaryTable}:`, error);
      return {
        success: false,
        response: createErrorResponse(AUTH_ERRORS.SERVER_ERROR)
      }
    }

    return {
      success: true,
      data
    }
  } catch (error) {
    logger.error(`Joined ownership validation error for ${primaryTable}:`, error);
    return {
      success: false,
      response: createErrorResponse(AUTH_ERRORS.SERVER_ERROR)
    }
  }
}

/**
 * Check if user has access to multiple resources at once
 */
export async function validateBulkUserOwnership(
  supabase: Awaited<ReturnType<typeof createClient>>,
  userId: string,
  table: string,
  resourceIds: string[],
  userColumn: string = 'user_id'
): Promise<OwnershipResult> {
  try {
    const { data, error } = await supabase
      .from(table)
      .select('id')
      .in('id', resourceIds)
      .eq(userColumn, userId)

    if (error) {
      logger.error(`Bulk ownership validation error for ${table}:`, error);
      return {
        success: false,
        response: createErrorResponse(AUTH_ERRORS.SERVER_ERROR)
      }
    }

    // Check if all requested resources are owned by user
    const ownedIds = data.map(item => item.id)
    const unauthorized = resourceIds.filter(id => !ownedIds.includes(id))

    if (unauthorized.length > 0) {
      return {
        success: false,
        response: createErrorResponse(AUTH_ERRORS.FORBIDDEN)
      }
    }

    return {
      success: true,
      data: ownedIds
    }
  } catch (error) {
    logger.error(`Bulk ownership validation error for ${table}:`, error);
    return {
      success: false,
      response: createErrorResponse(AUTH_ERRORS.SERVER_ERROR)
    }
  }
}

/**
 * Validate project ownership specifically (most common use case)
 */
export async function validateProjectOwnership(
  supabase: Awaited<ReturnType<typeof createClient>>,
  userId: string,
  projectId: string
): Promise<OwnershipResult> {
  return validateUserOwnership(supabase, userId, 'projects', projectId)
}

/**
 * Validate chapter ownership through project relationship
 */
export async function validateChapterOwnership(
  supabase: Awaited<ReturnType<typeof createClient>>,
  userId: string,
  chapterId: string
): Promise<OwnershipResult> {
  return validateUserOwnershipViaJoin(
    supabase,
    userId,
    'chapters',
    chapterId,
    'projects',
    'project_id'
  )
}