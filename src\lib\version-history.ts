import { createClient } from '@/lib/supabase/client'

import { logger } from '@/lib/services/logger';

export interface ChapterVersion {
  id: string
  chapter_id: string
  user_id: string
  version_number: number
  title: string | null
  content: string | null
  word_count: number
  outline: string | null
  ai_notes: Record<string, unknown>
  change_summary: string | null
  is_auto_save: boolean
  created_at: string
}

export interface ProjectSnapshot {
  id: string
  project_id: string
  user_id: string
  name: string
  description: string | null
  snapshot_data: Record<string, unknown>
  created_at: string
}

export interface VersionComparison {
  oldVersion: ChapterVersion
  newVersion: ChapterVersion
  changes: {
    titleChanged: boolean
    contentChanged: boolean
    wordCountDelta: number
    addedLines: string[]
    removedLines: string[]
  }
}

export class VersionHistoryService {
  private supabase

  constructor() {
    this.supabase = createClient()
  }

  // Get all versions for a chapter
  async getChapterVersions(chapterId: string, userId: string): Promise<ChapterVersion[]> {
    const { data, error } = await this.supabase
      .from('chapter_versions')
      .select('*')
      .eq('chapter_id', chapterId)
      .eq('user_id', userId)
      .order('version_number', { ascending: false })

    if (error) throw error
    return data || []
  }

  // Get a specific version
  async getChapterVersion(versionId: string, userId: string): Promise<ChapterVersion | null> {
    const { data, error } = await this.supabase
      .from('chapter_versions')
      .select('*')
      .eq('id', versionId)
      .eq('user_id', userId)
      .single()

    if (error) return null
    return data
  }

  // Manually create a version (for important milestones)
  async createManualVersion(
    chapterId: string, 
    userId: string, 
    changeSummary: string
  ): Promise<boolean> {
    try {
      // Get current chapter data
      const { data: chapter, error: chapterError } = await this.supabase
        .from('chapters')
        .select('*')
        .eq('id', chapterId)
        .eq('project_id', chapterId) // This should be user_id check in real implementation
        .single()

      if (chapterError || !chapter) return false

      // Get next version number
      const { data: lastVersion } = await this.supabase
        .from('chapter_versions')
        .select('version_number')
        .eq('chapter_id', chapterId)
        .order('version_number', { ascending: false })
        .limit(1)
        .single()

      const nextVersionNumber = (lastVersion?.version_number || 0) + 1

      // Create version
      const { error } = await this.supabase
        .from('chapter_versions')
        .insert({
          chapter_id: chapterId,
          user_id: userId,
          version_number: nextVersionNumber,
          title: chapter.title,
          content: chapter.content,
          word_count: chapter.actual_word_count || 0,
          outline: chapter.outline,
          ai_notes: chapter.ai_notes,
          change_summary: changeSummary,
          is_auto_save: false
        })

      return !error
    } catch (error) {
      logger.error('Error creating manual version:', error);
      return false
    }
  }

  // Restore a chapter to a specific version
  async restoreChapterVersion(
    chapterId: string, 
    versionId: string, 
    userId: string
  ): Promise<boolean> {
    try {
      // Get the version to restore
      const version = await this.getChapterVersion(versionId, userId)
      if (!version) return false

      // Update the chapter with version data
      const { error } = await this.supabase
        .from('chapters')
        .update({
          title: version.title,
          content: version.content,
          actual_word_count: version.word_count,
          outline: version.outline,
          ai_notes: version.ai_notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', chapterId)

      return !error
    } catch (error) {
      logger.error('Error restoring version:', error);
      return false
    }
  }

  // Compare two versions
  async compareVersions(
    version1Id: string, 
    version2Id: string, 
    userId: string
  ): Promise<VersionComparison | null> {
    try {
      const [version1, version2] = await Promise.all([
        this.getChapterVersion(version1Id, userId),
        this.getChapterVersion(version2Id, userId)
      ])

      if (!version1 || !version2) return null

      // Simple line-by-line comparison
      const content1Lines = (version1.content || '').split('\n')
      const content2Lines = (version2.content || '').split('\n')

      const addedLines = content2Lines.filter(line => !content1Lines.includes(line))
      const removedLines = content1Lines.filter(line => !content2Lines.includes(line))

      return {
        oldVersion: version1,
        newVersion: version2,
        changes: {
          titleChanged: version1.title !== version2.title,
          contentChanged: version1.content !== version2.content,
          wordCountDelta: version2.word_count - version1.word_count,
          addedLines,
          removedLines
        }
      }
    } catch (error) {
      logger.error('Error comparing versions:', error);
      return null
    }
  }

  // Clean up old auto-save versions (keep manual saves)
  async cleanupOldVersions(chapterId: string, keepCount: number = 20): Promise<boolean> {
    try {
      // Get versions to delete (keep manual saves and recent auto-saves)
      const { data: versionsToDelete } = await this.supabase
        .from('chapter_versions')
        .select('id')
        .eq('chapter_id', chapterId)
        .eq('is_auto_save', true)
        .order('version_number', { ascending: false })
        .range(keepCount, 1000) // Keep first keepCount, delete the rest

      if (versionsToDelete && versionsToDelete.length > 0) {
        const idsToDelete = versionsToDelete.map(v => v.id)
        await this.supabase
          .from('chapter_versions')
          .delete()
          .in('id', idsToDelete)
      }

      return true
    } catch (error) {
      logger.error('Error cleaning up versions:', error);
      return false
    }
  }

  // Project-level snapshots
  async createProjectSnapshot(
    projectId: string, 
    userId: string, 
    name: string, 
    description?: string
  ): Promise<boolean> {
    try {
      // Get complete project data
      const [project, chapters, characters, storyBible] = await Promise.all([
        this.supabase.from('projects').select('*').eq('id', projectId).single(),
        this.supabase.from('chapters').select('*').eq('project_id', projectId).order('chapter_number'),
        this.supabase.from('characters').select('*').eq('project_id', projectId),
        this.supabase.from('story_bible').select('*').eq('project_id', projectId)
      ])

      const snapshotData = {
        project: project.data,
        chapters: chapters.data || [],
        characters: characters.data || [],
        storyBible: storyBible.data || [],
        timestamp: new Date().toISOString()
      }

      const { error } = await this.supabase
        .from('project_snapshots')
        .insert({
          project_id: projectId,
          user_id: userId,
          name,
          description,
          snapshot_data: snapshotData
        })

      return !error
    } catch (error) {
      logger.error('Error creating project snapshot:', error);
      return false
    }
  }

  async getProjectSnapshots(projectId: string, userId: string): Promise<ProjectSnapshot[]> {
    const { data, error } = await this.supabase
      .from('project_snapshots')
      .select('*')
      .eq('project_id', projectId)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  }

  async deleteProjectSnapshot(snapshotId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('project_snapshots')
        .delete()
        .eq('id', snapshotId)
        .eq('user_id', userId)

      return !error
    } catch (error) {
      logger.error('Error deleting snapshot:', error);
      return false
    }
  }
}

// Utility functions for text diffing
export function calculateTextDiff(oldText: string, newText: string) {
  const oldLines = oldText.split('\n')
  const newLines = newText.split('\n')
  
  const changes = {
    added: [] as string[],
    removed: [] as string[],
    unchanged: [] as string[]
  }

  // Simple line-by-line diff
  const oldSet = new Set(oldLines)
  const newSet = new Set(newLines)

  oldLines.forEach(line => {
    if (!newSet.has(line)) {
      changes.removed.push(line)
    } else {
      changes.unchanged.push(line)
    }
  })

  newLines.forEach(line => {
    if (!oldSet.has(line)) {
      changes.added.push(line)
    }
  })

  return changes
}

export function formatVersionDate(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMins / 60)
  const diffDays = Math.floor(diffHours / 24)

  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`
  if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`
  if (diffDays < 7) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`
  
  return date.toLocaleDateString()
}