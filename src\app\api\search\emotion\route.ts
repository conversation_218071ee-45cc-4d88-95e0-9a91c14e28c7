import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import ServiceManager from '@/lib/services/service-manager';
import { SemanticSearchService } from '@/lib/services/semantic-search';
import { authenticateUserForProject } from '@/lib/api/auth-helpers';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { emotion, projectId, limit } = body;

    if (!emotion || !projectId) {
      return NextResponse.json(
        { error: 'Emotion and projectId are required' },
        { status: 400 }
      );
    }

    // Check authentication and project access
    const { user, error: authError } = await authenticateUserForProject(projectId);
    if (authError) return authError;

    const serviceManager = ServiceManager.getInstance();
    await serviceManager.initialize();
    
    const searchService = await serviceManager.getService('semantic-search');

    if (!searchService) {
      return NextResponse.json(
        { error: 'Semantic search service not available' },
        { status: 503 }
      );
    }

    const searchResult = await (searchService as SemanticSearchService).searchByEmotion(
      emotion,
      projectId,
      limit || 10
    );

    if (!searchResult.success) {
      return NextResponse.json(
        { error: searchResult.error || 'Emotion search failed' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      results: searchResult.data,
      emotion,
      total: searchResult.data?.length || 0
    });

  } catch (error) {
    console.error('Emotion search API error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false 
      },
      { status: 500 }
    );
  }
}