import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { logger } from '@/lib/services/logger'

export async function POST(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Please log in to accept the invitation' }, { status: 401 })
    }

    const inviteToken = params.token

    // Find the invitation
    const { data: invitation, error: inviteError } = await supabase
      .from('project_collaborators')
      .select(`
        *,
        project:projects(
          id,
          title,
          user_id
        )
      `)
      .eq('invite_token', inviteToken)
      .eq('status', 'pending')
      .single()

    if (inviteError || !invitation) {
      return NextResponse.json({ error: 'Invalid or expired invitation' }, { status: 404 })
    }

    // Check if invitation has expired
    if (new Date(invitation.invite_expires_at) < new Date()) {
      return NextResponse.json({ error: 'This invitation has expired' }, { status: 400 })
    }

    // Update the invitation with the actual user ID
    const { error: updateError } = await supabase
      .from('project_collaborators')
      .update({
        user_id: user.id,
        status: 'active',
        joined_at: new Date().toISOString(),
        invite_token: null,
        invite_expires_at: null
      })
      .eq('id', invitation.id)

    if (updateError) {
      logger.error('Failed to accept invitation', updateError)
      return NextResponse.json({ error: 'Failed to accept invitation' }, { status: 500 })
    }

    // Create initial collaboration session for this user
    const sessionId = `project:${invitation.project.id}:welcome`
    await supabase
      .from('collaboration_sessions')
      .upsert({
        id: sessionId,
        project_id: invitation.project.id,
        owner_id: invitation.project.user_id,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'id'
      })

    return NextResponse.json({
      success: true,
      project: {
        id: invitation.project.id,
        title: invitation.project.title,
        role: invitation.role
      }
    })

  } catch (error) {
    logger.error('Error accepting invitation', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const supabase = await createClient()
    const inviteToken = params.token

    // Find the invitation
    const { data: invitation, error } = await supabase
      .from('project_collaborators')
      .select(`
        *,
        project:projects(
          id,
          title
        ),
        inviter:profiles!project_collaborators_invited_by_fkey(
          id,
          full_name,
          email
        )
      `)
      .eq('invite_token', inviteToken)
      .eq('status', 'pending')
      .single()

    if (error || !invitation) {
      return NextResponse.json({ error: 'Invalid or expired invitation' }, { status: 404 })
    }

    // Check if invitation has expired
    if (new Date(invitation.invite_expires_at) < new Date()) {
      return NextResponse.json({ error: 'This invitation has expired' }, { status: 400 })
    }

    return NextResponse.json({
      invitation: {
        id: invitation.id,
        project: {
          id: invitation.project.id,
          title: invitation.project.title
        },
        inviter: {
          name: invitation.inviter.full_name || invitation.inviter.email
        },
        role: invitation.role,
        expires_at: invitation.invite_expires_at
      }
    })

  } catch (error) {
    logger.error('Error fetching invitation', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}