'use client'

import { useState } from 'react'
import { logger } from '@/lib/services/logger';

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { toast } from '@/hooks/use-toast'
import { 
  BookOpen,
  Sparkles,
  Copy,
  Download,
  RefreshCw,
  CheckCircle2,
  Edit3,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface BookSummaryGeneratorProps {
  projectId: string
  bookTitle: string
  totalWordCount: number
  chapterCount: number
  className?: string
}

interface SummaryType {
  id: string
  name: string
  description: string
  wordCount: number
  template: string
}

const summaryTypes: SummaryType[] = [
  {
    id: 'elevator-pitch',
    name: 'Elevator Pitch',
    description: 'A 30-second pitch for your book',
    wordCount: 50,
    template: 'hook'
  },
  {
    id: 'back-cover',
    name: 'Back Cover Blurb',
    description: 'Compelling copy for your book\'s back cover',
    wordCount: 150,
    template: 'blurb'
  },
  {
    id: 'synopsis',
    name: 'Synopsis',
    description: 'Detailed plot summary for agents/publishers',
    wordCount: 500,
    template: 'synopsis'
  },
  {
    id: 'query-letter',
    name: 'Query Letter',
    description: 'Professional query letter for literary agents',
    wordCount: 300,
    template: 'query'
  },
  {
    id: 'amazon-description',
    name: 'Amazon Description',
    description: 'SEO-optimized description for online retailers',
    wordCount: 200,
    template: 'retail'
  }
]

export function BookSummaryGenerator({
  projectId,
  bookTitle,
  totalWordCount,
  chapterCount,
  className
}: BookSummaryGeneratorProps) {
  const [selectedType, setSelectedType] = useState<string>('back-cover')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedSummaries, setGeneratedSummaries] = useState<Record<string, string>>({})
  const [editedSummaries, setEditedSummaries] = useState<Record<string, string>>({})
  const [targetAudience, setTargetAudience] = useState('general')
  const [tone, setTone] = useState('professional')

  const handleGenerate = async (typeId: string) => {
    setIsGenerating(true)
    
    try {
      const response = await fetch('/api/analysis/book-summary', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId,
          summaryType: typeId,
          targetAudience,
          tone,
          regenerate: !!generatedSummaries[typeId]
        })
      })

      if (!response.ok) throw new Error('Failed to generate summary')

      const data = await response.json()
      setGeneratedSummaries(prev => ({
        ...prev,
        [typeId]: data.summary
      }))
      setEditedSummaries(prev => ({
        ...prev,
        [typeId]: data.summary
      }))

      toast({
        title: 'Summary generated',
        description: `Your ${summaryTypes.find(t => t.id === typeId)?.name} has been created.`
      })
    } catch (error) {
      logger.error('Error generating summary:', error);
      toast({
        variant: 'destructive',
        title: 'Generation failed',
        description: 'Failed to generate summary. Please try again.'
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: 'Copied',
      description: 'Summary copied to clipboard'
    })
  }

  const handleExport = () => {
    const allSummaries = Object.entries(editedSummaries)
      .map(([typeId, summary]) => {
        const type = summaryTypes.find(t => t.id === typeId)
        return `## ${type?.name}\n\n${summary}\n\n---\n`
      })
      .join('\n')

    const blob = new Blob([allSummaries], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${bookTitle.replace(/\s+/g, '-')}-summaries.md`
    a.click()
    URL.revokeObjectURL(url)
  }

  const currentType = summaryTypes.find(t => t.id === selectedType)
  const hasGeneratedSummary = !!generatedSummaries[selectedType]
  const currentSummary = editedSummaries[selectedType] || ''

  return (
    <Card className={`w-full ${className || ''}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Book Summary Generator
            </CardTitle>
            <CardDescription>
              Create professional summaries and marketing copy for your book
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleExport}
            disabled={Object.keys(editedSummaries).length === 0}
          >
            <Download className="h-4 w-4 mr-2" />
            Export All
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Book Stats */}
        <div className="flex gap-4 text-sm">
          <Badge variant="secondary">
            {totalWordCount.toLocaleString()} words
          </Badge>
          <Badge variant="secondary">
            {chapterCount} chapters
          </Badge>
        </div>

        {/* Configuration */}
        <div className="grid gap-4 md:grid-cols-3">
          <div className="space-y-2">
            <Label>Summary Type</Label>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {summaryTypes.map(type => (
                  <SelectItem key={type.id} value={type.id}>
                    <div className="flex items-center gap-2">
                      {type.name}
                      {generatedSummaries[type.id] && (
                        <CheckCircle2 className="h-3 w-3 text-green-500" />
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Target Audience</Label>
            <Select value={targetAudience} onValueChange={setTargetAudience}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="general">General Readers</SelectItem>
                <SelectItem value="young-adult">Young Adult</SelectItem>
                <SelectItem value="literary">Literary Fiction Readers</SelectItem>
                <SelectItem value="genre">Genre Enthusiasts</SelectItem>
                <SelectItem value="professional">Industry Professionals</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Tone</Label>
            <Select value={tone} onValueChange={setTone}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="professional">Professional</SelectItem>
                <SelectItem value="casual">Casual</SelectItem>
                <SelectItem value="dramatic">Dramatic</SelectItem>
                <SelectItem value="humorous">Humorous</SelectItem>
                <SelectItem value="mysterious">Mysterious</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Summary Type Info */}
        {currentType && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>{currentType.name}:</strong> {currentType.description} 
              (Target: ~{currentType.wordCount} words)
            </AlertDescription>
          </Alert>
        )}

        {/* Generate/Regenerate Button */}
        <div className="flex gap-2">
          <Button 
            onClick={() => handleGenerate(selectedType)}
            disabled={isGenerating}
            className="flex-1"
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : hasGeneratedSummary ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Regenerate
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                Generate {currentType?.name}
              </>
            )}
          </Button>
        </div>

        {/* Generated Summary */}
        {hasGeneratedSummary && (
          <Tabs defaultValue="edit" className="space-y-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="edit">Edit</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>

            <TabsContent value="edit" className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Generated {currentType?.name}</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleCopy(currentSummary)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <Textarea
                  value={currentSummary}
                  onChange={(e) => setEditedSummaries(prev => ({
                    ...prev,
                    [selectedType]: e.target.value
                  }))}
                  className="min-h-[200px] font-mono text-sm"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>
                    {currentSummary.split(/\s+/).filter(w => w.length > 0).length} words
                  </span>
                  {currentSummary !== generatedSummaries[selectedType] && (
                    <Badge variant="outline" className="text-xs">
                      <Edit3 className="h-3 w-3 mr-1" />
                      Edited
                    </Badge>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="prose prose-sm max-w-none">
                    {currentSummary.split('\n\n').map((paragraph, i) => (
                      <p key={i}>{paragraph}</p>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}