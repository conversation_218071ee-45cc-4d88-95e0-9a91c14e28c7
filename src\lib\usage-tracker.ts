import { createClient } from '@/lib/supabase/server'
import { logger } from '@/lib/services/logger';

import { checkUsageLimit, getUserTier, type SubscriptionTier } from '@/lib/subscription'

export interface UsageEvent {
  userId: string
  eventType: 'ai_generation' | 'ai_words' | 'project_creation' | 'export' | 'storage_usage'
  metadata?: Record<string, unknown>
  amount?: number
}

export async function trackUsage(event: UsageEvent): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createClient()
    
    // Get current period (month)
    const now = new Date()
    const periodStart = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().slice(0, 7)
    
    // Get or create usage record for this period
    let { data: usage } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', event.userId)
      .eq('period_start', periodStart)
      .single()

    if (!usage) {
      const { data: newUsage, error } = await supabase
        .from('usage_tracking')
        .insert({
          user_id: event.userId,
          period_start: periodStart,
          ai_generations: 0,
          ai_words_used: 0,
          projects: 0,
          exports: 0,
          storage_used: 0
        })
        .select()
        .single()

      if (error) throw error
      usage = newUsage
    }

    // Update usage based on event type
    const updates: Record<string, number> = {}
    
    switch (event.eventType) {
      case 'ai_generation':
        updates.ai_generations = usage.ai_generations + (event.amount || 1)
        break
      case 'ai_words':
        updates.ai_words_used = (usage.ai_words_used || 0) + (event.amount || 0)
        break
      case 'project_creation':
        updates.projects = usage.projects + (event.amount || 1)
        break
      case 'export':
        updates.exports = usage.exports + (event.amount || 1)
        break
      case 'storage_usage':
        updates.storage_used = event.amount || usage.storage_used
        break
    }

    // Update the usage record
    const { error: updateError } = await supabase
      .from('usage_tracking')
      .update(updates)
      .eq('id', usage.id)

    if (updateError) throw updateError

    // Log the event
    await supabase
      .from('usage_events')
      .insert({
        user_id: event.userId,
        event_type: event.eventType,
        amount: event.amount || 1,
        metadata: event.metadata
      })

    return { success: true }
  } catch (error) {
    logger.error('Usage tracking error:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function checkUsageBeforeAction(
  userId: string,
  actionType: 'ai_generation' | 'ai_words' | 'project_creation' | 'export' | 'storage_usage',
  amount: number = 1
): Promise<{ allowed: boolean; reason?: string; usage?: Record<string, unknown>; limits?: Record<string, unknown> }> {
  try {
    const supabase = await createClient()
    
    // Get user's subscription
    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single()

    // Get current usage
    const periodStart = new Date().toISOString().slice(0, 7)
    const { data: usage } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', userId)
      .eq('period_start', periodStart)
      .single()

    const currentUsage = usage || {
      ai_generations: 0,
      ai_words_used: 0,
      projects: 0,
      exports: 0,
      storage_used: 0
    }

    // Check limits based on action type
    let limitType: keyof SubscriptionTier['limits']
    let currentAmount: number
    
    switch (actionType) {
      case 'ai_generation':
        limitType = 'monthlyWords' // Changed to use word limits
        currentAmount = currentUsage.ai_words_used || 0
        break
      case 'ai_words':
        limitType = 'monthlyWords'
        currentAmount = currentUsage.ai_words_used || 0
        break
      case 'project_creation':
        limitType = 'projects'
        currentAmount = currentUsage.projects
        break
      case 'export':
        // Export limits are format-based, not numeric
        return { allowed: true, usage: currentUsage, limits: getUserTier(subscription).limits }
      case 'storage_usage':
        limitType = 'storageGB'
        currentAmount = currentUsage.storage_used
        break
      default:
        return { allowed: false, reason: 'Invalid action type' }
    }

    const limitCheck = checkUsageLimit(subscription, limitType, currentAmount + amount - 1)
    
    if (!limitCheck.allowed) {
      const tier = getUserTier(subscription)
      return {
        allowed: false,
        reason: `You've reached your ${tier.name} plan limit for ${actionType.replace('_', ' ')}. Please upgrade to continue.`,
        usage: currentUsage,
        limits: tier.limits
      }
    }

    return {
      allowed: true,
      usage: currentUsage,
      limits: getUserTier(subscription).limits
    }
  } catch (error) {
    logger.error('Usage check error:', error);
    return { allowed: false, reason: 'Failed to check usage limits' }
  }
}

export async function getUserUsageStats(userId: string) {
  try {
    const supabase = await createClient()
    
    // Get current period usage
    const periodStart = new Date().toISOString().slice(0, 7)
    const { data: currentUsage } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', userId)
      .eq('period_start', periodStart)
      .single()

    // Get subscription
    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single()

    const tier = getUserTier(subscription)
    const usage = currentUsage || {
      ai_generations: 0,
      ai_words_used: 0,
      projects: 0,
      exports: 0,
      storage_used: 0
    }

    // Calculate remaining limits
    const stats = {
      aiGenerations: {
        used: usage.ai_generations,
        limit: tier.limits.aiGenerations,
        remaining: tier.limits.aiGenerations === -1 ? -1 : Math.max(0, tier.limits.aiGenerations - usage.ai_generations)
      },
      projects: {
        used: usage.projects,
        limit: tier.limits.projects,
        remaining: tier.limits.projects === -1 ? -1 : Math.max(0, tier.limits.projects - usage.projects)
      },
      storage: {
        used: usage.storage_used,
        limit: tier.limits.storageGB,
        remaining: tier.limits.storageGB === -1 ? -1 : Math.max(0, tier.limits.storageGB - usage.storage_used)
      },
      tier: tier
    }

    return { success: true, stats }
  } catch (error) {
    logger.error('Usage stats error:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}