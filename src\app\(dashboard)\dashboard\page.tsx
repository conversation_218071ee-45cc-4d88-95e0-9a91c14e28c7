import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { AnalyticsDashboard } from '@/components/analytics/analytics-dashboard'
import { AnalyticsErrorBoundary } from '@/components/analytics/analytics-error-boundary'
import { FirstTimeUserWrapper } from '@/components/onboarding/first-time-user-wrapper'
import { StoryCreationFAB } from '@/components/story/story-creation-fab'


export default async function DashboardPage() {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect('/login')
  }
  
  const { data: projects } = await supabase
    .from('projects')
    .select(`
      id, 
      title, 
      primary_genre,
      series_books!series_books_project_id_fkey(
        series_id,
        book_number,
        series:series(
          id,
          title,
          universe_id,
          universe:universe_id(
            id,
            name
          )
        )
      )
    `)
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })
  
  return (
    <FirstTimeUserWrapper hasProjects={(projects?.length || 0) > 0}>
      <div className="min-h-screen bg-background">
        <div className="fixed inset-0 paper-texture opacity-30" />
        
        <div className="relative z-10 py-8">
          <div className="container">
            <div className="mb-8">
              <h1 className="text-3xl font-literary-display text-foreground mb-2">
                Writing Dashboard
              </h1>
              <p className="text-muted-foreground">
                Track your writing progress, analyze your productivity, and improve your craft
              </p>
            </div>
            
            <AnalyticsErrorBoundary>
              <AnalyticsDashboard 
                userId={user.id} 
                projects={projects || []}
              />
            </AnalyticsErrorBoundary>
          </div>
        </div>
      </div>
      
      <StoryCreationFAB />
    </FirstTimeUserWrapper>
  )
}