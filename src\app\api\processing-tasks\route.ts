import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/db/types'

const getTasksSchema = z.object({
  projectId: z.string().uuid().optional(),
  status: z.enum(['pending', 'processing', 'completed', 'failed', 'cancelled']).optional(),
  taskType: z.string().optional(),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 20),
  offset: z.string().optional().transform(val => val ? parseInt(val) : 0),
})

export async function GET(request: NextRequest) {
  try {
    const supabase: SupabaseClient<Database> = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const params = getTasksSchema.parse({
      projectId: searchParams.get('projectId'),
      status: searchParams.get('status'),
      taskType: searchParams.get('taskType'),
      limit: searchParams.get('limit'),
      offset: searchParams.get('offset'),
    })

    let query = supabase
      .from('processing_tasks')
      .select('*, projects(title)')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(params.offset, params.offset + params.limit - 1)

    if (params.projectId) {
      query = query.eq('project_id', params.projectId)
    }

    if (params.status) {
      query = query.eq('status', params.status)
    }

    if (params.taskType) {
      query = query.eq('task_type', params.taskType)
    }

    const { data: tasks, error } = await query

    if (error) {
      console.error('Error fetching processing tasks:', error)
      return NextResponse.json({ error: 'Failed to fetch processing tasks' }, { status: 500 })
    }

    // Get task statistics
    const { data: stats } = await supabase
      .from('processing_tasks')
      .select('status')
      .eq('user_id', user.id)

    const taskStats = {
      total: stats?.length || 0,
      pending: stats?.filter((t: { status: string }) => t.status === 'pending').length || 0,
      processing: stats?.filter((t: { status: string }) => t.status === 'processing').length || 0,
      completed: stats?.filter((t: { status: string }) => t.status === 'completed').length || 0,
      failed: stats?.filter((t: { status: string }) => t.status === 'failed').length || 0,
      cancelled: stats?.filter((t: { status: string }) => t.status === 'cancelled').length || 0,
    }

    return NextResponse.json({
      tasks: tasks || [],
      stats: taskStats,
      limit: params.limit,
      offset: params.offset,
    })
  } catch (error) {
    console.error('Error in processing tasks GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase: SupabaseClient<Database> = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'cancel') {
      const body = await request.json()
      const { taskIds } = body

      if (!taskIds || !Array.isArray(taskIds) || taskIds.length === 0) {
        return NextResponse.json({ error: 'Task IDs required' }, { status: 400 })
      }

      // Cancel tasks that are pending or processing
      const { data: cancelled, error: cancelError } = await supabase
        .from('processing_tasks')
        .update({ 
          status: 'cancelled',
          updated_at: new Date().toISOString(),
          completed_at: new Date().toISOString(),
        })
        .eq('user_id', user.id)
        .in('id', taskIds)
        .in('status', ['pending', 'processing'])
        .select()

      if (cancelError) {
        console.error('Error cancelling tasks:', cancelError)
        return NextResponse.json({ error: 'Failed to cancel tasks' }, { status: 500 })
      }

      return NextResponse.json({ 
        success: true,
        cancelled: cancelled?.length || 0,
        message: `${cancelled?.length || 0} task(s) cancelled` 
      })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
  } catch (error) {
    console.error('Error in processing tasks POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase: SupabaseClient<Database> = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const taskId = searchParams.get('id')

    if (!taskId) {
      // Delete all completed/failed/cancelled tasks older than 30 days
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const { error: deleteError } = await supabase
        .from('processing_tasks')
        .delete()
        .eq('user_id', user.id)
        .in('status', ['completed', 'failed', 'cancelled'])
        .lt('completed_at', thirtyDaysAgo.toISOString())

      if (deleteError) {
        console.error('Error cleaning up old tasks:', deleteError)
        return NextResponse.json({ error: 'Failed to clean up old tasks' }, { status: 500 })
      }

      return NextResponse.json({ 
        success: true,
        message: 'Old tasks cleaned up successfully' 
      })
    } else {
      // Delete specific task if it's completed, failed, or cancelled
      const { data: task } = await supabase
        .from('processing_tasks')
        .select('status')
        .eq('id', taskId)
        .eq('user_id', user.id)
        .single()

      if (!task) {
        return NextResponse.json({ error: 'Task not found' }, { status: 404 })
      }

      if (!['completed', 'failed', 'cancelled'].includes(task.status)) {
        return NextResponse.json({ error: 'Cannot delete active tasks' }, { status: 400 })
      }

      const { error: deleteError } = await supabase
        .from('processing_tasks')
        .delete()
        .eq('id', taskId)
        .eq('user_id', user.id)

      if (deleteError) {
        console.error('Error deleting task:', deleteError)
        return NextResponse.json({ error: 'Failed to delete task' }, { status: 500 })
      }

      return NextResponse.json({ success: true, message: 'Task deleted' })
    }
  } catch (error) {
    console.error('Error in processing tasks DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}