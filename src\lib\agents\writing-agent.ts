import { logger } from '@/lib/services/logger';
import { BaseAgent } from './base-agent';
import { ChapterContent, ChapterOutline } from './types';
import { ContextManager } from '../services/context-manager';
import { AI_MODELS, getAIConfig, AI_CONTEXT_LIMITS } from '../config/ai-settings';
import { tokenManager, fitsInContext, truncateToFit } from '../services/token-manager';
import { AIServiceBase } from '../services/ai-service-base';
import type { ChatCompletionMessageParam } from 'openai/resources/chat';

export class WritingAgent extends BaseAgent {
  private contextManager?: ContextManager;
  private aiService: AIServiceBase;

  constructor(context: import('./types').BookContext, contextManager?: ContextManager) {
    super(context);
    this.contextManager = contextManager;
    this.aiService = new WritingAgentAIService();
  }

  async execute(): Promise<Record<string, unknown>> {
    // Execute method for base class compatibility
    // When used through orchestrator, chapter info should be in context
    const currentChapterIndex = this.context.currentProgress?.chaptersWritten || 0;
    const chapterOutlines = this.context.chapterOutlines?.chapters;
    
    if (!chapterOutlines || currentChapterIndex >= chapterOutlines.length) {
      throw new Error('No chapter outline available for writing');
    }
    
    const chapterToWrite = chapterOutlines[currentChapterIndex];
    const chapterContent = await this.writeChapter(chapterToWrite);
    
    return {
      type: 'chapter_written',
      chapterNumber: chapterContent.chapterNumber,
      title: chapterContent.title,
      content: chapterContent.content,
      wordCount: chapterContent.wordCount,
      scenes: chapterContent.scenes,
      characterVoices: chapterContent.characterVoices,
      themes: chapterContent.themes,
      continuityNotes: chapterContent.continuityNotes,
      continuityWarnings: chapterContent.continuityWarnings
    };
  }

  async writeChapter(chapterOutline: ChapterOutline): Promise<ChapterContent> {
    if (!this.context.storyStructure || !this.context.characters || !this.context.chapterOutlines) {
      throw new Error('Writing Agent requires complete story context');
    }

    // Build advanced writing techniques guidance
    const advancedTechniques = [];
    
    if (this.context.settings?.useDeepPOV) {
      advancedTechniques.push(`DEEP POV IMMERSION: Filter EVERYTHING through ${chapterOutline.povCharacter}'s consciousness. No narrator observations - only what the character perceives, thinks, and feels.`);
    }
    
    if (this.context.settings?.showDontTell) {
      advancedTechniques.push(`SHOW DON'T TELL PRIORITY: Convert ALL emotions and states into actions, dialogue, and sensory details. Never state feelings directly.`);
    }
    
    if (this.context.settings?.layeredMetaphors) {
      advancedTechniques.push(`METAPHORICAL DEPTH: Weave recurring symbolic imagery throughout. Create metaphors that resonate with themes and character arcs.`);
    }
    
    if (this.context.settings?.sensoryRich) {
      advancedTechniques.push(`SENSORY IMMERSION: Include all five senses in every scene. Use unexpected sensory details to create atmosphere.`);
    }
    
    if (this.context.settings?.subtextHeavy) {
      advancedTechniques.push(`SUBTEXT MASTERY: Every dialogue exchange must have underlying meaning. Characters rarely say what they truly mean.`);
    }
    
    if (this.context.settings?.varyProse) {
      advancedTechniques.push(`PROSE RHYTHM: Intentionally vary sentence length and structure. Create musicality through repetition, alliteration, and cadence.`);
    }
    
    if (this.context.settings?.emotionalNuance) {
      advancedTechniques.push(`EMOTIONAL COMPLEXITY: Layer contradictory emotions. Show internal conflict through physical sensations and micro-actions.`);
    }
    
    if (this.context.settings?.cinematicScenes) {
      advancedTechniques.push(`CINEMATIC QUALITY: Write scenes as if directing a film. Focus on visual composition, movement, and dramatic angles.`);
    }
    
    if (this.context.settings?.literaryAllusions) {
      advancedTechniques.push(`LITERARY ALLUSIONS: Include subtle references to classic literature, mythology, or cultural works that enhance meaning.`);
    }
    
    if (this.context.settings?.preciseLanguage) {
      advancedTechniques.push(`PRECISE LANGUAGE: Choose specific, evocative words. Replace generic verbs and adjectives with precise, unusual choices.`);
    }

    const systemPrompt = this.createSystemPrompt(
      'Writing Agent',
      `You are a master novelist with the prose beauty of Donna Tartt, the emotional depth of Khaled Hosseini, the page-turning momentum of Gillian Flynn, and the immersive world-building of Brandon Sanderson. Your writing must meet the standards of NYT bestsellers and literary award winners.

LITERARY EXCELLENCE STANDARDS:
- Every sentence must earn its place through beauty, clarity, or impact
- Show, don't tell—use actions, dialogue, and sensory details to convey information
- Create prose that readers will highlight and share on social media
- Balance commercial readability with literary sophistication
- Write scenes that could be excerpted in writing workshops as examples of excellence

IMMERSIVE WRITING TECHNIQUES:
- SENSORY ENGAGEMENT: Use all five senses in unexpected ways
- EMOTIONAL LAYERING: Surface emotions hiding deeper truths
- SUBTEXT MASTERY: Characters say one thing but mean another
- METAPHOR & SYMBOLISM: Layer meaning without being heavy-handed
- RHYTHM & FLOW: Vary sentence structure for musical prose

CHAPTER REQUIREMENTS:
- Target: ${chapterOutline.wordCountTarget} words (quality over quantity)
- POV: ${chapterOutline.povCharacter} (deep POV immersion)
- Voice: ${this.context.settings?.narrativeVoice || 'Third Person'} (with personality)
- Tense: ${this.context.settings?.tense || 'Past'} (consistent and invisible)
- Style: ${this.context.settings?.writingStyle || 'Standard'} (elevated to excellence)
- Tone: ${this.context.settings?.tone?.join(', ') || 'Not specified'} (nuanced and layered)

SHOW DON'T TELL MASTERY:
- Replace "she was angry" with her actions, dialogue, internal sensations
- Convert exposition into conflict-driven scenes
- Use specific details instead of generic descriptions
- Let readers draw conclusions from evidence, not statements
- Trust reader intelligence—imply rather than explain

DIALOGUE EXCELLENCE:
- Each character's voice must be distinct without tags
- Subtext in every exchange—what's not said matters
- Avoid "as you know" exposition dumps
- Use interruptions, silence, and action beats effectively
- Make every line either reveal character or advance plot

SCENE CONSTRUCTION:
- Start scenes as late as possible, leave as early as possible
- Include micro-tensions in every paragraph
- Layer external action with internal reaction
- Create scenes that serve multiple purposes simultaneously
- End scenes with questions, not answers

EMOTIONAL RESONANCE:
- Make readers feel first, think second
- Use physical sensations to convey emotions
- Create moments of unexpected vulnerability
- Build to emotional peaks that feel earned
- Leave space for readers to fill with their own experiences

Remember: You're not just writing a chapter—you're creating an experience readers will remember forever. Every word should contribute to the spell that keeps them reading until 3 AM.

${advancedTechniques.length > 0 ? `
ADVANCED WRITING TECHNIQUES TO APPLY:
${advancedTechniques.join('\n\n')}
` : ''}`
    );

    // Get intelligent context from memory manager if available
    let memoryContext = '';
    if (this.contextManager) {
      const writingContext = await this.contextManager.getWritingContext(
        chapterOutline.number,
        `Chapter ${chapterOutline.number}: ${chapterOutline.title}. ${chapterOutline.summary}`
      );
      
      memoryContext = `
RELEVANT MEMORY CONTEXT:
${writingContext.contextSummary}

CHARACTER STATES:
${JSON.stringify(writingContext.characterStates, null, 2)}

RECENT EVENTS:
${writingContext.recentEvents.join('\n')}

ACTIVE PLOT THREADS:
${writingContext.plotThreads.map(thread => `- ${thread.description}`).join('\n')}
`;
    }

    const contextData = this.buildChapterContext(chapterOutline);
    
    // Estimate token usage and truncate if necessary
    const estimatedTokens = this.estimateTokens(systemPrompt + contextData + memoryContext);
    let truncatedContext = contextData;
    
    if (estimatedTokens > AI_CONTEXT_LIMITS.MAX_INPUT_TOKENS) {
      logger.warn(`Context too large (${estimatedTokens} tokens);, truncating...`);
      // Prioritize recent context and character states
      truncatedContext = this.truncateContext(contextData, AI_CONTEXT_LIMITS.MAX_INPUT_TOKENS - 2000);
    }

    const messages: ChatCompletionMessageParam[] = [
      { role: 'system', content: systemPrompt },
      {
        role: 'user',
        content: `Write Chapter ${chapterOutline.number}: "${chapterOutline.title}"

CHAPTER OUTLINE:
${JSON.stringify(chapterOutline, null, 2)}

STORY CONTEXT:
${truncatedContext}

${memoryContext}

Write the complete chapter content following these requirements:
1. Start with an engaging opening that draws readers in
2. Follow the scene structure outlined in the chapter plan
3. Maintain the established writing style and character voices
4. Include dialogue, action, and internal thoughts appropriate to the POV
5. Build toward the planned conflicts and resolutions
6. End with the specified cliffhanger or transition

Target approximately ${chapterOutline.wordCountTarget} words.`
      }
    ];

    const tools = [
      {
        type: 'function' as const,
        function: {
          name: 'write_chapter',
          description: 'Write complete chapter content',
          parameters: {
            type: 'object',
            properties: {
              chapterNumber: { type: 'number' },
              title: { type: 'string' },
              content: { type: 'string', description: 'Full chapter text content' },
              wordCount: { type: 'number' },
              scenes: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    content: { type: 'string' },
                    wordCount: { type: 'number' },
                    purpose: { type: 'string' }
                  }
                }
              },
              characterVoices: { type: 'array', items: { type: 'string' } },
              themes: { type: 'array', items: { type: 'string' } },
              continuityNotes: { type: 'array', items: { type: 'string' } }
            },
            required: ['chapterNumber', 'title', 'content', 'wordCount', 'scenes', 'characterVoices', 'themes']
          }
        }
      }
    ];

    const aiConfig = getAIConfig('CHAPTER_WRITING');
    
    const completionResult = await this.aiService.generateWithAI<{
      choices: Array<{
        message: {
          tool_calls?: Array<{
            function: {
              arguments: string;
            }
          }>;
        }
      }>;
    }>(
      messages[messages.length - 1].content as string,
      {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.max_tokens,
        systemPrompt: messages[0].content as string,
        responseFormat: 'json',
        retryConfig: {
          maxRetries: 3,
          onRetry: (error, attempt) => {
            logger.info(`Chapter writing retry ${attempt}:`, error.message);
          }
        }
      }
    );
    
    if (!completionResult.success || !completionResult.data) {
      throw new Error(completionResult.error || 'Failed to generate chapter');
    }
    
    const completion = completionResult.data;
    
    if (completion.choices[0]?.message.tool_calls) {
      const toolCall = completion.choices[0].message.tool_calls[0];
      if (toolCall) {
        const result = JSON.parse(toolCall.function.arguments);
        
        // Update memory with the new chapter content
        if (this.contextManager) {
          await this.contextManager.addChapterContent(result);
          
          // Check for inconsistencies
          const inconsistencies = await this.contextManager.findInconsistencies(
            result.content,
            result.chapterNumber
          );
          
          if (inconsistencies.length > 0) {
            result.continuityWarnings = inconsistencies;
            logger.warn(`Chapter ${result.chapterNumber} inconsistencies:`, inconsistencies);
          }
        }
        
        return result;
      }
    }
    
    throw new Error('Writing Agent failed to generate chapter content');
  }

  private buildChapterContext(chapterOutline: ChapterOutline): string {
    const characters = this.context.characters!;
    const relevantCharacters = [
      ...characters.protagonists,
      ...characters.antagonists,
      ...characters.supporting
    ].filter(char => 
      chapterOutline.scenes.some(scene => 
        scene.characters.includes(char.name) || scene.characters.includes(char.id)
      )
    );

    const previousChapters = this.context.chapterOutlines!.chapters
      .filter(ch => ch.number < chapterOutline.number)
      .slice(-2); // Last 2 chapters for context

    return `
RELEVANT CHARACTERS:
${relevantCharacters.map(char => `
${char.name} (${char.role}):
- Personality: ${char.personality.traits.join(', ')}
- Current Arc Point: ${char.arc.startingPoint}
- Voice: ${char.voice.speakingStyle}
- Motivation: ${char.motivation}
`).join('\n')}

PREVIOUS CHAPTERS CONTEXT:
${previousChapters.map(ch => `
Chapter ${ch.number}: ${ch.title}
- Summary: ${ch.summary}
- Key resolutions: ${ch.resolutions.join('; ')}
`).join('\n')}

CHARACTER STATES AT CHAPTER START:
${chapterOutline.characterStates.map(state => `
${state.characterId}: ${state.emotionalState} | ${state.physicalState}
Knowledge: ${state.knowledge.join(', ')}
`).join('\n')}

WORLD SETTING:
- Time Period: ${this.context.settings?.timePeriod || 'Contemporary'}
- World Type: ${this.context.settings?.worldType || 'Real World'}
- Magic/Tech Level: ${this.context.settings?.magicTechLevel || 'None'}
    `.trim();
  }
  
  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }
  
  private truncateContext(context: string, maxLength: number): string {
    if (context.length <= maxLength * 4) return context;
    
    // Prioritize keeping character information and recent context
    const sections = context.split('\n\n');
    const prioritySections = sections.filter(section => 
      section.includes('RELEVANT CHARACTERS:') ||
      section.includes('CHARACTER STATES AT CHAPTER START:') ||
      section.includes('PREVIOUS CHAPTERS CONTEXT:')
    );
    
    const otherSections = sections.filter(section => 
      !prioritySections.includes(section)
    );
    
    let result = prioritySections.join('\n\n');
    
    // Add other sections until we reach the limit
    for (const section of otherSections) {
      if ((result + '\n\n' + section).length <= maxLength * 4) {
        result += '\n\n' + section;
      } else {
        break;
      }
    }
    
    return result;
  }
}

// Dedicated AI service for Writing Agent
class WritingAgentAIService extends AIServiceBase {
  constructor() {
    super({
      name: 'WritingAgentAI',
      version: '1.0.0',
      endpoints: ['/api/agents/write'],
      healthCheck: 'writing-agent-health'
    });
  }
}