version: '3.8'

services:
  bookscribe-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: bookscribe-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
    env_file:
      - .env.local
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    restart: unless-stopped
    networks:
      - bookscribe-dev-network
    command: npm run dev

networks:
  bookscribe-dev-network:
    driver: bridge
