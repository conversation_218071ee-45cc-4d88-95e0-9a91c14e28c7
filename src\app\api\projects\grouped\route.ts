import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getPaginationParams } from '@/lib/api/middleware'
import { z } from 'zod'

// Validation schema for query parameters
const groupedProjectsQuerySchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(20),
  seriesLimit: z.coerce.number().int().positive().max(100).default(10),
  standaloneLimit: z.coerce.number().int().positive().max(100).default(10),
  search: z.string().optional(),
  sortBy: z.enum(['created_at', 'updated_at', 'title']).default('updated_at'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
})

interface SeriesBookWithProject {
  book_number: number
  book_role: string
  projects: {
    id: string
    title: string
    description?: string
    status: string
    current_word_count: number
    target_word_count: number
    primary_genre?: string
    created_at: string
    updated_at: string
  } | null
}

interface _SeriesWithBooks {
  id: string
  title: string
  description?: string
  genre?: string
  planned_book_count?: number
  current_book_count: number
  publication_status: string
  created_at: string
  series_books?: SeriesBookWithProject[]
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = {
      page: searchParams.get('page'),
      limit: searchParams.get('limit'),
      seriesLimit: searchParams.get('seriesLimit'),
      standaloneLimit: searchParams.get('standaloneLimit'),
      search: searchParams.get('search'),
      sortBy: searchParams.get('sortBy'),
      sortOrder: searchParams.get('sortOrder')
    }

    const validatedQuery = groupedProjectsQuerySchema.parse(queryParams)

    // Skip series functionality until database tables are created
    const series: unknown[] = []
    const seriesBooks: unknown[] = []

    // Fetch standalone projects (not in any series)
    // Note: We'll fetch all projects and filter out those in series

    // Apply search filter if provided
    let projectsQuery = supabase
      .from('projects')
      .select('*', { count: 'exact' })
      .eq('user_id', user.id)

    if (validatedQuery.search) {
      const searchTerm = `%${validatedQuery.search}%`
      projectsQuery = projectsQuery.or(`title.ilike.${searchTerm},description.ilike.${searchTerm}`)
    }

    // Apply sorting
    projectsQuery = projectsQuery.order(validatedQuery.sortBy, { ascending: validatedQuery.sortOrder === 'asc' })

    // To get standalone projects, we need to fetch all projects and filter out those in series
    const { data: allProjects, error: allProjectsError, count: totalProjectsCount } = await projectsQuery

    if (allProjectsError) {
      console.error('Error fetching projects:', allProjectsError)
      return NextResponse.json({ error: 'Failed to fetch projects' }, { status: 500 })
    }

    // Create a map of projects by ID for easy lookup
    const projectsMap = new Map()
    allProjects?.forEach(project => {
      projectsMap.set(project.id, project)
    })

    // Get all project IDs that are in series
    const projectsInSeries = new Set<string>()
    seriesBooks?.forEach(sb => {
      if (sb.project_id) {
        projectsInSeries.add(sb.project_id)
      }
    })

    // Filter standalone projects
    const allStandalone = allProjects?.filter(p => !projectsInSeries.has(p.id)) || []

    // Apply pagination to standalone projects
    const standaloneOffset = (validatedQuery.page - 1) * validatedQuery.standaloneLimit
    const standalone = allStandalone.slice(standaloneOffset, standaloneOffset + validatedQuery.standaloneLimit)
    const totalStandalone = allStandalone.length

    // Apply pagination to series
    const seriesOffset = (validatedQuery.page - 1) * validatedQuery.seriesLimit
    const paginatedSeries = series?.slice(seriesOffset, seriesOffset + validatedQuery.seriesLimit) || []
    const totalSeries = series?.length || 0

    // Transform series data for better structure
    const seriesWithBooks = paginatedSeries?.map(s => {
      // Get books for this series
      const booksInSeries = seriesBooks
        ?.filter(sb => sb.series_id === s.id)
        ?.map(sb => {
          const project = projectsMap.get(sb.project_id)
          return project ? {
            ...project,
            book_number: sb.book_number,
            book_role: sb.book_role,
          } : null
        })
        ?.filter(book => book !== null)
        ?.sort((a, b) => a.book_number - b.book_number) || []

      return {
        id: s.id,
        title: s.title,
        description: s.description,
        genre: s.genre,
        planned_book_count: s.planned_book_count,
        current_book_count: s.current_book_count,
        publication_status: s.publication_status,
        created_at: s.created_at,
        books: booksInSeries
      }
    }) || []

    // Calculate totals
    const totalBooksInSeries = projectsInSeries.size
    const totalProjects = totalProjectsCount || (totalBooksInSeries + totalStandalone)

    // Calculate pagination metadata
    const seriesPages = Math.ceil(totalSeries / validatedQuery.seriesLimit)
    const standalonePages = Math.ceil(totalStandalone / validatedQuery.standaloneLimit)
    const maxPages = Math.max(seriesPages, standalonePages)

    return NextResponse.json({
      series: seriesWithBooks,
      standalone,
      stats: {
        totalSeries,
        totalBooksInSeries,
        totalStandalone,
        totalProjects,
      },
      pagination: {
        page: validatedQuery.page,
        seriesLimit: validatedQuery.seriesLimit,
        standaloneLimit: validatedQuery.standaloneLimit,
        seriesPages,
        standalonePages,
        maxPages,
        hasMore: validatedQuery.page < maxPages
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Invalid query parameters',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Error in grouped projects GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}