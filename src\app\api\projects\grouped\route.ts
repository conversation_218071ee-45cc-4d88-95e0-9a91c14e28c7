import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

interface SeriesBookWithProject {
  book_number: number
  book_role: string
  projects: {
    id: string
    title: string
    description?: string
    status: string
    current_word_count: number
    target_word_count: number
    primary_genre?: string
    created_at: string
    updated_at: string
  } | null
}

interface _SeriesWithBooks {
  id: string
  title: string
  description?: string
  genre?: string
  planned_book_count?: number
  current_book_count: number
  publication_status: string
  created_at: string
  series_books?: SeriesBookWithProject[]
}

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Skip series functionality until database tables are created
    const series: unknown[] = []
    const seriesBooks: unknown[] = []

    // Fetch standalone projects (not in any series)
    // Note: We'll fetch all projects and filter out those in series

    // To get standalone projects, we need to fetch all projects and filter out those in series
    const { data: allProjects, error: allProjectsError } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (allProjectsError) {
      console.error('Error fetching projects:', allProjectsError)
      return NextResponse.json({ error: 'Failed to fetch projects' }, { status: 500 })
    }

    // Create a map of projects by ID for easy lookup
    const projectsMap = new Map()
    allProjects?.forEach(project => {
      projectsMap.set(project.id, project)
    })

    // Get all project IDs that are in series
    const projectsInSeries = new Set<string>()
    seriesBooks?.forEach(sb => {
      if (sb.project_id) {
        projectsInSeries.add(sb.project_id)
      }
    })

    // Filter standalone projects
    const standalone = allProjects?.filter(p => !projectsInSeries.has(p.id)) || []

    // Transform series data for better structure
    const seriesWithBooks = series?.map(s => {
      // Get books for this series
      const booksInSeries = seriesBooks
        ?.filter(sb => sb.series_id === s.id)
        ?.map(sb => {
          const project = projectsMap.get(sb.project_id)
          return project ? {
            ...project,
            book_number: sb.book_number,
            book_role: sb.book_role,
          } : null
        })
        ?.filter(book => book !== null)
        ?.sort((a, b) => a.book_number - b.book_number) || []

      return {
        id: s.id,
        title: s.title,
        description: s.description,
        genre: s.genre,
        planned_book_count: s.planned_book_count,
        current_book_count: s.current_book_count,
        publication_status: s.publication_status,
        created_at: s.created_at,
        books: booksInSeries
      }
    }) || []

    // Calculate totals
    const totalSeries = seriesWithBooks.length
    const totalBooksInSeries = projectsInSeries.size
    const totalStandalone = standalone.length
    const totalProjects = totalBooksInSeries + totalStandalone

    return NextResponse.json({ 
      series: seriesWithBooks,
      standalone,
      stats: {
        totalSeries,
        totalBooksInSeries,
        totalStandalone,
        totalProjects,
      }
    })
  } catch (error) {
    console.error('Error in grouped projects GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}