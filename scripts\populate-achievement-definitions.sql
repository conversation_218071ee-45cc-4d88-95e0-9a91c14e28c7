-- =====================================================
-- POPULATE ACHIEVEMENT DEFINITIONS
-- Insert default achievement definitions for BookScribe AI
-- =====================================================

-- Insert default achievement definitions
INSERT INTO public.achievement_definitions (id, title, description, category, tier, icon, target_value, condition_type, condition_params, display_order) VALUES
-- Writing achievements
('first_words', 'First Words', 'Write your first 100 words', 'writing', 'bronze', '✏️', 100, 'word_count', '{"total": true}', 1),
('prolific_writer', 'Prolific Writer', 'Write 10,000 words', 'writing', 'silver', '📝', 10000, 'word_count', '{"total": true}', 2),
('novelist', 'Novelist', 'Write 50,000 words', 'writing', 'gold', '📚', 50000, 'word_count', '{"total": true}', 3),
('epic_author', 'Epic Author', 'Write 100,000 words', 'writing', 'platinum', '🏆', 100000, 'word_count', '{"total": true}', 4),

-- Consistency achievements
('getting_started', 'Getting Started', 'Write for 3 consecutive days', 'consistency', 'bronze', '🔥', 3, 'streak', '{}', 10),
('habit_builder', 'Habit Builder', 'Write for 7 consecutive days', 'consistency', 'silver', '💪', 7, 'streak', '{}', 11),
('dedicated_writer', 'Dedicated Writer', 'Write for 30 consecutive days', 'consistency', 'gold', '⭐', 30, 'streak', '{}', 12),
('writing_master', 'Writing Master', 'Write for 100 consecutive days', 'consistency', 'platinum', '👑', 100, 'streak', '{}', 13),

-- Quality achievements
('quality_conscious', 'Quality Conscious', 'Achieve 80% quality score on a chapter', 'quality', 'bronze', '✨', 80, 'quality_score', '{"minimum": 80}', 20),
('excellence_seeker', 'Excellence Seeker', 'Achieve 90% quality score on a chapter', 'quality', 'silver', '🌟', 90, 'quality_score', '{"minimum": 90}', 21),
('perfectionist', 'Perfectionist', 'Achieve 95% quality score on a chapter', 'quality', 'gold', '💎', 95, 'quality_score', '{"minimum": 95}', 22),
('quality_master', 'Quality Master', 'Maintain 85%+ quality across 10 chapters', 'quality', 'platinum', '🏅', 10, 'quality_score', '{"minimum": 85, "consecutive": 10}', 23),

-- Milestone achievements
('chapter_one', 'Chapter One', 'Complete your first chapter', 'milestones', 'bronze', '📖', 1, 'chapters', '{}', 30),
('five_chapters', 'Five Chapters', 'Complete 5 chapters', 'milestones', 'silver', '📑', 5, 'chapters', '{}', 31),
('halfway_there', 'Halfway There', 'Complete 50% of your book', 'milestones', 'gold', '📊', 50, 'custom', '{"type": "project_progress"}', 32),
('book_complete', 'Book Complete', 'Complete your first book', 'milestones', 'platinum', '🎉', 100, 'custom', '{"type": "project_complete"}', 33),

-- Special achievements
('night_owl', 'Night Owl', 'Write after midnight', 'special', 'bronze', '🦉', 1, 'custom', '{"type": "time_based", "hour": 0}', 40),
('early_bird', 'Early Bird', 'Write before 6 AM', 'special', 'bronze', '🌅', 1, 'custom', '{"type": "time_based", "hour": 6}', 41),
('marathon_session', 'Marathon Session', 'Write for 4 hours straight', 'special', 'silver', '⏱️', 240, 'custom', '{"type": "session_duration"}', 42),
('speed_demon', 'Speed Demon', 'Write 1000 words in an hour', 'special', 'gold', '⚡', 1000, 'custom', '{"type": "words_per_hour"}', 43)
ON CONFLICT (id) DO NOTHING;

-- Success message
SELECT 'SUCCESS: Achievement definitions populated!' as status, COUNT(*) as total_achievements FROM public.achievement_definitions;
