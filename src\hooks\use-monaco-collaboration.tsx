import { useEffect, useRef, useState, useCallback } from 'react'
import { editor as monacoEditor } from 'monaco-editor'
import { collaborationServiceRealtime, type CollaborationUser } from '@/lib/services/collaboration-service-realtime'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import type { UserSubscription } from '@/lib/subscription'

interface UseMonacoCollaborationOptions {
  sessionId: string
  userId: string
  userName: string
  userEmail: string
  subscription: UserSubscription | null
  enabled?: boolean
  onChange?: (content: string) => void
}

interface CollaboratorCursor {
  userId: string
  decoration: string[]
  color: string
}

export function useMonacoCollaboration(
  editor: monacoEditor.IStandaloneCodeEditor | null,
  options: UseMonacoCollaborationOptions
) {
  const { sessionId, userId, userName, userEmail, subscription, enabled = true, onChange } = options
  const { toast } = useToast()
  
  const [collaborators, setCollaborators] = useState<CollaborationUser[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const cursorsRef = useRef<Map<string, CollaboratorCursor>>(new Map())
  const decorationsRef = useRef<Map<string, string[]>>(new Map())
  const isInitializedRef = useRef(false)
  const isApplyingRemoteChange = useRef(false)
  
  // Connect to collaboration service
  useEffect(() => {
    if (!enabled || !editor || isInitializedRef.current) return
    
    const connect = async () => {
      try {
        // Check if user can use real-time collaboration
        if (!collaborationServiceRealtime.canUseRealTimeCollaboration(subscription)) {
          logger.info('Real-time collaboration not available for current subscription')
          return
        }
        
        await collaborationServiceRealtime.connect(sessionId, userId, userName, userEmail, subscription)
        setIsConnected(true)
        isInitializedRef.current = true
        setError(null)
        
        // Get initial collaborators
        const activeUsers = collaborationServiceRealtime.getActiveUsers(sessionId)
        setCollaborators(activeUsers.filter(u => u.id !== userId))
        
        toast({
          title: "Collaboration Connected",
          description: "Real-time collaboration is now active"
        })
      } catch (error) {
        const err = error as Error
        logger.error('Failed to connect to collaboration service', err)
        setError(err)
        toast({
          title: "Collaboration Error",
          description: err.message || "Failed to enable real-time collaboration",
          variant: "destructive"
        })
      }
    }
    
    connect()
    
    return () => {
      if (isInitializedRef.current) {
        collaborationServiceRealtime.disconnect(sessionId, userId)
        setIsConnected(false)
        isInitializedRef.current = false
        
        // Clean up all decorations
        cursorsRef.current.forEach((cursor) => {
          if (editor) {
            editor.deltaDecorations(cursor.decoration, [])
          }
        })
        decorationsRef.current.forEach((decorations) => {
          if (editor) {
            editor.deltaDecorations(decorations, [])
          }
        })
        cursorsRef.current.clear()
        decorationsRef.current.clear()
      }
    }
  }, [enabled, editor, sessionId, userId, userName, userEmail, subscription, toast])
  
  // Subscribe to collaboration events
  useEffect(() => {
    if (!isConnected || !editor) return
    
    const unsubscribe = collaborationServiceRealtime.subscribe(sessionId, (event) => {
      switch (event.type) {
        case 'user.joined':
          handleUserJoined(event.data)
          break
        case 'user.left':
          handleUserLeft(event.userId)
          break
        case 'cursor.moved':
          if (event.userId !== userId) {
            handleCursorMoved(event.userId, event.data)
          }
          break
        case 'selection.changed':
          if (event.userId !== userId) {
            handleSelectionChanged(event.userId, event.data)
          }
          break
        case 'content.changed':
          if (event.userId !== userId) {
            handleContentChanged(event.data)
          }
          break
      }
    })
    
    return unsubscribe
  }, [isConnected, editor, sessionId, userId])
  
  // Handle local cursor position changes
  useEffect(() => {
    if (!isConnected || !editor) return
    
    let lastPosition: { line: number; column: number } | null = null
    const sendCursorUpdate = (position: { line: number; column: number }) => {
      collaborationServiceRealtime.sendCursorPosition(sessionId, {
        userId,
        line: position.line,
        column: position.column
      })
    }
    
    const disposable = editor.onDidChangeCursorPosition((e) => {
      const position = {
        line: e.position.lineNumber,
        column: e.position.column
      }
      
      // Debounce cursor updates
      if (!lastPosition || 
          lastPosition.line !== position.line || 
          Math.abs(lastPosition.column - position.column) > 5) {
        sendCursorUpdate(position)
        lastPosition = position
      }
    })
    
    return () => disposable.dispose()
  }, [isConnected, editor, sessionId, userId])
  
  // Handle local selection changes
  useEffect(() => {
    if (!isConnected || !editor) return
    
    const disposable = editor.onDidChangeCursorSelection((e) => {
      const selection = e.selection
      if (!selection.isEmpty()) {
        collaborationServiceRealtime.sendSelectionRange(sessionId, {
          userId,
          startLine: selection.startLineNumber,
          startColumn: selection.startColumn,
          endLine: selection.endLineNumber,
          endColumn: selection.endColumn
        })
      }
    })
    
    return () => disposable.dispose()
  }, [isConnected, editor, sessionId, userId])
  
  // Handle local content changes
  useEffect(() => {
    if (!isConnected || !editor) return
    
    const disposable = editor.onDidChangeModelContent((e) => {
      // Skip if this is a remote change we're applying
      if (isApplyingRemoteChange.current) return
      
      e.changes.forEach(change => {
        collaborationServiceRealtime.sendContentChange(sessionId, {
          userId,
          sessionId,
          type: change.text ? (change.rangeLength > 0 ? 'replace' : 'insert') : 'delete',
          range: {
            startLine: change.range.startLineNumber,
            startColumn: change.range.startColumn,
            endLine: change.range.endLineNumber,
            endColumn: change.range.endColumn
          },
          text: change.text
        })
      })
    })
    
    return () => disposable.dispose()
  }, [isConnected, editor, sessionId, userId])
  
  // Handler functions
  const handleUserJoined = useCallback((user: CollaborationUser) => {
    setCollaborators(prev => [...prev.filter(u => u.id !== user.id), user])
    
    toast({
      title: "Collaborator Joined",
      description: `${user.name} joined the session`,
      duration: 3000
    })
  }, [toast])
  
  const handleUserLeft = useCallback((userId: string) => {
    setCollaborators(prev => prev.filter(u => u.id !== userId))
    
    // Remove cursor decorations
    const cursor = cursorsRef.current.get(userId)
    if (cursor && editor) {
      editor.deltaDecorations(cursor.decoration, [])
      cursorsRef.current.delete(userId)
    }
    
    // Remove selection decorations
    const selections = decorationsRef.current.get(userId)
    if (selections && editor) {
      editor.deltaDecorations(selections, [])
      decorationsRef.current.delete(userId)
    }
    
    // Remove custom styles
    const cursorStyle = document.getElementById(`collaborator-cursor-style-${userId}`)
    if (cursorStyle) cursorStyle.remove()
    
    const selectionStyle = document.getElementById(`collaborator-selection-style-${userId}`)
    if (selectionStyle) selectionStyle.remove()
  }, [editor])
  
  const handleCursorMoved = useCallback((userId: string, position: { line: number; column: number }) => {
    if (!editor) return
    
    const user = collaboratorServiceRealtime.getActiveUsers(sessionId).find(u => u.id === userId)
    if (!user) return
    
    // Remove old cursor decoration
    const oldCursor = cursorsRef.current.get(userId)
    if (oldCursor) {
      editor.deltaDecorations(oldCursor.decoration, [])
    }
    
    // Add new cursor decoration
    const newDecoration = editor.deltaDecorations([], [
      {
        range: new monacoEditor.Range(position.line, position.column, position.line, position.column),
        options: {
          className: `collaborator-cursor collaborator-cursor-${userId}`,
          hoverMessage: { value: user.name },
          zIndex: 100,
          stickiness: monacoEditor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges
        }
      }
    ])
    
    cursorsRef.current.set(userId, {
      userId,
      decoration: newDecoration,
      color: user.color
    })
    
    // Inject custom CSS for this cursor
    injectCursorStyle(userId, user.color, user.name)
  }, [editor, sessionId])
  
  const handleSelectionChanged = useCallback((
    userId: string,
    selection: { startLine: number; startColumn: number; endLine: number; endColumn: number }
  ) => {
    if (!editor) return
    
    const user = collaboratorServiceRealtime.getActiveUsers(sessionId).find(u => u.id === userId)
    if (!user) return
    
    // Remove old selection decoration
    const oldSelections = decorationsRef.current.get(userId)
    if (oldSelections) {
      editor.deltaDecorations(oldSelections, [])
    }
    
    // Add new selection decoration
    const newDecoration = editor.deltaDecorations([], [
      {
        range: new monacoEditor.Range(
          selection.startLine,
          selection.startColumn,
          selection.endLine,
          selection.endColumn
        ),
        options: {
          className: `collaborator-selection collaborator-selection-${userId}`,
          hoverMessage: { value: `Selected by ${user.name}` },
          zIndex: 50,
          stickiness: monacoEditor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges
        }
      }
    ])
    
    decorationsRef.current.set(userId, newDecoration)
    
    // Inject custom CSS for this selection
    injectSelectionStyle(userId, user.color)
  }, [editor, sessionId])
  
  const handleContentChanged = useCallback((change: any) => {
    if (!editor) return
    
    const model = editor.getModel()
    if (!model) return
    
    // Apply the change to the editor
    isApplyingRemoteChange.current = true
    
    try {
      const range = new monacoEditor.Range(
        change.range.startLine,
        change.range.startColumn,
        change.range.endLine,
        change.range.endColumn
      )
      
      const operation = {
        range,
        text: change.text,
        forceMoveMarkers: true
      }
      
      model.pushEditOperations([], [operation], () => null)
      
      // Notify parent component of content change
      if (onChange) {
        onChange(model.getValue())
      }
    } catch (error) {
      logger.error('Failed to apply remote change', error)
    } finally {
      isApplyingRemoteChange.current = false
    }
  }, [editor, onChange])
  
  // Inject styles for cursor and selection
  const injectCursorStyle = (userId: string, color: string, userName: string) => {
    const styleId = `collaborator-cursor-style-${userId}`
    let style = document.getElementById(styleId)
    
    if (!style) {
      style = document.createElement('style')
      style.id = styleId
      document.head.appendChild(style)
    }
    
    const initials = userName.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2)
    
    style.textContent = `
      .collaborator-cursor-${userId} {
        position: relative;
      }
      .collaborator-cursor-${userId}::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 2px;
        height: 100%;
        background-color: ${color};
        z-index: 100;
      }
      .collaborator-cursor-${userId}::after {
        content: '${initials}';
        position: absolute;
        top: -1.5em;
        left: -1px;
        background-color: ${color};
        color: white;
        padding: 2px 6px;
        border-radius: 3px 3px 3px 0;
        font-size: 11px;
        font-weight: bold;
        line-height: 1;
        white-space: nowrap;
        z-index: 101;
        pointer-events: none;
        user-select: none;
      }
    `
  }
  
  const injectSelectionStyle = (userId: string, color: string) => {
    const styleId = `collaborator-selection-style-${userId}`
    let style = document.getElementById(styleId)
    
    if (!style) {
      style = document.createElement('style')
      style.id = styleId
      document.head.appendChild(style)
    }
    
    style.textContent = `
      .collaborator-selection-${userId} {
        background-color: ${color}20 !important;
        border-left: 2px solid ${color} !important;
      }
    `
  }
  
  return {
    isConnected,
    collaborators,
    error,
    // Expose methods for manual control if needed
    sendCursorPosition: (line: number, column: number) => {
      if (isConnected) {
        collaborationServiceRealtime.sendCursorPosition(sessionId, { userId, line, column })
      }
    },
    sendSelectionRange: (start: { line: number; column: number }, end: { line: number; column: number }) => {
      if (isConnected) {
        collaborationServiceRealtime.sendSelectionRange(sessionId, {
          userId,
          startLine: start.line,
          startColumn: start.column,
          endLine: end.line,
          endColumn: end.column
        })
      }
    }
  }
}