import { useEffect, useRef, useState, useCallback } from 'react'
import { editor as monacoEditor } from 'monaco-editor'
import { collaborationService, type CollaborationUser } from '@/lib/services/collaboration-service'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import type { UserSubscription } from '@/lib/subscription'

interface UseMonacoCollaborationOptions {
  sessionId: string
  userId: string
  userName: string
  userEmail: string
  subscription: UserSubscription | null
  enabled?: boolean
}

interface CollaboratorCursor {
  userId: string
  decoration: string[]
  color: string
}

export function useMonacoCollaboration(
  editor: monacoEditor.IStandaloneCodeEditor | null,
  options: UseMonacoCollaborationOptions
) {
  const { sessionId, userId, userName, userEmail, subscription, enabled = true } = options
  const { toast } = useToast()
  
  const [collaborators, setCollaborators] = useState<CollaborationUser[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const cursorsRef = useRef<Map<string, CollaboratorCursor>>(new Map())
  const decorationsRef = useRef<Map<string, string[]>>(new Map())
  const isInitializedRef = useRef(false)
  
  // Connect to collaboration service
  useEffect(() => {
    if (!enabled || !editor || isInitializedRef.current) return
    
    const connect = async () => {
      try {
        // Check if user can use real-time collaboration
        if (!collaborationService.canUseRealTimeCollaboration(subscription)) {
          logger.info('Real-time collaboration not available for current subscription')
          return
        }
        
        await collaborationService.connect(sessionId, userId, subscription)
        setIsConnected(true)
        isInitializedRef.current = true
        
        toast({
          title: "Collaboration Connected",
          description: "Real-time collaboration is now active"
        })
      } catch (error) {
        logger.error('Failed to connect to collaboration service', error)
        toast({
          title: "Collaboration Error",
          description: "Failed to enable real-time collaboration",
          variant: "destructive"
        })
      }
    }
    
    connect()
    
    return () => {
      if (isInitializedRef.current) {
        collaborationService.disconnect(sessionId)
        setIsConnected(false)
        isInitializedRef.current = false
      }
    }
  }, [enabled, editor, sessionId, userId, subscription, toast])
  
  // Subscribe to collaboration events
  useEffect(() => {
    if (!isConnected || !editor) return
    
    const unsubscribe = collaborationService.subscribe(sessionId, (event) => {
      switch (event.type) {
        case 'user.joined':
          handleUserJoined(event.data)
          break
        case 'user.left':
          handleUserLeft(event.userId)
          break
        case 'cursor.moved':
          if (event.userId !== userId) {
            handleCursorMoved(event.userId, event.data)
          }
          break
        case 'selection.changed':
          if (event.userId !== userId) {
            handleSelectionChanged(event.userId, event.data)
          }
          break
        case 'content.changed':
          if (event.userId !== userId) {
            handleContentChanged(event.data)
          }
          break
      }
    })
    
    return unsubscribe
  }, [isConnected, editor, sessionId, userId])
  
  // Handle local cursor position changes
  useEffect(() => {
    if (!isConnected || !editor) return
    
    const disposable = editor.onDidChangeCursorPosition((e) => {
      const position = e.position
      collaborationService.sendCursorPosition(sessionId, {
        userId,
        line: position.lineNumber,
        column: position.column
      })
    })
    
    return () => disposable.dispose()
  }, [isConnected, editor, sessionId, userId])
  
  // Handle local selection changes
  useEffect(() => {
    if (!isConnected || !editor) return
    
    const disposable = editor.onDidChangeCursorSelection((e) => {
      const selection = e.selection
      if (!selection.isEmpty()) {
        collaborationService.sendSelectionRange(sessionId, {
          userId,
          startLine: selection.startLineNumber,
          startColumn: selection.startColumn,
          endLine: selection.endLineNumber,
          endColumn: selection.endColumn
        })
      }
    })
    
    return () => disposable.dispose()
  }, [isConnected, editor, sessionId, userId])
  
  // Handle local content changes
  useEffect(() => {
    if (!isConnected || !editor) return
    
    const disposable = editor.onDidChangeModelContent((e) => {
      e.changes.forEach(change => {
        collaborationService.sendContentChange(sessionId, {
          userId,
          sessionId,
          type: change.text ? (change.rangeLength > 0 ? 'replace' : 'insert') : 'delete',
          range: {
            startLine: change.range.startLineNumber,
            startColumn: change.range.startColumn,
            endLine: change.range.endLineNumber,
            endColumn: change.range.endColumn
          },
          text: change.text
        })
      })
    })
    
    return () => disposable.dispose()
  }, [isConnected, editor, sessionId, userId])
  
  // Handler functions
  const handleUserJoined = useCallback((user: CollaborationUser) => {
    setCollaborators(prev => [...prev.filter(u => u.id !== user.id), user])
    
    toast({
      title: "Collaborator Joined",
      description: `${user.name} joined the session`
    })
  }, [toast])
  
  const handleUserLeft = useCallback((userId: string) => {
    setCollaborators(prev => prev.filter(u => u.id !== userId))
    
    // Remove cursor decorations
    const cursor = cursorsRef.current.get(userId)
    if (cursor && editor) {
      editor.deltaDecorations(cursor.decoration, [])
      cursorsRef.current.delete(userId)
    }
    
    // Remove selection decorations
    const selections = decorationsRef.current.get(userId)
    if (selections && editor) {
      editor.deltaDecorations(selections, [])
      decorationsRef.current.delete(userId)
    }
  }, [editor])
  
  const handleCursorMoved = useCallback((userId: string, position: { line: number; column: number }) => {
    if (!editor) return
    
    const user = collaborators.find(u => u.id === userId)
    if (!user) return
    
    // Remove old cursor decoration
    const oldCursor = cursorsRef.current.get(userId)
    if (oldCursor) {
      editor.deltaDecorations(oldCursor.decoration, [])
    }
    
    // Add new cursor decoration
    const newDecoration = editor.deltaDecorations([], [
      {
        range: new monacoEditor.Range(position.line, position.column, position.line, position.column),
        options: {
          className: 'collaborator-cursor',
          hoverMessage: { value: user.name },
          zIndex: 100,
          beforeContentClassName: 'collaborator-cursor-before',
          afterContentClassName: 'collaborator-cursor-after',
          // Custom CSS will be injected to style these based on user color
        }
      }
    ])
    
    cursorsRef.current.set(userId, {
      userId,
      decoration: newDecoration,
      color: user.color
    })
    
    // Inject custom CSS for this cursor
    injectCursorStyle(userId, user.color)
  }, [editor, collaborators])
  
  const handleSelectionChanged = useCallback((
    userId: string,
    selection: { startLine: number; startColumn: number; endLine: number; endColumn: number }
  ) => {
    if (!editor) return
    
    const user = collaborators.find(u => u.id === userId)
    if (!user) return
    
    // Remove old selection decoration
    const oldSelections = decorationsRef.current.get(userId)
    if (oldSelections) {
      editor.deltaDecorations(oldSelections, [])
    }
    
    // Add new selection decoration
    const newDecoration = editor.deltaDecorations([], [
      {
        range: new monacoEditor.Range(
          selection.startLine,
          selection.startColumn,
          selection.endLine,
          selection.endColumn
        ),
        options: {
          className: `collaborator-selection-${userId}`,
          hoverMessage: { value: `Selected by ${user.name}` },
          zIndex: 50
        }
      }
    ])
    
    decorationsRef.current.set(userId, newDecoration)
    
    // Inject custom CSS for this selection
    injectSelectionStyle(userId, user.color)
  }, [editor, collaborators])
  
  const handleContentChanged = useCallback((change: any) => {
    if (!editor) return
    
    const model = editor.getModel()
    if (!model) return
    
    // Apply the change to the editor
    // This would need to handle operational transformation in a real implementation
    // For now, we'll do a simple apply
    try {
      const range = new monacoEditor.Range(
        change.range.startLine,
        change.range.startColumn,
        change.range.endLine,
        change.range.endColumn
      )
      
      const operation = {
        range,
        text: change.text,
        forceMoveMarkers: true
      }
      
      model.pushEditOperations([], [operation], () => null)
    } catch (error) {
      logger.error('Failed to apply remote change', error)
    }
  }, [editor])
  
  // Inject styles for cursor and selection
  const injectCursorStyle = (userId: string, color: string) => {
    const styleId = `collaborator-cursor-style-${userId}`
    let style = document.getElementById(styleId)
    
    if (!style) {
      style = document.createElement('style')
      style.id = styleId
      document.head.appendChild(style)
    }
    
    style.textContent = `
      .collaborator-cursor-${userId}::before {
        content: '';
        position: absolute;
        width: 2px;
        height: 1.2em;
        background-color: ${color};
        z-index: 100;
      }
      .collaborator-cursor-${userId}::after {
        content: '${userId.substring(0, 2).toUpperCase()}';
        position: absolute;
        top: -1.2em;
        left: 0;
        background-color: ${color};
        color: white;
        padding: 2px 4px;
        border-radius: 3px;
        font-size: 11px;
        font-weight: bold;
        z-index: 101;
      }
    `
  }
  
  const injectSelectionStyle = (userId: string, color: string) => {
    const styleId = `collaborator-selection-style-${userId}`
    let style = document.getElementById(styleId)
    
    if (!style) {
      style = document.createElement('style')
      style.id = styleId
      document.head.appendChild(style)
    }
    
    style.textContent = `
      .collaborator-selection-${userId} {
        background-color: ${color}20;
        border-left: 2px solid ${color};
      }
    `
  }
  
  return {
    isConnected,
    collaborators,
    // Expose methods for manual control if needed
    sendCursorPosition: (line: number, column: number) => {
      if (isConnected) {
        collaborationService.sendCursorPosition(sessionId, { userId, line, column })
      }
    },
    sendSelectionRange: (start: { line: number; column: number }, end: { line: number; column: number }) => {
      if (isConnected) {
        collaborationService.sendSelectionRange(sessionId, {
          userId,
          startLine: start.line,
          startColumn: start.column,
          endLine: end.line,
          endColumn: end.column
        })
      }
    }
  }
}