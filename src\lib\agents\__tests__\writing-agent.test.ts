import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { WritingAgent } from '../writing-agent';
import type { BookContext, ChapterContent, ChapterOutline, Scene } from '../types';
import OpenAI from 'openai';

// Mock OpenAI
jest.mock('openai');

describe('WritingAgent', () => {
  let writer: WritingAgent;
  let mockContext: BookContext;
  let mockOpenAI: jest.Mocked<OpenAI>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockContext = {
      projectId: 'test-project-id',
      currentChapter: 1,
      settings: {
        primaryGenre: 'fantasy',
        secondaryGenres: ['adventure'],
        targetAudience: 'young-adult',
        writingStyle: 'descriptive',
        narrativeVoice: 'third-person',
        tense: 'past',
        pacing: 'medium',
        violenceLevel: 'moderate',
        romanceLevel: 'low',
        profanityLevel: 'mild',
        useDeepPOV: true,
        showDontTell: true,
        varyProse: true,
        sensoryRich: true,
      },
      targetWordCount: 80000,
      targetChapters: 20,
      storyStructure: {
        title: 'The Quest',
        premise: 'A hero saves the kingdom',
        genre: 'fantasy',
        themes: ['courage', 'growth'],
        acts: [],
        conflicts: [],
        timeline: [],
        worldBuilding: {
          setting: {
            timeForPeriod: 'Medieval fantasy',
            locations: [],
            culture: 'Medieval kingdom',
            technology: 'Pre-industrial',
          },
          rules: [],
          history: [],
        },
        plotPoints: [],
      },
      characters: {
        protagonists: [
          {
            id: '1',
            name: 'Aria',
            role: 'protagonist',
            appearance: 'Young woman with silver hair',
            personality: {
              traits: ['brave', 'kind'],
              strengths: ['leadership'],
              weaknesses: ['impulsive'],
              fears: ['failure'],
              desires: ['peace'],
            },
            backstory: 'Orphan with hidden powers',
            motivation: 'Save her people',
            arc: {
              type: 'positive_change',
              startingPoint: 'Naive',
              endingPoint: 'Wise',
              keyMoments: [],
              internalConflict: 'Self-doubt',
              externalConflict: 'Evil forces',
            },
            voice: {
              speakingStyle: 'Direct and honest',
              vocabulary: 'Simple but growing',
              mannerisms: ['Touches pendant when nervous'],
            },
            relationships: [],
          },
        ],
        antagonists: [],
        supporting: [],
        relationships: [],
      },
      chapterOutlines: {
        chapters: [
          {
            number: 1,
            title: 'The Awakening',
            summary: 'Aria discovers her magical abilities',
            wordCountTarget: 4000,
            scenes: [
              {
                id: 'scene-1',
                title: 'Village Festival',
                description: 'Annual harvest festival disrupted',
                wordCountTarget: 2000,
                setting: 'Village square',
                characters: ['Aria', 'Villagers'],
                purpose: 'Introduce protagonist and world',
                conflict: 'Magic manifests unexpectedly',
                outcome: 'Aria exposed as magic user',
              },
              {
                id: 'scene-2',
                title: 'The Elder\'s Warning',
                description: 'Village elder reveals truth',
                wordCountTarget: 2000,
                setting: 'Elder\'s cottage',
                characters: ['Aria', 'Elder Thorne'],
                purpose: 'Exposition and call to adventure',
                conflict: 'Aria resists her destiny',
                outcome: 'Accepts she must leave',
              },
            ],
            povCharacter: 'Aria',
            objectives: ['Introduce protagonist', 'Establish magical world'],
            conflicts: ['Internal: fear of power', 'External: village reaction'],
            resolutions: ['Aria accepts her nature'],
            characterStates: [],
            plotAdvancement: ['Inciting incident', 'Call to adventure'],
          },
        ],
        totalWordCount: 80000,
        estimatedReadingTime: 320,
      },
    };

    mockOpenAI = {
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    } as unknown as jest.Mocked<OpenAI>;

    (OpenAI as jest.MockedClass<typeof OpenAI>).mockImplementation(() => mockOpenAI);
    
    writer = new WritingAgent(mockContext);
  });

  describe('execute', () => {
    it('should write a complete chapter', async () => {
      const mockChapterContent: ChapterContent = {
        chapterNumber: 1,
        title: 'The Awakening',
        content: `The harvest festival had always been Aria's favorite time of year. The village square bloomed with golden lanterns, their warm light dancing across faces flushed with laughter and autumn ale.

She stood at the edge of the crowd, fingers wrapped around the worn leather cord of her mother's pendant. The metal felt unusually warm against her palm—a detail she would later remember as the first warning.

"Aria! Come dance!" Her friend Tam's voice cut through the music, his grin wide and infectious.

She shook her head, offering a small smile. Something felt different tonight. The air itself seemed to hum with an energy she couldn't name, like the moment before lightning strikes.

The musicians struck up a lively reel, and the crowd surged into motion. Aria found herself pulled forward, Tam's hand warm in hers. She tried to relax, to lose herself in the familiar steps, but the strange sensation only grew stronger.

Heat bloomed in her chest, spreading outward like ripples on water. The pendant burned now, almost painful against her skin.

"Tam, I need to—"

The words died on her lips as golden light erupted from her hands.

The music stopped. The dancing stopped. Everything stopped except the light pouring from Aria like a miniature sun.`,
        wordCount: 224,
        scenes: [
          {
            id: 'scene-1',
            content: 'The harvest festival scene content...',
            wordCount: 224,
            purpose: 'Introduce protagonist and world',
          },
        ],
        characterVoices: ['Aria - honest and observant', 'Tam - cheerful friend'],
        themes: ['Power awakening', 'Fear of the unknown'],
        continuityNotes: ['Pendant is mother\'s', 'First manifestation of magic'],
      };

      const mockResponse = {
        choices: [{
          message: {
            tool_calls: [{
              function: {
                arguments: JSON.stringify({
                  chapterNumber: mockChapterContent.chapterNumber,
                  title: mockChapterContent.title,
                  content: mockChapterContent.content,
                  wordCount: mockChapterContent.wordCount,
                  scenes: mockChapterContent.scenes,
                  characterVoices: mockChapterContent.characterVoices,
                  themes: mockChapterContent.themes,
                  continuityNotes: mockChapterContent.continuityNotes,
                }),
              },
            }],
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const result = await writer.execute();

      expect(result).toEqual(mockChapterContent);
      expect(result.chapterNumber).toBe(1);
      expect(result.content).toContain('Aria');
      expect(result.wordCount).toBeGreaterThan(0);
    });

    it('should throw error when required context is missing', async () => {
      const incompleteContext = { ...mockContext };
      delete incompleteContext.chapterOutlines;
      
      writer = new WritingAgent(incompleteContext);
      
      await expect(writer.execute()).rejects.toThrow('Writing Agent requires complete context');
    });

    it('should respect writing style settings', async () => {
      // Test that deep POV is applied
      const mockResponse = {
        choices: [{
          message: {
            tool_calls: [{
              function: {
                arguments: JSON.stringify({
                  chapterNumber: 1,
                  title: 'Test',
                  content: 'The pendant felt warm—no, it burned. Aria\'s fingers trembled. Why now? Why here? She could feel their eyes on her, see the fear creeping into their faces.',
                  wordCount: 30,
                  scenes: [],
                  characterVoices: [],
                  themes: [],
                  continuityNotes: [],
                }),
              },
            }],
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const result = await writer.execute();

      // Check for deep POV indicators
      expect(result.content).toContain('felt');
      expect(result.content).toContain('?'); // Internal questions
      expect(result.content).not.toContain('Aria thought'); // Avoid filter words
    });

    it('should match target word count', async () => {
      const targetWords = 4000;
      
      const mockResponse = {
        choices: [{
          message: {
            tool_calls: [{
              function: {
                arguments: JSON.stringify({
                  chapterNumber: 1,
                  title: 'Test Chapter',
                  content: 'A'.repeat(targetWords), // Simplified for testing
                  wordCount: targetWords,
                  scenes: [],
                  characterVoices: [],
                  themes: [],
                  continuityNotes: [],
                }),
              },
            }],
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const result = await writer.execute();

      // Allow 10% variance from target
      expect(result.wordCount).toBeGreaterThanOrEqual(targetWords * 0.9);
      expect(result.wordCount).toBeLessThanOrEqual(targetWords * 1.1);
    });

    it('should include sensory details when enabled', async () => {
      const mockResponse = {
        choices: [{
          message: {
            tool_calls: [{
              function: {
                arguments: JSON.stringify({
                  chapterNumber: 1,
                  title: 'Sensory Rich',
                  content: 'The acrid smoke stung her nostrils. Rough stone scraped against her palms. Somewhere, a bell tolled—deep, mournful. The taste of copper filled her mouth.',
                  wordCount: 30,
                  scenes: [],
                  characterVoices: [],
                  themes: [],
                  continuityNotes: [],
                }),
              },
            }],
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const result = await writer.execute();

      // Check for sensory words
      const sensoryWords = ['smoke', 'stung', 'rough', 'scraped', 'tolled', 'taste'];
      const containsSensory = sensoryWords.some(word => result.content.includes(word));
      expect(containsSensory).toBe(true);
    });

    it('should maintain continuity with previous chapters', async () => {
      // Add completed chapters to context
      mockContext.completedChapters = [
        {
          chapterNumber: 0,
          title: 'Prologue',
          content: 'The prophecy spoke of a silver-haired child...',
          wordCount: 1000,
          scenes: [],
          characterVoices: [],
          themes: ['prophecy'],
          continuityNotes: ['Silver hair is sign of chosen one'],
        },
      ];

      mockContext.currentChapter = 1;
      writer = new WritingAgent(mockContext);

      const mockResponse = {
        choices: [{
          message: {
            tool_calls: [{
              function: {
                arguments: JSON.stringify({
                  chapterNumber: 1,
                  title: 'The Awakening',
                  content: 'Aria tucked a strand of silver hair behind her ear...',
                  wordCount: 50,
                  scenes: [],
                  characterVoices: [],
                  themes: ['prophecy', 'awakening'],
                  continuityNotes: ['References silver hair from prophecy'],
                }),
              },
            }],
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const result = await writer.execute();

      expect(result.content).toContain('silver hair');
      expect(result.continuityNotes).toContain('prophecy');
    });

    it('should handle multiple scenes in a chapter', async () => {
      const mockResponse = {
        choices: [{
          message: {
            tool_calls: [{
              function: {
                arguments: JSON.stringify({
                  chapterNumber: 1,
                  title: 'Multi-Scene Chapter',
                  content: 'Scene 1 content... [scene break] Scene 2 content...',
                  wordCount: 4000,
                  scenes: [
                    {
                      id: 'scene-1',
                      content: 'Scene 1 content...',
                      wordCount: 2000,
                      purpose: 'Setup',
                    },
                    {
                      id: 'scene-2',
                      content: 'Scene 2 content...',
                      wordCount: 2000,
                      purpose: 'Payoff',
                    },
                  ],
                  characterVoices: [],
                  themes: [],
                  continuityNotes: [],
                }),
              },
            }],
          },
        }],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse as any);

      const result = await writer.execute();

      expect(result.scenes).toHaveLength(2);
      expect(result.scenes[0].wordCount + result.scenes[1].wordCount).toBe(result.wordCount);
    });
  });
});