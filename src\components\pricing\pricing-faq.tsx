'use client'

import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'

const faqs = [
  {
    question: 'What\'s the difference between AI intelligence levels?',
    answer: 'Each tier offers increasingly sophisticated AI capabilities. Essential AI handles basic writing tasks efficiently. Enhanced AI understands complex story structures. Premium AI manages intricate narratives with deep character development. Advanced and Ultimate AI can handle epic series, maintaining consistency across hundreds of thousands of words while understanding subtle themes and narrative techniques.'
  },
  {
    question: 'How are AI words counted?',
    answer: 'AI words are counted based on the actual content generated by our AI agents. Whether it\'s a story outline, character profile, chapter content, or editing suggestions, we count the words in the output. This gives you more flexibility and value compared to counting generations.'
  },
  {
    question: 'Can I change plans anytime?',
    answer: 'Yes! You can upgrade or downgrade your plan at any time. When upgrading, you\'ll get immediate access to new features. When downgrading, changes take effect at the next billing cycle.'
  },
  {
    question: 'What happens if I exceed my word limit?',
    answer: 'You\'ll receive notifications when you reach 80% and 90% of your monthly word limit. Once reached, you can either wait for your monthly reset or upgrade to a higher plan for immediate access to more words.'
  },
  {
    question: 'Do you offer educational discounts?',
    answer: 'Yes! We offer 50% off for students and educators with valid institutional email addresses. We also have special pricing for writing workshops and educational institutions.'
  },
  {
    question: 'What\'s included in the white-glove support?',
    answer: 'Studio subscribers get a dedicated success manager, monthly strategy calls, priority response times (under 2 hours), custom onboarding, and direct access to our development team for feature requests.'
  },
  {
    question: 'Can I export my work if I cancel?',
    answer: 'Absolutely! Your work belongs to you. Even after cancellation, you\'ll have 30 days to export all your projects in any supported format. We believe in data portability.'
  },
  {
    question: 'What\'s the difference between the AI agents?',
    answer: 'Each agent specializes in different aspects of writing: Story Architect (plot structure), Character Developer (character depth), Writing Agent (content generation), Editor Agent (prose quality), Chapter Planner (scene organization), and Adaptive Planning (story adjustments). The free tier includes Story Architect and Writing Agent, while higher tiers unlock additional specialized agents.'
  },
  {
    question: 'Do you offer a free trial?',
    answer: 'Our Starter tier is free forever with 10,000 AI words per month. For paid tiers, we offer a 30-day money-back guarantee, so you can try any plan risk-free.'
  },
  {
    question: 'Why do longer stories need higher AI intelligence?',
    answer: 'As your story grows, maintaining consistency becomes exponentially more complex. Higher AI intelligence levels can track more characters, remember plot details across chapters, maintain voice consistency, and understand the intricate relationships between story elements. This ensures your 300,000-word epic is as coherent as your 10,000-word novella.'
  }
]

export function PricingFAQ() {
  return (
    <div className="mx-auto max-w-3xl">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold font-literary-display mb-2">
          Frequently Asked Questions
        </h2>
        <p className="text-muted-foreground">
          Everything you need to know about our pricing and plans
        </p>
      </div>
      
      <Accordion type="single" collapsible className="w-full">
        {faqs.map((faq, index) => (
          <AccordionItem key={index} value={`item-${index}`}>
            <AccordionTrigger className="text-left">
              {faq.question}
            </AccordionTrigger>
            <AccordionContent className="text-muted-foreground">
              {faq.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  )
}