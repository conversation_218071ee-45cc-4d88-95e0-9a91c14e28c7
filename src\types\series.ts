export interface Book {
  id: string
  title: string
  description?: string
  status: string
  current_word_count: number
  target_word_count: number
  primary_genre?: string
  created_at?: string
  updated_at?: string
}

export interface SeriesBook extends Book {
  book_number: number
  book_role: string
  chronological_order?: number
  publication_order?: number
  introduces_characters?: string[]
  concludes_arcs?: string[]
  sets_up_future?: string[]
}

export interface Series {
  id: string
  title: string
  description?: string
  genre?: string
  planned_book_count?: number
  current_book_count: number
  publication_status: string
  created_at?: string
  updated_at?: string
  user_id?: string
  books: SeriesBook[]
}

export interface GroupedProjectsData {
  series: Series[]
  standalone: Book[]
  stats: {
    totalSeries: number
    totalBooksInSeries: number
    totalStandalone: number
    totalProjects: number
  }
}