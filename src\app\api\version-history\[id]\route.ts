import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { authenticateUser, handleRouteError } from '@/lib/auth'

interface ChapterWithProject {
  projects?: {
    user_id: string;
  } | {
    user_id: string;
  }[];
}

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const supabase = await createClient()

    // Get version with ownership verification
    const { data: version, error } = await supabase
      .from('chapter_versions')
      .select(`
        *,
        chapters!inner (
          id,
          title,
          chapter_number,
          projects!inner (
            id,
            title,
            user_id
          )
        )
      `)
      .eq('id', params.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Version not found' }, { status: 404 })
      }
      console.error('Error fetching version:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Check access rights
    const isOwner = version.chapters.projects.user_id === authResult.user?.id
    
    if (!isOwner) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Clean up response
    const { chapters, ...versionData } = version
    
    return NextResponse.json({ 
      version: {
        ...versionData,
        chapter_title: chapters.title,
        chapter_number: chapters.chapter_number,
        project_title: chapters.projects.title
      }
    })

  } catch (error) {
    return handleRouteError(error, 'Version History GET')
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const supabase = await createClient()

    // Get version to check ownership
    const { data: version } = await supabase
      .from('chapter_versions')
      .select(`
        id,
        version_number,
        chapters!inner (
          id,
          projects!inner (
            id,
            user_id
          )
        )
      `)
      .eq('id', params.id)
      .single()

    if (!version) {
      return NextResponse.json({ error: 'Version not found' }, { status: 404 })
    }

    // Only project owner can delete versions
    const chapters = version.chapters as ChapterWithProject
    const projectData = Array.isArray(chapters) ? chapters[0]?.projects : chapters?.projects
    const ownerId = Array.isArray(projectData) ? projectData[0]?.user_id : projectData?.user_id
    if (ownerId !== authResult.user?.id) {
      return NextResponse.json({ error: 'Only project owner can delete versions' }, { status: 403 })
    }

    // Don't allow deletion of version 1 (original)
    if (version.version_number === 1) {
      return NextResponse.json({ error: 'Cannot delete the original version' }, { status: 400 })
    }

    // Delete version
    const { error } = await supabase
      .from('chapter_versions')
      .delete()
      .eq('id', params.id)

    if (error) {
      console.error('Error deleting version:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    return handleRouteError(error, 'Version History DELETE')
  }
}