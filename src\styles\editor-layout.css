/* Editor Layout Animations and Polish */

/* Panel Transitions */
.panel-transition {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth panel resize */
.resizable-panel {
  transition: box-shadow 0.2s ease;
}

.resizable-panel.resizing {
  box-shadow: -4px 0 12px -2px rgba(0, 0, 0, 0.1);
}

/* Chapter Navigator Animations */
.chapter-item {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.chapter-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: var(--literary-amber);
  transform: scaleY(0);
  transition: transform 0.2s ease;
}

.chapter-item:hover::before {
  transform: scaleY(1);
}

.chapter-item.active::before {
  transform: scaleY(1);
  background: var(--primary);
}

/* Tab animations */
.tab-content-enter {
  opacity: 0;
  transform: translateY(10px);
}

.tab-content-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.2s ease;
}

/* Collapse/Expand animations */
.panel-collapse {
  animation: panelCollapse 0.3s ease forwards;
}

.panel-expand {
  animation: panelExpand 0.3s ease forwards;
}

@keyframes panelCollapse {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-20px);
  }
}

@keyframes panelExpand {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Toolbar animations */
.toolbar-button {
  transition: all 0.15s ease;
  position: relative;
}

.toolbar-button:hover {
  transform: translateY(-1px);
}

.toolbar-button:active {
  transform: translateY(0);
}

/* Smooth scrollbars */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 48, 0.3) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(139, 92, 48, 0.3);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(139, 92, 48, 0.5);
}

/* Focus mode animations */
.focus-mode-overlay {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Enhanced Panel resize handle improvements */
.resize-handle {
  position: relative;
  cursor: col-resize;
  transition: all 0.2s ease;
  touch-action: none;
  user-select: none;
}

.resize-handle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 32px;
  background-color: currentColor;
  opacity: 0;
  transition: all 0.2s ease;
  border-radius: 1px;
}

.resize-handle:hover::after {
  opacity: 0.3;
  height: 40px;
}

.resize-handle.active::after,
.resize-handle.resizing::after {
  opacity: 0.6;
  height: 48px;
  width: 3px;
}

/* Touch-friendly resize handle */
@media (hover: none) and (pointer: coarse) {
  .resize-handle {
    width: 12px;
  }

  .resize-handle::after {
    opacity: 0.4;
    width: 3px;
    height: 40px;
  }
}

/* Smooth tab transitions */
.tabs-list {
  position: relative;
}

.tabs-list::after {
  content: '';
  position: absolute;
  bottom: 0;
  height: 2px;
  background: var(--primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced resizable panel animations */
.resizable-panel-container {
  transition: width 0.1s ease-out;
}

.resizable-panel-container.resizing {
  transition: none;
  box-shadow:
    -4px 0 12px -2px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(var(--primary), 0.2);
}

.resizable-panel-container.dragging {
  box-shadow:
    -8px 0 24px -4px rgba(0, 0, 0, 0.15),
    0 0 0 2px rgba(var(--primary), 0.3);
}

/* Responsive panel behavior */
@media (max-width: 768px) {
  .resizable-panel-container {
    max-width: 90vw !important;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  }
}

/* Panel collapse animation */
.panel-collapse-enter {
  transform: translateX(100%);
}

.panel-collapse-enter-active {
  transform: translateX(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.panel-collapse-exit {
  transform: translateX(0);
}

.panel-collapse-exit-active {
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Button hover effects */
.hover-lift {
  transition: all 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
}

/* Smooth panel shadows */
.panel-shadow {
  box-shadow: 
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease;
}

.panel-shadow:hover {
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(139, 92, 48, 0.1) 0%,
    rgba(139, 92, 48, 0.2) 50%,
    rgba(139, 92, 48, 0.1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Smooth content transitions */
.content-fade {
  animation: contentFade 0.4s ease;
}

@keyframes contentFade {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}