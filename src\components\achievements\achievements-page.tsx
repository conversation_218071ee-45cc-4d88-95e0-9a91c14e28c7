'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { AchievementGallery } from './achievement-gallery'
import { Trophy, TrendingUp, Star, Target, Zap, Award } from 'lucide-react'
import { useAchievements } from '@/hooks/use-achievements'

interface AchievementsPageProps {
  userId: string
}

export function AchievementsPage({ userId }: AchievementsPageProps) {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const { achievements, loading, stats } = useAchievements(userId)

  const categoryIcons = {
    writing: <Trophy className="h-5 w-5" />,
    consistency: <TrendingUp className="h-5 w-5" />,
    quality: <Star className="h-5 w-5" />,
    milestones: <Target className="h-5 w-5" />,
    special: <Zap className="h-5 w-5" />
  }

  return (
    <div className="container py-8 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Achievements</h1>
        <p className="text-muted-foreground">
          Track your writing journey and unlock rewards as you progress
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base font-medium">Total Unlocked</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalUnlocked || 0}</div>
            <p className="text-xs text-muted-foreground">of {stats?.totalAchievements || 0} total</p>
            <Progress 
              value={(stats?.totalUnlocked || 0) / (stats?.totalAchievements || 1) * 100} 
              className="h-2 mt-2" 
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base font-medium">Achievement Points</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalPoints || 0}</div>
            <p className="text-xs text-muted-foreground">lifetime points earned</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base font-medium">Current Streak</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.currentStreak || 0} days</div>
            <p className="text-xs text-muted-foreground">keep it going!</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base font-medium">Rarest Achievement</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Award className="h-5 w-5 text-purple-500" />
              <span className="font-medium text-sm truncate">
                {stats?.rarestAchievement || 'None yet'}
              </span>
            </div>
            {stats?.rarestAchievementRarity && (
              <p className="text-xs text-muted-foreground mt-1">
                Only {stats.rarestAchievementRarity}% have this
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Achievement Gallery</CardTitle>
              <CardDescription>
                Your collection of writing accomplishments
              </CardDescription>
            </div>
            <div className="flex gap-2">
              {Object.entries(categoryIcons).map(([category, icon]) => (
                <Badge
                  key={category}
                  variant={selectedCategory === category ? 'default' : 'outline'}
                  className="cursor-pointer"
                  onClick={() => setSelectedCategory(category)}
                >
                  {icon}
                  <span className="ml-1 capitalize">{category}</span>
                </Badge>
              ))}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <AchievementGallery
            userId={userId}
            showUnlocked={true}
            showProgress={true}
            category={selectedCategory === 'all' ? undefined : selectedCategory}
          />
        </CardContent>
      </Card>

      {/* Tips Section */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="text-lg">Achievement Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Trophy className="h-4 w-4 text-yellow-500" />
                Writing Achievements
              </h4>
              <p className="text-sm text-muted-foreground">
                Focus on consistent daily writing to unlock word count milestones faster.
                Quality achievements require maintaining high scores across multiple chapters.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Zap className="h-4 w-4 text-purple-500" />
                Special Achievements
              </h4>
              <p className="text-sm text-muted-foreground">
                Some achievements are hidden until unlocked. Experiment with different
                writing times and session lengths to discover secret achievements.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}