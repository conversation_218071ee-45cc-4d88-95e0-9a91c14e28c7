/**
 * Theme Settings Section
 * Appearance and theme customization
 */

'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Palette, 
  Sun, 
  Moon, 
  Monitor, 
  Check,
  Sparkles
} from 'lucide-react';

import { useTheme } from '@/hooks/use-theme';
import { getAllThemes as _getAllThemes, getThemesByMode } from '@/lib/themes/theme-registry';
import { useThemeSettings } from '@/lib/settings/settings-store';

interface ThemePreviewCardProps {
  themeId: string;
  themeName: string;
  themeDescription: string;
  themeMode: 'light' | 'dark';
  colors: {
    background: string;
    text: string;
    accent: string;
    card: string;
  };
  isActive: boolean;
  onSelect: () => void;
}

function ThemePreviewCard({ 
  themeId: _themeId, 
  themeName, 
  themeDescription, 
  themeMode, 
  colors, 
  isActive, 
  onSelect 
}: ThemePreviewCardProps) {
  return (
    <Card 
      className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
        isActive ? 'ring-2 ring-primary shadow-lg' : 'hover:ring-1 hover:ring-border'
      }`}
      onClick={onSelect}
    >
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {themeMode === 'light' ? (
              <Sun className="h-3 w-3 text-amber-600" />
            ) : (
              <Moon className="h-3 w-3 text-blue-400" />
            )}
            <CardTitle className="text-sm font-medium">{themeName}</CardTitle>
          </div>
          {isActive && (
            <Check className="h-3 w-3 text-primary" />
          )}
        </div>
        <CardDescription className="text-xs line-clamp-1">
          {themeDescription}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* Color Preview */}
        <div 
          className="rounded-md p-3 mb-2 border"
          style={{ 
            backgroundColor: colors.background,
            borderColor: colors.accent + '40'
          }}
        >
          <div 
            className="text-xs font-medium mb-1"
            style={{ color: colors.text }}
          >
            Sample Text
          </div>
          <div className="flex gap-1">
            <div 
              className="w-2 h-2 rounded-full"
              style={{ backgroundColor: colors.accent }}
            />
            <div 
              className="w-2 h-2 rounded-full"
              style={{ backgroundColor: colors.card }}
            />
            <div 
              className="w-2 h-2 rounded-full opacity-60"
              style={{ backgroundColor: colors.text }}
            />
          </div>
        </div>
        
        <Badge 
          variant={themeMode === 'light' ? 'default' : 'secondary'} 
          className="text-xs"
        >
          {themeMode}
        </Badge>
      </CardContent>
    </Card>
  );
}

export function ThemeSettingsSection() {
  const { switchTheme, isThemeActive, theme: _theme } = useTheme();
  const { theme: themeSettings, updateTheme } = useThemeSettings();

  const lightThemes = getThemesByMode('light');
  const darkThemes = getThemesByMode('dark');

  const handleThemeModeChange = (mode: 'light' | 'dark' | 'system') => {
    updateTheme({ themeMode: mode });
    
    if (mode === 'system') {
      switchTheme('system');
    } else {
      // Switch to default theme for the selected mode
      const defaultTheme = mode === 'light' ? lightThemes[0] : darkThemes[0];
      if (defaultTheme) {
        switchTheme(defaultTheme.id);
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <div className="flex items-center gap-2 mb-2">
          <Palette className="w-5 h-5 text-primary" />
          <h3 className="text-lg font-semibold">Appearance</h3>
        </div>
        <p className="text-sm text-muted-foreground">
          Customize the visual appearance of BookScribe AI with themes and color schemes.
        </p>
      </div>

      {/* Theme Mode Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Theme Mode</CardTitle>
          <CardDescription>
            Choose how BookScribe AI should determine which theme to use
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={themeSettings.themeMode}
            onValueChange={handleThemeModeChange}
            className="grid grid-cols-3 gap-4"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="light" id="light" />
              <Label htmlFor="light" className="flex items-center gap-2 cursor-pointer">
                <Sun className="w-4 h-4" />
                Light
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="dark" id="dark" />
              <Label htmlFor="dark" className="flex items-center gap-2 cursor-pointer">
                <Moon className="w-4 h-4" />
                Dark
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="system" id="system" />
              <Label htmlFor="system" className="flex items-center gap-2 cursor-pointer">
                <Monitor className="w-4 h-4" />
                System
              </Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      <Separator />

      {/* Light Themes */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Sun className="w-4 h-4 text-amber-600" />
          <h4 className="font-semibold">Light Themes</h4>
          <Badge variant="outline" className="text-xs">
            {lightThemes.length}
          </Badge>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {lightThemes.map((themeData) => (
            <ThemePreviewCard
              key={themeData.id}
              themeId={themeData.id}
              themeName={themeData.name}
              themeDescription={themeData.description}
              themeMode={themeData.mode}
              colors={{
                background: themeData.colors.background,
                text: themeData.colors.text,
                accent: themeData.colors.accent,
                card: themeData.colors.window
              }}
              isActive={isThemeActive(themeData.id)}
              onSelect={() => {
                switchTheme(themeData.id);
                updateTheme({ currentTheme: themeData.id, themeMode: 'light' });
              }}
            />
          ))}
        </div>
      </div>

      <Separator />

      {/* Dark Themes */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Moon className="w-4 h-4 text-blue-400" />
          <h4 className="font-semibold">Dark Themes</h4>
          <Badge variant="outline" className="text-xs">
            {darkThemes.length}
          </Badge>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {darkThemes.map((themeData) => (
            <ThemePreviewCard
              key={themeData.id}
              themeId={themeData.id}
              themeName={themeData.name}
              themeDescription={themeData.description}
              themeMode={themeData.mode}
              colors={{
                background: themeData.colors.background,
                text: themeData.colors.text,
                accent: themeData.colors.accent,
                card: themeData.colors.window
              }}
              isActive={isThemeActive(themeData.id)}
              onSelect={() => {
                switchTheme(themeData.id);
                updateTheme({ currentTheme: themeData.id, themeMode: 'dark' });
              }}
            />
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Sparkles className="w-4 h-4" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Quickly switch between popular theme combinations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                switchTheme('writers-sanctuary-light');
                updateTheme({ currentTheme: 'writers-sanctuary-light', themeMode: 'light' });
              }}
            >
              <Sun className="w-3 w-3 mr-1" />
              Default Light
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                switchTheme('evening-study-dark');
                updateTheme({ currentTheme: 'evening-study-dark', themeMode: 'dark' });
              }}
            >
              <Moon className="w-3 w-3 mr-1" />
              Default Dark
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                switchTheme('system');
                updateTheme({ themeMode: 'system' });
              }}
            >
              <Monitor className="w-3 w-3 mr-1" />
              Auto
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
