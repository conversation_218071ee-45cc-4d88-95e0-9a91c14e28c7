'use client'

import React, { useState, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Toggle } from '@/components/ui/toggle'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Bold, 
  Italic, 
  Underline,
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Quote,
  Heading1,
  Heading2,
  Heading3,
  Link,
  Image,
  Code,
  MoreVertical,
  Undo,
  Redo,
  Type,
  Palette,
  ChevronDown,
  Minus,
  CheckSquare,
  Table,
  Highlighter
} from 'lucide-react'

interface FormattingToolbarProps {
  onFormat: (command: string, value?: string) => void
  onUndo?: () => void
  onRedo?: () => void
  canUndo?: boolean
  canRedo?: boolean
  className?: string
}

interface FormatButton {
  icon: React.ComponentType<{ className?: string }>
  command: string
  label: string
  shortcut?: string
}

function EnhancedFormattingToolbarComponent({ 
  onFormat, 
  onUndo,
  onRedo,
  canUndo = true,
  canRedo = true,
  className = '' 
}: FormattingToolbarProps) {
  const [activeFormats, setActiveFormats] = useState<Set<string>>(new Set())

  const toggleFormat = useCallback((format: string) => {
    const newFormats = new Set(activeFormats)
    if (newFormats.has(format)) {
      newFormats.delete(format)
    } else {
      newFormats.add(format)
    }
    setActiveFormats(newFormats)
    onFormat(format)
  }, [activeFormats, onFormat])

  const formatButtons: FormatButton[] = [
    { icon: Bold, command: 'bold', label: 'Bold', shortcut: 'Ctrl+B' },
    { icon: Italic, command: 'italic', label: 'Italic', shortcut: 'Ctrl+I' },
    { icon: Underline, command: 'underline', label: 'Underline', shortcut: 'Ctrl+U' },
    { icon: Strikethrough, command: 'strikethrough', label: 'Strikethrough' },
  ]

  const headingButtons: FormatButton[] = [
    { icon: Heading1, command: 'heading1', label: 'Heading 1', shortcut: 'Ctrl+Alt+1' },
    { icon: Heading2, command: 'heading2', label: 'Heading 2', shortcut: 'Ctrl+Alt+2' },
    { icon: Heading3, command: 'heading3', label: 'Heading 3', shortcut: 'Ctrl+Alt+3' },
  ]

  const listButtons: FormatButton[] = [
    { icon: List, command: 'bulletList', label: 'Bullet List', shortcut: 'Ctrl+Shift+8' },
    { icon: ListOrdered, command: 'orderedList', label: 'Numbered List', shortcut: 'Ctrl+Shift+7' },
    { icon: CheckSquare, command: 'checkList', label: 'Checklist' },
  ]

  const insertButtons: FormatButton[] = [
    { icon: Link, command: 'link', label: 'Insert Link', shortcut: 'Ctrl+K' },
    { icon: Image, command: 'image', label: 'Insert Image' },
    { icon: Quote, command: 'blockquote', label: 'Quote', shortcut: 'Ctrl+Shift+>' },
    { icon: Code, command: 'code', label: 'Code Block', shortcut: 'Ctrl+`' },
    { icon: Table, command: 'table', label: 'Insert Table' },
    { icon: Minus, command: 'horizontalRule', label: 'Horizontal Rule' },
  ]

  const alignmentButtons: FormatButton[] = [
    { icon: AlignLeft, command: 'alignLeft', label: 'Align Left' },
    { icon: AlignCenter, command: 'alignCenter', label: 'Align Center' },
    { icon: AlignRight, command: 'alignRight', label: 'Align Right' },
  ]

  // Create memoized handlers
  const createToggleHandler = useCallback((command: string) => () => toggleFormat(command), [toggleFormat])

  const FormatToggle = ({ button }: { button: FormatButton }) => {
    const Icon = button.icon
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <Toggle
            pressed={activeFormats.has(button.command)}
            onPressedChange={createToggleHandler(button.command)}
            size="sm"
            className="h-8 px-2 hover:bg-warm-100 data-[state=on]:bg-warm-200 data-[state=on]:text-warm-900"
            aria-label={button.label}
          >
            <Icon className="h-4 w-4" />
          </Toggle>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="flex items-center gap-2">
          <span>{button.label}</span>
          {button.shortcut && (
            <kbd className="text-xs bg-muted px-1 py-0.5 rounded">{button.shortcut}</kbd>
          )}
        </TooltipContent>
      </Tooltip>
    )
  }

  const FormatButton = ({ button }: { button: FormatButton }) => {
    const Icon = button.icon
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onFormat(button.command)}
            className="h-8 px-2 hover:bg-warm-100"
            aria-label={button.label}
          >
            <Icon className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="flex items-center gap-2">
          <span>{button.label}</span>
          {button.shortcut && (
            <kbd className="text-xs bg-muted px-1 py-0.5 rounded">{button.shortcut}</kbd>
          )}
        </TooltipContent>
      </Tooltip>
    )
  }

  return (
    <TooltipProvider delayDuration={300}>
      <div className={`flex items-center gap-1 px-3 py-2 bg-gradient-to-b from-warm-50 to-warm-100/50 border-b border-warm-200/50 ${className}`}>
        {/* Undo/Redo */}
        <div className="flex items-center gap-0.5 mr-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={onUndo}
                disabled={!canUndo}
                className="h-8 px-2 hover:bg-warm-100"
                aria-label="Undo"
              >
                <Undo className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom" className="flex items-center gap-2">
              <span>Undo</span>
              <kbd className="text-xs bg-muted px-1 py-0.5 rounded">Ctrl+Z</kbd>
            </TooltipContent>
          </Tooltip>
          
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={onRedo}
                disabled={!canRedo}
                className="h-8 px-2 hover:bg-warm-100"
                aria-label="Redo"
              >
                <Redo className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom" className="flex items-center gap-2">
              <span>Redo</span>
              <kbd className="text-xs bg-muted px-1 py-0.5 rounded">Ctrl+Y</kbd>
            </TooltipContent>
          </Tooltip>
        </div>

        <Separator orientation="vertical" className="h-6 bg-warm-200/50" />

        {/* Text Formatting */}
        <div className="flex items-center gap-0.5">
          {formatButtons.map((button) => (
            <FormatToggle key={button.command} button={button} />
          ))}
          
          {/* Highlight Color */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onFormat('highlight')}
                className="h-8 px-2 hover:bg-warm-100"
                aria-label="Highlight"
              >
                <Highlighter className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <span>Highlight</span>
            </TooltipContent>
          </Tooltip>
        </div>

        <Separator orientation="vertical" className="h-6 bg-warm-200/50" />

        {/* Headings Dropdown */}
        <div className="flex items-center">
          <DropdownMenu>
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2 hover:bg-warm-100 flex items-center gap-1"
                  >
                    <Type className="h-4 w-4" />
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <span>Headings</span>
              </TooltipContent>
            </Tooltip>
            <DropdownMenuContent align="start" className="w-48">
              {headingButtons.map((button) => {
                const Icon = button.icon
                return (
                  <DropdownMenuItem
                    key={button.command}
                    onClick={() => onFormat(button.command)}
                    className="flex items-center justify-between"
                  >
                    <span className="flex items-center gap-2">
                      <Icon className="h-4 w-4" />
                      {button.label}
                    </span>
                    {button.shortcut && (
                      <kbd className="text-xs bg-muted px-1 py-0.5 rounded ml-2">
                        {button.shortcut}
                      </kbd>
                    )}
                  </DropdownMenuItem>
                )
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <Separator orientation="vertical" className="h-6 bg-warm-200/50" />

        {/* Lists */}
        <div className="flex items-center gap-0.5">
          {listButtons.slice(0, 2).map((button) => (
            <FormatButton key={button.command} button={button} />
          ))}
        </div>

        <Separator orientation="vertical" className="h-6 bg-warm-200/50" />

        {/* Alignment */}
        <div className="flex items-center gap-0.5">
          {alignmentButtons.map((button) => (
            <FormatButton key={button.command} button={button} />
          ))}
        </div>

        <Separator orientation="vertical" className="h-6 bg-warm-200/50" />

        {/* Insert Elements */}
        <div className="flex items-center gap-0.5">
          {insertButtons.slice(0, 3).map((button) => (
            <FormatButton key={button.command} button={button} />
          ))}
          
          {/* More Options */}
          <DropdownMenu>
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2 hover:bg-warm-100"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <span>More Options</span>
              </TooltipContent>
            </Tooltip>
            <DropdownMenuContent align="end" className="w-48">
              {[...insertButtons.slice(3), ...listButtons.slice(2)].map((button) => {
                const Icon = button.icon
                return (
                  <DropdownMenuItem
                    key={button.command}
                    onClick={() => onFormat(button.command)}
                    className="flex items-center gap-2"
                  >
                    <Icon className="h-4 w-4" />
                    {button.label}
                  </DropdownMenuItem>
                )
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Format Painter */}
        <div className="ml-auto flex items-center gap-0.5">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onFormat('formatPainter')}
                className="h-8 px-2 hover:bg-warm-100"
                aria-label="Format Painter"
              >
                <Palette className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <span>Format Painter</span>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </TooltipProvider>
  )
}

export const EnhancedFormattingToolbar = React.memo(EnhancedFormattingToolbarComponent)