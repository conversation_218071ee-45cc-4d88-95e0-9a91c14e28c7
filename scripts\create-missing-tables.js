const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function createTables() {
  console.log('🚀 Creating missing database tables...')
  
  try {
    // First, let's check what tables exist
    console.log('\n📋 Checking existing tables...')
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
    
    if (tablesError) {
      console.error('Error checking tables:', tablesError)
      return
    }
    
    const existingTables = tables.map(t => t.table_name)
    console.log('Existing tables:', existingTables)
    
    // Check if user_achievements exists
    if (!existingTables.includes('user_achievements')) {
      console.log('\n🔧 Creating user_achievements table...')
      
      // Create achievement_definitions first
      if (!existingTables.includes('achievement_definitions')) {
        console.log('Creating achievement_definitions table...')
        // We'll need to use SQL for complex table creation
        // For now, let's create a simple version
        const { error } = await supabase.rpc('create_achievement_tables')
        if (error) {
          console.log('RPC not available, tables may need manual creation')
        }
      }
    } else {
      console.log('✅ user_achievements table already exists')
    }
    
    // Check if quality_metrics exists
    if (!existingTables.includes('quality_metrics')) {
      console.log('\n🔧 Creating quality_metrics table...')
      // Similar approach for quality_metrics
    } else {
      console.log('✅ quality_metrics table already exists')
    }
    
    console.log('\n🎉 Table creation process completed!')
    console.log('\n⚠️  Note: Some tables may need to be created manually in Supabase dashboard')
    console.log('Please run the SQL migrations in the Supabase SQL editor:')
    console.log('- supabase/migrations/20250119_create_achievements.sql')
    console.log('- supabase/migrations/20250119_create_quality_metrics.sql')
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  }
}

createTables()
