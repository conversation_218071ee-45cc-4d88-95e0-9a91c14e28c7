/**
 * Streaming Chat API
 * Provides streaming chat functionality using Vercel AI SDK
 */

import { NextRequest } from 'next/server'
import { streamText } from 'ai'
import { openai } from '@ai-sdk/openai'
import { authenticateUser, handleRouteError } from '@/lib/auth'
import { aiLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter'
import { AI_MODELS, AI_TEMPERATURE } from '@/lib/config/ai-settings'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'

// Message validation schema
const messageSchema = z.object({
  role: z.enum(['user', 'assistant', 'system']),
  content: z.string().min(1).max(10000),
  id: z.string().optional()
})

const chatRequestSchema = z.object({
  messages: z.array(messageSchema).min(1).max(50),
  model: z.string().optional(),
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().min(1).max(4000).optional(),
  systemPrompt: z.string().optional(),
  context: z.object({
    projectId: z.string().optional(),
    chapterId: z.string().optional(),
    characterNames: z.array(z.string()).optional(),
    storyGenre: z.string().optional(),
    writingStyle: z.string().optional()
  }).optional()
})

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for chat (50 messages per hour)
    const clientIP = getClientIP(request)
    const rateLimitResult = aiLimiter.check(50, clientIP)
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset)
    }

    // Authenticate user
    const { user } = await authenticateUser(request)
    if (!user) {
      return new Response('Unauthorized', { status: 401 })
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = chatRequestSchema.parse(body)

    const {
      messages,
      model = AI_MODELS.FAST, // Use fast model for chat
      temperature = AI_TEMPERATURE.BALANCED,
      maxTokens = 1000, // Shorter responses for chat
      systemPrompt,
      context
    } = validatedData

    // Map our model names to provider models
    const modelMap: Record<string, string> = {
      'gpt-4.1-2025-04-14': 'gpt-4-turbo',
      'gpt-4o-mini': 'gpt-4o-mini',
      'gpt-4-turbo': 'gpt-4-turbo',
      'gpt-4-0125-preview': 'gpt-4-turbo',
      'gpt-3.5-turbo': 'gpt-3.5-turbo',
      'grok-beta': 'grok-beta',
      'grok-vision-beta': 'grok-vision-beta',
    }

    const mappedModel = modelMap[model] || 'gpt-4o-mini'

    // Build context-aware system prompt
    let enhancedSystemPrompt = systemPrompt || 'You are an expert creative writing assistant. Help users with their writing projects by providing thoughtful, constructive feedback and suggestions.'

    if (context) {
      const contextParts = []
      
      if (context.storyGenre) {
        contextParts.push(`The story genre is ${context.storyGenre}.`)
      }
      
      if (context.writingStyle) {
        contextParts.push(`The writing style should be ${context.writingStyle}.`)
      }
      
      if (context.characterNames && context.characterNames.length > 0) {
        contextParts.push(`Key characters include: ${context.characterNames.join(', ')}.`)
      }
      
      if (contextParts.length > 0) {
        enhancedSystemPrompt += '\n\nContext: ' + contextParts.join(' ')
      }
    }

    // Convert messages to the format expected by Vercel AI SDK
    const formattedMessages = messages.map(msg => ({
      role: msg.role as 'user' | 'assistant' | 'system',
      content: msg.content
    }))

    // Add system message if not already present
    if (!formattedMessages.some(msg => msg.role === 'system')) {
      formattedMessages.unshift({
        role: 'system',
        content: enhancedSystemPrompt
      })
    }

    logger.info(`Chat message started for user ${user.id}`, {
      model: mappedModel,
      messageCount: messages.length,
      hasContext: !!context
    })

    // Create streaming response
    const result = await streamText({
      model: openai(mappedModel),
      messages: formattedMessages,
      temperature,
      maxTokens,
      onFinish: async ({ text, finishReason, usage }) => {
        logger.info(`Chat message completed for user ${user.id}`, {
          finishReason,
          tokensUsed: usage?.totalTokens,
          responseLength: text.length
        })
      }
    })

    return result.toAIStreamResponse()

  } catch (error) {
    logger.error('Error in chat API:', error)
    
    if (error instanceof z.ZodError) {
      return new Response(
        JSON.stringify({ 
          error: 'Invalid request data', 
          details: error.errors 
        }), 
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    return handleRouteError(error, 'Chat API')
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'models':
        return new Response(
          JSON.stringify({
            success: true,
            data: {
              available: Object.keys(AI_MODELS),
              recommended: AI_MODELS.FAST,
              premium: AI_MODELS.PRIMARY
            }
          }),
          { headers: { 'Content-Type': 'application/json' } }
        )

      case 'context-types':
        return new Response(
          JSON.stringify({
            success: true,
            data: {
              supportedContext: [
                'projectId',
                'chapterId', 
                'characterNames',
                'storyGenre',
                'writingStyle'
              ],
              genres: [
                'fantasy',
                'science-fiction',
                'mystery',
                'romance',
                'thriller',
                'literary-fiction',
                'young-adult',
                'historical-fiction'
              ],
              styles: [
                'literary',
                'commercial',
                'minimalist',
                'descriptive',
                'dialogue-heavy',
                'action-packed'
              ]
            }
          }),
          { headers: { 'Content-Type': 'application/json' } }
        )

      case 'limits':
        return new Response(
          JSON.stringify({
            success: true,
            data: {
              rateLimit: '50 messages per hour',
              maxMessages: 50,
              maxMessageLength: 10000,
              maxTokens: 4000,
              authentication: 'required'
            }
          }),
          { headers: { 'Content-Type': 'application/json' } }
        )

      default:
        return new Response(
          JSON.stringify({
            service: 'streaming-chat',
            version: '2.0.0',
            endpoints: [
              'POST /api/ai/chat - Send chat message with streaming response',
              'GET /api/ai/chat?action=models - Get available models',
              'GET /api/ai/chat?action=context-types - Get supported context types',
              'GET /api/ai/chat?action=limits - Get API limits and requirements'
            ],
            features: [
              'Real-time streaming responses',
              'Context-aware conversations',
              'Multiple model support',
              'Rate limiting protection',
              'Message history support'
            ]
          }),
          { headers: { 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    return handleRouteError(error, 'Chat API GET')
  }
}
