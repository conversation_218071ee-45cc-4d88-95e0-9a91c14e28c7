import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';
import { logger } from '@/lib/services/logger';
import { 
  generalLimiter, 
  aiLimiter, 
  authLimiter, 
  getClientIP, 
  createRateLimitResponse 
} from '@/lib/rate-limiter';
import { 
  AI_RATE_LIMITS, 
  createAIRateLimitResponse 
} from '@/lib/rate-limiter-ai';

// Types for middleware configuration
export interface MiddlewareConfig {
  auth?: boolean;
  rateLimit?: {
    type: 'general' | 'ai' | 'auth' | 'ai-generation' | 'ai-chat' | 'ai-analysis';
    requests?: number;
  };
  validation?: z.ZodSchema;
  projectAccess?: boolean;
}

export interface AuthenticatedRequest extends NextRequest {
  user: {
    id: string;
    email: string;
  };
  supabase: ReturnType<typeof createClient>;
}

export interface ProjectRequest extends AuthenticatedRequest {
  project: {
    id: string;
    user_id: string;
    [key: string]: unknown;
  };
}

// Error response helper
function createErrorResponse(message: string, status: number = 400) {
  return NextResponse.json({ error: message }, { status });
}

// Authentication middleware
async function authenticate(request: NextRequest): Promise<{ user: AuthenticatedRequest['user']; supabase: AuthenticatedRequest['supabase'] } | NextResponse> {
  const supabase = await createClient();
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error || !user) {
    logger.warn('Authentication failed', { error, path: request.url });
    return createErrorResponse('Unauthorized', 401);
  }
  
  return { 
    user: { id: user.id, email: user.email! }, 
    supabase 
  };
}

// Rate limiting middleware
function checkRateLimit(request: NextRequest, config: MiddlewareConfig['rateLimit']): NextResponse | null {
  if (!config) return null;
  
  const clientIP = getClientIP(request);
  
  // Handle AI-specific rate limits
  if (config.type.startsWith('ai-')) {
    const aiType = config.type.replace('ai-', '') as keyof typeof AI_RATE_LIMITS;
    const limits = AI_RATE_LIMITS[aiType];
    if (limits) {
      const result = limits.limiter.check(limits.requests, clientIP);
      if (!result.success) {
        return createAIRateLimitResponse(result.reset);
      }
    }
  } else {
    // Handle general rate limits
    const limiter = config.type === 'auth' ? authLimiter : 
                   config.type === 'ai' ? aiLimiter : 
                   generalLimiter;
    const requests = config.requests || 100;
    const result = limiter.check(requests, clientIP);
    
    if (!result.success) {
      return createRateLimitResponse(result.remaining, result.reset);
    }
  }
  
  return null;
}

// Validation middleware
async function validateRequest<T extends z.ZodSchema>(
  request: NextRequest, 
  schema: T
): Promise<z.infer<T> | NextResponse> {
  try {
    const body = await request.json();
    const result = schema.safeParse(body);
    
    if (!result.success) {
      logger.warn('Validation failed', { 
        errors: result.error.errors, 
        path: request.url 
      });
      return createErrorResponse(
        'Invalid request data: ' + result.error.errors.map(e => e.message).join(', ')
      );
    }
    
    return result.data;
  } catch (error) {
    logger.error('Failed to parse request body', { error, path: request.url });
    return createErrorResponse('Invalid JSON body');
  }
}

// Project access middleware
async function checkProjectAccess(
  request: AuthenticatedRequest,
  projectId: string
): Promise<ProjectRequest['project'] | NextResponse> {
  const { data: project, error } = await request.supabase
    .from('projects')
    .select('*')
    .eq('id', projectId)
    .eq('user_id', request.user.id)
    .single();
    
  if (error || !project) {
    logger.warn('Project access denied', { 
      projectId, 
      userId: request.user.id, 
      error 
    });
    return createErrorResponse('Project not found', 404);
  }
  
  return project;
}

// Main middleware wrapper
export function withMiddleware<T extends NextRequest = NextRequest>(
  handler: (request: T) => Promise<NextResponse>,
  config: MiddlewareConfig = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      // Rate limiting
      const rateLimitResponse = checkRateLimit(request, config.rateLimit);
      if (rateLimitResponse) return rateLimitResponse;
      
      // Authentication
      let authenticatedRequest = request as T;
      if (config.auth !== false) {
        const authResult = await authenticate(request);
        if (authResult instanceof NextResponse) return authResult;
        
        // Attach auth data to request
        (authenticatedRequest as AuthenticatedRequest).user = authResult.user;
        (authenticatedRequest as AuthenticatedRequest).supabase = authResult.supabase;
      }
      
      // Validation
      if (config.validation) {
        const validationResult = await validateRequest(request, config.validation);
        if (validationResult instanceof NextResponse) return validationResult;
        
        // Attach validated data to request
        (authenticatedRequest as AuthenticatedRequest & { validated: unknown }).validated = validationResult;
      }
      
      // Execute handler
      return await handler(authenticatedRequest);
      
    } catch (error) {
      logger.error('Middleware error', { 
        error, 
        path: request.url,
        method: request.method 
      });
      
      if (error instanceof Error) {
        return createErrorResponse(error.message, 500);
      }
      
      return createErrorResponse('Internal server error', 500);
    }
  };
}

// Specialized middleware for AI routes
export function withAIRoute<T extends AuthenticatedRequest = AuthenticatedRequest>(
  handler: (request: T) => Promise<NextResponse>,
  aiType: 'generation' | 'chat' | 'analysis' = 'generation',
  validation?: z.ZodSchema
) {
  return withMiddleware(handler, {
    auth: true,
    rateLimit: { type: `ai-${aiType}` as const },
    validation
  });
}

// Specialized middleware for public routes
export function withPublicRoute<T extends NextRequest = NextRequest>(
  handler: (request: T) => Promise<NextResponse>,
  validation?: z.ZodSchema
) {
  return withMiddleware(handler, {
    auth: false,
    rateLimit: { type: 'general' },
    validation
  });
}

// Specialized middleware for project-specific routes
export function withProjectAccess<T extends ProjectRequest = ProjectRequest>(
  handler: (request: T, params: { id: string }) => Promise<NextResponse>,
  validation?: z.ZodSchema
) {
  return async (
    request: NextRequest,
    context: { params: Promise<{ id: string }> }
  ): Promise<NextResponse> => {
    const { id } = await context.params;
    
    return withMiddleware(async (req: AuthenticatedRequest) => {
      // Check project access
      const project = await checkProjectAccess(req, id);
      if (project instanceof NextResponse) return project;
      
      // Attach project to request
      (req as ProjectRequest).project = project;
      
      return handler(req as T, { id });
    }, {
      auth: true,
      rateLimit: { type: 'general' },
      validation
    })(request);
  };
}

// Helper to extract validated data from request
export function getValidatedData<T>(request: AuthenticatedRequest & { validated?: T }): T {
  if (!request.validated) {
    throw new Error('No validated data found on request');
  }
  return request.validated;
}

// Helper to extract pagination params
export interface PaginationParams {
  page: number;
  limit: number;
  offset: number;
}

export function getPaginationParams(request: NextRequest): PaginationParams {
  const url = new URL(request.url);
  const page = Math.max(1, parseInt(url.searchParams.get('page') || '1'));
  const limit = Math.min(100, Math.max(1, parseInt(url.searchParams.get('limit') || '20')));
  const offset = (page - 1) * limit;
  
  return { page, limit, offset };
}

// Helper to create paginated response
export function createPaginatedResponse<T>(
  data: T[],
  total: number,
  params: PaginationParams
) {
  return NextResponse.json({
    data,
    pagination: {
      page: params.page,
      limit: params.limit,
      total,
      totalPages: Math.ceil(total / params.limit),
      hasMore: params.offset + data.length < total
    }
  });
}

// Export helper functions for API routes
export function apiError(message: string, status: number = 400) {
  return NextResponse.json({ error: message }, { status });
}

export function apiResponse(data: unknown, status: number = 200) {
  return NextResponse.json(data, { status });
}