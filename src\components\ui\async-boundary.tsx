'use client';

import { ReactNode, Suspense } from 'react';
import { ErrorBoundary } from '@/components/error/error-boundary';
import { SectionLoader, PageLoader } from '@/components/ui/loading-states';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface AsyncBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  errorFallback?: ReactNode;
  loadingType?: 'page' | 'section' | 'inline' | 'custom';
  loadingMessage?: string;
  isolate?: boolean;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  showErrorDetails?: boolean;
}

export function AsyncBoundary({
  children,
  fallback,
  errorFallback,
  loadingType = 'section',
  loadingMessage,
  isolate = true,
  onError,
  showErrorDetails = false
}: AsyncBoundaryProps) {
  const getLoadingFallback = () => {
    if (fallback) return fallback;
    
    switch (loadingType) {
      case 'page':
        return <PageLoader message={loadingMessage} />;
      case 'section':
        return <SectionLoader />;
      case 'inline':
        return <span className="text-muted-foreground">Loading...</span>;
      default:
        return <SectionLoader />;
    }
  };

  const defaultErrorFallback = (
    <div className={`${isolate ? 'p-4' : 'min-h-[400px] flex items-center justify-center'}`}>
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Something went wrong</AlertTitle>
        <AlertDescription className="mt-2 space-y-2">
          <p>We encountered an error loading this content.</p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="mt-2"
            aria-label="Reload page"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reload Page
          </Button>
        </AlertDescription>
      </Alert>
    </div>
  );

  return (
    <ErrorBoundary
      fallback={errorFallback || defaultErrorFallback}
      onError={onError}
      showErrorDetails={showErrorDetails}
      isolate={isolate}
    >
      <Suspense fallback={getLoadingFallback()}>
        {children}
      </Suspense>
    </ErrorBoundary>
  );
}

// HOC version for easier component wrapping
export function withAsyncBoundary<T extends object>(
  Component: React.ComponentType<T>,
  boundaryProps?: Omit<AsyncBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: T) => (
    <AsyncBoundary {...boundaryProps}>
      <Component {...props} />
    </AsyncBoundary>
  );

  WrappedComponent.displayName = `withAsyncBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

// Utility component for lazy-loaded components
export function LazyBoundary({
  children,
  loadingMessage = 'Loading component...',
  ...props
}: AsyncBoundaryProps) {
  return (
    <AsyncBoundary
      loadingType="section"
      loadingMessage={loadingMessage}
      isolate={true}
      {...props}
    >
      {children}
    </AsyncBoundary>
  );
}