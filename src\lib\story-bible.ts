import { StoryBible, BookContext, StoryStructure, CharacterProfiles, ChapterOutlines, WorldBuilding, CharacterState, PlotThread, EstablishedFact, ThemeOccurrence } from './agents/types';
import { logger } from '@/lib/services/logger';

import { ProjectSettings } from './types/project-settings';
import { createClient } from './supabase/client';

export class StoryBibleManager {
  private projectId: string;
  private storyBible: StoryBible;
  private supabase = createClient();

  constructor(projectId: string) {
    this.projectId = projectId;
    this.storyBible = this.initializeEmptyBible();
  }

  private initializeEmptyBible(): StoryBible {
    return {
      projectId: this.projectId,
      lastUpdated: new Date(),
      structure: {} as StoryStructure,
      characters: {} as CharacterProfiles,
      world: {
        setting: {
          timeForPeriod: '',
          locations: [],
          culture: '',
          technology: '',
        },
        rules: [],
        history: []
      } as WorldBuilding,
      timeline: [],
      themes: [],
      continuity: {
        characterStates: new Map(),
        plotThreads: [],
        establishedFacts: [],
      },
      style: {
        voice: '',
        tone: [],
        vocabulary: '',
        sentenceStructure: '',
        paragraphLength: '',
        dialogueStyle: '',
        descriptionStyle: '',
      },
    };
  }

  async loadStoryBible(): Promise<StoryBible> {
    try {
      const { data, error } = await this.supabase
        .from('story_bibles')
        .select('*')
        .eq('project_id', this.projectId)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found error
        throw error;
      }

      if (data) {
        this.storyBible = {
          projectId: this.projectId,
          lastUpdated: new Date(data.last_updated),
          structure: data.structure_data || {},
          characters: data.character_data || {},
          world: data.world_data || {},
          timeline: data.timeline_data || [],
          themes: data.theme_tracking || [],
          continuity: data.continuity_data || this.storyBible.continuity,
          style: data.style_guide || this.storyBible.style,
        };
      }

      return this.storyBible;
    } catch (error) {
      logger.error('Error loading story bible:', error);
      return this.storyBible;
    }
  }

  async saveStoryBible(): Promise<void> {
    try {
      this.storyBible.lastUpdated = new Date();

      const { error } = await this.supabase
        .from('story_bibles')
        .upsert({
          project_id: this.projectId,
          structure_data: this.storyBible.structure,
          character_data: this.storyBible.characters,
          world_data: this.storyBible.world,
          timeline_data: this.storyBible.timeline,
          theme_tracking: this.storyBible.themes,
          continuity_data: this.storyBible.continuity,
          style_guide: this.storyBible.style,
          last_updated: this.storyBible.lastUpdated.toISOString(),
        });

      if (error) {
        throw error;
      }
    } catch (error) {
      logger.error('Error saving story bible:', error);
      throw error;
    }
  }

  updateStructure(structure: StoryStructure): void {
    this.storyBible.structure = structure;
    this.storyBible.world = structure.worldBuilding;
    this.storyBible.timeline = structure.timeline;
    this.storyBible.themes = structure.themes.map(theme => ({
      theme,
      occurrences: [],
      development: '',
    }));
  }

  updateCharacters(characters: CharacterProfiles): void {
    this.storyBible.characters = characters;
    
    // Initialize character states
    const allCharacters = [
      ...characters.protagonists,
      ...characters.antagonists,
      ...characters.supporting,
    ];
    
    allCharacters.forEach(char => {
      this.storyBible.continuity.characterStates.set(char.id, {
        characterId: char.id,
        emotionalState: 'stable',
        physicalState: 'healthy',
        knowledge: [],
        relationships: '',
      });
    });
  }

  updateChapterOutlines(chapterOutlines: ChapterOutlines): void {
    // Extract plot threads from chapter outlines
    const plotThreads = new Set<string>();
    
    chapterOutlines.chapters.forEach(chapter => {
      chapter.plotAdvancement?.forEach(plot => {
        plotThreads.add(plot);
      });
    });

    // Update continuity with new plot threads
    plotThreads.forEach(threadName => {
      if (!this.storyBible.continuity.plotThreads.find(t => t.name === threadName)) {
        this.storyBible.continuity.plotThreads.push({
          id: `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: threadName,
          description: threadName,
          status: 'active',
          introduction: 1,
          relatedCharacters: [],
        });
      }
    });
  }

  getContextForChapter(_chapterNumber: number, settings?: ProjectSettings): BookContext {
    const defaultSettings: ProjectSettings = {
      projectName: '',
      description: '',
      targetAudience: 'adult_25_plus',
      contentRating: 'PG13',
      projectScope: 'standalone',
      initialConcept: '',
      primaryGenre: 'fantasy',
      subgenre: '',
      writingStyle: 'commercial',
      tone: ['epic_heroic'],
      narrativeVoice: 'third_person_limited',
      tense: 'past',
      structureType: 'three_act',
      pacingPreference: 'balanced',
      chapterStructure: 'scene_based',
      timelineComplexity: 'linear',
      protagonistTypes: ['the_hero'],
      antagonistTypes: ['the_villain'],
      characterComplexity: 'complex_layered',
      characterArcTypes: ['positive_change'],
      timePeriod: 'contemporary',
      geographicSetting: 'urban',
      worldType: 'real_world',
      magicTechLevel: 'no_magic_current_tech',
      majorThemes: ['good_vs_evil'],
      philosophicalThemes: ['morality'],
      socialThemes: ['class_struggle'],
      contentWarnings: [],
      targetWordCount: 80000,
      chapterCountType: 'flexible',
      povCharacterCount: 1,
      povCharacterType: 'single_pov',
      researchNeeds: [],
      factCheckingLevel: 'minimal'
    };

    return {
      projectId: this.projectId,
      settings: settings || defaultSettings,
      storyStructure: this.storyBible.structure,
      characters: this.storyBible.characters,
      chapterOutlines: undefined, // Will be filled by calling code
      storyBible: this.storyBible,
    };
  }

  addEstablishedFact(fact: string, chapterNumber: number, category: 'character' | 'world' | 'plot' | 'relationship', importance: 'critical' | 'major' | 'minor'): void {
    this.storyBible.continuity.establishedFacts.push({
      id: `fact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      description: fact,
      chapterEstablished: chapterNumber,
      category,
      importance,
    });
  }

  updateCharacterState(characterId: string, newState: CharacterState): void {
    this.storyBible.continuity.characterStates.set(characterId, newState);
  }

  getCharacterState(characterId: string): CharacterState | undefined {
    return this.storyBible.continuity.characterStates.get(characterId);
  }

  getActivePlotThreads(): PlotThread[] {
    return this.storyBible.continuity.plotThreads.filter(thread => thread.status === 'active');
  }

  resolvePlotThread(threadId: string, chapterNumber: number): void {
    const thread = this.storyBible.continuity.plotThreads.find(t => t.id === threadId);
    if (thread) {
      thread.status = 'resolved';
      thread.resolution = chapterNumber;
    }
  }

  getEstablishedFacts(category?: string): EstablishedFact[] {
    return category 
      ? this.storyBible.continuity.establishedFacts.filter(f => f.category === category)
      : this.storyBible.continuity.establishedFacts;
  }

  addThemeOccurrence(theme: string, chapterNumber: number, description: string, treatment: string): void {
    let themeTracker = this.storyBible.themes.find(t => t.theme === theme);
    
    if (!themeTracker) {
      themeTracker = {
        theme,
        occurrences: [],
        development: '',
      };
      this.storyBible.themes.push(themeTracker);
    }

    themeTracker.occurrences.push({
      chapterNumber,
      description,
      treatment,
    });
  }

  getThemeDevelopment(theme: string): ThemeOccurrence[] {
    const themeTracker = this.storyBible.themes.find(t => t.theme === theme);
    return themeTracker ? themeTracker.occurrences : [];
  }

  updateStyleGuide(style: Record<string, unknown>): void {
    this.storyBible.style = { ...this.storyBible.style, ...style };
  }

  getStoryBible(): StoryBible {
    return this.storyBible;
  }

  validateConsistency(): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Check character consistency
    const characters = [
      ...this.storyBible.characters.protagonists || [],
      ...this.storyBible.characters.antagonists || [],
      ...this.storyBible.characters.supporting || [],
    ];

    // Validate character states exist for all characters
    characters.forEach(char => {
      if (!this.storyBible.continuity.characterStates.has(char.id)) {
        issues.push(`Missing character state for ${char.name}`);
      }
    });

    // Check for unresolved plot threads
    const unresolvedThreads = this.storyBible.continuity.plotThreads.filter(t => t.status === 'active');
    if (unresolvedThreads.length > 5) {
      issues.push(`Too many unresolved plot threads (${unresolvedThreads.length})`);
    }

    // Check world-building consistency
    if (!this.storyBible.world || !this.storyBible.world.rules) {
      issues.push('Missing world-building rules');
    }

    return {
      isValid: issues.length === 0,
      issues,
    };
  }
}