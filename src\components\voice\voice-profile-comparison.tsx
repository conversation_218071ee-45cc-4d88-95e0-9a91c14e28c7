'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts'
import {
  Mic,
  BookOpen,
  User,
  TrendingUp,
  TrendingDown,
  Minus,
  AlertCircle,
  ChevronRight
} from 'lucide-react'

interface VoiceProfile {
  id: string
  name: string
  type: 'author' | 'character' | 'narrator'
  patterns: {
    sentenceStructure: {
      averageLength: number
      complexityScore: number
      variationScore: number
    }
    vocabulary: {
      uniqueWords: number
      averageWordLength: number
      formalityScore: number
      commonPhrases: string[]
    }
    style: {
      descriptiveness: number
      dialogueRatio: number
      actionRatio: number
      introspectionRatio: number
    }
    tone: {
      emotionalRange: string[]
      intensity: number
      consistency: number
    }
    rhythm: {
      paragraphLengthVariation: number
      pacingScore: number
    }
  }
  confidence: number
}

interface VoiceProfileComparisonProps {
  profiles: VoiceProfile[]
  currentProfileId?: string
}

interface ComparisonData {
  barData: Array<{
    metric: string
    [key: string]: string | number
  }>
  radarData: Array<{
    attribute: string
    [key: string]: string | number
  }>
  differences: Array<{
    category: string
    metrics: Array<{
      name: string
      profile1Value: number
      profile2Value: number
      difference: number
      trend: 'up' | 'down' | 'neutral'
    }>
  }>
}

export function VoiceProfileComparison({ profiles, currentProfileId }: VoiceProfileComparisonProps) {
  const [profile1Id, setProfile1Id] = useState<string>(currentProfileId || '')
  const [profile2Id, setProfile2Id] = useState<string>('')
  const [comparisonData, setComparisonData] = useState<ComparisonData | null>(null)

  const profile1 = profiles.find(p => p.id === profile1Id)
  const profile2 = profiles.find(p => p.id === profile2Id)

  useEffect(() => {
    if (profile1 && profile2) {
      generateComparisonData()
    }
  }, [profile1, profile2])

  const generateComparisonData = () => {
    if (!profile1 || !profile2) return

    // Structure data for bar chart
    const barData = [
      {
        metric: 'Sentence Length',
        [profile1.name]: profile1.patterns.sentenceStructure.averageLength,
        [profile2.name]: profile2.patterns.sentenceStructure.averageLength,
      },
      {
        metric: 'Complexity',
        [profile1.name]: profile1.patterns.sentenceStructure.complexityScore,
        [profile2.name]: profile2.patterns.sentenceStructure.complexityScore,
      },
      {
        metric: 'Formality',
        [profile1.name]: profile1.patterns.vocabulary.formalityScore,
        [profile2.name]: profile2.patterns.vocabulary.formalityScore,
      },
      {
        metric: 'Dialogue %',
        [profile1.name]: profile1.patterns.style.dialogueRatio,
        [profile2.name]: profile2.patterns.style.dialogueRatio,
      },
      {
        metric: 'Descriptiveness',
        [profile1.name]: profile1.patterns.style.descriptiveness,
        [profile2.name]: profile2.patterns.style.descriptiveness,
      },
      {
        metric: 'Emotional Intensity',
        [profile1.name]: profile1.patterns.tone.intensity,
        [profile2.name]: profile2.patterns.tone.intensity,
      },
    ]

    // Structure data for radar chart
    const radarData = [
      {
        aspect: 'Structure',
        [profile1.name]: profile1.patterns.sentenceStructure.complexityScore,
        [profile2.name]: profile2.patterns.sentenceStructure.complexityScore,
        fullMark: 100,
      },
      {
        aspect: 'Vocabulary',
        [profile1.name]: profile1.patterns.vocabulary.formalityScore,
        [profile2.name]: profile2.patterns.vocabulary.formalityScore,
        fullMark: 100,
      },
      {
        aspect: 'Dialogue',
        [profile1.name]: profile1.patterns.style.dialogueRatio,
        [profile2.name]: profile2.patterns.style.dialogueRatio,
        fullMark: 100,
      },
      {
        aspect: 'Description',
        [profile1.name]: profile1.patterns.style.descriptiveness,
        [profile2.name]: profile2.patterns.style.descriptiveness,
        fullMark: 100,
      },
      {
        aspect: 'Emotion',
        [profile1.name]: profile1.patterns.tone.intensity,
        [profile2.name]: profile2.patterns.tone.intensity,
        fullMark: 100,
      },
      {
        aspect: 'Pacing',
        [profile1.name]: profile1.patterns.rhythm.pacingScore || 50,
        [profile2.name]: profile2.patterns.rhythm.pacingScore || 50,
        fullMark: 100,
      },
    ]

    setComparisonData({ barData, radarData })
  }

  const getMetricDifference = (metric1: number, metric2: number) => {
    const diff = metric1 - metric2
    const percentage = Math.abs(diff)
    
    if (Math.abs(diff) < 5) {
      return { icon: Minus, color: 'text-muted-foreground', text: 'Similar' }
    } else if (diff > 0) {
      return { icon: TrendingUp, color: 'text-green-600', text: `+${percentage.toFixed(0)}%` }
    } else {
      return { icon: TrendingDown, color: 'text-red-600', text: `-${percentage.toFixed(0)}%` }
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'author':
        return <BookOpen className="h-4 w-4" />
      case 'character':
        return <User className="h-4 w-4" />
      case 'narrator':
        return <Mic className="h-4 w-4" />
      default:
        return <Mic className="h-4 w-4" />
    }
  }

  if (profiles.length < 2) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Voice Profile Comparison</CardTitle>
          <CardDescription>
            Create at least two voice profiles to compare them
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              You need at least two voice profiles to use the comparison tool.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Voice Profile Comparison</CardTitle>
        <CardDescription>
          Compare voice characteristics between two profiles
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Profile Selection */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium mb-2 block">First Profile</label>
            <Select value={profile1Id} onValueChange={setProfile1Id}>
              <SelectTrigger>
                <SelectValue placeholder="Select profile" />
              </SelectTrigger>
              <SelectContent>
                {profiles.map((profile) => (
                  <SelectItem key={profile.id} value={profile.id}>
                    <div className="flex items-center gap-2">
                      {getTypeIcon(profile.type)}
                      <span>{profile.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Second Profile</label>
            <Select 
              value={profile2Id} 
              onValueChange={setProfile2Id}
              disabled={!profile1Id}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select profile to compare" />
              </SelectTrigger>
              <SelectContent>
                {profiles
                  .filter(p => p.id !== profile1Id)
                  .map((profile) => (
                    <SelectItem key={profile.id} value={profile.id}>
                      <div className="flex items-center gap-2">
                        {getTypeIcon(profile.type)}
                        <span>{profile.name}</span>
                      </div>
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {profile1 && profile2 && comparisonData && (
          <>
            <Separator />

            {/* Profile Summary */}
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  {getTypeIcon(profile1.type)}
                  <h4 className="font-medium">{profile1.name}</h4>
                  <Badge variant="outline" className="ml-auto">
                    {Math.round(profile1.confidence * 100)}%
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {profile1.type} voice profile
                </p>
              </div>

              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  {getTypeIcon(profile2.type)}
                  <h4 className="font-medium">{profile2.name}</h4>
                  <Badge variant="outline" className="ml-auto">
                    {Math.round(profile2.confidence * 100)}%
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {profile2.type} voice profile
                </p>
              </div>
            </div>

            <Tabs defaultValue="metrics" className="mt-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="metrics">Key Metrics</TabsTrigger>
                <TabsTrigger value="charts">Visual Comparison</TabsTrigger>
                <TabsTrigger value="phrases">Phrases & Style</TabsTrigger>
              </TabsList>

              <TabsContent value="metrics" className="space-y-4 mt-4">
                <div className="space-y-3">
                  {/* Sentence Structure */}
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium">Sentence Structure</h5>
                    <div className="grid grid-cols-3 gap-2 text-sm">
                      <div className="p-3 bg-muted rounded-lg">
                        <p className="text-muted-foreground">Average Length</p>
                        <div className="flex items-center justify-between mt-1">
                          <span className="font-medium">
                            {profile1.patterns.sentenceStructure.averageLength.toFixed(1)}
                          </span>
                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">
                            {profile2.patterns.sentenceStructure.averageLength.toFixed(1)}
                          </span>
                        </div>
                      </div>
                      <div className="p-3 bg-muted rounded-lg">
                        <p className="text-muted-foreground">Complexity</p>
                        <div className="flex items-center justify-between mt-1">
                          <span className="font-medium">
                            {profile1.patterns.sentenceStructure.complexityScore}
                          </span>
                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">
                            {profile2.patterns.sentenceStructure.complexityScore}
                          </span>
                        </div>
                      </div>
                      <div className="p-3 bg-muted rounded-lg">
                        <p className="text-muted-foreground">Variation</p>
                        <div className="flex items-center justify-between mt-1">
                          <span className="font-medium">
                            {profile1.patterns.sentenceStructure.variationScore}
                          </span>
                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">
                            {profile2.patterns.sentenceStructure.variationScore}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Style Ratios */}
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium">Writing Style</h5>
                    <div className="space-y-2">
                      {[
                        { label: 'Dialogue', key: 'dialogueRatio' },
                        { label: 'Action', key: 'actionRatio' },
                        { label: 'Description', key: 'descriptiveness' },
                        { label: 'Introspection', key: 'introspectionRatio' },
                      ].map(({ label, key }) => {
                        const value1 = profile1.patterns.style[key as keyof typeof profile1.patterns.style]
                        const value2 = profile2.patterns.style[key as keyof typeof profile2.patterns.style]
                        const diff = getMetricDifference(value1, value2)
                        
                        return (
                          <div key={key} className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">{label}</span>
                            <div className="flex items-center gap-4">
                              <Progress value={value1} className="w-24 h-2" />
                              <span className="text-sm font-medium w-12 text-right">
                                {value1}%
                              </span>
                              <diff.icon className={`h-4 w-4 ${diff.color}`} />
                              <span className="text-sm font-medium w-12">
                                {value2}%
                              </span>
                              <Progress value={value2} className="w-24 h-2" />
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="charts" className="space-y-6 mt-4">
                {/* Bar Chart Comparison */}
                <div>
                  <h5 className="text-sm font-medium mb-3">Metric Comparison</h5>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={comparisonData.barData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="metric" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey={profile1.name} fill="#3b82f6" />
                      <Bar dataKey={profile2.name} fill="#10b981" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>

                {/* Radar Chart */}
                <div>
                  <h5 className="text-sm font-medium mb-3">Voice Profile Overview</h5>
                  <ResponsiveContainer width="100%" height={300}>
                    <RadarChart data={comparisonData.radarData}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="aspect" />
                      <PolarRadiusAxis angle={90} domain={[0, 100]} />
                      <Radar
                        name={profile1.name}
                        dataKey={profile1.name}
                        stroke="#3b82f6"
                        fill="#3b82f6"
                        fillOpacity={0.6}
                      />
                      <Radar
                        name={profile2.name}
                        dataKey={profile2.name}
                        stroke="#10b981"
                        fill="#10b981"
                        fillOpacity={0.6}
                      />
                      <Legend />
                    </RadarChart>
                  </ResponsiveContainer>
                </div>
              </TabsContent>

              <TabsContent value="phrases" className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4">
                  {/* Profile 1 Phrases */}
                  <div>
                    <h5 className="text-sm font-medium mb-2">{profile1.name} - Common Phrases</h5>
                    <ScrollArea className="h-48 border rounded-lg p-3">
                      <div className="space-y-2">
                        {profile1.patterns.vocabulary.commonPhrases.length > 0 ? (
                          profile1.patterns.vocabulary.commonPhrases.map((phrase, idx) => (
                            <Badge key={idx} variant="secondary" className="mr-2 mb-2">
                              {phrase}
                            </Badge>
                          ))
                        ) : (
                          <p className="text-sm text-muted-foreground">No common phrases identified</p>
                        )}
                      </div>
                    </ScrollArea>
                  </div>

                  {/* Profile 2 Phrases */}
                  <div>
                    <h5 className="text-sm font-medium mb-2">{profile2.name} - Common Phrases</h5>
                    <ScrollArea className="h-48 border rounded-lg p-3">
                      <div className="space-y-2">
                        {profile2.patterns.vocabulary.commonPhrases.length > 0 ? (
                          profile2.patterns.vocabulary.commonPhrases.map((phrase, idx) => (
                            <Badge key={idx} variant="secondary" className="mr-2 mb-2">
                              {phrase}
                            </Badge>
                          ))
                        ) : (
                          <p className="text-sm text-muted-foreground">No common phrases identified</p>
                        )}
                      </div>
                    </ScrollArea>
                  </div>
                </div>

                {/* Emotional Range Comparison */}
                <div>
                  <h5 className="text-sm font-medium mb-2">Emotional Range</h5>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-3 border rounded-lg">
                      <p className="text-sm text-muted-foreground mb-2">{profile1.name}</p>
                      <div className="flex flex-wrap gap-1">
                        {profile1.patterns.tone.emotionalRange.map((emotion, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {emotion}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="p-3 border rounded-lg">
                      <p className="text-sm text-muted-foreground mb-2">{profile2.name}</p>
                      <div className="flex flex-wrap gap-1">
                        {profile2.patterns.tone.emotionalRange.map((emotion, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {emotion}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </>
        )}
      </CardContent>
    </Card>
  )
}