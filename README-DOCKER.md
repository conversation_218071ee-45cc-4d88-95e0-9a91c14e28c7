# BookScribe AI - Docker Quick Start

This is a quick start guide for running BookScribe AI with Docker.

## Prerequisites

1. **Docker Desktop** - Download and install from [docker.com](https://www.docker.com/products/docker-desktop)
2. **Environment Variables** - You'll need API keys for OpenAI, Supabase, and Stripe

## Quick Setup (Windows)

### 1. Test Docker Setup
```powershell
.\docker-test.ps1
```

### 2. Configure Environment
```powershell
# Copy example environment file
copy .env.example .env.local

# Edit .env.local with your actual API keys
notepad .env.local
```

### 3. Build and Run
```powershell
# Production build and run
.\docker-build.ps1

# Development build and run
.\docker-build.ps1 -Environment development
```

## Quick Setup (Linux/macOS)

### 1. Configure Environment
```bash
# Copy example environment file
cp .env.example .env.local

# Edit .env.local with your actual API keys
nano .env.local
```

### 2. Build and Run
```bash
# Production build and run
./docker-build.sh

# Development build and run
./docker-build.sh -e development
```

## Manual Docker Commands

### Production
```bash
# Build
docker build -t bookscribe-ai .

# Run
docker run -d --name bookscribe-app -p 3000:3000 --env-file .env.local bookscribe-ai

# Or use docker-compose
docker-compose up -d
```

### Development
```bash
# Build
docker build -f Dockerfile.dev -t bookscribe-ai:dev .

# Run with docker-compose
docker-compose -f docker-compose.dev.yml up -d
```

## Required Environment Variables

Add these to your `.env.local` file:

```env
# Supabase (Required)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI (Required)
OPENAI_API_KEY=your_openai_api_key

# Stripe (Required for payments)
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## Accessing the Application

Once running, the application will be available at:
- **URL**: http://localhost:3000
- **Health Check**: http://localhost:3000/api/health?basic=true

## Useful Commands

```bash
# View logs
docker logs -f bookscribe-app

# Stop container
docker stop bookscribe-app

# Remove container
docker rm bookscribe-app

# View running containers
docker ps

# Access container shell
docker exec -it bookscribe-app sh
```

## Troubleshooting

### Container won't start
1. Check logs: `docker logs bookscribe-app`
2. Verify environment variables in `.env.local`
3. Ensure port 3000 is not in use

### Build fails
1. Clear Docker cache: `docker system prune -a`
2. Rebuild without cache: `docker build --no-cache -t bookscribe-ai .`

### Health check fails
1. Test manually: `curl http://localhost:3000/api/health?basic=true`
2. Check if application is responding
3. Verify environment configuration

## Next Steps

1. **Configure your environment variables** in `.env.local`
2. **Set up your Supabase database** (see main README.md)
3. **Configure Stripe** for payments (see main README.md)
4. **Deploy to production** (see DOCKER.md for detailed deployment guide)

For detailed documentation, see [DOCKER.md](./DOCKER.md).
