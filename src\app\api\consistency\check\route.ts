import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { authenticateUser, handleRouteError } from '@/lib/auth'
import { ConsistencyValidator } from '@/lib/services/consistency-validator'
import { z } from 'zod'

const ConsistencyCheckSchema = z.object({
  projectId: z.string(),
  chapterId: z.string().optional(),
  content: z.string().optional(),
  checkType: z.enum(['chapter', 'book', 'timeline', 'characters']).optional(),
})

export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const body = await request.json()
    const { projectId, chapterId, content, checkType = 'chapter' } = ConsistencyCheckSchema.parse(body)

    const supabase = await createClient()

    // Verify project ownership
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('*, chapters(*)')
      .eq('id', projectId)
      .eq('user_id', authResult.user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Initialize consistency validator
    const validator = new ConsistencyValidator()
    await validator.initialize()

    // Build book context from project data
    const bookContext = {
      projectId,
      settings: project.project_settings || {},
      projectSelections: project.project_selections || {},
      storyPrompt: project.story_prompt,
      targetWordCount: project.target_word_count,
      targetChapters: project.target_chapters,
      currentChapters: project.chapters || [],
      characters: project.metadata?.characters || {},
      worldBuilding: project.metadata?.worldBuilding || {},
      timeline: project.metadata?.timeline || [],
    }

    // Perform consistency check based on type
    let consistencyReport
    
    switch (checkType) {
      case 'chapter':
        if (!chapterId || !content) {
          return NextResponse.json({ error: 'Chapter ID and content required for chapter check' }, { status: 400 })
        }
        consistencyReport = await validator.validateChapterConsistency(
          { content, chapterNumber: project.chapters.findIndex((c: any) => c.id === chapterId) + 1 },
          bookContext
        )
        break
        
      case 'book':
        consistencyReport = await validator.validateBookConsistency(bookContext)
        break
        
      case 'timeline':
        consistencyReport = await validator.validateTimelineConsistency(bookContext)
        break
        
      case 'characters':
        consistencyReport = await validator.validateCharacterConsistency(bookContext)
        break
        
      default:
        return NextResponse.json({ error: 'Invalid check type' }, { status: 400 })
    }

    if (!consistencyReport.success) {
      throw new Error(consistencyReport.error || 'Consistency check failed')
    }

    // Save consistency report
    const { error: insertError } = await supabase
      .from('consistency_reports')
      .insert({
        user_id: authResult.user.id,
        project_id: projectId,
        chapter_id: chapterId,
        check_type: checkType,
        overall_score: consistencyReport.data?.overallScore || 0,
        issues: consistencyReport.data?.issues || [],
        character_consistency: consistencyReport.data?.characterConsistency || 0,
        timeline_consistency: consistencyReport.data?.timelineConsistency || 0,
        plot_consistency: consistencyReport.data?.plotConsistency || 0,
        world_consistency: consistencyReport.data?.worldConsistency || 0,
        style_consistency: consistencyReport.data?.styleConsistency || 0,
      })

    if (insertError) {
      console.error('Error saving consistency report:', insertError)
    }

    return NextResponse.json({
      success: true,
      report: consistencyReport.data,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    return handleRouteError(error, 'Consistency Check')
  }
}