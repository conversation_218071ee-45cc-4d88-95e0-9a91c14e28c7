'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  ArrowLeft, 
  BookOpen, 
  Plus, 
  Settings, 
  Users, 
  Globe,
  TrendingUp,
  FileText,
  Edit,
  Trash2,
  Link as LinkIcon,
  UnlinkIcon
} from 'lucide-react';
import { createClient } from '@/lib/supabase/client';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';
import { CharacterContinuityTracker } from '@/components/series/character-continuity-tracker';
import { SeriesConsistencyDashboard } from '@/components/series/series-consistency-dashboard';

interface Series {
  id: string;
  title: string;
  description: string;
  created_at: string;
  updated_at: string;
}

interface Project {
  id: string;
  title: string;
  description: string;
  status: string;
  target_word_count: number;
  current_word_count: number;
  created_at: string;
}

export default function SeriesDetailPage() {
  const params = useParams();
  const router = useRouter();
  const seriesId = params.id as string;
  
  const [series, setSeries] = useState<Series | null>(null);
  const [projects, setProjects] = useState<Project[]>([]);
  const [availableProjects, setAvailableProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddProjectModalOpen, setIsAddProjectModalOpen] = useState(false);
  const [editedSeries, setEditedSeries] = useState({ title: '', description: '' });
  
  const { user } = useAuth();
  const { toast } = useToast();
  const supabase = createClient();

  useEffect(() => {
    if (seriesId) {
      fetchSeriesData();
    }
  }, [seriesId]);

  const fetchSeriesData = async () => {
    if (!user) return;

    try {
      // Fetch series details
      const { data: seriesData, error: seriesError } = await supabase
        .from('series')
        .select('*')
        .eq('id', seriesId)
        .eq('user_id', user.id)
        .single();

      if (seriesError) throw seriesError;
      
      setSeries(seriesData);
      setEditedSeries({ title: seriesData.title, description: seriesData.description || '' });

      // Fetch projects in this series
      const { data: projectsData, error: projectsError } = await supabase
        .from('projects')
        .select('*')
        .eq('series_id', seriesId)
        .eq('user_id', user.id)
        .order('created_at', { ascending: true });

      if (projectsError) throw projectsError;
      setProjects(projectsData || []);

      // Fetch available projects (not in any series)
      const { data: availableData, error: availableError } = await supabase
        .from('projects')
        .select('*')
        .is('series_id', null)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (availableError) throw availableError;
      setAvailableProjects(availableData || []);

    } catch (error) {
      console.error('Error fetching series data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load series data. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSeries = async () => {
    if (!series || !editedSeries.title.trim()) return;

    try {
      const { error } = await supabase
        .from('series')
        .update({
          title: editedSeries.title.trim(),
          description: editedSeries.description.trim(),
          updated_at: new Date().toISOString()
        })
        .eq('id', seriesId);

      if (error) throw error;

      setSeries(prev => prev ? {
        ...prev,
        title: editedSeries.title.trim(),
        description: editedSeries.description.trim()
      } : null);
      
      setIsEditModalOpen(false);
      toast({
        title: 'Success',
        description: 'Series updated successfully.',
      });
    } catch (error) {
      console.error('Error updating series:', error);
      toast({
        title: 'Error',
        description: 'Failed to update series. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const handleAddProjectToSeries = async (projectId: string) => {
    try {
      const { error } = await supabase
        .from('projects')
        .update({ series_id: seriesId })
        .eq('id', projectId);

      if (error) throw error;

      // Move project from available to series projects
      const project = availableProjects.find(p => p.id === projectId);
      if (project) {
        setProjects(prev => [...prev, project]);
        setAvailableProjects(prev => prev.filter(p => p.id !== projectId));
      }

      toast({
        title: 'Success',
        description: 'Project added to series successfully.',
      });
    } catch (error) {
      console.error('Error adding project to series:', error);
      toast({
        title: 'Error',
        description: 'Failed to add project to series. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const handleRemoveProjectFromSeries = async (projectId: string) => {
    try {
      const { error } = await supabase
        .from('projects')
        .update({ series_id: null })
        .eq('id', projectId);

      if (error) throw error;

      // Move project from series to available projects
      const project = projects.find(p => p.id === projectId);
      if (project) {
        setAvailableProjects(prev => [...prev, project]);
        setProjects(prev => prev.filter(p => p.id !== projectId));
      }

      toast({
        title: 'Success',
        description: 'Project removed from series successfully.',
      });
    } catch (error) {
      console.error('Error removing project from series:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove project from series. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const handleDeleteSeries = async () => {
    if (!series) return;
    
    if (!confirm(`Are you sure you want to delete the series "${series.title}"? This will not delete the projects, but they will no longer be part of this series.`)) {
      return;
    }

    try {
      // First, remove series_id from all projects
      await supabase
        .from('projects')
        .update({ series_id: null })
        .eq('series_id', seriesId);

      // Then delete the series
      const { error } = await supabase
        .from('series')
        .delete()
        .eq('id', seriesId);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Series deleted successfully.',
      });
      
      router.push('/series');
    } catch (error) {
      console.error('Error deleting series:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete series. Please try again.',
        variant: 'destructive'
      });
    }
  };

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading series...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!series) {
    return (
      <div className="container py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Series Not Found</h1>
          <p className="text-muted-foreground mb-6">The requested series could not be found.</p>
          <Button asChild>
            <Link href="/series">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Series
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/series">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Series
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{series.title}</h1>
            <p className="text-muted-foreground mt-1">
              {series.description || 'No description provided'}
            </p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Series</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 pt-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-title">Series Title</Label>
                  <Input
                    id="edit-title"
                    value={editedSeries.title}
                    onChange={(e) => setEditedSeries(prev => ({ ...prev, title: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea
                    id="edit-description"
                    value={editedSeries.description}
                    onChange={(e) => setEditedSeries(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                  />
                </div>
                <div className="flex justify-end gap-2 pt-4">
                  <Button 
                    variant="outline" 
                    onClick={() => setIsEditModalOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleUpdateSeries}>
                    Save Changes
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Button 
            variant="destructive" 
            size="sm"
            onClick={handleDeleteSeries}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="continuity">Continuity</TabsTrigger>
          <TabsTrigger value="consistency">Consistency</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <BookOpen className="h-8 w-8 text-primary" />
                  <div>
                    <p className="text-2xl font-bold">{projects.length}</p>
                    <p className="text-sm text-muted-foreground">Books</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <FileText className="h-8 w-8 text-primary" />
                  <div>
                    <p className="text-2xl font-bold">
                      {projects.reduce((total, p) => total + (p.target_word_count || 0), 0).toLocaleString()}
                    </p>
                    <p className="text-sm text-muted-foreground">Target Words</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <TrendingUp className="h-8 w-8 text-primary" />
                  <div>
                    <p className="text-2xl font-bold">
                      {projects.reduce((total, p) => total + (p.current_word_count || 0), 0).toLocaleString()}
                    </p>
                    <p className="text-sm text-muted-foreground">Current Words</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Globe className="h-8 w-8 text-primary" />
                  <div>
                    <p className="text-2xl font-bold">
                      {Math.round(
                        projects.reduce((total, p) => total + (p.current_word_count || 0), 0) /
                        Math.max(projects.reduce((total, p) => total + (p.target_word_count || 0), 0), 1) * 100
                      )}%
                    </p>
                    <p className="text-sm text-muted-foreground">Complete</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Projects in Series */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Books in Series</CardTitle>
                <CardDescription>
                  Manage the books that belong to this series
                </CardDescription>
              </div>
              <Dialog open={isAddProjectModalOpen} onOpenChange={setIsAddProjectModalOpen}>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Book
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Book to Series</DialogTitle>
                  </DialogHeader>
                  <div className="pt-4">
                    {availableProjects.length === 0 ? (
                      <div className="text-center py-6">
                        <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground">
                          No available projects to add. All your projects are already in series or you need to create new projects.
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-2 max-h-60 overflow-y-auto">
                        {availableProjects.map((project) => (
                          <div key={project.id} className="flex items-center justify-between p-3 border rounded-lg">
                            <div>
                              <h4 className="font-medium">{project.title}</h4>
                              <p className="text-sm text-muted-foreground line-clamp-1">
                                {project.description}
                              </p>
                            </div>
                            <Button 
                              size="sm"
                              onClick={() => {
                                handleAddProjectToSeries(project.id);
                                setIsAddProjectModalOpen(false);
                              }}
                            >
                              <LinkIcon className="h-4 w-4 mr-1" />
                              Add
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              {projects.length === 0 ? (
                <div className="text-center py-8">
                  <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No books in this series</h3>
                  <p className="text-muted-foreground mb-4">
                    Add existing projects to this series or create new ones.
                  </p>
                  <Button onClick={() => setIsAddProjectModalOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add First Book
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {projects.map((project, index) => (
                    <div key={project.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full text-sm font-medium">
                          {index + 1}
                        </div>
                        <div>
                          <h4 className="font-medium">{project.title}</h4>
                          <p className="text-sm text-muted-foreground line-clamp-1">
                            {project.description}
                          </p>
                          <div className="flex items-center gap-4 mt-1">
                            <span className="text-xs text-muted-foreground">
                              {(project.current_word_count || 0).toLocaleString()} / {(project.target_word_count || 0).toLocaleString()} words
                            </span>
                            <Badge variant="outline" className="text-xs">
                              {project.status}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/projects/${project.id}`}>
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Link>
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleRemoveProjectFromSeries(project.id)}
                        >
                          <UnlinkIcon className="h-4 w-4 mr-1" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="continuity">
          <CharacterContinuityTracker seriesId={seriesId} />
        </TabsContent>

        <TabsContent value="consistency">
          <SeriesConsistencyDashboard seriesId={seriesId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}