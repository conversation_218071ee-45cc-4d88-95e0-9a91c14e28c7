// This file configures the initialization of Sentry on the client.
// The added config here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

// Temporarily disable Sen<PERSON> to debug 500 errors
// import * as Sen<PERSON> from "@sentry/nextjs";

// Sentry.init({
//   dsn: "https://<EMAIL>/4509668380049408",

//   // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.
//   tracesSampleRate: 1,

//   // Setting this option to true will print useful information to the console while you're setting up Sentry.
//   debug: false,
// });

// export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;