# API Middleware and Repository Pattern Documentation

This directory contains centralized API utilities to reduce code duplication across the BookScribe codebase.

## API Middleware (`middleware.ts`)

The middleware system provides consistent handling for:
- Authentication
- Rate limiting
- Request/response validation
- Error handling

### Basic Usage

```typescript
import { withMiddleware, apiResponse, apiError } from '@/lib/api/middleware'
import { ValidationSchemas } from '@/lib/api/validation-schemas'

// Standard authenticated route
export const POST = withMiddleware(
  async (request, { user, supabase, body }) => {
    // Your route logic here
    const data = await processRequest(body)
    return apiResponse({ success: true, data })
  },
  {
    validateBody: ValidationSchemas.yourSchema
  }
)

// AI route with rate limiting
export const POST = withAIRoute(
  async (request, context) => {
    // AI processing logic
  },
  'generation', // rate limit key
  {
    validateBody: ValidationSchemas.generateContent
  }
)

// Public route (no auth)
export const GET = withPublicRoute(
  async (request, context) => {
    // Public endpoint logic
  }
)

// Route with project access validation
export const PUT = withProjectAccess(
  async (request, { user, supabase, project, params }) => {
    // project is automatically loaded and validated
    return apiResponse({ project })
  }
)
```

## Validation Schemas (`validation-schemas.ts`)

Pre-built validation schemas for common request patterns:

```typescript
import { ValidationSchemas } from '@/lib/api/validation-schemas'

// Use individual schemas
const projectSchema = ValidationSchemas.createProject
const chapterSchema = ValidationSchemas.updateChapter

// Create paginated schemas
const paginatedUsers = ValidationSchemas.createPaginated(userFilterSchema)

// Create bulk operation schemas
const bulkChapters = ValidationSchemas.createBulk(ValidationSchemas.createChapter)
```

## Repository Pattern (`/repositories`)

Data access layer providing consistent database operations:

### Using Repositories

```typescript
import { createRepositories } from '@/lib/db/repositories'
import { createClient } from '@/lib/supabase/server'

// In API route
const supabase = await createClient()
const { projects, chapters, characters } = createRepositories(supabase)

// Find by ID
const { data: project, error } = await projects.findById(projectId)

// Find with pagination
const { data: chapters, totalPages } = await chapters.findMany(
  { project_id: projectId },
  { page: 1, limit: 20, orderBy: 'chapter_number' }
)

// Create with validation
const { data: newChapter } = await chapters.create({
  project_id: projectId,
  title: 'Chapter 1',
  content: '...'
})

// Complex queries
const stats = await projects.getStatistics(projectId)
const hasAccess = await projects.hasAccess(projectId, userId)
```

### Creating Custom Repositories

```typescript
import { BaseRepository } from '@/lib/db/repositories'
import { SupabaseClient } from '@supabase/supabase-js'

export class CustomRepository extends BaseRepository<CustomType> {
  constructor(supabase: SupabaseClient) {
    super(supabase, 'table_name')
  }

  // Add custom methods
  async customQuery(param: string) {
    return this.withErrorHandling(async () => {
      const { data, error } = await this.supabase
        .from(this.tableName)
        .select('*')
        .eq('param', param)
      
      if (error) throw error
      return data
    })
  }
}
```

## Migration Guide

### Before (Duplicate Code)
```typescript
// In multiple API routes
try {
  const clientIP = getClientIP(request)
  const { limiter, requests } = AI_RATE_LIMITS.chat
  const rateLimitResult = limiter.check(requests, clientIP)
  
  if (!rateLimitResult.success) {
    return createAIRateLimitResponse(rateLimitResult.reset)
  }
  
  const authResult = await authenticateUser()
  if (!authResult.success) {
    return authResult.response!
  }
  
  const body = await request.json()
  const validation = schema.safeParse(body)
  // ... more boilerplate
  
} catch (error) {
  return handleRouteError(error, 'Route Name')
}
```

### After (Using Middleware)
```typescript
export const POST = withAIRoute(
  async (request, { user, supabase, body }) => {
    // Direct to business logic
    return apiResponse({ data: result })
  },
  'chat',
  { validateBody: schema }
)
```

### Database Queries Before
```typescript
// Scattered across routes
const { data: project } = await supabase
  .from('projects')
  .select('*')
  .eq('id', projectId)
  .eq('user_id', userId)
  .single()

if (!project) {
  return NextResponse.json({ error: 'Not found' }, { status: 404 })
}
```

### Database Queries After
```typescript
const { projects } = createRepositories(supabase)
const { data: project, error } = await projects.findById(projectId)

if (!project || !await projects.hasAccess(projectId, userId)) {
  return apiError('Project not found or access denied', 404)
}
```

## Benefits

1. **Reduced Duplication**: Common patterns extracted to reusable functions
2. **Consistent Error Handling**: All errors handled the same way
3. **Type Safety**: Full TypeScript support with inferred types
4. **Better Testing**: Easier to mock middleware and repositories
5. **Maintainability**: Changes to common logic in one place
6. **Performance**: Built-in caching and optimization in repositories