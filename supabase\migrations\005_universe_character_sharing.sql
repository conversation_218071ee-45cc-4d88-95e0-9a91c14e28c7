-- Create shared universes table
CREATE TABLE universes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    rules JSONB DEFAULT '{}',
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add universe reference to series table
ALTER TABLE series 
ADD COLUMN universe_id UUID REFERENCES universes(id) ON DELETE SET NULL;

-- Create character sharing table
CREATE TABLE character_shares (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID REFERENCES characters(id) ON DELETE CASCADE,
    source_series_id UUID REFERENCES series(id) ON DELETE CASCADE,
    target_series_id UUID REFERENCES series(id) ON DELETE CASCADE,
    share_type TEXT CHECK (share_type IN ('reference', 'guest', 'permanent')) DEFAULT 'reference',
    version_notes TEXT,
    timeline_offset INTEGER DEFAULT 0, -- days offset between series timelines
    character_state JSONB DEFAULT '{}', -- character state in target series
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(character_id, source_series_id, target_series_id)
);

-- Create character variants table for different versions in different series
CREATE TABLE character_variants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    base_character_id UUID REFERENCES characters(id) ON DELETE CASCADE,
    series_id UUID REFERENCES series(id) ON DELETE CASCADE,
    variant_name TEXT NOT NULL,
    variant_data JSONB NOT NULL DEFAULT '{}',
    is_canon BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create cross-series references table
CREATE TABLE cross_series_references (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_book_id UUID REFERENCES books(id) ON DELETE CASCADE,
    source_chapter_id UUID REFERENCES chapters(id),
    target_series_id UUID REFERENCES series(id) ON DELETE CASCADE,
    reference_type TEXT CHECK (reference_type IN ('character', 'event', 'location', 'object', 'concept')),
    reference_data JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create locations table (enhanced for sharing)
CREATE TABLE locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    series_id UUID REFERENCES series(id) ON DELETE CASCADE,
    universe_id UUID REFERENCES universes(id) ON DELETE SET NULL,
    name TEXT NOT NULL,
    description TEXT,
    parent_location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
    location_type TEXT CHECK (location_type IN ('world', 'continent', 'country', 'region', 'city', 'building', 'room', 'other')),
    features JSONB DEFAULT '[]',
    significance TEXT,
    is_shareable BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create location shares table
CREATE TABLE location_shares (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
    source_series_id UUID REFERENCES series(id) ON DELETE CASCADE,
    target_series_id UUID REFERENCES series(id) ON DELETE CASCADE,
    share_type TEXT CHECK (share_type IN ('reference', 'duplicate', 'linked')) DEFAULT 'reference',
    modifications JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enhanced plot threads table
CREATE TABLE plot_threads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    series_id UUID REFERENCES series(id),
    name TEXT NOT NULL,
    description TEXT,
    status TEXT CHECK (status IN ('setup', 'active', 'resolved', 'abandoned', 'paused')) DEFAULT 'setup',
    thread_type TEXT CHECK (thread_type IN ('main', 'subplot', 'character_arc', 'mystery', 'romance', 'conflict')),
    importance TEXT CHECK (importance IN ('critical', 'major', 'minor', 'background')) DEFAULT 'major',
    started_chapter_id UUID REFERENCES chapters(id),
    resolved_chapter_id UUID REFERENCES chapters(id),
    related_characters UUID[] DEFAULT '{}',
    related_locations UUID[] DEFAULT '{}',
    notes JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create universe timeline events (shared across series)
CREATE TABLE universe_timeline_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    universe_id UUID REFERENCES universes(id) ON DELETE CASCADE,
    event_date TIMESTAMPTZ,
    relative_date TEXT, -- for fantasy/sci-fi dates
    event_name TEXT NOT NULL,
    description TEXT,
    affected_series UUID[] DEFAULT '{}',
    event_type TEXT CHECK (event_type IN ('historical', 'cataclysm', 'political', 'discovery', 'character', 'other')),
    importance TEXT CHECK (importance IN ('universe-changing', 'major', 'minor', 'personal')) DEFAULT 'major',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_character_shares_source ON character_shares(source_series_id);
CREATE INDEX idx_character_shares_target ON character_shares(target_series_id);
CREATE INDEX idx_character_shares_character ON character_shares(character_id);
CREATE INDEX idx_locations_universe ON locations(universe_id);
CREATE INDEX idx_locations_series ON locations(series_id);
CREATE INDEX idx_plot_threads_project ON plot_threads(project_id);
CREATE INDEX idx_plot_threads_series ON plot_threads(series_id);
CREATE INDEX idx_universe_timeline_universe ON universe_timeline_events(universe_id);

-- RLS Policies

-- Universes
ALTER TABLE universes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view universes they created or are part of" ON universes
    FOR SELECT USING (
        auth.uid() = created_by OR
        EXISTS (
            SELECT 1 FROM series s
            WHERE s.universe_id = universes.id
            AND s.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create universes" ON universes
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own universes" ON universes
    FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete their own universes" ON universes
    FOR DELETE USING (auth.uid() = created_by);

-- Character shares
ALTER TABLE character_shares ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view character shares for their series" ON character_shares
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM series s
            WHERE (s.id = source_series_id OR s.id = target_series_id)
            AND s.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create character shares from their series" ON character_shares
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM series s
            WHERE s.id = source_series_id
            AND s.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update character shares they created" ON character_shares
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM series s
            WHERE s.id = source_series_id
            AND s.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete character shares they created" ON character_shares
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM series s
            WHERE s.id = source_series_id
            AND s.user_id = auth.uid()
        )
    );

-- Character variants
ALTER TABLE character_variants ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view character variants for their series" ON character_variants
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM series s
            WHERE s.id = series_id
            AND s.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create character variants for their series" ON character_variants
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM series s
            WHERE s.id = series_id
            AND s.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update character variants for their series" ON character_variants
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM series s
            WHERE s.id = series_id
            AND s.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete character variants for their series" ON character_variants
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM series s
            WHERE s.id = series_id
            AND s.user_id = auth.uid()
        )
    );

-- Locations
ALTER TABLE locations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view locations for their projects" ON locations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects p
            WHERE p.id = project_id
            AND p.user_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM series s
            WHERE s.id = series_id
            AND s.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create locations for their projects" ON locations
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM projects p
            WHERE p.id = project_id
            AND p.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update locations for their projects" ON locations
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM projects p
            WHERE p.id = project_id
            AND p.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete locations for their projects" ON locations
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM projects p
            WHERE p.id = project_id
            AND p.user_id = auth.uid()
        )
    );

-- Plot threads
ALTER TABLE plot_threads ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view plot threads for their projects" ON plot_threads
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects p
            WHERE p.id = project_id
            AND p.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create plot threads for their projects" ON plot_threads
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM projects p
            WHERE p.id = project_id
            AND p.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update plot threads for their projects" ON plot_threads
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM projects p
            WHERE p.id = project_id
            AND p.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete plot threads for their projects" ON plot_threads
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM projects p
            WHERE p.id = project_id
            AND p.user_id = auth.uid()
        )
    );

-- Universe timeline events
ALTER TABLE universe_timeline_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view timeline events for universes they're part of" ON universe_timeline_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM universes u
            WHERE u.id = universe_id
            AND (
                u.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM series s
                    WHERE s.universe_id = u.id
                    AND s.user_id = auth.uid()
                )
            )
        )
    );

CREATE POLICY "Users can create timeline events for their universes" ON universe_timeline_events
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM universes u
            WHERE u.id = universe_id
            AND u.created_by = auth.uid()
        )
    );

CREATE POLICY "Users can update timeline events for their universes" ON universe_timeline_events
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM universes u
            WHERE u.id = universe_id
            AND u.created_by = auth.uid()
        )
    );

CREATE POLICY "Users can delete timeline events for their universes" ON universe_timeline_events
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM universes u
            WHERE u.id = universe_id
            AND u.created_by = auth.uid()
        )
    );

-- Helper functions for character sharing
CREATE OR REPLACE FUNCTION share_character_to_series(
    p_character_id UUID,
    p_source_series_id UUID,
    p_target_series_id UUID,
    p_share_type TEXT DEFAULT 'reference',
    p_version_notes TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_share_id UUID;
BEGIN
    INSERT INTO character_shares (
        character_id,
        source_series_id,
        target_series_id,
        share_type,
        version_notes
    ) VALUES (
        p_character_id,
        p_source_series_id,
        p_target_series_id,
        p_share_type,
        p_version_notes
    )
    ON CONFLICT (character_id, source_series_id, target_series_id)
    DO UPDATE SET
        share_type = EXCLUDED.share_type,
        version_notes = EXCLUDED.version_notes,
        updated_at = NOW()
    RETURNING id INTO v_share_id;
    
    RETURN v_share_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get all characters available in a series (including shared)
CREATE OR REPLACE FUNCTION get_series_characters(p_series_id UUID)
RETURNS TABLE (
    character_id UUID,
    character_name TEXT,
    character_role TEXT,
    source_type TEXT, -- 'native', 'shared', 'variant'
    source_series_id UUID,
    source_series_name TEXT,
    share_type TEXT
) AS $$
BEGIN
    RETURN QUERY
    -- Native characters
    SELECT 
        c.id,
        c.name,
        c.role,
        'native'::TEXT,
        s.id,
        s.title,
        NULL::TEXT
    FROM characters c
    JOIN books b ON b.id = c.book_id
    JOIN series_books sb ON sb.book_id = b.id
    JOIN series s ON s.id = sb.series_id
    WHERE sb.series_id = p_series_id
    
    UNION ALL
    
    -- Shared characters
    SELECT 
        c.id,
        c.name,
        c.role,
        'shared'::TEXT,
        cs.source_series_id,
        s.title,
        cs.share_type
    FROM character_shares cs
    JOIN characters c ON c.id = cs.character_id
    JOIN series s ON s.id = cs.source_series_id
    WHERE cs.target_series_id = p_series_id;
END;
$$ LANGUAGE plpgsql;