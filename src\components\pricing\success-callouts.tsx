'use client'

import { Quote } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

const successStories = [
  {
    quote: "The Author tier's AI understood my 200k word fantasy series better than I did - it caught plot inconsistencies I'd missed across 3 books!",
    author: "<PERSON> Chen",
    role: "Fantasy Author",
    tier: "Author",
    books: "The Shadowlands Trilogy"
  },
  {
    quote: "Professional tier helped me maintain perfect consistency across my 7-book mystery series. The AI remembered details from book 1 when I was writing book 7.",
    author: "<PERSON>",
    role: "Mystery Writer",
    tier: "Professional",
    books: "Detective Morgan Series"
  },
  {
    quote: "Studio tier manages our 12-author shared universe effortlessly. The AI tracks hundreds of characters across 30+ books without breaking a sweat.",
    author: "<PERSON> Liu",
    role: "Publishing House Editor",
    tier: "Studio",
    books: "Nexus Universe"
  }
]

export function SuccessCallouts() {
  return (
    <section className="py-16 px-4">
      <div className="container max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold font-literary-display mb-4">
            AI Intelligence in Action
          </h2>
          <p className="text-lg text-muted-foreground">
            See how our adaptive AI helps authors at every level achieve their writing goals
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {successStories.map((story, index) => (
            <Card key={index} className="relative hover:shadow-lg transition-all">
              <div className="absolute -top-3 -left-3 w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <Quote className="w-5 h-5 text-primary" />
              </div>
              <Badge 
                variant="outline" 
                className="absolute top-4 right-4 text-xs"
              >
                {story.tier} Tier
              </Badge>
              <CardContent className="pt-8">
                <blockquote className="text-sm italic mb-4 leading-relaxed">
                  "{story.quote}"
                </blockquote>
                <div className="space-y-1">
                  <p className="font-semibold text-sm">{story.author}</p>
                  <p className="text-xs text-muted-foreground">{story.role}</p>
                  <p className="text-xs text-primary">{story.books}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-8 text-center">
          <p className="text-sm text-muted-foreground">
            *These are illustrative examples of how our AI intelligence scales with project complexity
          </p>
        </div>
      </div>
    </section>
  )
}