import { NextRequest, NextResponse } from 'next/server';
import { withMiddleware } from '@/lib/api/middleware';
import { z } from 'zod';
import { logger } from '@/lib/services/logger';
import { VoiceAnalyzer } from '@/lib/analysis/voice-analyzer';

// Request validation schema
const voiceAnalysisSchema = z.object({
  content: z.string().min(100, 'Content must be at least 100 characters'),
  projectId: z.string().uuid('Invalid project ID'),
  existingProfile: z.any().optional()
});

export const POST = withMiddleware(
  async (request: NextRequest) => {
    try {
      const body = await request.json();
      const validatedData = voiceAnalysisSchema.parse(body);
      
      logger.info('Voice analysis requested', {
        projectId: validatedData.projectId,
        contentLength: validatedData.content.length,
        hasExistingProfile: !!validatedData.existingProfile
      });
      
      const voiceAnalyzer = new VoiceAnalyzer();
      
      let profile = validatedData.existingProfile;
      let matches = [];
      
      if (profile) {
        // Analyze current content against existing voice profile
        matches = await voiceAnalyzer.analyzeVoiceMatch(
          validatedData.content, 
          profile
        );
      } else {
        // Create initial voice profile from content
        profile = await voiceAnalyzer.analyzeUserVoice(
          [validatedData.content], 
          validatedData.projectId
        );
      }
      
      return NextResponse.json({
        success: true,
        profile,
        matches,
        metadata: {
          contentLength: validatedData.content.length,
          wordCount: validatedData.content.split(/\s+/).filter(word => word.length > 0).length,
          timestamp: new Date().toISOString()
        }
      });
      
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid request data', details: error.errors },
          { status: 400 }
        );
      }
      
      logger.error('Voice analysis error:', error);
      return NextResponse.json(
        { error: 'Failed to analyze voice' },
        { status: 500 }
      );
    }
  },
  {
    auth: true,
    rateLimit: {
      type: 'ai-analysis',
      requests: 10
    }
  }
);
