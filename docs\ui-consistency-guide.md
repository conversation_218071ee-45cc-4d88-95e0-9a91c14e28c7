# UI Consistency Guide

## Spacing System

Use these standardized spacing values throughout the application:

### Padding & Margin Scale
- `p-2` / `m-2` - 8px (0.5rem) - Tight spacing for compact elements
- `p-3` / `m-3` - 12px (0.75rem) - Small spacing for badges, chips
- `p-4` / `m-4` - 16px (1rem) - Default spacing for most components
- `p-6` / `m-6` - 24px (1.5rem) - Large spacing for cards, sections
- `p-8` / `m-8` - 32px (2rem) - Extra large spacing for major sections

### Gap Values
- `gap-1` - 4px (0.25rem) - Minimal gap for icon groups
- `gap-2` - 8px (0.5rem) - Small gap for tight layouts
- `gap-3` - 12px (0.75rem) - Compact gap
- `gap-4` - 16px (1rem) - Default gap for most layouts
- `gap-6` - 24px (1.5rem) - Large gap for sections

### Component-Specific Standards

#### Buttons
- Default: `h-10 px-4 py-2`
- Small: `h-9 px-3`
- Large: `h-12 px-6`
- Icon: `h-10 w-10`

#### Cards
- Card: `rounded-lg border shadow-sm`
- CardHeader: `p-6 space-y-1.5`
- CardContent: `p-6 pt-0`
- CardFooter: `p-6 pt-0`

#### Inputs
- All inputs: `h-10 px-4 py-2 border rounded-lg`
- Consistent with button heights for alignment

#### Dialogs/Modals
- Content padding: `p-6`
- Content gap: `gap-6`
- Close button position: `right-4 top-4`

## Typography System

### Font Sizes (Responsive)
All font sizes scale with user settings:
- `.text-xs` - ~12px at default
- `.text-sm` - 14px (base UI size)
- `.text-base` - ~16px at default
- `.text-lg` - ~18px at default
- `.text-xl` - ~20px at default
- `.text-2xl` - ~24px at default
- `.text-3xl` - ~30px at default
- `.text-4xl` - ~36px at default

### Font Families
Defined by user settings:
- UI Text: `var(--settings-ui-font)`
- Editor/Code: `var(--settings-editor-font)`
- Reading/Headers: `var(--settings-reading-font)`

### Font Weights
- Normal text: `font-normal` (400)
- Medium emphasis: `font-medium` (500)
- Strong emphasis: `font-semibold` (600)
- Bold: `font-bold` (700)

## Color System

### Theme Variables
Always use CSS variables for colors:
- Background: `bg-background`
- Foreground: `text-foreground`
- Primary: `bg-primary` / `text-primary`
- Secondary: `bg-secondary` / `text-secondary`
- Muted: `bg-muted` / `text-muted-foreground`
- Card: `bg-card` / `text-card-foreground`
- Border: `border-border`

### Status Colors
Use semantic status tokens:
- Completed: `bg-status-completed` / `text-status-completed`
- In Progress: `bg-status-in-progress` / `text-status-in-progress`
- Draft: `bg-status-draft` / `text-status-draft`
- Published: `bg-status-published` / `text-status-published`
- Default: `bg-status-default` / `text-status-default`

## Border System

### Border Widths
- Default: `border` (1px)
- Focus rings: `ring-2` (2px)
- Never use `border-2` unless specifically needed

### Border Radius
- Small: `rounded-sm` (0.375rem)
- Medium: `rounded-md` (0.5rem)
- Large: `rounded-lg` (0.75rem)
- Extra large: `rounded-xl` (1rem)

## Transitions

### Duration
- Fast: `duration-200` (200ms)
- Normal: `duration-300` (300ms)
- Slow: `duration-500` (500ms)

### Easing
- Default: `transition-all`
- Smooth: `ease-out`

## Monaco Editor

The Monaco editor now automatically adapts to the current theme using CSS variables. No hardcoded colors should be used in Monaco configuration.

## Best Practices

1. **Never use inline styles** - Use Tailwind classes
2. **Avoid hardcoded colors** - Use theme variables
3. **Use consistent spacing** - Follow the spacing scale
4. **Respect user settings** - Font sizes and families should adapt
5. **Maintain semantic meaning** - Use status colors appropriately
6. **Test all themes** - Ensure consistency across all theme variants

## Utility Classes

For consistent spacing patterns:
- `.spacing-xs` - `p-2 gap-2`
- `.spacing-sm` - `p-3 gap-3`
- `.spacing-md` - `p-4 gap-4`
- `.spacing-lg` - `p-6 gap-6`
- `.spacing-xl` - `p-8 gap-8`