'use client'

import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator, DropdownMenuLabel } from '@/components/ui/dropdown-menu'
import { <PERSON>, <PERSON>, Monitor, Feather as _Feather, Palette } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useTheme } from '@/hooks/use-theme'
import { useThemeSettings } from '@/lib/settings/settings-store'
import Link from 'next/link'

export function ThemeToggle() {
  const { theme, setTheme, currentTheme, switchTheme, getThemesByMode, mounted: themesMounted } = useTheme()
  const { theme: themeSettings } = useThemeSettings()
  const [mounted, setMounted] = useState(false)

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  // Debug logging
  useEffect(() => {
    if (mounted && themesMounted) {
      console.log('ThemeToggle Debug:', {
        theme,
        currentTheme,
        themeSettings,
        mounted,
        themesMounted
      })
    }
  }, [theme, currentTheme, themeSettings, mounted, themesMounted])

  if (!mounted || !themesMounted) {
    return (
      <Button variant="ghost" size="icon" className="h-9 w-9" disabled>
        <Palette className="h-4 w-4" />
      </Button>
    )
  }

  const lightThemes = getThemesByMode('light')
  const darkThemes = getThemesByMode('dark')

  // Function to handle "Default" button - reverts to user's saved default theme
  const handleSystemTheme = () => {
    // Get the user's preferred theme mode from settings
    const preferredMode = themeSettings?.themeMode || 'system'

    if (preferredMode === 'light') {
      // User prefers light mode - use first available light theme as default
      const defaultLightTheme = lightThemes[0]
      if (defaultLightTheme) {
        switchTheme(defaultLightTheme.id)
      }
    } else if (preferredMode === 'dark') {
      // User prefers dark mode - use first available dark theme as default
      const defaultDarkTheme = darkThemes[0]
      if (defaultDarkTheme) {
        switchTheme(defaultDarkTheme.id)
      }
    } else {
      // User has system preference - detect system theme and use appropriate default
      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      const defaultTheme = systemPrefersDark ? darkThemes[0] : lightThemes[0]
      if (defaultTheme) {
        switchTheme(defaultTheme.id)
      }
    }

    // Also update next-themes to 'system' for consistency
    setTheme('system')
  }

  // Function to check if current theme is the user's default theme
  const isDefaultThemeActive = () => {
    if (!mounted || !themesMounted) return false

    const preferredMode = themeSettings?.themeMode || 'system'

    if (preferredMode === 'light') {
      // Check if current theme matches default light theme
      const defaultLightTheme = lightThemes[0]
      return defaultLightTheme && currentTheme === defaultLightTheme.id
    } else if (preferredMode === 'dark') {
      // Check if current theme matches default dark theme
      const defaultDarkTheme = darkThemes[0]
      return defaultDarkTheme && currentTheme === defaultDarkTheme.id
    } else {
      // For system preference, check if current theme matches system-appropriate default
      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      const defaultTheme = systemPrefersDark ? darkThemes[0] : lightThemes[0]
      return defaultTheme && currentTheme === defaultTheme.id
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-9 w-9"
          aria-label="Toggle theme"
        >
          <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel className="p-0">
          <Link 
            href="/customization" 
            className="flex items-center px-2 py-1.5 text-sm font-semibold hover:text-primary transition-colors w-full"
          >
            <Palette className="mr-2 h-4 w-4" />
            Customization
          </Link>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuLabel className="text-xs text-muted-foreground">
          Light Themes
        </DropdownMenuLabel>
        {lightThemes.map((themeData) => (
          <DropdownMenuItem
            key={themeData.id}
            onClick={() => switchTheme(themeData.id)}
            className="flex items-center justify-between"
          >
            <div className="flex items-center">
              <Sun className="mr-2 h-4 w-4" />
              <span>{themeData.name}</span>
            </div>
            {currentTheme === themeData.id && (
              <div className="w-2 h-2 rounded-full bg-primary" />
            )}
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />

        <DropdownMenuLabel className="text-xs text-muted-foreground">
          Dark Themes
        </DropdownMenuLabel>
        {darkThemes.map((themeData) => (
          <DropdownMenuItem
            key={themeData.id}
            onClick={() => switchTheme(themeData.id)}
            className="flex items-center justify-between"
          >
            <div className="flex items-center">
              <Moon className="mr-2 h-4 w-4" />
              <span>{themeData.name}</span>
            </div>
            {currentTheme === themeData.id && (
              <div className="w-2 h-2 rounded-full bg-primary" />
            )}
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={handleSystemTheme}>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center">
              <Monitor className="mr-2 h-4 w-4" />
              <span>Default</span>
            </div>
            {isDefaultThemeActive() && (
              <div className="w-2 h-2 rounded-full bg-primary" />
            )}
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Compact theme toggle for mobile
export function CompactThemeToggle() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark')
    } else if (theme === 'dark') {
      setTheme('system')
    } else {
      setTheme('light')
    }
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="h-8 w-8"
      aria-label={`Current theme: ${theme}. Click to change.`}
    >
      {theme === 'light' && <Sun className="h-4 w-4" />}
      {theme === 'dark' && <Moon className="h-4 w-4" />}
      {theme === 'system' && <Monitor className="h-4 w-4" />}
    </Button>
  )
}