'use client'

import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator, DropdownMenuLabel } from '@/components/ui/dropdown-menu'
import { Moon, Sun, Palette } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useTheme } from '@/hooks/use-theme'
import Link from 'next/link'

export function ThemeToggle() {
  const { theme, setTheme, themes, mounted } = useTheme()
  const [isClient, setIsClient] = useState(false)

  // Prevent hydration mismatch
  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient || !mounted) {
    return (
      <Button variant="ghost" size="icon" className="h-9 w-9" disabled>
        <Palette className="h-4 w-4" />
      </Button>
    )
  }

  const lightThemes = themes.filter(t => t.mode === 'light')
  const darkThemes = themes.filter(t => t.mode === 'dark')
  const currentTheme = themes.find(t => t.id === theme)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-9 w-9"
          aria-label="Toggle theme"
        >
          {currentTheme?.mode === 'dark' ? (
            <Moon className="h-4 w-4" />
          ) : (
            <Sun className="h-4 w-4" />
          )}
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel className="p-0">
          <Link 
            href="/customization" 
            className="flex items-center px-2 py-1.5 text-sm font-semibold hover:text-primary transition-colors w-full"
          >
            <Palette className="mr-2 h-4 w-4" />
            Customization
          </Link>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuLabel className="text-xs text-muted-foreground">
          Light Themes
        </DropdownMenuLabel>
        {lightThemes.map((themeData) => (
          <DropdownMenuItem
            key={themeData.id}
            onClick={() => setTheme(themeData.id)}
            className="flex items-center justify-between"
          >
            <div className="flex items-center">
              <Sun className="mr-2 h-4 w-4" />
              <span>{themeData.name}</span>
            </div>
            {theme === themeData.id && (
              <div className="w-2 h-2 rounded-full bg-primary" />
            )}
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />

        <DropdownMenuLabel className="text-xs text-muted-foreground">
          Dark Themes
        </DropdownMenuLabel>
        {darkThemes.map((themeData) => (
          <DropdownMenuItem
            key={themeData.id}
            onClick={() => setTheme(themeData.id)}
            className="flex items-center justify-between"
          >
            <div className="flex items-center">
              <Moon className="mr-2 h-4 w-4" />
              <span>{themeData.name}</span>
            </div>
            {theme === themeData.id && (
              <div className="w-2 h-2 rounded-full bg-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Compact theme toggle for mobile
export function CompactThemeToggle() {
  const { theme, setTheme, themes, mounted } = useTheme()
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient || !mounted) {
    return null
  }

  const currentTheme = themes.find(t => t.id === theme)
  const currentIndex = themes.findIndex(t => t.id === theme)
  
  const toggleTheme = () => {
    const nextIndex = (currentIndex + 1) % themes.length
    const nextTheme = themes[nextIndex]
    if (nextTheme) {
      setTheme(nextTheme.id)
    }
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="h-8 w-8"
      aria-label={`Current theme: ${currentTheme?.name}. Click to change.`}
    >
      {currentTheme?.mode === 'dark' ? (
        <Moon className="h-4 w-4" />
      ) : (
        <Sun className="h-4 w-4" />
      )}
    </Button>
  )
}