import { describe, it, expect, jest, beforeEach } from '@jest/globals'
import { authenticateUser, createErrorResponse, createSuccessResponse, validateEnvironment, handleRouteError } from '../server'
import { validateUserOwnership, validateProjectOwnership } from '../validation'
import { AUTH_ERRORS } from '../types'

// Type-safe Supabase mock interfaces
interface MockSupabaseAuth {
  getUser: jest.Mock
}

interface MockSupabaseClient {
  auth: MockSupabaseAuth
  from: jest.Mock
}

// Mock Supabase
jest.mock('@/lib/supabase/server', () => ({
  createClient: jest.fn()
}))

describe('Auth Server Utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('authenticateUser', () => {
    it('should return success with valid user', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' }
      const mockSupabase = {
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: mockUser },
            error: null
          })
        }
      } as Partial<MockSupabaseClient>

      const { createClient } = await import('@/lib/supabase/server')
      ;(createClient as jest.MockedFunction<typeof createClient>).mockResolvedValue(mockSupabase as MockSupabaseClient)

      const result = await authenticateUser()
      
      expect(result.success).toBe(true)
      expect(result.user).toEqual(mockUser)
      expect(result.supabase).toBe(mockSupabase)
    })

    it('should return unauthorized error for invalid user', async () => {
      const mockSupabase = {
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: null },
            error: { message: 'Invalid token' }
          })
        }
      } as Partial<MockSupabaseClient>

      const { createClient } = await import('@/lib/supabase/server')
      ;(createClient as jest.MockedFunction<typeof createClient>).mockResolvedValue(mockSupabase as MockSupabaseClient)

      const result = await authenticateUser()
      
      expect(result.success).toBe(false)
      expect(result.response?.status).toBe(401)
    })
  })

  describe('createErrorResponse', () => {
    it('should create properly formatted error response', () => {
      const response = createErrorResponse(AUTH_ERRORS.UNAUTHORIZED)
      
      expect(response.status).toBe(401)
      // Note: Testing NextResponse body is complex, so we verify the structure
      expect(response).toBeDefined()
    })
  })

  describe('createSuccessResponse', () => {
    it('should create properly formatted success response', () => {
      const data = { test: 'data' }
      const response = createSuccessResponse(data, 'Test message')
      
      expect(response).toBeDefined()
    })
  })

  describe('validateEnvironment', () => {
    it('should validate required environment variables', () => {
      process.env.TEST_VAR = 'exists'
      
      const result = validateEnvironment(['TEST_VAR', 'MISSING_VAR'])
      
      expect(result.valid).toBe(false)
      expect(result.missing).toContain('MISSING_VAR')
      expect(result.missing).not.toContain('TEST_VAR')
    })

    it('should return valid when all variables exist', () => {
      process.env.VAR1 = 'exists'
      process.env.VAR2 = 'exists'
      
      const result = validateEnvironment(['VAR1', 'VAR2'])
      
      expect(result.valid).toBe(true)
      expect(result.missing).toHaveLength(0)
    })
  })

  describe('handleRouteError', () => {
    it('should handle Error objects', () => {
      const error = new Error('Test error')
      const response = handleRouteError(error, 'Test Context')
      
      expect(response.status).toBe(500)
    })

    it('should handle unknown errors', () => {
      const response = handleRouteError('string error', 'Test Context')
      
      expect(response.status).toBe(500)
    })
  })
})

describe('Auth Validation Utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('validateUserOwnership', () => {
    it('should return success for valid ownership', async () => {
      const mockData = { id: 'resource-123', user_id: 'user-123' }
      const mockSingle = jest.fn().mockResolvedValue({
        data: mockData,
        error: null
      })
      const mockEq = jest.fn().mockReturnValue({ single: mockSingle })
      const mockSelect = jest.fn().mockReturnValue({ eq: mockEq })
      const mockFrom = jest.fn().mockReturnValue({ select: mockSelect })

      const mockSupabase = { from: mockFrom }

      const result = await validateUserOwnership(
        mockSupabase as MockSupabaseClient,
        'user-123',
        'projects',
        'resource-123'
      )

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockData)
    })

    it('should return not found for missing resource', async () => {
      const mockSingle = jest.fn().mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' }
      })
      const mockEq = jest.fn().mockReturnValue({ single: mockSingle })
      const mockSelect = jest.fn().mockReturnValue({ eq: mockEq })
      const mockFrom = jest.fn().mockReturnValue({ select: mockSelect })

      const mockSupabase = { from: mockFrom }

      const result = await validateUserOwnership(
        mockSupabase as MockSupabaseClient,
        'user-123',
        'projects',
        'missing-resource'
      )

      expect(result.success).toBe(false)
      expect(result.response?.status).toBe(404)
    })
  })

  describe('validateProjectOwnership', () => {
    it('should call validateUserOwnership with correct parameters', async () => {
      const mockData = { id: 'project-123', user_id: 'user-123' }
      const mockSingle = jest.fn().mockResolvedValue({
        data: mockData,
        error: null
      })
      const mockEq = jest.fn().mockReturnValue({ single: mockSingle })
      const mockSelect = jest.fn().mockReturnValue({ eq: mockEq })
      const mockFrom = jest.fn().mockReturnValue({ select: mockSelect })

      const mockSupabase = { from: mockFrom }

      const result = await validateProjectOwnership(
        mockSupabase as MockSupabaseClient,
        'user-123',
        'project-123'
      )

      expect(result.success).toBe(true)
      expect(mockFrom).toHaveBeenCalledWith('projects')
    })
  })
})