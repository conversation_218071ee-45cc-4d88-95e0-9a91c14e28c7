'use client'

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Command } from 'lucide-react'

interface ShortcutGroup {
  title: string
  shortcuts: {
    keys: string[]
    description: string
  }[]
}

const shortcutGroups: ShortcutGroup[] = [
  {
    title: 'General',
    shortcuts: [
      { keys: ['Ctrl', 'K'], description: 'Open command menu' },
      { keys: ['Ctrl', '/'], description: 'Focus search' },
      { keys: ['Shift', '?'], description: 'Show keyboard shortcuts' },
      { keys: ['Esc'], description: 'Close dialogs / Exit focus mode' },
      { keys: ['Ctrl', 'R'], description: 'Refresh page' },
      { keys: ['Ctrl', 'W'], description: 'Word count popup' },
    ]
  },
  {
    title: 'Navigation',
    shortcuts: [
      { keys: ['Ctrl', 'P'], description: 'View all projects' },
      { keys: ['Ctrl', 'N'], description: 'Create new project' },
      { keys: ['Ctrl', 'T'], description: 'Browse templates' },
      { keys: ['Ctrl', 'Shift', 'D'], description: 'Go to demo' },
      { keys: ['Ctrl', 'Shift', 'H'], description: 'Go to home' },
      { keys: ['Alt', '←'], description: 'Go back' },
      { keys: ['Alt', '→'], description: 'Go forward' },
      { keys: ['Ctrl', 'F'], description: 'Find in document' },
      { keys: ['Ctrl', 'G'], description: 'Go to line' },
    ]
  },
  {
    title: 'File Operations',
    shortcuts: [
      { keys: ['Ctrl', 'S'], description: 'Save document' },
      { keys: ['Ctrl', 'Shift', 'N'], description: 'New chapter' },
      { keys: ['Ctrl', 'E'], description: 'Quick export' },
      { keys: ['Ctrl', 'Enter'], description: 'Quick save and continue' },
    ]
  },
  {
    title: 'Text Editing',
    shortcuts: [
      { keys: ['Ctrl', 'Z'], description: 'Undo' },
      { keys: ['Ctrl', 'Y'], description: 'Redo' },
      { keys: ['Ctrl', 'Shift', 'Z'], description: 'Redo (alternative)' },
      { keys: ['Ctrl', 'A'], description: 'Select all' },
      { keys: ['Ctrl', 'D'], description: 'Duplicate line' },
      { keys: ['Ctrl', 'Shift', 'K'], description: 'Delete line' },
      { keys: ['Alt', '↑'], description: 'Move line up' },
      { keys: ['Alt', '↓'], description: 'Move line down' },
      { keys: ['Ctrl', '/'], description: 'Toggle comment' },
    ]
  },
  {
    title: 'Text Formatting',
    shortcuts: [
      { keys: ['Ctrl', 'B'], description: 'Bold text' },
      { keys: ['Ctrl', 'I'], description: 'Italic text' },
      { keys: ['Ctrl', 'U'], description: 'Underline text' },
    ]
  },
  {
    title: 'Find & Replace',
    shortcuts: [
      { keys: ['Ctrl', 'F'], description: 'Find in document' },
      { keys: ['Ctrl', 'H'], description: 'Find and replace' },
      { keys: ['F3'], description: 'Find next' },
      { keys: ['Shift', 'F3'], description: 'Find previous' },
    ]
  },
  {
    title: 'View & Panels',
    shortcuts: [
      { keys: ['F11'], description: 'Toggle focus mode' },
      { keys: ['Ctrl', '1'], description: 'Toggle chapter navigator' },
      { keys: ['Ctrl', '2'], description: 'Toggle story bible' },
      { keys: ['Ctrl', '3'], description: 'Toggle AI assistant' },
      { keys: ['Ctrl', '5'], description: 'Toggle writing stats' },
      { keys: ['Ctrl', '+'], description: 'Zoom in' },
      { keys: ['Ctrl', '-'], description: 'Zoom out' },
      { keys: ['Ctrl', '0'], description: 'Reset zoom' },
    ]
  },
  {
    title: 'AI Features',
    shortcuts: [
      { keys: ['Ctrl', 'Shift', 'J'], description: 'AI content generation' },
      { keys: ['Ctrl', 'Shift', 'R'], description: 'AI rewrite selection' },
      { keys: ['Ctrl', 'Shift', 'T'], description: 'AI continue writing' },
      { keys: ['Ctrl', 'Shift', 'I'], description: 'Toggle AI assistant' },
    ]
  },
  {
    title: 'Project Navigation',
    shortcuts: [
      { keys: ['Ctrl', 'W'], description: 'Go to write page' },
      { keys: ['Ctrl', 'O'], description: 'Go to overview' },
      { keys: ['Ctrl', 'Shift', 'B'], description: 'Go to story bible' },
      { keys: ['Ctrl', 'D'], description: 'Go to dashboard' },
      { keys: ['Ctrl', 'Shift', 'G'], description: 'Generate structure' },
      { keys: ['Ctrl', 'Shift', 'C'], description: 'Generate chapter' },
    ]
  }
]

interface KeyboardShortcutsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function KeyboardShortcutsModal({ open, onOpenChange }: KeyboardShortcutsModalProps) {
  const isMac = typeof window !== 'undefined' && navigator.platform.toUpperCase().indexOf('MAC') >= 0

  const formatKey = (key: string) => {
    if (key === 'Ctrl' && isMac) return '⌘'
    if (key === 'Alt' && isMac) return '⌥'
    if (key === 'Shift' && isMac) return '⇧'
    if (key === '←') return '←'
    if (key === '→') return '→'
    return key
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Command className="h-5 w-5" />
            Keyboard Shortcuts
          </DialogTitle>
          <DialogDescription>
            Speed up your workflow with these keyboard shortcuts
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="all" className="flex-1 overflow-hidden flex flex-col">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="editing">Editing</TabsTrigger>
            <TabsTrigger value="navigation">Navigation</TabsTrigger>
            <TabsTrigger value="ai">AI Features</TabsTrigger>
          </TabsList>
          
          <div className="flex-1 overflow-y-auto mt-4">
            <TabsContent value="all" className="space-y-6">
              {shortcutGroups.map((group) => (
                <ShortcutGroup key={group.title} group={group} formatKey={formatKey} />
              ))}
            </TabsContent>
            
            <TabsContent value="general" className="space-y-6">
              <ShortcutGroup 
                group={shortcutGroups.find(g => g.title === 'General')!} 
                formatKey={formatKey} 
              />
              <ShortcutGroup 
                group={shortcutGroups.find(g => g.title === 'File Operations')!} 
                formatKey={formatKey} 
              />
            </TabsContent>
            
            <TabsContent value="editing" className="space-y-6">
              <ShortcutGroup 
                group={shortcutGroups.find(g => g.title === 'Text Editing')!} 
                formatKey={formatKey} 
              />
              <ShortcutGroup 
                group={shortcutGroups.find(g => g.title === 'Text Formatting')!} 
                formatKey={formatKey} 
              />
              <ShortcutGroup 
                group={shortcutGroups.find(g => g.title === 'Find & Replace')!} 
                formatKey={formatKey} 
              />
            </TabsContent>
            
            <TabsContent value="navigation" className="space-y-6">
              <ShortcutGroup 
                group={shortcutGroups.find(g => g.title === 'Navigation')!} 
                formatKey={formatKey} 
              />
              <ShortcutGroup 
                group={shortcutGroups.find(g => g.title === 'View & Panels')!} 
                formatKey={formatKey} 
              />
              <ShortcutGroup 
                group={shortcutGroups.find(g => g.title === 'Project Navigation')!} 
                formatKey={formatKey} 
              />
            </TabsContent>
            
            <TabsContent value="ai" className="space-y-6">
              <ShortcutGroup 
                group={shortcutGroups.find(g => g.title === 'AI Features')!} 
                formatKey={formatKey} 
              />
            </TabsContent>
          </div>
        </Tabs>
        
        <div className="text-xs text-muted-foreground mt-4 text-center">
          Press <kbd className="px-1.5 py-0.5 text-xs font-mono bg-muted rounded">Shift</kbd> + <kbd className="px-1.5 py-0.5 text-xs font-mono bg-muted rounded">?</kbd> anytime to view shortcuts
        </div>
      </DialogContent>
    </Dialog>
  )
}

function ShortcutGroup({ group, formatKey }: { group: ShortcutGroup; formatKey: (key: string) => string }) {
  return (
    <div>
      <h3 className="font-medium mb-3">{group.title}</h3>
      <div className="space-y-2">
        {group.shortcuts.map((shortcut, index) => (
          <div 
            key={index} 
            className="flex items-center justify-between py-2 px-3 rounded-lg hover:bg-muted/50 transition-colors"
          >
            <span className="text-sm">{shortcut.description}</span>
            <div className="flex gap-1">
              {shortcut.keys.map((key, keyIndex) => (
                <Badge 
                  key={keyIndex} 
                  variant="secondary" 
                  className="font-mono text-xs px-2"
                >
                  {formatKey(key)}
                </Badge>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}