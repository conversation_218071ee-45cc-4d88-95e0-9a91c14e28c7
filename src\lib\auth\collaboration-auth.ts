import { createClient } from '@/lib/supabase/server'
import { logger } from '@/lib/services/logger'

export interface CollaborationPermissions {
  canView: boolean
  canEdit: boolean
  canManageTeam: boolean
  canExport: boolean
  canDelete: boolean
  role: 'owner' | 'editor' | 'viewer' | null
}

/**
 * Check if a user has access to a project and what permissions they have
 */
export async function checkProjectAccess(
  userId: string,
  projectId: string
): Promise<CollaborationPermissions> {
  const supabase = await createClient()
  
  // Default permissions (no access)
  const defaultPermissions: CollaborationPermissions = {
    canView: false,
    canEdit: false,
    canManageTeam: false,
    canExport: false,
    canDelete: false,
    role: null
  }
  
  try {
    // Check if user owns the project
    const { data: project } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', projectId)
      .single()
    
    if (!project) {
      return defaultPermissions
    }
    
    if (project.user_id === userId) {
      // Owner has all permissions
      return {
        canView: true,
        canEdit: true,
        canManageTeam: true,
        canExport: true,
        canDelete: true,
        role: 'owner'
      }
    }
    
    // Check if user is a collaborator
    const { data: collaborator } = await supabase
      .from('project_collaborators')
      .select('role, permissions, status')
      .eq('project_id', projectId)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single()
    
    if (!collaborator) {
      return defaultPermissions
    }
    
    // Return permissions based on role
    return {
      canView: true, // All collaborators can view
      canEdit: collaborator.permissions?.can_write || false,
      canManageTeam: collaborator.permissions?.can_manage_team || false,
      canExport: collaborator.permissions?.can_export || false,
      canDelete: collaborator.permissions?.can_delete || false,
      role: collaborator.role
    }
  } catch (error) {
    logger.error('Error checking project access', error)
    return defaultPermissions
  }
}

/**
 * Check if a user can perform a specific action on a project
 */
export async function canPerformAction(
  userId: string,
  projectId: string,
  action: 'view' | 'edit' | 'manage_team' | 'export' | 'delete'
): Promise<boolean> {
  const permissions = await checkProjectAccess(userId, projectId)
  
  switch (action) {
    case 'view':
      return permissions.canView
    case 'edit':
      return permissions.canEdit
    case 'manage_team':
      return permissions.canManageTeam
    case 'export':
      return permissions.canExport
    case 'delete':
      return permissions.canDelete
    default:
      return false
  }
}

/**
 * Get all projects a user has access to (owned or collaborating)
 */
export async function getUserAccessibleProjects(userId: string) {
  const supabase = await createClient()
  
  try {
    // Get owned projects
    const { data: ownedProjects } = await supabase
      .from('projects')
      .select('*, series_books(*)')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })
    
    // Get collaborated projects
    const { data: collaborations } = await supabase
      .from('project_collaborators')
      .select(`
        role,
        permissions,
        joined_at,
        project:projects(
          *,
          series_books(*)
        )
      `)
      .eq('user_id', userId)
      .eq('status', 'active')
      .order('joined_at', { ascending: false })
    
    // Combine and format results
    const allProjects = [
      ...(ownedProjects || []).map(p => ({
        ...p,
        role: 'owner' as const,
        permissions: {
          canView: true,
          canEdit: true,
          canManageTeam: true,
          canExport: true,
          canDelete: true
        }
      })),
      ...(collaborations || []).map(c => ({
        ...c.project,
        role: c.role,
        permissions: {
          canView: true,
          canEdit: c.permissions?.can_write || false,
          canManageTeam: c.permissions?.can_manage_team || false,
          canExport: c.permissions?.can_export || false,
          canDelete: c.permissions?.can_delete || false
        }
      }))
    ]
    
    return allProjects
  } catch (error) {
    logger.error('Error getting user accessible projects', error)
    return []
  }
}

/**
 * Check if a user can join a collaboration session
 */
export async function canJoinCollaborationSession(
  userId: string,
  sessionId: string
): Promise<boolean> {
  const supabase = await createClient()
  
  try {
    // Extract project ID from session ID (format: project:PROJECT_ID:document:DOC_ID)
    const parts = sessionId.split(':')
    if (parts.length < 2 || parts[0] !== 'project') {
      return false
    }
    
    const projectId = parts[1]
    
    // Check if user has at least view access to the project
    const permissions = await checkProjectAccess(userId, projectId)
    return permissions.canView
  } catch (error) {
    logger.error('Error checking collaboration session access', error)
    return false
  }
}

/**
 * Rate limiting for collaboration events
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(
  userId: string,
  action: string,
  maxRequests: number = 100,
  windowMs: number = 60000 // 1 minute
): boolean {
  const key = `${userId}:${action}`
  const now = Date.now()
  
  const limit = rateLimitMap.get(key)
  
  if (!limit || now > limit.resetTime) {
    // Create new limit window
    rateLimitMap.set(key, {
      count: 1,
      resetTime: now + windowMs
    })
    return true
  }
  
  if (limit.count >= maxRequests) {
    return false
  }
  
  limit.count++
  return true
}

/**
 * Clean up expired rate limit entries periodically
 */
setInterval(() => {
  const now = Date.now()
  for (const [key, limit] of rateLimitMap.entries()) {
    if (now > limit.resetTime) {
      rateLimitMap.delete(key)
    }
  }
}, 60000) // Clean up every minute