# Real-Time Collaboration Implementation

## Overview

BookScribe AI now supports real-time collaborative editing for Studio tier subscribers. This feature allows multiple writers to work on the same document simultaneously with live cursors, selections, and synchronized content changes.

## Architecture

### Components

1. **CollaborationService** (`src/lib/services/collaboration-service.ts`)
   - WebSocket-based communication layer
   - Handles connection management and reconnection logic
   - Manages collaboration sessions and user presence
   - Simulates collaboration in development mode

2. **useMonacoCollaboration Hook** (`src/hooks/use-monaco-collaboration.tsx`)
   - React hook for Monaco editor integration
   - Handles cursor decorations and selection highlights
   - Synchronizes content changes between collaborators
   - Manages visual indicators for each user

3. **CollaborationIndicator** (`src/components/collaboration/collaboration-indicator.tsx`)
   - UI component showing active collaborators
   - Connection status indicator
   - Toggle for enabling/disabling collaboration
   - Session link sharing functionality

4. **CollaborativeMonacoEditor** (`src/components/editor/collaborative-monaco-editor.tsx`)
   - Wrapped Monaco editor with built-in collaboration
   - Automatic theme switching
   - Collaboration toolbar integration

## Implementation Details

### WebSocket Protocol

The collaboration service uses WebSocket for real-time communication with the following event types:

- `user.joined` - New user joins the session
- `user.left` - User leaves the session
- `cursor.moved` - Cursor position updates
- `selection.changed` - Selection range updates
- `content.changed` - Document content changes

### User Identification

Each collaborator is identified by:
- Unique user ID
- Display name
- Email address
- Assigned color for visual differentiation

### Cursor and Selection Visualization

- Each user's cursor is displayed with a colored line and their initials
- Selections are highlighted with a semi-transparent background in the user's color
- Hover tooltips show the user's name

### Conflict Resolution

Currently implements a simple last-write-wins strategy. For production use, consider implementing:
- Operational Transformation (OT)
- Conflict-free Replicated Data Types (CRDTs)

## Usage

### Basic Integration

```typescript
import { CollaborativeMonacoEditor } from '@/components/editor/collaborative-monaco-editor'

function MyEditor() {
  const [content, setContent] = useState('')
  
  return (
    <CollaborativeMonacoEditor
      value={content}
      onChange={setContent}
      projectId="project-123"
      documentId="chapter-1"
      userId="user-456"
      userName="John Writer"
      userEmail="<EMAIL>"
      subscription={userSubscription}
      enableCollaboration={true}
    />
  )
}
```

### Custom Integration

```typescript
import { useMonacoCollaboration } from '@/hooks/use-monaco-collaboration'

function CustomEditor() {
  const editorRef = useRef<IStandaloneCodeEditor | null>(null)
  
  const { isConnected, collaborators } = useMonacoCollaboration(
    editorRef.current,
    {
      sessionId: 'project-123-doc-456',
      userId: 'user-789',
      userName: 'Jane Author',
      userEmail: '<EMAIL>',
      subscription: userSubscription,
      enabled: true
    }
  )
  
  // Your custom editor implementation
}
```

## Configuration

### Environment Variables

```env
# WebSocket server URL for collaboration
NEXT_PUBLIC_COLLAB_WS_URL=wss://collab.bookscribe.ai

# Enable collaboration features
NEXT_PUBLIC_ENABLE_COLLABORATION=true
```

### Subscription Requirements

Real-time collaboration is only available for Studio tier subscribers. The system automatically checks subscription status before enabling collaboration features.

## Security Considerations

1. **Authentication**: All WebSocket connections must be authenticated
2. **Authorization**: Users can only access documents they have permission to edit
3. **Data Encryption**: Use WSS (WebSocket Secure) for all connections
4. **Session Management**: Implement proper session timeouts and cleanup

## Performance Optimization

1. **Debouncing**: Cursor and selection updates are debounced to reduce network traffic
2. **Batching**: Multiple rapid changes are batched into single updates
3. **Compression**: WebSocket messages are compressed for efficiency
4. **Lazy Loading**: Collaboration features are only loaded when needed

## Future Enhancements

1. **Voice/Video Chat**: Integrated communication for collaborators
2. **Comments and Annotations**: Inline commenting system
3. **Version History**: Track changes with attribution
4. **Presence Awareness**: Show which sections users are working on
5. **Offline Support**: Queue changes when disconnected
6. **Mobile Support**: Collaboration on mobile devices

## Testing

### Unit Tests
```bash
npm run test src/lib/services/collaboration-service.test.ts
npm run test src/hooks/use-monaco-collaboration.test.tsx
```

### Integration Tests
```bash
npm run test:e2e tests/collaboration.spec.ts
```

### Manual Testing
1. Open two browser windows with the same document
2. Enable collaboration in both windows
3. Type in one window and observe updates in the other
4. Test cursor movements and selections
5. Test disconnection and reconnection scenarios

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check WebSocket server URL
   - Verify subscription tier
   - Check network connectivity

2. **Cursors Not Visible**
   - Ensure CSS styles are loaded
   - Check browser console for errors
   - Verify user colors are assigned

3. **Changes Not Syncing**
   - Check WebSocket connection status
   - Verify session IDs match
   - Look for JavaScript errors

### Debug Mode

Enable debug logging:
```typescript
logger.setLevel('debug')
```

## Production Deployment

### Infrastructure Requirements

1. **WebSocket Server**
   - Node.js with Socket.io or similar
   - Redis for session management
   - Load balancer with sticky sessions

2. **Database**
   - Store collaboration sessions
   - Track user presence
   - Log activity for analytics

3. **Monitoring**
   - Connection metrics
   - Performance monitoring
   - Error tracking

### Scaling Considerations

1. **Horizontal Scaling**: Use Redis pub/sub for multi-server deployments
2. **Geographic Distribution**: Deploy WebSocket servers in multiple regions
3. **Connection Limits**: Implement per-session user limits
4. **Rate Limiting**: Prevent abuse with message rate limits

## API Reference

See the TypeScript interfaces in:
- `src/lib/services/collaboration-service.ts`
- `src/hooks/use-monaco-collaboration.tsx`

For detailed API documentation and examples.