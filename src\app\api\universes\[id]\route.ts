import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { logger } from '@/lib/services/logger';

export const runtime = 'nodejs';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id } = params;
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: universe, error } = await supabase
      .from('universes')
      .select(`
        *,
        series:series(
          id,
          title,
          description,
          books:series_books(
            book:books(
              id,
              title
            )
          )
        ),
        timeline_events:universe_timeline_events(*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Universe not found' }, { status: 404 });
      }
      logger.error('Error fetching universe:', error);
      return NextResponse.json({ error: 'Failed to fetch universe' }, { status: 500 });
    }

    return NextResponse.json({ universe });
  } catch (error) {
    logger.error('Error in GET /api/universes/[id]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id } = params;
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, rules } = body;

    const updates: Record<string, unknown> = {
      updated_at: new Date().toISOString()
    };

    if (name !== undefined) updates.name = name.trim();
    if (description !== undefined) updates.description = description?.trim();
    if (rules !== undefined) updates.rules = rules;

    const { data: universe, error } = await supabase
      .from('universes')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      logger.error('Error updating universe:', error);
      return NextResponse.json({ error: 'Failed to update universe' }, { status: 500 });
    }

    return NextResponse.json({ universe });
  } catch (error) {
    logger.error('Error in PUT /api/universes/[id]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id } = params;
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { error } = await supabase
      .from('universes')
      .delete()
      .eq('id', id);

    if (error) {
      logger.error('Error deleting universe:', error);
      return NextResponse.json({ error: 'Failed to delete universe' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error('Error in DELETE /api/universes/[id]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}