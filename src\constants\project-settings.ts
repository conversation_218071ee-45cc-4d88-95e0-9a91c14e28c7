import type {
  ProjectSettings,
  Genre,
  FantasySubgenre,
  SciFiSubgenre,
  MysterySubgenre,
  NarrativeVoice,
  <PERSON>se,
  Tone,
  WritingStyle,
  StructureType,
  PacingPreference,
  ChapterStructure,
  TimelineComplexity,
  ProtagonistType,
  AntagonistType,
  CharacterComplexity,
  CharacterArcType,
  TimePeriod,
  GeographicSetting,
  WorldType,
  MagicTechLevel,
  MajorTheme,
  PhilosophicalTheme,
  SocialTheme,
  TargetAudience,
  ContentRating,
  ContentWarning,
  ProjectScope,
  SeriesType,
  InterconnectionLevel,
  POVType,
  ResearchNeed,
  FactCheckingLevel,
} from '@/lib/types/project-settings';

export const GENRES: Record<Genre, string> = {
  fantasy: 'Fantasy',
  science_fiction: 'Science Fiction',
  mystery_thriller: 'Mystery/Thriller',
  romance: 'Romance',
  historical_fiction: 'Historical Fiction',
  literary_fiction: 'Literary Fiction',
  horror: 'Horror',
  adventure: 'Adventure',
  young_adult: 'Young Adult',
  contemporary_fiction: 'Contemporary Fiction',
};

export const FANTASY_SUBGENRES: Record<FantasySubgenre, string> = {
  epic_fantasy: 'Epic Fantasy',
  urban_fantasy: 'Urban Fantasy',
  dark_fantasy: 'Dark Fantasy',
  high_fantasy: 'High Fantasy',
  low_fantasy: 'Low Fantasy',
  sword_sorcery: 'Sword & Sorcery',
  magical_realism: 'Magical Realism',
};

export const SCIFI_SUBGENRES: Record<SciFiSubgenre, string> = {
  space_opera: 'Space Opera',
  cyberpunk: 'Cyberpunk',
  dystopian: 'Dystopian',
  time_travel: 'Time Travel',
  hard_scifi: 'Hard Sci-Fi',
  soft_scifi: 'Soft Sci-Fi',
  steampunk: 'Steampunk',
  biopunk: 'Biopunk',
};

export const MYSTERY_SUBGENRES: Record<MysterySubgenre, string> = {
  cozy_mystery: 'Cozy Mystery',
  police_procedural: 'Police Procedural',
  noir: 'Noir',
  psychological_thriller: 'Psychological Thriller',
  legal_thriller: 'Legal Thriller',
  espionage: 'Espionage',
};

export const SUBGENRES_BY_GENRE: Record<Genre, Record<string, string>> = {
  fantasy: FANTASY_SUBGENRES,
  science_fiction: SCIFI_SUBGENRES,
  mystery_thriller: MYSTERY_SUBGENRES,
  romance: {},
  historical_fiction: {},
  literary_fiction: {},
  horror: {},
  adventure: {},
  young_adult: {},
  contemporary_fiction: {},
};

export const NARRATIVE_VOICES: Record<NarrativeVoice, { label: string; description: string }> = {
  first_person: {
    label: 'First Person',
    description: 'Story told from "I" perspective',
  },
  third_person_limited: {
    label: 'Third Person Limited',
    description: 'Story told from one character\'s perspective using "he/she"',
  },
  third_person_omniscient: {
    label: 'Third Person Omniscient',
    description: 'All-knowing narrator with access to all characters\' thoughts',
  },
  second_person: {
    label: 'Second Person',
    description: 'Story told using "you" perspective',
  },
  multiple_pov: {
    label: 'Multiple POV',
    description: 'Story switches between different character perspectives',
  },
};

export const TENSES: Record<Tense, string> = {
  present: 'Present Tense',
  past: 'Past Tense',
  mixed: 'Mixed Tense',
};

export const TONES: Record<Tone, { label: string; description: string }> = {
  dark_gritty: {
    label: 'Dark & Gritty',
    description: 'Serious, realistic, often violent or mature themes',
  },
  light_humorous: {
    label: 'Light & Humorous',
    description: 'Comedic, uplifting, witty dialogue and situations',
  },
  epic_heroic: {
    label: 'Epic & Heroic',
    description: 'Grand scale, legendary heroes, sweeping adventures',
  },
  intimate_personal: {
    label: 'Intimate & Personal',
    description: 'Character-focused, emotional depth, personal stakes',
  },
  mysterious_suspenseful: {
    label: 'Mysterious & Suspenseful',
    description: 'Tension-building, unknown threats, plot twists',
  },
  romantic_passionate: {
    label: 'Romantic & Passionate',
    description: 'Emotional intensity, relationship-focused, sensual',
  },
  philosophical_contemplative: {
    label: 'Philosophical & Contemplative',
    description: 'Thought-provoking, explores deep questions, reflective',
  },
};

export const WRITING_STYLES: Record<WritingStyle, { label: string; description: string }> = {
  literary: {
    label: 'Literary',
    description: 'Artistic prose, symbolism, character depth over plot',
  },
  commercial: {
    label: 'Commercial',
    description: 'Accessible, plot-driven, broad appeal',
  },
  pulp: {
    label: 'Pulp',
    description: 'Fast-paced, action-heavy, straightforward prose',
  },
  experimental: {
    label: 'Experimental',
    description: 'Unconventional structure, innovative techniques',
  },
  minimalist: {
    label: 'Minimalist',
    description: 'Sparse prose, simple sentences, understated',
  },
  descriptive: {
    label: 'Descriptive',
    description: 'Rich details, immersive settings, sensory language',
  },
  dialogue_heavy: {
    label: 'Dialogue-Heavy',
    description: 'Character conversations drive the story',
  },
};

export const STRUCTURE_TYPES: Record<StructureType, { label: string; description: string }> = {
  three_act: {
    label: 'Three-Act Structure',
    description: 'Classic beginning, middle, end with two major plot points',
  },
  heros_journey: {
    label: "Hero's Journey",
    description: 'Joseph Campbell\'s monomyth with departure, initiation, return',
  },
  save_the_cat: {
    label: 'Save the Cat',
    description: 'Blake Snyder\'s 15-beat structure for commercial stories',
  },
  freytags_pyramid: {
    label: "Freytag's Pyramid",
    description: 'Five-part structure: exposition, rising action, climax, falling action, resolution',
  },
  seven_point: {
    label: 'Seven-Point Story Structure',
    description: 'Dan Wells\' approach with hook, plot turns, pinch points, and resolution',
  },
  fichtean_curve: {
    label: 'Fichtean Curve',
    description: 'Series of crises with rising tension throughout',
  },
};

export const PACING_PREFERENCES: Record<PacingPreference, string> = {
  fast_paced: 'Fast-Paced Action',
  slow_burn: 'Slow Burn',
  balanced: 'Balanced',
  character_driven: 'Character-Driven',
  plot_driven: 'Plot-Driven',
};

export const CHAPTER_STRUCTURES: Record<ChapterStructure, string> = {
  fixed_length: 'Fixed Length',
  variable_length: 'Variable Length',
  scene_based: 'Scene-Based',
  time_based: 'Time-Based',
};

export const TIMELINE_COMPLEXITIES: Record<TimelineComplexity, string> = {
  linear: 'Linear',
  flashbacks: 'With Flashbacks',
  multiple_timelines: 'Multiple Timelines',
  non_linear: 'Non-Linear Narrative',
};

export const PROTAGONIST_TYPES: Record<ProtagonistType, string> = {
  the_hero: 'The Hero',
  the_antihero: 'The Antihero',
  the_reluctant_hero: 'The Reluctant Hero',
  the_tragic_hero: 'The Tragic Hero',
  the_everyman: 'The Everyman',
  the_mentor: 'The Mentor',
  the_innocent: 'The Innocent',
};

export const ANTAGONIST_TYPES: Record<AntagonistType, string> = {
  the_villain: 'The Villain',
  the_shadow: 'The Shadow',
  the_rival: 'The Rival',
  the_skeptic: 'The Skeptic',
  the_threshold_guardian: 'The Threshold Guardian',
  the_shapeshifter: 'The Shapeshifter',
};

export const CHARACTER_COMPLEXITIES: Record<CharacterComplexity, string> = {
  simple_archetypal: 'Simple/Archetypal',
  complex_layered: 'Complex/Layered',
  morally_ambiguous: 'Morally Ambiguous',
  ensemble_cast: 'Ensemble Cast',
};

export const CHARACTER_ARC_TYPES: Record<CharacterArcType, string> = {
  positive_change: 'Positive Change',
  negative_change: 'Negative Change',
  flat_arc: 'Flat Arc',
  corruption_arc: 'Corruption Arc',
  redemption_arc: 'Redemption Arc',
};

export const TIME_PERIODS: Record<TimePeriod, string> = {
  contemporary: 'Contemporary',
  historical: 'Historical',
  near_future: 'Near Future',
  far_future: 'Far Future',
  alternate_history: 'Alternate History',
  timeless: 'Timeless',
};

export const GEOGRAPHIC_SETTINGS: Record<GeographicSetting, string> = {
  urban: 'Urban',
  rural: 'Rural',
  suburban: 'Suburban',
  wilderness: 'Wilderness',
  island: 'Island',
  underground: 'Underground',
  space: 'Space',
  alternate_dimension: 'Alternate Dimension',
};

export const WORLD_TYPES: Record<WorldType, string> = {
  real_world: 'Real World',
  alternate_reality: 'Alternate Reality',
  fantasy_world: 'Fantasy World',
  scifi_universe: 'Sci-Fi Universe',
  post_apocalyptic: 'Post-Apocalyptic',
  steampunk: 'Steampunk',
  cyberpunk: 'Cyberpunk',
};

export const MAGIC_TECH_LEVELS: Record<MagicTechLevel, string> = {
  no_magic_current_tech: 'No Magic/Current Tech',
  low_magic_near_future: 'Low Magic/Near Future Tech',
  high_magic_advanced_tech: 'High Magic/Advanced Tech',
  magitech_fusion: 'Magitech Fusion',
};

export const MAJOR_THEMES: Record<MajorTheme, string> = {
  love_relationships: 'Love & Relationships',
  good_vs_evil: 'Good vs Evil',
  coming_of_age: 'Coming of Age',
  redemption: 'Redemption',
  sacrifice: 'Sacrifice',
  power_corruption: 'Power & Corruption',
  identity: 'Identity',
  family: 'Family',
  survival: 'Survival',
  justice: 'Justice',
};

export const PHILOSOPHICAL_THEMES: Record<PhilosophicalTheme, string> = {
  existentialism: 'Existentialism',
  morality: 'Morality',
  free_will_vs_destiny: 'Free Will vs Destiny',
  nature_vs_nurture: 'Nature vs Nurture',
  technology_vs_humanity: 'Technology vs Humanity',
};

export const SOCIAL_THEMES: Record<SocialTheme, string> = {
  class_struggle: 'Class Struggle',
  prejudice_discrimination: 'Prejudice & Discrimination',
  war_peace: 'War & Peace',
  environmental_issues: 'Environmental Issues',
  political_intrigue: 'Political Intrigue',
};

export const TARGET_AUDIENCES: Record<TargetAudience, string> = {
  children_8_12: 'Children (8-12)',
  young_adult_13_17: 'Young Adult (13-17)',
  new_adult_18_25: 'New Adult (18-25)',
  adult_25_plus: 'Adult (25+)',
  all_ages: 'All Ages',
};

export const CONTENT_RATINGS: Record<ContentRating, { label: string; description: string }> = {
  G: { label: 'G', description: 'General Audiences' },
  PG: { label: 'PG', description: 'Parental Guidance Suggested' },
  PG13: { label: 'PG-13', description: 'Parents Strongly Cautioned' },
  R: { label: 'R', description: 'Restricted' },
  NC17: { label: 'NC-17', description: 'Adults Only' },
};

export const CONTENT_WARNINGS: Record<ContentWarning, string> = {
  violence_mild: 'Violence (Mild)',
  violence_moderate: 'Violence (Moderate)',
  violence_graphic: 'Violence (Graphic)',
  sexual_content_mild: 'Sexual Content (Mild)',
  sexual_content_moderate: 'Sexual Content (Moderate)',
  sexual_content_explicit: 'Sexual Content (Explicit)',
  language_mild: 'Language (Mild)',
  language_moderate: 'Language (Moderate)',
  language_strong: 'Language (Strong)',
  substance_use: 'Substance Use',
  mental_health_triggers: 'Mental Health Triggers',
  abuse_themes: 'Abuse Themes',
  death_themes: 'Death Themes',
};

export const PROJECT_SCOPES: Record<ProjectScope, string> = {
  standalone: 'Standalone Novel',
  duology: 'Duology',
  trilogy: 'Trilogy',
  series_4_7: 'Series (4-7 books)',
  epic_series_8_plus: 'Epic Series (8+ books)',
  anthology: 'Anthology',
};

export const SERIES_TYPES: Record<SeriesType, string> = {
  sequential: 'Sequential',
  parallel_timelines: 'Parallel Timelines',
  different_characters_same_world: 'Different Characters, Same World',
  generational_saga: 'Generational Saga',
};

export const INTERCONNECTION_LEVELS: Record<InterconnectionLevel, string> = {
  loose_connection: 'Loose Connection',
  moderate_connection: 'Moderate Connection',
  tight_continuity: 'Tight Continuity',
  shared_universe: 'Shared Universe',
};

export const POV_TYPES: Record<POVType, string> = {
  single_pov: 'Single POV',
  dual_pov: 'Dual POV',
  multiple_pov_3_5: 'Multiple POV (3-5)',
  ensemble_cast_6_plus: 'Ensemble Cast (6+)',
};

export const RESEARCH_NEEDS: Record<ResearchNeed, string> = {
  historical_accuracy: 'Historical Accuracy',
  scientific_accuracy: 'Scientific Accuracy',
  cultural_authenticity: 'Cultural Authenticity',
  technical_expertise: 'Technical Expertise',
  medical_accuracy: 'Medical Accuracy',
  legal_accuracy: 'Legal Accuracy',
  military_tactics: 'Military Tactics',
};

export const FACT_CHECKING_LEVELS: Record<FactCheckingLevel, string> = {
  minimal: 'Minimal',
  moderate: 'Moderate',
  extensive: 'Extensive',
  expert_review: 'Expert Review Required',
};

export const WORD_COUNT_RANGES = {
  short_novel: { min: 40000, max: 60000, label: 'Short Novel (40k-60k)' },
  standard_novel: { min: 70000, max: 100000, label: 'Standard Novel (70k-100k)' },
  long_novel: { min: 100000, max: 150000, label: 'Long Novel (100k-150k)' },
  epic_novel: { min: 150000, max: 300000, label: 'Epic Novel (150k-300k)' },
};

export const CONFLICT_TYPES = {
  person_vs_person: 'Person vs Person',
  person_vs_self: 'Person vs Self',
  person_vs_society: 'Person vs Society',
  person_vs_nature: 'Person vs Nature',
  person_vs_technology: 'Person vs Technology',
  person_vs_supernatural: 'Person vs Supernatural',
  person_vs_fate: 'Person vs Fate',
};

export const STORY_BEGINNINGS = {
  action: 'In Media Res (Action)',
  dialogue: 'Opening Dialogue',
  setting: 'Setting Description',
  character: 'Character Introduction',
  mystery: 'Mystery/Question',
  backstory: 'Backstory',
  normal_world: 'Normal World',
};

export const STORY_ENDINGS = {
  closed: 'Closed (Resolved)',
  open: 'Open (Ambiguous)',
  twist: 'Twist Ending',
  cliffhanger: 'Cliffhanger',
  circular: 'Circular (Returns to Beginning)',
  epilogue: 'Epilogue Resolution',
};

export const PLOT_DEVICES = {
  foreshadowing: 'Foreshadowing',
  flashback: 'Flashback',
  prophecy: 'Prophecy',
  macguffin: 'MacGuffin',
  red_herring: 'Red Herring',
  deus_ex_machina: 'Deus Ex Machina',
  chekhov_gun: "Chekhov's Gun",
  unreliable_narrator: 'Unreliable Narrator',
  frame_story: 'Frame Story',
  parallel_plots: 'Parallel Plots',
};

export const ADVANCED_WRITING_TECHNIQUES: Record<string, { label: string; description: string }> = {
  useDeepPOV: {
    label: 'Deep Point of View',
    description: 'Filter everything through character\'s perception, thoughts, and emotions',
  },
  showDontTell: {
    label: 'Show, Don\'t Tell',
    description: 'Emphasize action and sensory details over exposition and explanation',
  },
  layeredMetaphors: {
    label: 'Layered Metaphors',
    description: 'Include symbolic imagery and recurring metaphors throughout the narrative',
  },
  sensoryRich: {
    label: 'Sensory-Rich Prose',
    description: 'Prioritize vivid descriptions using all five senses for immersion',
  },
  subtextHeavy: {
    label: 'Heavy Subtext',
    description: 'Characters say one thing but mean another, rich underlying meanings',
  },
  varyProse: {
    label: 'Varied Prose Rhythm',
    description: 'Intentionally vary sentence structure, length, and rhythm for flow',
  },
  emotionalNuance: {
    label: 'Emotional Nuance',
    description: 'Layer complex emotions, show internal conflict and subtle feelings',
  },
  cinematicScenes: {
    label: 'Cinematic Writing',
    description: 'Write scenes with visual/cinematic quality, as if for film',
  },
  literaryAllusions: {
    label: 'Literary Allusions',
    description: 'Include references to literature, mythology, or cultural works',
  },
  preciseLanguage: {
    label: 'Precise Language',
    description: 'Use specific, evocative words over generic descriptions',
  },
};

export const DEFAULT_PROJECT_SETTINGS: Partial<ProjectSettings> = {
  targetAudience: 'adult_25_plus',
  contentRating: 'PG13',
  projectScope: 'standalone',
  primaryGenre: 'fantasy',
  narrativeVoice: 'third_person_limited',
  tense: 'past',
  tone: ['epic_heroic'],
  writingStyle: 'commercial',
  structureType: 'three_act',
  pacingPreference: 'balanced',
  chapterStructure: 'fixed_length',
  timelineComplexity: 'linear',
  characterComplexity: 'complex_layered',
  timePeriod: 'timeless',
  geographicSetting: 'urban',
  worldType: 'fantasy_world',
  magicTechLevel: 'high_magic_advanced_tech',
  majorThemes: ['good_vs_evil', 'coming_of_age'],
  contentWarnings: [],
  targetWordCount: 100000,
  chapterCountType: 'flexible',
  povCharacterCount: 1,
  povCharacterType: 'single_pov',
  researchNeeds: [],
  factCheckingLevel: 'moderate',
  // Advanced writing techniques default to false
  useDeepPOV: false,
  showDontTell: false,
  layeredMetaphors: false,
  sensoryRich: false,
  subtextHeavy: false,
  varyProse: false,
  emotionalNuance: false,
  cinematicScenes: false,
  literaryAllusions: false,
  preciseLanguage: false,
};