import { logError } from './error-handling';

import { logger } from '@/lib/services/logger';

/**
 * Global error handler for unhandled promise rejections
 * This should be initialized early in the application lifecycle
 */
export function initializeGlobalErrorHandlers() {
  if (typeof window !== 'undefined') {
    // Client-side error handlers
    window.addEventListener('unhandledrejection', (event) => {
      logger.error('Unhandled promise rejection:', event.reason);
      
      logError(new Error(event.reason?.message || 'Unhandled promise rejection'), {
        action: 'unhandled_promise_rejection',
        metadata: {
          reason: event.reason,
          promise: event.promise,
          type: 'unhandledrejection'
        }
      });

      // Prevent the default browser behavior (showing error in console)
      event.preventDefault();
    });

    window.addEventListener('error', (event) => {
      logger.error('Global error:', event.error);
      
      logError(event.error || new Error(event.message), {
        action: 'global_error',
        metadata: {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          type: 'error'
        }
      });
    });
  } else {
    // Server-side error handlers (Node.js)
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      
      logError(new Error(reason?.toString() || 'Unhandled promise rejection'), {
        action: 'unhandled_promise_rejection_server',
        metadata: {
          reason,
          type: 'unhandledRejection'
        }
      });
    });

    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      
      logError(error, {
        action: 'uncaught_exception_server',
        metadata: {
          type: 'uncaughtException'
        }
      });

      // Exit the process after logging
      process.exit(1);
    });
  }
}

/**
 * Wrapper for async functions to ensure errors are caught
 */
export function withErrorHandling<T extends (...args: unknown[]) => Promise<unknown>>(
  fn: T,
  context?: string
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args);
    } catch (error) {
      logError(error instanceof Error ? error : new Error(String(error)), {
        action: context || 'async_function_error',
        metadata: {
          functionName: fn.name,
          args: args.length > 0 ? args : undefined
        }
      });
      throw error;
    }
  }) as T;
}

/**
 * Safe promise wrapper that logs rejections
 */
export function safePromise<T>(
  promise: Promise<T>,
  context?: string
): Promise<T | undefined> {
  return promise.catch((error) => {
    logError(error instanceof Error ? error : new Error(String(error)), {
      action: context || 'promise_rejection',
      metadata: {
        type: 'safe_promise'
      }
    });
    return undefined;
  });
}

/**
 * Execute async function with timeout
 */
export async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  context?: string
): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => {
      reject(new Error(`Operation timed out after ${timeoutMs}ms`));
    }, timeoutMs);
  });

  try {
    return await Promise.race([promise, timeoutPromise]);
  } catch (error) {
    if (error instanceof Error && error.message.includes('timed out')) {
      logError(error, {
        action: context || 'operation_timeout',
        metadata: {
          timeoutMs,
          type: 'timeout'
        }
      });
    }
    throw error;
  }
}