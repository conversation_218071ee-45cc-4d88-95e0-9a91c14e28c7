'use client';

import { useEffect, useRef, useState } from 'react';
import { logger } from '@/lib/services/logger';

import Editor from '@monaco-editor/react';
import type { Monaco } from '@monaco-editor/react';
import { editor } from 'monaco-editor';
import { configureMonacoLoader, getOptimizedEditorOptions, getFocusModeEditorOptions } from './monaco-config';
import { useEditorStore } from '@/stores/editor-store';
import { SelectionMenu } from './selection-menu';
import { EnhancedFormattingToolbar } from './enhanced-formatting-toolbar';
import { AISuggestions } from './ai-suggestions';
import { AiAssistantPopup } from './ai-assistant-popup';
import { WritingStats } from './writing-stats';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, CheckCircle, X, Focus, Minimize, Maximize, Save } from 'lucide-react';
import { ComponentErrorBoundary } from '@/components/error/component-error-boundary';
import { useEditorPerformance } from '@/hooks/use-editor-performance';
import { useTypographySettings, useEditorSettings } from '@/lib/settings/settings-store';
import { writingSessionTracker } from '@/lib/services/writing-session-tracker';
import { useAuth } from '@/contexts/auth-context';
import { useRealtimeEditor } from '@/hooks/use-realtime-editor';
import { RealtimeIndicator } from './realtime-indicator';
import { useChapterCompletion } from '@/hooks/use-chapter-completion';
import { updateMonacoTheme } from '@/lib/monaco-theme-generator';

type MonacoEditor = editor.IStandaloneCodeEditor;
type CursorSelectionChangedEvent = editor.ICursorSelectionChangedEvent;
type CursorPositionChangedEvent = editor.ICursorPositionChangedEvent;

interface Suggestion {
  id: string;
  type: 'grammar' | 'style' | 'character' | 'plot' | 'pacing' | 'dialogue';
  severity: 'error' | 'warning' | 'suggestion';
  message: string;
  range: {
    startLineNumber: number;
    startColumn: number;
    endLineNumber: number;
    endColumn: number;
  };
  replacement?: string;
  explanation?: string;
}

export interface UnifiedMonacoEditorProps {
  // Basic props
  value?: string;
  initialContent?: string;
  onChange?: (value: string) => void;
  onContentChange?: (content: string) => void;
  onSave?: () => void;
  readOnly?: boolean;
  height?: string;
  
  // Feature toggles
  showToolbar?: boolean;
  showStats?: boolean;
  showAISuggestions?: boolean;
  showSelectionMenu?: boolean;
  enableRealTimeAnalysis?: boolean;
  
  // Advanced features
  projectId?: string;
  chapterNumber?: number;
  chapterId?: string;
  
  // Mode selection
  mode?: 'basic' | 'advanced';
  
  // Focus mode for distraction-free writing
  focusMode?: boolean;
  onFocusModeToggle?: (enabled: boolean) => void;
  
  // Real-time collaboration
  enableRealtime?: boolean;
  
  // Chapter completion
  showCompleteChapter?: boolean;
  onChapterComplete?: () => void;
}

// Configure Monaco loader to avoid CSP issues
if (typeof window !== 'undefined') {
  configureMonacoLoader();
}

// Monaco is now loaded locally, so fallback is not needed

export function UnifiedMonacoEditor({ 
  value,
  initialContent = '', 
  onChange,
  onContentChange,
  onSave,
  readOnly = false,
  height = '600px',
  showToolbar = true,
  showStats = false,
  showAISuggestions = true,
  showSelectionMenu = true,
  enableRealTimeAnalysis = false,
  projectId,
  chapterNumber,
  chapterId,
  mode = 'basic',
  focusMode = false,
  onFocusModeToggle,
  enableRealtime = false,
  showCompleteChapter = false,
  onChapterComplete
}: UnifiedMonacoEditorProps) {
  const { 
    content, 
    setContent, 
    setSelection, 
    clearSelection,
    selectedText 
  } = useEditorStore();
  
  const { typography } = useTypographySettings();
  const { editor: editorSettings } = useEditorSettings();
  
  const editorRef = useRef<MonacoEditor | null>(null);
  const monacoRef = useRef<Monaco | null>(null);
  const [showSelectionMenuState, setShowSelectionMenuState] = useState(false);
  const [selectionPosition, setSelectionPosition] = useState({ x: 0, y: 0 });
  const [showAISuggestionsPanel, setShowAISuggestionsPanel] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [, setUserVoiceProfile] = useState<Record<string, unknown> | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const sessionStartedRef = useRef(false);
  const lastWordCountRef = useRef(0);
  const { user } = useAuth();

  const editorValue = value !== undefined ? value : content;
  
  // Chapter completion hook
  const { completeChapter, isCompleting } = useChapterCompletion({
    chapterId: chapterId || '',
    projectId: projectId || '',
    onComplete: () => {
      onChapterComplete?.();
    }
  });
  
  // Real-time collaboration
  const [isRealtimeConnected, setIsRealtimeConnected] = useState(false);
  const { 
    sendCursorPosition, 
    sendSelection, 
    collaborators 
  } = useRealtimeEditor({
    chapterId,
    projectId,
    userId: user?.id,
    onChapterUpdate: (updatedChapter) => {
      // Handle chapter updates from other users
      if (updatedChapter.content && updatedChapter.content !== editorValue) {
        setContent(updatedChapter.content);
        if (onChange) onChange(updatedChapter.content);
      }
    },
    enabled: enableRealtime && !!chapterId && !!user?.id
  });
  
  useEffect(() => {
    setIsRealtimeConnected(enableRealtime && collaborators.length >= 0);
  }, [enableRealtime, collaborators]);

  // Performance optimization hook
  const { performanceMetrics, isLargeDocument, forceOptimization: _forceOptimization } = useEditorPerformance(
    editorRef,
    editorValue,
    {
      maxDocumentSize: 50000,
      changeDebounceMs: 300,
      enableVirtualScrolling: true,
      limitSyntaxHighlighting: true,
    }
  );

  useEffect(() => {
    if (initialContent && content !== initialContent) {
      setContent(initialContent);
    }
  }, [initialContent, content, setContent]);

  // Auto-save functionality
  useEffect(() => {
    if (editorSettings.autoSave && hasUnsavedChanges && onSave) {
      // Clear existing timeout
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }

      // Set new timeout
      autoSaveTimeoutRef.current = setTimeout(() => {
        onSave();
        setHasUnsavedChanges(false);
      }, editorSettings.autoSaveInterval * 1000);
    }

    // Cleanup on unmount or when dependencies change
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [editorSettings.autoSave, editorSettings.autoSaveInterval, hasUnsavedChanges, onSave, editorValue]);

  // End writing session on cleanup
  useEffect(() => {
    return () => {
      if (sessionStartedRef.current && user && projectId) {
        writingSessionTracker.endSession(user.id, projectId);
        sessionStartedRef.current = false;
      }
    };
  }, [user, projectId]);

  // Update editor options when typography or editor settings change
  useEffect(() => {
    if (editorRef.current) {
      const newOptions = focusMode 
        ? getFocusModeEditorOptions(typography, editorSettings)
        : getOptimizedEditorOptions(typography, editorSettings);
      editorRef.current.updateOptions(newOptions);
    }
  }, [typography, editorSettings, focusMode]);
  
  // Watch for theme changes and update Monaco theme
  useEffect(() => {
    if (!monacoRef.current || !editorRef.current) return;
    
    const observer = new MutationObserver(() => {
      if (monacoRef.current) {
        updateMonacoTheme(monacoRef.current, 'bookscribe-theme');
        if (editorRef.current) {
          editorRef.current.updateOptions({ theme: 'bookscribe-theme' });
        }
      }
    });
    
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });
    
    return () => observer.disconnect();
  }, []);

  const handleEditorDidMount = (editor: MonacoEditor, monaco: Monaco) => {
    editorRef.current = editor;
    monacoRef.current = monaco;

    // Apply the current theme immediately
    updateMonacoTheme(monaco, 'bookscribe-theme');
    
    // Apply optimized editor configuration for prose writing with user typography and editor settings
    editor.updateOptions({
      ...getOptimizedEditorOptions(typography, editorSettings),
      theme: 'bookscribe-theme'
    });

    // Save on Ctrl+S
    editor.addAction({
      id: 'save-content',
      label: 'Save',
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS],
      run: () => {
        onSave?.();
        setHasUnsavedChanges(false);
      }
    });

    // Real-time cursor tracking
    if (enableRealtime && sendCursorPosition) {
      editor.onDidChangeCursorPosition((e: CursorPositionChangedEvent) => {
        const position = e.position;
        sendCursorPosition(position.lineNumber, position.column);
      });
    }

    if (showSelectionMenu) {
      // Handle text selection
      editor.onDidChangeCursorSelection((e: CursorSelectionChangedEvent) => {
        const selection = e.selection;
        const text = editor.getModel()?.getValueInRange(selection) || '';
        
        if (text.trim().length > 0) {
          // Send selection for real-time collaboration
          if (enableRealtime && sendSelection) {
            const startOffset = editor.getModel()?.getOffsetAt(selection.getStartPosition()) || 0;
            const endOffset = editor.getModel()?.getOffsetAt(selection.getEndPosition()) || 0;
            sendSelection(startOffset, endOffset);
          }
          const model = editor.getModel();
          if (model) {
            const startOffset = model.getOffsetAt(selection.getStartPosition());
            const endOffset = model.getOffsetAt(selection.getEndPosition());
            setSelection(text, startOffset, endOffset);
          }
          
          const position = editor.getScrolledVisiblePosition(selection.getEndPosition());
          if (position) {
            setSelectionPosition({
              x: position.left,
              y: position.top + position.height
            });
            setShowSelectionMenuState(true);
          }
        } else {
          clearSelection();
          setShowSelectionMenuState(false);
        }
      });
    }

    // Track cursor position for AI suggestions
    editor.onDidChangeCursorPosition((e: CursorPositionChangedEvent) => {
      const position = e.position;
      const offset = editor.getModel()?.getOffsetAt(position) || 0;
      setCursorPosition(offset);
    });

    // Set up real-time analysis if in advanced mode or grammar check is enabled
    if ((mode === 'advanced' && enableRealTimeAnalysis) || editorSettings.grammarCheck) {
      let analysisTimeout: NodeJS.Timeout;
      
      editor.onDidChangeModelContent(() => {
        clearTimeout(analysisTimeout);
        analysisTimeout = setTimeout(() => {
          analyzeContent();
        }, 2000);
      });

      // Initial analysis
      analyzeContent();
    }
  };

  const handleEditorChange = (value: string | undefined) => {
    const newContent = value || '';
    setContent(newContent);
    onContentChange?.(newContent);
    onChange?.(newContent);
    setHasUnsavedChanges(true);
    
    // Track writing session
    if (user && projectId) {
      const wordCount = newContent.split(/\s+/).filter(word => word.length > 0).length;
      
      // Start session if not started
      if (!sessionStartedRef.current && wordCount > 0) {
        writingSessionTracker.startSession(user.id, projectId, undefined, wordCount);
        sessionStartedRef.current = true;
        lastWordCountRef.current = wordCount;
      }
      
      // Update word count if session is active
      if (sessionStartedRef.current) {
        writingSessionTracker.updateWordCount(user.id, projectId, wordCount);
        lastWordCountRef.current = wordCount;
      }
    }
  };

  const analyzeContent = async () => {
    if (!projectId || !editorRef.current) return;
    
    setIsAnalyzing(true);
    
    try {
      const model = editorRef.current.getModel();
      const content = model?.getValue() || '';
      
      const response = await fetch('/api/analysis/content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content,
          projectId,
          chapterNumber,
          analysisTypes: editorSettings.grammarCheck 
            ? ['grammar', 'style', 'character', 'plot', 'pacing', 'dialogue']
            : ['character', 'plot', 'pacing', 'dialogue'], // Skip grammar/style if disabled
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.suggestions || []);
        setUserVoiceProfile(data.voiceProfile || null);
        
        // Apply decorations
        if (monacoRef.current && model) {
          const decorations = suggestions.map(suggestion => ({
            range: suggestion.range,
            options: {
              isWholeLine: false,
              className: `suggestion-${suggestion.severity}`,
              glyphMarginClassName: `glyph-${suggestion.severity}`,
              hoverMessage: { value: suggestion.message },
            },
          }));
          
          editorRef.current.deltaDecorations([], decorations);
        }
      }
    } catch (error) {
      logger.error('Error analyzing content:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const applySuggestion = (suggestion: Suggestion) => {
    if (!editorRef.current || !suggestion.replacement) return;
    
    const model = editorRef.current.getModel();
    if (!model) return;
    
    const edit = {
      range: suggestion.range,
      text: suggestion.replacement,
    };
    
    editorRef.current.executeEdits('apply-suggestion', [edit]);
    
    // Remove the suggestion
    setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));
  };

  const dismissSuggestion = (suggestionId: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
  };

  const handleFormat = (command: string, value?: string) => {
    if (!editorRef.current) return;
    
    const editor = editorRef.current;
    const selection = editor.getSelection();
    if (!selection) return;
    
    const selectedText = editor.getModel()?.getValueInRange(selection) || '';
    let formattedText = selectedText;
    
    // Apply formatting based on command
    switch (command) {
      case 'bold':
        formattedText = selectedText ? `**${selectedText}**` : '**bold text**';
        break;
      case 'italic':
        formattedText = selectedText ? `*${selectedText}*` : '*italic text*';
        break;
      case 'underline':
        formattedText = selectedText ? `<u>${selectedText}</u>` : '<u>underlined text</u>';
        break;
      case 'strikethrough':
        formattedText = selectedText ? `~~${selectedText}~~` : '~~strikethrough text~~';
        break;
      case 'heading1':
        formattedText = `# ${selectedText || 'Heading 1'}`;
        break;
      case 'heading2':
        formattedText = `## ${selectedText || 'Heading 2'}`;
        break;
      case 'heading3':
        formattedText = `### ${selectedText || 'Heading 3'}`;
        break;
      case 'bulletList':
        formattedText = `- ${selectedText || 'List item'}`;
        break;
      case 'orderedList':
        formattedText = `1. ${selectedText || 'List item'}`;
        break;
      case 'blockquote':
        formattedText = `> ${selectedText || 'Quote'}`;
        break;
      case 'code':
        formattedText = selectedText ? `\`${selectedText}\`` : '`code`';
        break;
      case 'link':
        formattedText = `[${selectedText || 'link text'}](${value || 'https://example.com'})`;
        break;
      case 'image':
        formattedText = `![${selectedText || 'alt text'}](${value || 'image-url.jpg'})`;
        break;
      default:
        return;
    }
    
    // Replace selection with formatted text
    editor.executeEdits('format-text', [{
      range: selection,
      text: formattedText
    }]);
    
    // Adjust cursor position for empty selections
    if (!selectedText) {
      const position = selection.getStartPosition();
      let newColumn = position.column;
      
      // Position cursor inside the formatting marks
      switch (command) {
        case 'bold':
          newColumn += 2;
          break;
        case 'italic':
          newColumn += 1;
          break;
        case 'underline':
          newColumn += 3;
          break;
        case 'strikethrough':
          newColumn += 2;
          break;
      }
      
      editor.setPosition({ lineNumber: position.lineNumber, column: newColumn });
    }
  };

  // Focus mode renders a full-screen, distraction-free editor
  if (focusMode) {
    return (
      <div className="fixed inset-0 z-50 bg-background flex flex-col">
        {/* Minimal header in focus mode */}
        <div className="flex items-center justify-between p-4 border-b border-border bg-background/95 backdrop-blur-sm">
          <div className="flex items-center gap-2">
            <Focus className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-muted-foreground">Focus Mode</span>
          </div>
          <div className="flex items-center gap-2">
            {hasUnsavedChanges && editorSettings.autoSave && (
              <Badge variant="secondary" className="text-xs animate-pulse">
                <Save className="w-3 h-3 mr-1" />
                Auto-save in {editorSettings.autoSaveInterval}s
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onSave?.()}
              className="text-muted-foreground hover:text-foreground"
            >
              Save
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onFocusModeToggle?.(false)}
              className="text-muted-foreground hover:text-foreground"
            >
              <Minimize className="w-4 h-4" />
              Exit Focus
            </Button>
          </div>
        </div>
        
        {/* Full-screen editor */}
        <div className="flex-1 relative">
          <Editor
            defaultLanguage="markdown"
            value={editorValue}
            onChange={handleEditorChange}
            onMount={(editor, monaco) => {
              editorRef.current = editor;
              monacoRef.current = monaco;
              
              // Apply optimized focus mode configuration with user typography and editor settings
              editor.updateOptions(getFocusModeEditorOptions(typography, editorSettings));

              // Add focus mode keyboard shortcuts
              editor.addCommand(monaco.KeyCode.Escape, () => {
                onFocusModeToggle?.(false);
              });
              
              editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
                onSave?.();
                setHasUnsavedChanges(false);
              });
            }}
            theme="bookscribe-theme"
            options={{
              readOnly,
              automaticLayout: true,
            }}
          />
        </div>
      </div>
    );
  }

  // Normal mode with all features
  return (
    <div className="relative h-full">
      {/* Performance indicator and focus mode toggle */}
      <div className="absolute top-2 right-2 z-10 flex items-center gap-2">
        {isLargeDocument && (
          <Badge 
            variant="outline" 
            className="text-xs bg-background/95 backdrop-blur-sm"
            title={`Performance mode: ${performanceMetrics.characterCount.toLocaleString()} chars, ${performanceMetrics.lineCount.toLocaleString()} lines`}
          >
            Perf Mode
          </Badge>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onFocusModeToggle?.(true)}
          className="text-muted-foreground hover:text-foreground"
          title="Enter Focus Mode (Esc to exit)"
        >
          <Maximize className="w-4 h-4" />
        </Button>
        
        {/* Real-time indicator */}
        {enableRealtime && (
          <>
            <div className="h-4 w-px bg-border mx-2" />
            <RealtimeIndicator
              isConnected={isRealtimeConnected}
              collaborators={collaborators}
            />
          </>
        )}
        
        {/* Complete Chapter Button */}
        {showCompleteChapter && chapterId && (
          <>
            <div className="h-4 w-px bg-border mx-2" />
            <Button
              variant="default"
              size="sm"
              onClick={completeChapter}
              disabled={isCompleting}
              className="text-xs"
              title="Mark this chapter as complete"
            >
              <CheckCircle className="w-4 h-4 mr-1" />
              {isCompleting ? 'Completing...' : 'Complete Chapter'}
            </Button>
          </>
        )}
      </div>
      
      {showToolbar && (
        <EnhancedFormattingToolbar 
          onFormat={handleFormat}
          onUndo={() => editorRef.current?.trigger('keyboard', 'undo', null)}
          onRedo={() => editorRef.current?.trigger('keyboard', 'redo', null)}
          canUndo={true}
          canRedo={true}
        />
      )}
      
      <div className="relative" style={{ height: showToolbar ? `calc(${height} - 48px)` : height }}>
        {/* AI Suggestions button - circular in top right of editor */}
        {showAISuggestions && editorSettings.aiSuggestions && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-4 right-4 z-20 h-8 w-8 rounded-full shadow-md bg-background/95 hover:bg-accent"
            onClick={() => setShowAISuggestionsPanel(!showAISuggestionsPanel)}
            title="AI Suggestions"
          >
            <Lightbulb className={`h-4 w-4 ${showAISuggestionsPanel ? 'text-primary' : 'text-muted-foreground'}`} />
          </Button>
        )}
        
        <Editor
          defaultLanguage="markdown"
          value={editorValue}
          onChange={handleEditorChange}
          onMount={handleEditorDidMount}
          theme="bookscribe-theme"
          options={{
            readOnly,
            automaticLayout: true,
          }}
        />
        
        {showSelectionMenuState && showSelectionMenu && selectedText && (
          <SelectionMenu 
            selectedText={selectedText}
            position={selectionPosition}
            onClose={() => setShowSelectionMenuState(false)}
            onApplyEdit={(newText) => {
              if (!editorRef.current) return;
              const selection = editorRef.current.getSelection();
              if (!selection) return;
              editorRef.current.executeEdits('apply-edit', [{
                range: selection,
                text: newText
              }]);
              setShowSelectionMenuState(false);
            }}
          />
        )}
        
        {showAISuggestionsPanel && showAISuggestions && editorSettings.aiSuggestions && (
          <AiAssistantPopup 
            content={content}
            cursorPosition={cursorPosition}
            onInsertSuggestion={(text) => {
              // Insert suggestion at cursor position
              const editor = editorRef.current;
              if (editor) {
                const position = editor.getPosition();
                if (position) {
                  editor.executeEdits('ai-suggestion', [{
                    range: {
                      startLineNumber: position.lineNumber,
                      startColumn: position.column,
                      endLineNumber: position.lineNumber,
                      endColumn: position.column
                    },
                    text: text
                  }]);
                }
              }
            }}
            onClose={() => setShowAISuggestionsPanel(false)}
            visible={showAISuggestionsPanel}
          />
        )}
        
        {mode === 'advanced' && suggestions.length > 0 && editorSettings.aiSuggestions && (
          <Card className="absolute top-4 right-4 w-80 max-h-96 overflow-y-auto">
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold flex items-center gap-2">
                  <Lightbulb className="h-4 w-4" />
                  Writing Suggestions
                </h3>
                <Badge variant="outline">{suggestions.length}</Badge>
              </div>
              
              <div className="space-y-2">
                {suggestions.map((suggestion) => (
                  <div
                    key={suggestion.id}
                    className={`p-3 rounded-lg border ${
                      suggestion.severity === 'error'
                        ? 'border-destructive bg-destructive/10'
                        : suggestion.severity === 'warning'
                        ? 'border-yellow-500 bg-yellow-500/10'
                        : 'border-blue-500 bg-blue-500/10'
                    }`}
                  >
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1">
                        <Badge
                          variant={suggestion.severity === 'error' ? 'destructive' : 'secondary'}
                          className="mb-1"
                        >
                          {suggestion.type}
                        </Badge>
                        <p className="text-sm">{suggestion.message}</p>
                        {suggestion.explanation && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {suggestion.explanation}
                          </p>
                        )}
                      </div>
                      <div className="flex gap-1">
                        {suggestion.replacement && (
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-6 w-6"
                            onClick={() => applySuggestion(suggestion)}
                          >
                            <CheckCircle className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-6 w-6"
                          onClick={() => dismissSuggestion(suggestion.id)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        )}
      </div>
      
      {showStats && (
        <div className="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <WritingStats content={editorValue} />
        </div>
      )}
      
      {mode === 'advanced' && isAnalyzing && (
        <div className="absolute bottom-4 left-4">
          <Badge variant="secondary" className="animate-pulse">
            Analyzing content...
          </Badge>
        </div>
      )}
      
      {/* Auto-save indicator */}
      {editorSettings.autoSave && hasUnsavedChanges && (
        <div className="absolute bottom-4 right-4">
          <Badge variant="secondary" className="animate-pulse">
            <Save className="w-3 h-3 mr-1" />
            Auto-saving in {editorSettings.autoSaveInterval}s...
          </Badge>
        </div>
      )}
    </div>
  );
}

// Export wrapped component with error boundary
export function UnifiedMonacoEditorWithErrorBoundary(props: UnifiedMonacoEditorProps) {
  return (
    <ComponentErrorBoundary componentName="Monaco Editor">
      <UnifiedMonacoEditor {...props} />
    </ComponentErrorBoundary>
  );
}