import { cn } from '@/lib/utils';

interface ThemePreviewColorsProps {
  className?: string;
  themeClass?: string;
}

/**
 * Component to preview theme colors without inline styles
 * Uses a wrapper div with the theme class to show colors
 */
export function ThemePreviewColors({ className, themeClass }: ThemePreviewColorsProps) {
  return (
    <div className={cn(themeClass, className)}>
      <div className="rounded-lg p-4 mb-3 border bg-background border-accent/40">
        <div className="text-xs font-medium mb-2 text-foreground">
          Sample Text
        </div>
        <div className="flex gap-1">
          <div className="w-3 h-3 rounded-full bg-primary" />
          <div className="w-3 h-3 rounded-full bg-card" />
          <div className="w-3 h-3 rounded-full bg-foreground/60" />
        </div>
      </div>
    </div>
  );
}