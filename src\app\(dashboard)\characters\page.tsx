import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { CharacterManager } from '@/components/characters/character-manager'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Users } from 'lucide-react'

export default async function CharactersPage() {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect('/login')
  }
  
  const { data: projects } = await supabase
    .from('projects')
    .select('id, title')
    .eq('user_id', user.id)
    .order('title')
  
  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Character Manager</h1>
        <p className="text-muted-foreground">
          Manage all your characters across projects and series
        </p>
      </div>
      
      {projects && projects.length > 0 ? (
        <CharacterManager projects={projects} userId={user.id} />
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              No Projects Yet
            </CardTitle>
            <CardDescription>
              Create a project first to start managing characters
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Characters are managed within the context of your projects. Once you create
              a project, you'll be able to create and manage characters here.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}