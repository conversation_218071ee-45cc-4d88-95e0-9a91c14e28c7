# API Security and Performance Fixes - Action Plan

## Critical Security Fixes (Do First)

### 1. Fix Missing Authentication on Test Routes

**Files to modify:**
- `/src/app/api/debug/route.ts`
- `/src/app/api/hello/route.ts`
- `/src/app/api/test-simple/route.ts`
- `/src/app/api/test-auth/route.ts`
- `/src/app/api/sentry-example-api/route.ts`

**Implementation:**
```typescript
import { NextResponse } from 'next/server';
import { config } from '@/lib/config';

export async function GET() {
  // Block all test routes in production
  if (config.isProduction) {
    return NextResponse.json({ error: 'Not found' }, { status: 404 });
  }
  
  // For development, still require authentication
  const authResult = await authenticateUser();
  if (!authResult.success || !authResult.user) {
    return authResult.response!;
  }
  
  // ... rest of the route logic
}
```

### 2. Add Stripe Webhook Signature Verification

**File:** `/src/app/api/stripe/webhook/route.ts`

**Required changes:**
```typescript
import { headers } from 'next/headers';

export async function POST(request: NextRequest) {
  const body = await request.text();
  const signature = headers().get('stripe-signature');
  
  if (!signature) {
    return NextResponse.json({ error: 'No signature' }, { status: 400 });
  }
  
  let event: Stripe.Event;
  
  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      config.stripe.webhookSecret
    );
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return NextResponse.json(
      { error: 'Invalid signature' }, 
      { status: 400 }
    );
  }
  
  // Process verified webhook...
}
```

### 3. Add Missing Rate Limiting

**Files to modify:**
- `/src/app/api/profiles/user/route.ts`
- `/src/app/api/characters/route.ts`
- `/src/app/api/story-bible/route.ts`
- All analytics endpoints

**Template to add:**
```typescript
export async function GET(request: NextRequest) {
  try {
    // Add rate limiting
    const clientIP = getClientIP(request);
    const rateLimitResult = generalLimiter.check(30, clientIP); // 30 requests per 15 min
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }
    
    // Continue with authentication...
    const authResult = await authenticateUser();
    // ... rest of the route
  }
}
```

## High Priority Performance Fixes

### 4. Implement Database Transactions

**Create Supabase RPC functions:**

```sql
-- File: /supabase/migrations/add_transaction_functions.sql

-- Function for atomic story structure generation
CREATE OR REPLACE FUNCTION generate_story_structure_atomic(
  p_project_id uuid,
  p_user_id uuid,
  p_story_arcs jsonb,
  p_characters jsonb,
  p_chapters jsonb,
  p_story_bible jsonb
) RETURNS jsonb AS $$
DECLARE
  v_result jsonb;
BEGIN
  -- Delete existing data
  DELETE FROM story_arcs WHERE project_id = p_project_id;
  DELETE FROM characters WHERE project_id = p_project_id;
  DELETE FROM chapters WHERE project_id = p_project_id;
  DELETE FROM story_bible WHERE project_id = p_project_id;
  
  -- Insert story arcs
  INSERT INTO story_arcs (project_id, act_number, description, key_events)
  SELECT 
    p_project_id,
    (value->>'number')::int,
    value->>'description',
    COALESCE(value->'keyEvents', '[]'::jsonb)
  FROM jsonb_array_elements(p_story_arcs);
  
  -- Insert characters
  INSERT INTO characters (project_id, name, role, description, backstory, personality_traits)
  SELECT 
    p_project_id,
    value->>'name',
    value->>'role',
    value->>'description',
    COALESCE(value->>'backstory', ''),
    COALESCE(value->'personality', '{}'::jsonb)
  FROM jsonb_array_elements(p_characters);
  
  -- Insert chapters
  INSERT INTO chapters (project_id, chapter_number, title, target_word_count, outline, status)
  SELECT 
    p_project_id,
    (value->>'number')::int,
    value->>'title',
    COALESCE((value->>'wordCountTarget')::int, 4000),
    value,
    'planned'
  FROM jsonb_array_elements(p_chapters);
  
  -- Update project status
  UPDATE projects 
  SET status = 'writing', updated_at = NOW() 
  WHERE id = p_project_id AND user_id = p_user_id;
  
  v_result := jsonb_build_object(
    'success', true,
    'message', 'Story structure generated successfully'
  );
  
  RETURN v_result;
EXCEPTION
  WHEN OTHERS THEN
    -- Transaction will automatically rollback
    RETURN jsonb_build_object(
      'success', false,
      'error', SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION generate_story_structure_atomic TO authenticated;
```

**Update API route to use transaction:**
```typescript
// In /src/app/api/agents/generate/route.ts
const { data, error } = await supabase.rpc('generate_story_structure_atomic', {
  p_project_id: projectId,
  p_user_id: user.id,
  p_story_arcs: bookContext.storyStructure?.acts || [],
  p_characters: allCharacters,
  p_chapters: bookContext.chapterOutlines?.chapters || [],
  p_story_bible: storyBibleData
});

if (error) {
  throw new Error(`Transaction failed: ${error.message}`);
}
```

### 5. Add Response Caching

**Create cache utility:**
```typescript
// File: /src/lib/cache/api-cache.ts
import { cacheKeys, getServerCache, setServerCache } from '@/lib/cache/server';

export function withCache<T>(
  keyGenerator: (...args: any[]) => string,
  ttlMs: number = 5 * 60 * 1000 // 5 minutes default
) {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const cacheKey = keyGenerator(...args);
      
      // Check cache
      const cached = getServerCache(cacheKey);
      if (cached) {
        return NextResponse.json(cached);
      }
      
      // Call original method
      const result = await originalMethod.apply(this, args);
      
      // Cache successful responses
      if (result.status === 200) {
        const data = await result.json();
        setServerCache(cacheKey, data, ttlMs);
        return NextResponse.json(data);
      }
      
      return result;
    };
    
    return descriptor;
  };
}
```

### 6. Optimize Database Queries

**Create database views:**
```sql
-- File: /supabase/migrations/add_optimized_views.sql

-- Projects with all related counts
CREATE OR REPLACE VIEW projects_with_stats AS
SELECT 
  p.*,
  COUNT(DISTINCT c.id) as character_count,
  COUNT(DISTINCT ch.id) as chapter_count,
  COUNT(DISTINCT ch.id) FILTER (WHERE ch.status = 'complete') as completed_chapters,
  COALESCE(SUM(ch.actual_word_count), 0) as total_word_count
FROM projects p
LEFT JOIN characters c ON p.id = c.project_id
LEFT JOIN chapters ch ON p.id = ch.project_id
GROUP BY p.id;

-- Grant access
GRANT SELECT ON projects_with_stats TO authenticated;
```

## Standard Response Format Implementation

**Create response utility:**
```typescript
// File: /src/lib/api/response.ts
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code: string;
    details?: any;
  };
  meta?: {
    timestamp: number;
    requestId: string;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      hasMore: boolean;
    };
  };
}

export function createApiResponse<T>(
  data?: T,
  error?: { message: string; code: string; details?: any }
): NextResponse<ApiResponse<T>> {
  const response: ApiResponse<T> = {
    success: !error,
    meta: {
      timestamp: Date.now(),
      requestId: crypto.randomUUID(),
    },
  };
  
  if (error) {
    response.error = error;
    return NextResponse.json(response, { status: 400 });
  }
  
  response.data = data;
  return NextResponse.json(response);
}

export function createPaginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number
): NextResponse<ApiResponse<T[]>> {
  return createApiResponse(data, undefined).json({
    ...response,
    meta: {
      ...response.meta,
      pagination: {
        page,
        limit,
        total,
        hasMore: page * limit < total,
      },
    },
  });
}
```

## Request Logging Middleware

**Create logging middleware:**
```typescript
// File: /src/lib/api/logging.ts
import { NextRequest } from 'next/server';

interface LogContext {
  requestId: string;
  userId?: string;
  method: string;
  path: string;
  ip: string;
  userAgent?: string;
}

export function withLogging(
  handler: (req: NextRequest, context: LogContext) => Promise<NextResponse>
) {
  return async (req: NextRequest) => {
    const startTime = Date.now();
    const requestId = crypto.randomUUID();
    
    const context: LogContext = {
      requestId,
      method: req.method,
      path: req.nextUrl.pathname,
      ip: getClientIP(req),
      userAgent: req.headers.get('user-agent') || undefined,
    };
    
    try {
      const response = await handler(req, context);
      
      // Log success
      console.log({
        ...context,
        status: response.status,
        duration: Date.now() - startTime,
        type: 'api_request',
      });
      
      // Add request ID to response headers
      response.headers.set('X-Request-ID', requestId);
      
      return response;
    } catch (error) {
      // Log error
      console.error({
        ...context,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        duration: Date.now() - startTime,
        type: 'api_error',
      });
      
      throw error;
    }
  };
}
```

## Testing Checklist

After implementing fixes:

- [ ] Test all endpoints with authentication
- [ ] Verify Stripe webhooks with test events
- [ ] Test rate limiting with burst requests
- [ ] Verify transaction rollback on errors
- [ ] Check cache hit rates
- [ ] Monitor query performance
- [ ] Validate response formats
- [ ] Review logs for errors

## Deployment Steps

1. **Database migrations first**
   ```bash
   supabase db push
   ```

2. **Deploy API updates**
   ```bash
   npm run build
   npm run test
   git push origin main
   ```

3. **Monitor for errors**
   - Check Sentry for new errors
   - Monitor API response times
   - Watch for 500 errors

4. **Gradual rollout**
   - Deploy to staging first
   - Test all critical paths
   - Deploy to production with monitoring