import { PDFDocument, rgb, StandardFonts } from 'pdf-lib'
import { logger } from './logger'

/**
 * Service for adding watermarks to PDF documents
 */
export class WatermarkService {
  private static instance: WatermarkService
  
  private constructor() {}
  
  static getInstance(): WatermarkService {
    if (!WatermarkService.instance) {
      WatermarkService.instance = new WatermarkService()
    }
    return WatermarkService.instance
  }

  /**
   * Add watermark to PDF buffer
   */
  async addWatermark(
    pdfBuffer: ArrayBuffer,
    options: {
      text?: string
      logoUrl?: string
      opacity?: number
      position?: 'header' | 'footer' | 'diagonal'
    } = {}
  ): Promise<ArrayBuffer> {
    const {
      text = 'Created with BookScribe AI - Free Plan',
      logoUrl,
      opacity = 0.3,
      position = 'footer'
    } = options

    try {
      // Load the existing PDF
      const pdfDoc = await PDFDocument.load(pdfBuffer)
      const pages = pdfDoc.getPages()
      
      // Embed font
      const font = await pdfDoc.embedFont(StandardFonts.Helvetica)
      
      // Add watermark to each page
      for (const page of pages) {
        const { width, height } = page.getSize()
        
        if (position === 'diagonal') {
          // Diagonal watermark across the page
          const fontSize = 40
          const textWidth = font.widthOfTextAtSize(text, fontSize)
          const textHeight = font.heightAtSize(fontSize)
          
          page.drawText(text, {
            x: (width - textWidth) / 2,
            y: (height - textHeight) / 2,
            size: fontSize,
            font,
            color: rgb(0.5, 0.5, 0.5),
            opacity: opacity,
            rotate: {
              angle: -45,
              origin: { x: width / 2, y: height / 2 }
            }
          })
        } else if (position === 'header') {
          // Header watermark
          const fontSize = 12
          const textWidth = font.widthOfTextAtSize(text, fontSize)
          
          page.drawText(text, {
            x: (width - textWidth) / 2,
            y: height - 30,
            size: fontSize,
            font,
            color: rgb(0.5, 0.5, 0.5),
            opacity: opacity
          })
        } else {
          // Footer watermark (default)
          const fontSize = 10
          const textWidth = font.widthOfTextAtSize(text, fontSize)
          
          page.drawText(text, {
            x: (width - textWidth) / 2,
            y: 20,
            size: fontSize,
            font,
            color: rgb(0.5, 0.5, 0.5),
            opacity: opacity
          })
        }
        
        // Add logo if provided
        if (logoUrl) {
          try {
            // In a real implementation, you would fetch and embed the logo
            // For now, we'll skip logo implementation
            logger.info('Logo watermark not implemented yet', { logoUrl })
          } catch (error) {
            logger.error('Failed to add logo watermark', error)
          }
        }
      }
      
      // Save the modified PDF
      const modifiedPdfBytes = await pdfDoc.save()
      return modifiedPdfBytes.buffer
    } catch (error) {
      logger.error('Failed to add watermark to PDF', error)
      // Return original PDF if watermarking fails
      return pdfBuffer
    }
  }

  /**
   * Check if watermark is required for user tier
   */
  isWatermarkRequired(tierName: string): boolean {
    return tierName === 'Starter' || tierName === 'starter'
  }

  /**
   * Get watermark text based on tier
   */
  getWatermarkText(tierName: string): string {
    if (tierName === 'Starter' || tierName === 'starter') {
      return 'Created with BookScribe AI - Free Plan'
    }
    return '' // No watermark for paid tiers
  }

  /**
   * Apply watermark based on user tier
   */
  async applyWatermarkIfRequired(
    pdfBuffer: ArrayBuffer,
    userTier: string
  ): Promise<ArrayBuffer> {
    if (!this.isWatermarkRequired(userTier)) {
      return pdfBuffer
    }
    
    return this.addWatermark(pdfBuffer, {
      text: this.getWatermarkText(userTier),
      position: 'footer',
      opacity: 0.4
    })
  }
}

export const watermarkService = WatermarkService.getInstance()