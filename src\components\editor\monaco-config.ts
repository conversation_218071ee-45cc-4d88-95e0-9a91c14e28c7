import { loader } from '@monaco-editor/react';
import { logger } from '@/lib/services/logger';
import * as monaco from 'monaco-editor';
import { configureBasicMonacoWorkers } from '@/lib/monaco-workers';
import { updateMonacoTheme } from '@/lib/monaco-theme-generator';

import type { Monaco } from '@monaco-editor/react';
import { editor } from 'monaco-editor';
import { TypographySettings, EditorSettings, fontFamilyMap, textSizeMap, lineHeightMap, letterSpacingMap } from '@/lib/settings/settings-types';

// Performance-optimized Monaco configuration using local installation
export function configureMonacoLoader() {
  // Only configure in browser environment
  if (typeof window === 'undefined') return;

  try {
    // Configure Monaco workers first
    configureBasicMonacoWorkers();

    // Configure Monaco to use the local installation
    loader.config({
      monaco: monaco
    });

    logger.info('Monaco configured to use local installation with workers');

    // Initialize Monaco with local installation
    loader.init().then((monacoInstance) => {
      logger.info('Monaco loaded successfully from local installation');
      configureMonacoPerformance(monacoInstance);
    }).catch((error) => {
      logger.error('Failed to initialize Monaco from local installation:', error);
    });
  } catch (error) {
    logger.error('Monaco configuration failed:', error);
  }
}

// Configure Monaco for optimal performance
export function configureMonacoPerformance(monaco: Monaco) {
  // Apply dynamic theme based on current CSS variables
  updateMonacoTheme(monaco, 'bookscribe-theme');
  
  // Also register legacy themes for backward compatibility
  monaco.editor.defineTheme('literary-dark-spellcheck', {
    base: 'vs-dark',
    inherit: true,
    rules: [
      { token: 'spellcheck.error', foreground: 'ff6b6b', fontStyle: 'underline' }
    ],
    colors: {}
  });

  // Configure language features for better performance
  monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
    target: monaco.languages.typescript.ScriptTarget.ES2015,
    allowNonTsExtensions: true,
    moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
    module: monaco.languages.typescript.ModuleKind.CommonJS,
    noEmit: true,
    typeRoots: ['node_modules/@types']
  });

  // Disable features not needed for prose writing
  monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
    noSemanticValidation: true,
    noSyntaxValidation: true,
    noSuggestionDiagnostics: true
  });

  // Configure markdown language for better prose writing
  monaco.languages.setLanguageConfiguration('markdown', {
    wordPattern: /(-?\d*\.\d\w*)|([^\`\~\!\@\#\%\^\&\*\(\)\-\=\+\[\{\]\}\\\|\;\:\'\"\,\.\<\>\/\?\s]+)/g,
    brackets: [
      ['*', '*'],
      ['_', '_'],
      ['`', '`'],
      ['**', '**'],
      ['__', '__']
    ],
    autoClosingPairs: [
      { open: '*', close: '*' },
      { open: '_', close: '_' },
      { open: '`', close: '`' },
      { open: '"', close: '"' },
      { open: "'", close: "'" },
      { open: '(', close: ')' },
      { open: '[', close: ']' }
    ],
    surroundingPairs: [
      { open: '*', close: '*' },
      { open: '_', close: '_' },
      { open: '`', close: '`' },
      { open: '"', close: '"' },
      { open: "'", close: "'" },
      { open: '(', close: ')' },
      { open: '[', close: ']' }
    ]
  });

  // Set default theme to the dynamic one
  monaco.editor.setTheme('bookscribe-theme');
  
  // Listen for theme changes
  if (typeof window !== 'undefined') {
    const observer = new MutationObserver(() => {
      updateMonacoTheme(monaco, 'bookscribe-theme');
    });
    
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });
  }
}

// Optimized editor options for large documents
export function getOptimizedEditorOptions(
  typography?: Partial<TypographySettings>,
  editorSettings?: Partial<EditorSettings>
): editor.IStandaloneEditorConstructionOptions {
  // Get font settings from typography or use defaults
  const editorFont = typography?.editorFont 
    ? fontFamilyMap[typography.editorFont]
    : fontFamilyMap['jetbrains-mono'];
  
  // Get font size based on text size setting
  let fontSize = 14; // default
  if (typography?.textSize === 'custom' && typography.customTextSize) {
    fontSize = typography.customTextSize;
  } else if (typography?.textSize && typography.textSize !== 'custom') {
    const sizeConfig = textSizeMap[typography.textSize];
    if (sizeConfig) {
      fontSize = parseInt(sizeConfig.editor);
    }
  }
  
  // Calculate line height based on line height setting
  const lineHeightMultiplier = typography?.lineHeight 
    ? lineHeightMap[typography.lineHeight]
    : lineHeightMap.normal;
  const lineHeight = Math.round(fontSize * lineHeightMultiplier);
  
  // Get letter spacing from settings
  const letterSpacingStr = typography?.letterSpacing 
    ? letterSpacingMap[typography.letterSpacing]
    : letterSpacingMap.normal;
  // Convert em to pixels (approximation)
  const letterSpacing = parseFloat(letterSpacingStr) * fontSize;

  return {
    // Performance optimizations
    automaticLayout: true,
    minimap: { enabled: false }, // Disable minimap for better performance
    scrollBeyondLastLine: false,
    renderWhitespace: 'none',
    renderControlCharacters: false,
    renderLineHighlight: 'none',
    
    // Prose-specific optimizations with user settings
    wordWrap: editorSettings?.wordWrap ? 'bounded' : 'off',
    wordWrapColumn: 80,
    lineNumbers: editorSettings?.showLineNumbers ? 'on' : 'off',
    glyphMargin: false,
    folding: false,
    lineDecorationsWidth: editorSettings?.showLineNumbers ? 10 : 0,
    lineNumbersMinChars: editorSettings?.showLineNumbers ? 3 : 0,
    overviewRulerLanes: 0,
    hideCursorInOverviewRuler: true,
    tabSize: editorSettings?.tabSize || 2,
    
    // Disable features that slow down large documents
    quickSuggestions: false,
    suggestOnTriggerCharacters: false,
    acceptSuggestionOnCommitCharacter: false,
    tabCompletion: 'off',
    wordBasedSuggestions: 'off',
    parameterHints: { enabled: false },
    hover: { enabled: false },
    
    // Font and typography optimizations - now dynamic!
    fontFamily: editorFont,
    fontSize: fontSize,
    lineHeight: lineHeight,
    letterSpacing: letterSpacing,
    
    // Scrolling optimizations
    scrollbar: {
      vertical: 'visible',
      horizontal: 'hidden',
      verticalSliderSize: 8,
      alwaysConsumeMouseWheel: false
    },
    
    // Smooth scrolling and cursor animations
    smoothScrolling: true,
    cursorBlinking: 'phase',
    cursorSmoothCaretAnimation: 'on',
    
    // Advanced performance settings
    accessibilitySupport: 'off', // Disable unless needed
    mouseWheelZoom: false,
    multiCursorModifier: 'ctrlCmd',
    
    // Better text rendering
    padding: { top: 16, bottom: 16 },
    guides: {
      indentation: false,
      highlightActiveIndentation: false
    },
    
    // Additional editor-specific settings
    // Note: Browser spell check is not directly supported by Monaco,
    // but we can enable contextmenu for spell check suggestions
    contextmenu: editorSettings?.spellCheck !== false,
  };
}

// Optimized editor options for focus mode
export function getFocusModeEditorOptions(
  typography?: Partial<TypographySettings>,
  editorSettings?: Partial<EditorSettings>
): editor.IStandaloneEditorConstructionOptions {
  const baseOptions = getOptimizedEditorOptions(typography, editorSettings);
  
  // Apply focus mode specific adjustments
  let focusFontSize = 18; // default for focus mode
  if (typography?.textSize === 'custom' && typography.customTextSize) {
    focusFontSize = typography.customTextSize + 4; // Slightly larger in focus mode
  } else if (typography?.textSize && typography.textSize !== 'custom') {
    const sizeConfig = textSizeMap[typography.textSize];
    if (sizeConfig) {
      focusFontSize = parseInt(sizeConfig.editor) + 4;
    }
  }
    
  const lineHeightMultiplier = typography?.lineHeight 
    ? lineHeightMap[typography.lineHeight]
    : lineHeightMap.normal;
  const focusLineHeight = Math.round(focusFontSize * lineHeightMultiplier);
  
  return {
    ...baseOptions,
    wordWrapColumn: 100,
    fontSize: focusFontSize,
    lineHeight: focusLineHeight,
    letterSpacing: (baseOptions.letterSpacing || 0) + 0.2, // Slightly more spacing in focus mode
    padding: { top: 40, bottom: 40 },
    scrollBeyondLastLine: true,
    scrollbar: {
      ...baseOptions.scrollbar,
      vertical: 'auto',
      verticalSliderSize: 6,
    }
  };
}