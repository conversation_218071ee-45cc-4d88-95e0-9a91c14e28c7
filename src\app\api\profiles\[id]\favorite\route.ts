import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { supabase } from '@/lib/db/client';
import { logger } from '@/lib/services/logger';
import { authenticateUser } from '@/lib/api/auth-helpers';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, error: authError } = await authenticateUser();
    if (authError) return authError;

    const { id: profileId } = await params;

    // Check if profile exists
    const { data: profile, error: profileError } = await supabase
      .from('selection_profiles')
      .select('id, name')
      .eq('id', profileId)
      .single();

    if (profileError) {
      if (profileError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
      }
      throw profileError;
    }

    // Get user preferences
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('preferences')
      .eq('id', user.id)
      .single();

    if (userError) throw userError;

    const preferences = userData.preferences || {};
    const favorites = preferences.favoriteProfiles || [];

    // Check if already favorited
    if (favorites.includes(profileId)) {
      return NextResponse.json({ 
        error: 'Profile already favorited',
        isFavorited: true 
      }, { status: 400 });
    }

    // Add to favorites
    const updatedFavorites = [...favorites, profileId];
    const { error: updateError } = await supabase
      .from('users')
      .update({
        preferences: {
          ...preferences,
          favoriteProfiles: updatedFavorites
        }
      })
      .eq('id', user.id);

    if (updateError) throw updateError;

    // Track analytics event
    try {
      await supabase
        .from('selection_analytics')
        .insert({
          user_id: user.id,
          selection_profile_id: profileId,
          event_type: 'profile_favorited',
          selection_data: {
            profileName: profile.name
          }
        });
    } catch (analyticsError) {
      logger.warn('Failed to track profile favoriting analytics:', analyticsError);
    }

    return NextResponse.json({ 
      success: true,
      isFavorited: true,
      totalFavorites: updatedFavorites.length
    });
  } catch (error) {
    logger.error('Favorite profile API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, error: authError } = await authenticateUser();
    if (authError) return authError;

    const { id: profileId } = await params;

    // Get user preferences
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('preferences')
      .eq('id', user.id)
      .single();

    if (userError) throw userError;

    const preferences = userData.preferences || {};
    const favorites = preferences.favoriteProfiles || [];

    // Remove from favorites
    const updatedFavorites = favorites.filter((id: string) => id !== profileId);
    const { error: updateError } = await supabase
      .from('users')
      .update({
        preferences: {
          ...preferences,
          favoriteProfiles: updatedFavorites
        }
      })
      .eq('id', user.id);

    if (updateError) throw updateError;

    // Track analytics event
    try {
      await supabase
        .from('selection_analytics')
        .insert({
          user_id: user.id,
          selection_profile_id: profileId,
          event_type: 'profile_unfavorited'
        });
    } catch (analyticsError) {
      logger.warn('Failed to track profile unfavoriting analytics:', analyticsError);
    }

    return NextResponse.json({ 
      success: true,
      isFavorited: false,
      totalFavorites: updatedFavorites.length
    });
  } catch (error) {
    logger.error('Unfavorite profile API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, error: authError } = await authenticateUser();
    if (authError) return authError;

    const { id: profileId } = await params;

    // Get user preferences
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('preferences')
      .eq('id', user.id)
      .single();

    if (userError) throw userError;

    const preferences = userData.preferences || {};
    const favorites = preferences.favoriteProfiles || [];

    return NextResponse.json({ 
      isFavorited: favorites.includes(profileId),
      totalFavorites: favorites.length
    });
  } catch (error) {
    logger.error('Check favorite API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}