'use client'

import { PanelProps } from '@/lib/panels'
import { AiChatAssistant } from './ai-chat-assistant'
import { EnhancedDocumentNavigator } from './enhanced-document-navigator'
import { KnowledgeBaseEditor } from './knowledge-base-editor'
import { StoryBiblePanel } from './story-bible-panel'
import { VoiceAnalysisPanelEnhanced } from './voice-analysis-panel-enhanced'

// Adapter for AI Chat Assistant
export function AiChatPanelAdapter(props: PanelProps) {
  return (
    <AiChatAssistant
      projectId={props.projectId}
      chapterId={props.chapterId}
    />
  )
}

// Adapter for Document Navigator
export function ChaptersPanelAdapter(props: PanelProps) {
  return (
    <EnhancedDocumentNavigator
      projectId={props.projectId}
      currentChapterId={props.chapterId}
      content={props.content || ''}
      onChapterSelect={(id) => props.onAction('selectChapter', { chapterId: id })}
      onCreateChapter={() => props.onAction('createChapter')}
    />
  )
}

// Adapter for Knowledge Panel
export function KnowledgePanelAdapter(props: PanelProps) {
  return (
    <KnowledgeBaseEditor
      projectId={props.projectId}
      chapterId={props.chapterId}
      selectedText={props.selectedText}
    />
  )
}

// Adapter for Story Bible
export function StoryBiblePanelAdapter(props: PanelProps) {
  return (
    <StoryBiblePanel
      projectId={props.projectId}
      onClose={props.onClose}
    />
  )
}

// Adapter for Voice Analysis
export function VoiceAnalysisPanelAdapter(props: PanelProps) {
  return (
    <VoiceAnalysisPanelEnhanced
      content={props.content || ''}
      projectId={props.projectId}
      onClose={props.onClose}
    />
  )
}