import { SupabaseClient } from '@supabase/supabase-js';
import { BaseRepository, RepositoryResult, RepositoryListResult } from './base-repository';
import { Database } from '@/lib/db/types';

type Character = Database['public']['Tables']['characters']['Row'];
type CharacterInsert = Database['public']['Tables']['characters']['Insert'];
type CharacterUpdate = Database['public']['Tables']['characters']['Update'];

export interface CharacterWithRelationships extends Character {
  relationships?: Array<{
    character: Character;
    relationship_type: string;
  }>;
}

export class CharacterRepository extends BaseRepository<Character> {
  constructor(supabase: SupabaseClient) {
    super(supabase, 'characters');
  }
  
  /**
   * Find all characters for a project
   */
  async findByProjectId(projectId: string): Promise<RepositoryListResult<Character>> {
    return this.executeQuery<Character[]>((query) =>
      query
        .select(this.selectColumns)
        .eq('project_id', projectId)
        .is('deleted_at', null)
        .order('role', { ascending: true })
        .order('name', { ascending: true })
    );
  }
  
  /**
   * Find characters by role
   */
  async findByRole(
    projectId: string,
    role: 'protagonist' | 'antagonist' | 'supporting' | 'minor'
  ): Promise<RepositoryListResult<Character>> {
    return this.findAll({
      project_id: projectId,
      role
    });
  }
  
  /**
   * Find character with relationships
   */
  async findWithRelationships(
    characterId: string
  ): Promise<RepositoryResult<CharacterWithRelationships>> {
    // First get the character
    const characterResult = await this.findById(characterId);
    if (characterResult.error || !characterResult.data) {
      return characterResult;
    }
    
    // Then get relationships
    const relationshipsResult = await this.getCharacterRelationships(characterId);
    if (relationshipsResult.error) {
      return { error: relationshipsResult.error };
    }
    
    return {
      data: {
        ...characterResult.data,
        relationships: relationshipsResult.data
      }
    };
  }
  
  /**
   * Get character relationships
   */
  private async getCharacterRelationships(characterId: string): Promise<RepositoryListResult<{
    character: Character;
    relationship_type: string;
  }>> {
    const character = await this.findById(characterId);
    if (character.error || !character.data) {
      return { error: character.error || new Error('Character not found') };
    }
    
    const relationships = character.data.relationships as Array<{
      character_id: string;
      relationship_type: string;
    }> || [];
    
    if (relationships.length === 0) {
      return { data: [] };
    }
    
    // Fetch related characters
    const characterIds = relationships.map(r => r.character_id);
    const { data: relatedCharacters, error } = await this.supabase
      .from('characters')
      .select(this.selectColumns)
      .in('id', characterIds);
      
    if (error) {
      return { error: new Error(error.message) };
    }
    
    // Map relationships with character data
    const mappedRelationships = relationships.map(rel => {
      const character = relatedCharacters?.find(c => c.id === rel.character_id);
      return character ? {
        character,
        relationship_type: rel.relationship_type
      } : null;
    }).filter(Boolean) as Array<{ character: Character; relationship_type: string }>;
    
    return { data: mappedRelationships };
  }
  
  /**
   * Update character relationships
   */
  async updateRelationships(
    characterId: string,
    relationships: Array<{
      character_id: string;
      relationship_type: string;
    }>
  ): Promise<RepositoryResult<Character>> {
    return this.update(characterId, {
      relationships,
      updated_at: new Date().toISOString()
    });
  }
  
  /**
   * Search characters by name or description
   */
  async searchCharacters(
    projectId: string,
    searchTerm: string
  ): Promise<RepositoryListResult<Character>> {
    return this.executeQuery<Character[]>((query) =>
      query
        .select(this.selectColumns)
        .eq('project_id', projectId)
        .is('deleted_at', null)
        .or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,backstory.ilike.%${searchTerm}%`)
        .order('name', { ascending: true })
    );
  }
  
  /**
   * Get character arc progression
   */
  async getCharacterArcProgression(
    characterId: string
  ): Promise<RepositoryResult<Array<{
    chapter_number: number;
    chapter_title: string;
    character_state: unknown;
    notes: string;
  }>>> {
    const characterResult = await this.findById(characterId);
    if (characterResult.error || !characterResult.data) {
      return { error: characterResult.error || new Error('Character not found') };
    }
    
    // Get chapters that mention this character
    const { data: chapters, error } = await this.supabase
      .from('chapters')
      .select('id, chapter_number, title, ai_notes')
      .eq('project_id', characterResult.data.project_id)
      .is('deleted_at', null)
      .order('chapter_number', { ascending: true });
      
    if (error) {
      return { error: new Error(error.message) };
    }
    
    // Extract character progression from AI notes
    const progression = (chapters || []).map(chapter => {
      const aiNotes = chapter.ai_notes as Record<string, unknown> || {};
      const characterStates = aiNotes.characterStates || {};
      const characterState = characterStates[characterResult.data!.name] || null;
      
      return characterState ? {
        chapter_number: chapter.chapter_number,
        chapter_title: chapter.title,
        character_state: characterState,
        notes: aiNotes.characterNotes?.[characterResult.data!.name] || ''
      } : null;
    }).filter(Boolean) as Array<{
      chapter_number: number;
      chapter_title: string;
      character_state: unknown;
      notes: string;
    }>;
    
    return { data: progression };
  }
  
  /**
   * Merge duplicate characters
   */
  async mergeCharacters(
    primaryCharacterId: string,
    duplicateCharacterId: string
  ): Promise<RepositoryResult<Character>> {
    // Get both characters
    const [primaryResult, duplicateResult] = await Promise.all([
      this.findById(primaryCharacterId),
      this.findById(duplicateCharacterId)
    ]);
    
    if (primaryResult.error || !primaryResult.data) {
      return { error: primaryResult.error || new Error('Primary character not found') };
    }
    
    if (duplicateResult.error || !duplicateResult.data) {
      return { error: duplicateResult.error || new Error('Duplicate character not found') };
    }
    
    // Ensure same project
    if (primaryResult.data.project_id !== duplicateResult.data.project_id) {
      return { error: new Error('Characters must be from the same project') };
    }
    
    // Merge data (primary takes precedence)
    const mergedData: Partial<CharacterUpdate> = {
      description: primaryResult.data.description || duplicateResult.data.description,
      backstory: primaryResult.data.backstory || duplicateResult.data.backstory,
      personality: primaryResult.data.personality || duplicateResult.data.personality,
      appearance: primaryResult.data.appearance || duplicateResult.data.appearance,
      traits: {
        ...(duplicateResult.data.traits as Record<string, unknown> || {}),
        ...(primaryResult.data.traits as Record<string, unknown> || {})
      },
      updated_at: new Date().toISOString()
    };
    
    // Update primary character
    const updateResult = await this.update(primaryCharacterId, mergedData);
    if (updateResult.error) {
      return updateResult;
    }
    
    // Soft delete duplicate
    await this.delete(duplicateCharacterId, true);
    
    return updateResult;
  }
}