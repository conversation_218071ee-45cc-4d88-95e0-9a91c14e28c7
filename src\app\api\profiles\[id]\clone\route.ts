import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { supabase } from '@/lib/db/client';
import { z } from 'zod';
import { logger } from '@/lib/services/logger';

// Validation schema for profile cloning
const cloneProfileSchema = z.object({
  userId: z.string().uuid('User ID must be a valid UUID'),
  name: z.string()
    .min(1, 'Name cannot be empty')
    .max(100, 'Name must not exceed 100 characters')
    .regex(/^[^<>'"&]+$/, 'Name contains invalid characters')
    .optional(),
  description: z.string()
    .max(500, 'Description must not exceed 500 characters')
    .regex(/^[^<>'"&]*$/, 'Description contains invalid characters')
    .optional(),
  isPublic: z.boolean().optional()
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: profileId } = await params;
    
    // Validate profile ID format
    if (!z.string().uuid().safeParse(profileId).success) {
      return NextResponse.json({ error: 'Invalid profile ID format' }, { status: 400 });
    }
    
    const body = await request.json();
    
    // Validate request body
    const validationResult = cloneProfileSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }
    
    const { userId, name, description, isPublic } = validationResult.data;

    // Get the original profile
    const { data: originalProfile, error: fetchError } = await supabase
      .from('selection_profiles')
      .select('*')
      .eq('id', profileId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
      }
      throw fetchError;
    }

    // Create the cloned profile
    const clonedProfileData = {
      user_id: userId,
      name: name || `${originalProfile.name} (Copy)`,
      description: description || originalProfile.description,
      category: originalProfile.category,
      is_public: isPublic || false,
      is_featured: false, // Cloned profiles are never featured by default
      settings: originalProfile.settings,
      tags: originalProfile.tags || [],
      usage_count: 0, // Reset usage count for new profile
    };

    const { data: clonedProfile, error: createError } = await supabase
      .from('selection_profiles')
      .insert(clonedProfileData)
      .select()
      .single();

    if (createError) throw createError;

    // Increment usage count of original profile
    await supabase.rpc('increment_profile_usage', {
      profile_id: profileId
    });

    // Track analytics event
    try {
      await supabase
        .from('selection_analytics')
        .insert({
          user_id: userId,
          selection_profile_id: profileId,
          event_type: 'profile_cloned',
          selection_data: {
            originalProfileId: profileId,
            clonedProfileId: clonedProfile.id,
            originalName: originalProfile.name,
            clonedName: clonedProfile.name
          }
        });
    } catch (analyticsError) {
      logger.warn('Failed to track profile cloning analytics:', analyticsError);
      // Don't fail the request if analytics fails
    }

    // Transform response to match frontend interface
    const formattedProfile = {
      id: clonedProfile.id,
      name: clonedProfile.name,
      description: clonedProfile.description,
      category: clonedProfile.category,
      isPublic: clonedProfile.is_public,
      isFeatured: clonedProfile.is_featured,
      settings: clonedProfile.settings,
      usageCount: clonedProfile.usage_count,
      tags: clonedProfile.tags || [],
      createdAt: new Date(clonedProfile.created_at),
      createdBy: 'You'
    };

    return NextResponse.json({ 
      profile: formattedProfile,
      originalProfile: {
        id: originalProfile.id,
        name: originalProfile.name
      }
    });
  } catch (error) {
    logger.error('Clone profile API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}