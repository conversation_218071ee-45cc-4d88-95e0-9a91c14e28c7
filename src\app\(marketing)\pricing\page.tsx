'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  Shield, 
  CreditCard, 
  RefreshCw, 
  ArrowLeft,
  <PERSON>ather,
  CheckCircle2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { PricingToggle } from '@/components/pricing/pricing-toggle'
import { PricingCard } from '@/components/pricing/pricing-card'
import { FeatureComparison } from '@/components/pricing/feature-comparison'
import { PricingFAQ } from '@/components/pricing/pricing-faq'
import { AgentShowcase } from '@/components/pricing/agent-showcase'
import { AIIntelligenceShowcase } from '@/components/pricing/ai-intelligence-showcase'
import { SuccessCallouts } from '@/components/pricing/success-callouts'
import { SUBSCRIPTION_TIERS, type SubscriptionTier } from '@/lib/subscription'

export default function PricingPage() {
  const [isAnnual, setIsAnnual] = useState(false)
  const router = useRouter()

  const handleSelectTier = (tier: SubscriptionTier) => {
    // Navigate to signup with selected tier
    router.push(`/signup?plan=${tier.id}&billing=${isAnnual ? 'annual' : 'monthly'}`)
  }

  return (
    <div className="min-h-screen overflow-hidden">
      {/* Paper texture background */}
      <div className="fixed inset-0 paper-texture opacity-30" />
      
      {/* Header */}
      <header className="relative z-50 border-b border-border bg-background/95 backdrop-blur-xl shadow-sm">
        <div className="container flex h-20 items-center justify-between">
          <div className="flex items-center space-x-6">
            <Link href="/" className="flex items-center space-x-3">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80 blur-sm opacity-20" />
                <div className="relative w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-md">
                  <Feather className="w-6 h-6 text-primary-foreground" />
                </div>
              </div>
              <h1 className="text-2xl font-bold tracking-tight font-literary-display">
                BookScribe AI
              </h1>
            </Link>
            
            <Link href="/" className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors">
              <ArrowLeft className="w-4 h-4" />
              Back to Home
            </Link>
          </div>

          <nav className="flex items-center gap-4">
            <ThemeToggle />
            <Link href="/login">
              <Button variant="ghost">Sign In</Button>
            </Link>
          </nav>
        </div>
      </header>

      <main className="relative z-10 pb-20">
        {/* Hero Section */}
        <section className="py-16 px-4">
          <div className="container max-w-7xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-4 font-literary-display">
              AI That Matches Your Ambition
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              From first draft to bestseller, our adaptive AI intelligence scales with your story's complexity.
              Choose the plan that matches your creative vision.
            </p>
            
            <PricingToggle isAnnual={isAnnual} onToggle={setIsAnnual} />
          </div>
        </section>

        {/* Pricing Cards */}
        <section className="px-4 pb-16">
          <div className="container max-w-7xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
              {SUBSCRIPTION_TIERS.map((tier) => (
                <PricingCard
                  key={tier.id}
                  tier={tier}
                  isAnnual={isAnnual}
                  onSelect={handleSelectTier}
                />
              ))}
            </div>
          </div>
        </section>

        {/* Trust Indicators */}
        <section className="py-12 px-4 bg-muted/50">
          <div className="container max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
              <div className="flex flex-col items-center gap-2">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <Shield className="w-6 h-6 text-primary" />
                </div>
                <h3 className="font-semibold">30-Day Guarantee</h3>
                <p className="text-sm text-muted-foreground">
                  Full refund if not satisfied
                </p>
              </div>
              
              <div className="flex flex-col items-center gap-2">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <CreditCard className="w-6 h-6 text-primary" />
                </div>
                <h3 className="font-semibold">No Setup Fees</h3>
                <p className="text-sm text-muted-foreground">
                  Start writing immediately
                </p>
              </div>
              
              <div className="flex flex-col items-center gap-2">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <RefreshCw className="w-6 h-6 text-primary" />
                </div>
                <h3 className="font-semibold">Change Anytime</h3>
                <p className="text-sm text-muted-foreground">
                  Upgrade or downgrade freely
                </p>
              </div>
              
              <div className="flex flex-col items-center gap-2">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <CheckCircle2 className="w-6 h-6 text-primary" />
                </div>
                <h3 className="font-semibold">Keep Your Work</h3>
                <p className="text-sm text-muted-foreground">
                  Export anytime, even after canceling
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* AI Intelligence Showcase */}
        <section className="px-4 py-16 bg-muted/30">
          <AIIntelligenceShowcase />
        </section>

        {/* AI Agents Showcase */}
        <section className="px-4">
          <AgentShowcase />
        </section>

        {/* Success Stories */}
        <SuccessCallouts />

        {/* Feature Comparison */}
        <section className="py-16 px-4 bg-muted/30">
          <div className="container max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8 font-literary-display">
              Detailed Feature Comparison
            </h2>
            <FeatureComparison />
          </div>
        </section>

        {/* FAQ */}
        <section className="py-16 px-4">
          <PricingFAQ />
        </section>

        {/* CTA Section */}
        <section className="py-16 px-4 bg-primary/5">
          <div className="container max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4 font-literary-display">
              Ready to Write Your Masterpiece?
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Join thousands of authors using AI to bring their stories to life.
              Start with our free tier or choose a plan that fits your needs.
            </p>
            <div className="flex gap-4 justify-center">
              <Link href="/signup?plan=starter">
                <Button size="lg" variant="outline" className="font-mono">
                  Start Free
                </Button>
              </Link>
              <Link href="/signup?plan=writer">
                <Button size="lg" className="font-mono">
                  <Feather className="w-5 h-5 mr-2" />
                  Get Writer Plan
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </main>
    </div>
  )
}