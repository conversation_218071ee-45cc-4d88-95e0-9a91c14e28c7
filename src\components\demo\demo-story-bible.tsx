"use client";

import { useState } from "react";
import { But<PERSON> as _Button } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  BookOpen, 
  Users, 
  Globe, 
  Clock, 
  Target,
  Heart,
  Sword as _Sword,
  Crown as _Crown,
  Sparkles as _<PERSON>rk<PERSON>,
  MapPin,
  Scroll
} from "lucide-react";

const mockCharacters = [
  {
    id: 'aria',
    name: '<PERSON> Stormwind',
    role: 'Protagonist',
    age: 17,
    description: 'A young crystal mage discovering her destiny as the prophesied Guardian.',
    traits: ['Brave', 'Curious', 'Empathetic', 'Determined'],
    backstory: 'Orphaned at age 5, raised by the Academy. Unknown royal heritage.',
    relationships: [
      { character: '<PERSON>', type: 'Mentor', strength: 'Strong' },
      { character: '<PERSON><PERSON> Nightwhisper', type: 'Rival/Former Friend', strength: 'Complex' }
    ],
    arc: 'Reluctant hero → Confident guardian',
    status: 'Active'
  },
  {
    id: 'marcus',
    name: '<PERSON> Ironforge',
    role: 'Mentor',
    age: 156,
    description: 'Wise dwarf master of earth magic and former Crystal Guardian.',
    traits: ['Wise', 'Patient', 'Protective', 'Experienced'],
    backstory: 'Former Guardian who sealed the last great darkness 50 years ago.',
    relationships: [
      { character: '<PERSON> <PERSON>wind', type: 'Student', strength: 'Strong' },
      { character: 'Elder Council', type: 'Advisor', strength: 'Respected' }
    ],
    arc: 'Protective teacher → Trusting guide',
    status: 'Active'
  },
  {
    id: 'zara',
    name: 'Zara Nightwhisper',
    role: 'Antagonist',
    age: 19,
    description: 'Brilliant shadow magic prodigy corrupted by ancient dark powers.',
    traits: ['Ambitious', 'Intelligent', 'Ruthless', 'Charismatic'],
    backstory: 'Former top student and Aria\'s best friend before corruption.',
    relationships: [
      { character: 'Aria Stormwind', type: 'Former Friend/Enemy', strength: 'Intense' },
      { character: 'Shadow Council', type: 'Leader', strength: 'Commanding' }
    ],
    arc: 'Ambitious student → Corrupted villain',
    status: 'Antagonistic'
  }
];

const mockLocations = [
  {
    id: 'academy',
    name: 'Aethermoor Academy',
    type: 'Institution',
    description: 'Ancient floating academy for magical arts, built on a massive crystal formation.',
    significance: 'Central hub of magical learning and home to the Heartstone.',
    features: ['Crystal Halls', 'Floating Towers', 'Training Grounds', 'Great Library'],
    atmosphere: 'Mystical, scholarly, safe haven'
  },
  {
    id: 'caverns',
    name: 'Crystal Caverns',
    type: 'Natural Wonder',
    description: 'Vast underground network of crystal formations that power all magic.',
    significance: 'Source of all magical energy in Aethermoor.',
    features: ['Heartstone Chamber', 'Crystal Gardens', 'Echo Chambers', 'Ancient Runes'],
    atmosphere: 'Awe-inspiring, dangerous, sacred'
  },
  {
    id: 'shadowlands',
    name: 'The Shadowlands',
    type: 'Corrupted Realm',
    description: 'Dark realm beyond the protective barriers, where corrupted magic festers.',
    significance: 'Source of the ancient evil and Zara\'s power base.',
    features: ['Twisted Spires', 'Shadow Portals', 'Corrupted Crystals', 'Dark Citadel'],
    atmosphere: 'Foreboding, corrupted, hostile'
  }
];

const mockTimeline = [
  { chapter: 1, event: 'Aria activates the Heartstone', significance: 'Prophecy begins', status: 'Complete' },
  { chapter: 2, event: 'First shadow attack on Academy', significance: 'Ancient enemy awakens', status: 'Complete' },
  { chapter: 3, event: 'Aria meets Zara as enemy', significance: 'Personal stakes revealed', status: 'Planned' },
  { chapter: 5, event: 'Journey to Crystal Caverns', significance: 'Quest begins', status: 'Planned' },
  { chapter: 8, event: 'Discovery of Aria\'s heritage', significance: 'Identity revelation', status: 'Planned' },
  { chapter: 12, event: 'First confrontation with Zara', significance: 'Midpoint conflict', status: 'Planned' },
  { chapter: 18, event: 'Alliance with other Guardians', significance: 'Power gathering', status: 'Planned' },
  { chapter: 23, event: 'Final battle in Shadowlands', significance: 'Climax', status: 'Planned' },
  { chapter: 25, event: 'New Guardian Council formed', significance: 'Resolution', status: 'Planned' }
];

const mockPlotThreads = [
  { id: 1, thread: 'Aria\'s awakening powers', status: 'Active', chapters: '1-8', resolution: 'Ongoing' },
  { id: 2, thread: 'Ancient prophecy fulfillment', status: 'Active', chapters: '1-25', resolution: 'Series arc' },
  { id: 3, thread: 'Zara\'s corruption and redemption', status: 'Building', chapters: '3-24', resolution: 'Planned' },
  { id: 4, thread: 'Academy under threat', status: 'Active', chapters: '2-15', resolution: 'Mid-book' },
  { id: 5, thread: 'Crystal magic system exploration', status: 'Ongoing', chapters: '1-25', resolution: 'Educational' }
];

export function DemoStoryBible() {
  const [selectedCharacter, setSelectedCharacter] = useState<string | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null);

  return (
    <div className="max-w-6xl mx-auto">
      <Card className="border-border bg-card backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl flex items-center gap-2">
              <BookOpen className="w-6 h-6 text-primary" />
              Story Bible & World Management
            </CardTitle>
            <Badge variant="outline" className="border-primary/50 text-primary">
              Demo Content
            </Badge>
          </div>
          
          <p className="text-muted-foreground mt-2">
            Explore how BookScribe AI organizes and tracks all elements of your story world, 
            ensuring perfect consistency across your entire narrative.
          </p>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="characters" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="characters">Characters</TabsTrigger>
              <TabsTrigger value="world">World</TabsTrigger>
              <TabsTrigger value="timeline">Timeline</TabsTrigger>
              <TabsTrigger value="threads">Plot Threads</TabsTrigger>
            </TabsList>

            <TabsContent value="characters" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Character List */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Character Roster
                  </h3>
                  
                  {mockCharacters.map((character) => (
                    <Card
                      key={character.id}
                      className={`cursor-pointer transition-all border ${
                        selectedCharacter === character.id
                          ? 'border-primary/50 bg-primary/10'
                          : 'border-border bg-card hover:border-primary/30'
                      }`}
                      onClick={() => setSelectedCharacter(selectedCharacter === character.id ? null : character.id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold">{character.name}</h4>
                          <Badge variant="outline" className={`${
                            character.status === 'Active' ? 'text-green-400 border-green-400/50' : ''
                          }`}>
                            {character.role}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{character.description}</p>
                        <div className="flex flex-wrap gap-1">
                          {character.traits.slice(0, 3).map((trait) => (
                            <Badge key={trait} variant="outline" className="text-xs">
                              {trait}
                            </Badge>
                          ))}
                          {character.traits.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{character.traits.length - 3}
                            </Badge>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Character Details */}
                <div>
                  {selectedCharacter ? (
                    <Card className="border-border bg-card">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg">
                          {mockCharacters.find(c => c.id === selectedCharacter)?.name}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {(() => {
                          const character = mockCharacters.find(c => c.id === selectedCharacter);
                          if (!character) return null;
                          
                          return (
                            <>
                              <div>
                                <h5 className="font-medium mb-2 flex items-center gap-2">
                                  <Heart className="w-4 h-4 text-red-400" />
                                  Character Arc
                                </h5>
                                <p className="text-sm text-muted-foreground">{character.arc}</p>
                              </div>
                              
                              <div>
                                <h5 className="font-medium mb-2 flex items-center gap-2">
                                  <Scroll className="w-4 h-4 text-blue-400" />
                                  Backstory
                                </h5>
                                <p className="text-sm text-muted-foreground">{character.backstory}</p>
                              </div>
                              
                              <div>
                                <h5 className="font-medium mb-2 flex items-center gap-2">
                                  <Users className="w-4 h-4 text-purple-400" />
                                  Relationships
                                </h5>
                                <div className="space-y-2">
                                  {character.relationships.map((rel, index) => (
                                    <div key={index} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                                      <span className="text-sm">{rel.character}</span>
                                      <div className="flex items-center gap-2">
                                        <Badge variant="outline" className="text-xs">
                                          {rel.type}
                                        </Badge>
                                        <Badge variant="outline" className={`text-xs ${
                                          rel.strength === 'Strong' ? 'border-green-500/50 text-green-400' :
                                          rel.strength === 'Complex' ? 'border-amber-500/50 text-primary' :
                                          ''
                                        }`}>
                                          {rel.strength}
                                        </Badge>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                              
                              <div>
                                <h5 className="font-medium mb-2">Personality Traits</h5>
                                <div className="flex flex-wrap gap-1">
                                  {character.traits.map((trait) => (
                                    <Badge key={trait} variant="outline" className="text-xs">
                                      {trait}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </>
                          );
                        })()}
                      </CardContent>
                    </Card>
                  ) : (
                    <Card className="border-border bg-card">
                      <CardContent className="p-8 text-center">
                        <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <h4 className="font-medium mb-2">Select a Character</h4>
                        <p className="text-sm text-muted-foreground">
                          Click on a character to view their detailed profile, relationships, and development arc.
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="world" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Location List */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Globe className="w-5 h-5" />
                    World Locations
                  </h3>
                  
                  {mockLocations.map((location) => (
                    <Card
                      key={location.id}
                      className={`cursor-pointer transition-all border ${
                        selectedLocation === location.id
                          ? 'border-primary/50 bg-primary/10'
                          : 'border-border bg-card hover:border-primary/30'
                      }`}
                      onClick={() => setSelectedLocation(selectedLocation === location.id ? null : location.id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold">{location.name}</h4>
                          <Badge variant="outline" className="">
                            {location.type}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{location.description}</p>
                        <p className="text-xs text-primary">{location.significance}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Location Details */}
                <div>
                  {selectedLocation ? (
                    <Card className="border-border bg-card">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center gap-2">
                          <MapPin className="w-5 h-5" />
                          {mockLocations.find(l => l.id === selectedLocation)?.name}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {(() => {
                          const location = mockLocations.find(l => l.id === selectedLocation);
                          if (!location) return null;
                          
                          return (
                            <>
                              <div>
                                <h5 className="font-medium mb-2">Description</h5>
                                <p className="text-sm text-muted-foreground">{location.description}</p>
                              </div>
                              
                              <div>
                                <h5 className="font-medium mb-2">Story Significance</h5>
                                <p className="text-sm text-primary">{location.significance}</p>
                              </div>
                              
                              <div>
                                <h5 className="font-medium mb-2">Key Features</h5>
                                <div className="grid grid-cols-2 gap-2">
                                  {location.features.map((feature) => (
                                    <Badge key={feature} variant="outline" className="text-xs  justify-center">
                                      {feature}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                              
                              <div>
                                <h5 className="font-medium mb-2">Atmosphere</h5>
                                <p className="text-sm text-muted-foreground italic">{location.atmosphere}</p>
                              </div>
                            </>
                          );
                        })()}
                      </CardContent>
                    </Card>
                  ) : (
                    <Card className="border-border bg-card">
                      <CardContent className="p-8 text-center">
                        <Globe className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <h4 className="font-medium mb-2">Select a Location</h4>
                        <p className="text-sm text-muted-foreground">
                          Click on a location to explore its details, significance, and key features.
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="timeline" className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Story Timeline
              </h3>
              
              <Card className="border-border bg-card">
                <CardContent className="p-4">
                  <ScrollArea className="h-96">
                    <div className="space-y-4">
                      {mockTimeline.map((event, index) => (
                        <div key={index} className="flex items-start gap-4 p-3 rounded border border-border bg-card">
                          <div className="flex-shrink-0">
                            <Badge variant="outline" className={` ${
                              event.status === 'Complete' ? 'text-green-400' : 'text-muted-foreground'
                            }`}>
                              Ch. {event.chapter}
                            </Badge>
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium text-sm">{event.event}</h4>
                            <p className="text-xs text-primary mt-1">{event.significance}</p>
                          </div>
                          <Badge variant="outline" className={`text-xs ${
                            event.status === 'Complete' ? 'border-green-500/50 text-green-400' : ''
                          }`}>
                            {event.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="threads" className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Target className="w-5 h-5" />
                Plot Thread Tracking
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {mockPlotThreads.map((thread) => (
                  <Card key={thread.id} className="border-border bg-card">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-sm">{thread.thread}</h4>
                        <Badge variant="outline" className={`text-xs ${
                          thread.status === 'Active' ? 'border-green-500/50 text-green-400' :
                          thread.status === 'Building' ? 'border-amber-500/50 text-primary' :
                          ''
                        }`}>
                          {thread.status}
                        </Badge>
                      </div>
                      <div className="space-y-1 text-xs text-muted-foreground">
                        <div>Chapters: {thread.chapters}</div>
                        <div>Resolution: {thread.resolution}</div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
