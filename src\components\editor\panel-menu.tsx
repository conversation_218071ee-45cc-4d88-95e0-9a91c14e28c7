'use client'

import { useState } from 'react'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuCheckboxItem
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { 
  Layers, 
  Plus, 
  Save, 
  RotateCcw,
  Layout,
  Check
} from 'lucide-react'
import { usePanels } from '@/lib/panels'
import { Badge } from '@/components/ui/badge'

export function PanelMenu() {
  const { 
    plugins, 
    panelStates, 
    togglePanel, 
    activeLayout,
    layouts,
    saveLayout,
    loadLayout,
    resetToDefaultLayout
  } = usePanels()
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [layoutName, setLayoutName] = useState('')
  const [layoutDescription, setLayoutDescription] = useState('')

  // Group plugins by category
  const pluginsByCategory = plugins.reduce((acc, plugin) => {
    if (plugin.showInMenu !== false) {
      if (!acc[plugin.category]) acc[plugin.category] = []
      acc[plugin.category].push(plugin)
    }
    return acc
  }, {} as Record<string, typeof plugins>)

  const categoryLabels = {
    writing: 'Writing Tools',
    analysis: 'Analysis',
    series: 'Series Management',
    tools: 'Utilities',
    ai: 'AI Assistance'
  }

  const handleSaveLayout = () => {
    if (layoutName.trim()) {
      saveLayout(layoutName, layoutDescription)
      setShowSaveDialog(false)
      setLayoutName('')
      setLayoutDescription('')
    }
  }

  const visiblePanelCount = Array.from(panelStates.values()).filter(s => s.visible).length

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="gap-2">
            <Layers className="h-4 w-4" />
            Panels
            {visiblePanelCount > 0 && (
              <Badge variant="secondary" className="ml-1 text-xs">
                {visiblePanelCount}
              </Badge>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64">
          <DropdownMenuLabel>Panel Management</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {/* Panel toggles by category */}
          {Object.entries(pluginsByCategory).map(([category, categoryPlugins]) => (
            <DropdownMenuSub key={category}>
              <DropdownMenuSubTrigger>
                {categoryLabels[category as keyof typeof categoryLabels] || category}
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent>
                {categoryPlugins.map(plugin => {
                  const state = panelStates.get(plugin.id)
                  return (
                    <DropdownMenuCheckboxItem
                      key={plugin.id}
                      checked={state?.visible || false}
                      onCheckedChange={() => togglePanel(plugin.id)}
                    >
                      <div className="flex items-center gap-2 flex-1">
                        <plugin.icon className="h-4 w-4" />
                        <span>{plugin.name}</span>
                        {state?.pinned && (
                          <Badge variant="outline" className="ml-auto text-xs">
                            Pinned
                          </Badge>
                        )}
                      </div>
                    </DropdownMenuCheckboxItem>
                  )
                })}
              </DropdownMenuSubContent>
            </DropdownMenuSub>
          ))}
          
          <DropdownMenuSeparator />
          
          {/* Layout management */}
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <Layout className="h-4 w-4 mr-2" />
              Layouts
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              <DropdownMenuLabel className="text-xs">
                Current: {activeLayout.name}
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              {layouts.map(layout => (
                <DropdownMenuItem
                  key={layout.id}
                  onClick={() => loadLayout(layout.id)}
                  className="gap-2"
                >
                  {layout.id === activeLayout.id && (
                    <Check className="h-3 w-3" />
                  )}
                  <span className={layout.id === activeLayout.id ? 'font-medium' : ''}>
                    {layout.name}
                  </span>
                  {layout.isDefault && (
                    <Badge variant="outline" className="ml-auto text-xs">
                      Default
                    </Badge>
                  )}
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setShowSaveDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Save Current Layout
              </DropdownMenuItem>
              <DropdownMenuItem onClick={resetToDefaultLayout}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset to Default
              </DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuSub>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Save Layout Dialog */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Panel Layout</DialogTitle>
            <DialogDescription>
              Save your current panel arrangement as a custom layout
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="layout-name">Layout Name</Label>
              <Input
                id="layout-name"
                value={layoutName}
                onChange={(e) => setLayoutName(e.target.value)}
                placeholder="My Custom Layout"
              />
            </div>
            <div>
              <Label htmlFor="layout-description">Description (optional)</Label>
              <Textarea
                id="layout-description"
                value={layoutDescription}
                onChange={(e) => setLayoutDescription(e.target.value)}
                placeholder="Describe when this layout is useful..."
                rows={3}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveLayout} disabled={!layoutName.trim()}>
                <Save className="h-4 w-4 mr-2" />
                Save Layout
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}