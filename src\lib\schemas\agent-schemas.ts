/**
 * Zod Schemas for Agent Responses
 * These schemas are used for structured outputs with OpenAI API
 */

import { z } from 'zod'

// Story Structure Schema
export const storyStructureSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  genre: z.string().min(1, 'Genre is required'),
  themes: z.array(z.string()).min(1, 'At least one theme is required'),
  acts: z.array(z.object({
    number: z.number().int().positive(),
    title: z.string().min(1),
    description: z.string().min(1),
    keyEvents: z.array(z.string()),
    wordCount: z.number().int().positive()
  })).min(1, 'At least one act is required'),
  conflicts: z.array(z.object({
    type: z.enum(['internal', 'external', 'relational']),
    description: z.string().min(1),
    resolution: z.string().optional()
  })),
  timeline: z.array(z.object({
    event: z.string().min(1),
    chapter: z.number().int().positive().optional(),
    importance: z.enum(['critical', 'major', 'minor'])
  }))
})

export type StoryStructure = z.infer<typeof storyStructureSchema>

// Character Profile Schema
export const characterProfileSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1),
  role: z.enum(['protagonist', 'antagonist', 'supporting', 'minor']),
  description: z.string().min(1),
  backstory: z.string().min(1),
  personality: z.object({
    traits: z.array(z.string()).min(1),
    motivations: z.array(z.string()).min(1),
    fears: z.array(z.string()).min(1),
    goals: z.array(z.string()).min(1)
  }),
  physicalDescription: z.string().min(1),
  relationships: z.array(z.object({
    characterId: z.string().min(1),
    type: z.string().min(1),
    description: z.string().min(1)
  })),
  arc: z.object({
    startingPoint: z.string().min(1),
    transformation: z.string().min(1),
    endingPoint: z.string().min(1)
  }),
  voiceCharacteristics: z.array(z.string())
})

export type CharacterProfile = z.infer<typeof characterProfileSchema>

// Multiple Characters Response Schema
export const charactersResponseSchema = z.object({
  characters: z.array(characterProfileSchema).min(1, 'At least one character is required')
})

// Chapter Outline Schema
export const chapterOutlineSchema = z.object({
  chapterNumber: z.number().int().positive(),
  title: z.string().min(1),
  targetWordCount: z.number().int().positive(),
  summary: z.string().min(1),
  scenes: z.array(z.object({
    id: z.string().min(1),
    setting: z.string().min(1),
    characters: z.array(z.string()),
    purpose: z.string().min(1),
    conflict: z.string().min(1),
    outcome: z.string().min(1),
    estimatedWords: z.number().int().positive()
  })).min(1),
  povCharacter: z.string().optional(),
  timeline: z.string().min(1),
  keyEvents: z.array(z.string()),
  cliffhanger: z.string().optional(),
  transitions: z.object({
    from: z.string().optional(),
    to: z.string().optional()
  })
})

export type ChapterOutline = z.infer<typeof chapterOutlineSchema>

// Multiple Chapter Outlines Response Schema
export const chapterOutlinesResponseSchema = z.object({
  chapters: z.array(chapterOutlineSchema).min(1, 'At least one chapter outline is required')
})

// Chapter Content Schema (for non-structured text generation)
export const chapterMetadataSchema = z.object({
  chapterNumber: z.number().int().positive(),
  title: z.string().min(1),
  wordCount: z.number().int().positive(),
  scenes: z.array(z.object({
    id: z.string().min(1),
    content: z.string().min(1),
    wordCount: z.number().int().positive()
  })),
  characterVoices: z.record(z.string(), z.array(z.string())),
  consistencyNotes: z.array(z.string())
})

// Editorial Review Schema
export const editorialReviewSchema = z.object({
  chapterNumber: z.number().int().positive(),
  overallScore: z.number().min(0).max(100),
  consistency: z.object({
    score: z.number().min(0).max(100),
    issues: z.array(z.string()),
    suggestions: z.array(z.string())
  }),
  characterVoices: z.object({
    score: z.number().min(0).max(100),
    issues: z.array(z.string()),
    suggestions: z.array(z.string())
  }),
  plotProgression: z.object({
    score: z.number().min(0).max(100),
    issues: z.array(z.string()),
    suggestions: z.array(z.string())
  }),
  styleAndTone: z.object({
    score: z.number().min(0).max(100),
    issues: z.array(z.string()),
    suggestions: z.array(z.string())
  }),
  recommendations: z.array(z.string()),
  revisionPriority: z.enum(['low', 'medium', 'high'])
})

export type EditorialReview = z.infer<typeof editorialReviewSchema>

// Adaptive Planning Result Schema
export const adaptivePlanningResultSchema = z.object({
  needsAdjustment: z.boolean(),
  adjustments: z.array(z.object({
    type: z.enum(['character_development', 'plot_revision', 'pacing_adjustment', 'theme_modification', 'world_building']),
    severity: z.enum(['minor', 'moderate', 'major']),
    description: z.string().min(1),
    affectedChapters: z.array(z.number().int().positive()),
    specificChanges: z.array(z.object({
      chapterNumber: z.number().int().positive(),
      adjustments: z.array(z.object({
        field: z.string().min(1),
        oldValue: z.unknown(),
        newValue: z.unknown(),
        reason: z.string().min(1)
      }))
    })),
    characterStateUpdates: z.array(z.object({
      characterId: z.string().min(1),
      updates: z.object({
        emotionalState: z.string().optional(),
        knowledge: z.array(z.string()).optional(),
        relationships: z.string().optional(),
        arcProgression: z.string().optional()
      })
    })),
    plotThreadUpdates: z.array(z.object({
      threadId: z.string().min(1),
      updates: z.object({
        status: z.string().optional(),
        description: z.string().optional(),
        nextMilestone: z.string().optional()
      })
    })),
    worldRuleUpdates: z.array(z.object({
      rule: z.string().min(1),
      oldValue: z.string().min(1),
      newValue: z.string().min(1),
      reason: z.string().min(1)
    }))
  })),
  confidence: z.number().min(0).max(100),
  reasoning: z.string().min(1),
  futureChapterImpacts: z.array(z.object({
    chapterNumber: z.number().int().positive(),
    impactLevel: z.enum(['low', 'medium', 'high']),
    description: z.string().min(1),
    recommendedActions: z.array(z.string())
  }))
})

export type AdaptivePlanningResult = z.infer<typeof adaptivePlanningResultSchema>