'use client';

import { useState, useEffect } from 'react';
import { logger } from '@/lib/services/logger';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Tabs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Heart, 
  Users, 
  Zap, 
  Shield, 
  TrendingUp, 
  AlertTriangle,
  Target,
  Activity,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import { RelationshipGraph } from './relationship-graph';

interface RelationshipAnalysis {
  totalRelationships: number;
  byType: Record<string, number>;
  byIntensity: Record<string, number>;
  evolutionTrends: { chapter: number; totalChanges: number }[];
  conflicts: string[];
  alliances: string[];
  romanticArcs: string[];
  keyMoments: { chapter: number; event: string; impact: string }[];
}

interface RelationshipConflicts {
  contradictions: string[];
  inconsistencies: string[];
  suggestions: string[];
}

interface RelationshipDashboardProps {
  projectId: string;
  currentChapter?: number;
}

export function RelationshipDashboard({ projectId }: RelationshipDashboardProps) {
  const [analysis, setAnalysis] = useState<RelationshipAnalysis | null>(null);
  const [conflicts, setConflicts] = useState<RelationshipConflicts | null>(null);
  const [loading, setLoading] = useState(false);
  const [, setSelectedCharacter] = useState<string | null>(null);
  const [chapterRange] = useState<{ start: number; end: number } | undefined>();

  useEffect(() => {
    fetchAnalysis();
  }, [projectId]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchAnalysis = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/relationships/analyze?projectId=${projectId}`);
      if (response.ok) {
        const data = await response.json();
        setAnalysis(data.analysis);
        setConflicts(data.conflicts);
      }
    } catch (error) {
      logger.error('Error fetching relationship analysis:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'romantic': return <Heart className="w-4 h-4 text-red-500" />;
      case 'family': return <Users className="w-4 h-4 text-blue-500" />;
      case 'rivalry': return <Zap className="w-4 h-4 text-yellow-500" />;
      case 'alliance': return <Shield className="w-4 h-4 text-green-500" />;
      case 'mentorship': return <Target className="w-4 h-4 text-purple-500" />;
      default: return <Users className="w-4 h-4 text-gray-500" />;
    }
  };

  const getIntensityColor = (intensity: string) => {
    switch (intensity) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateHealthScore = () => {
    if (!analysis || !conflicts) return 0;
    
    const baseScore = 100;
    const contradictionPenalty = conflicts.contradictions.length * 15;
    const inconsistencyPenalty = conflicts.inconsistencies.length * 10;
    
    return Math.max(0, baseScore - contradictionPenalty - inconsistencyPenalty);
  };

  const healthScore = calculateHealthScore();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Relationship Analysis</h2>
          <p className="text-gray-600">
            Comprehensive analysis of character relationships and dynamics
          </p>
        </div>
        <Button onClick={fetchAnalysis} disabled={loading} size="sm">
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      {analysis && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Activity className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Relationships</p>
                  <p className="text-2xl font-bold">{analysis.totalRelationships}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <BarChart3 className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Relationship Health</p>
                  <p className="text-2xl font-bold">{healthScore}%</p>
                  <Progress value={healthScore} className="mt-1 h-1" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Heart className="w-5 h-5 text-red-600" />
                <div>
                  <p className="text-sm text-gray-600">Romantic Arcs</p>
                  <p className="text-2xl font-bold">{analysis.romanticArcs.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Zap className="w-5 h-5 text-yellow-600" />
                <div>
                  <p className="text-sm text-gray-600">Active Conflicts</p>
                  <p className="text-2xl font-bold">{analysis.conflicts.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Conflicts and Issues */}
      {conflicts && (conflicts.contradictions.length > 0 || conflicts.inconsistencies.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-red-600" />
              <span>Relationship Issues</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {conflicts.contradictions.length > 0 && (
              <div>
                <h4 className="font-semibold text-red-800 mb-2">Contradictions</h4>
                <div className="space-y-2">
                  {conflicts.contradictions.map((contradiction, index) => (
                    <Alert key={index} variant="destructive">
                      <AlertTriangle className="w-4 h-4" />
                      <AlertDescription>{contradiction}</AlertDescription>
                    </Alert>
                  ))}
                </div>
              </div>
            )}

            {conflicts.inconsistencies.length > 0 && (
              <div>
                <h4 className="font-semibold text-yellow-800 mb-2">Inconsistencies</h4>
                <div className="space-y-2">
                  {conflicts.inconsistencies.map((inconsistency, index) => (
                    <Alert key={index}>
                      <AlertTriangle className="w-4 h-4" />
                      <AlertDescription>{inconsistency}</AlertDescription>
                    </Alert>
                  ))}
                </div>
              </div>
            )}

            {conflicts.suggestions.length > 0 && (
              <div>
                <h4 className="font-semibold text-blue-800 mb-2">Suggestions</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {conflicts.suggestions.map((suggestion, index) => (
                    <li key={index} className="text-blue-700">{suggestion}</li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="graph" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="graph">Relationship Graph</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="evolution">Evolution</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="graph" className="space-y-4">
          <RelationshipGraph
            projectId={projectId}
            chapterRange={chapterRange}
            onNodeSelect={setSelectedCharacter}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {analysis && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Relationship Types */}
              <Card>
                <CardHeader>
                  <CardTitle>Relationship Types</CardTitle>
                  <CardDescription>
                    Distribution of relationship types in your story
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(analysis.byType).map(([type, count]) => (
                      <div key={type} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {getTypeIcon(type)}
                          <span className="capitalize">{type}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${(count / analysis.totalRelationships) * 100}%` }}
                            />
                          </div>
                          <Badge variant="outline">{count}</Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Intensity Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Intensity Distribution</CardTitle>
                  <CardDescription>
                    How intense are your character relationships?
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(analysis.byIntensity).map(([intensity, count]) => (
                      <div key={intensity} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full ${getIntensityColor(intensity).split(' ')[0]}`} />
                          <span className="capitalize">{intensity} Intensity</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-green-600 h-2 rounded-full" 
                              style={{ width: `${(count / analysis.totalRelationships) * 100}%` }}
                            />
                          </div>
                          <Badge variant="outline">{count}</Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Active Conflicts */}
              <Card>
                <CardHeader>
                  <CardTitle>Active Conflicts</CardTitle>
                  <CardDescription>
                    Current conflicts between characters
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {analysis.conflicts.length > 0 ? (
                    <div className="space-y-2">
                      {analysis.conflicts.map((conflict, index) => (
                        <div key={index} className="flex items-center space-x-2 p-2 bg-red-50 rounded-lg">
                          <Zap className="w-4 h-4 text-red-600" />
                          <span className="text-sm">{conflict}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm">No active conflicts detected</p>
                  )}
                </CardContent>
              </Card>

              {/* Alliances */}
              <Card>
                <CardHeader>
                  <CardTitle>Strong Alliances</CardTitle>
                  <CardDescription>
                    Powerful partnerships and friendships
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {analysis.alliances.length > 0 ? (
                    <div className="space-y-2">
                      {analysis.alliances.map((alliance, index) => (
                        <div key={index} className="flex items-center space-x-2 p-2 bg-green-50 rounded-lg">
                          <Shield className="w-4 h-4 text-green-600" />
                          <span className="text-sm">{alliance}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm">No strong alliances detected</p>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="evolution" className="space-y-4">
          {analysis && (
            <Card>
              <CardHeader>
                <CardTitle>Relationship Evolution</CardTitle>
                <CardDescription>
                  How relationships change throughout your story
                </CardDescription>
              </CardHeader>
              <CardContent>
                {analysis.evolutionTrends.length > 0 ? (
                  <div className="space-y-4">
                    <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                      {/* Simple bar chart representation */}
                      <div className="flex items-end space-x-2 h-40">
                        {analysis.evolutionTrends.slice(0, 10).map((trend, index) => (
                          <div key={index} className="flex flex-col items-center space-y-1">
                            <div 
                              className="bg-blue-500 w-8 rounded-t"
                              style={{ height: `${(trend.totalChanges / Math.max(...analysis.evolutionTrends.map(t => t.totalChanges))) * 120}px` }}
                            />
                            <span className="text-xs">{trend.chapter}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">
                      Chapter-by-chapter relationship changes. Higher bars indicate more relationship development.
                    </p>
                  </div>
                ) : (
                  <p className="text-gray-500">No evolution data available yet</p>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          {analysis && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Romantic Arcs */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Heart className="w-5 h-5 text-red-600" />
                    <span>Romantic Storylines</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {analysis.romanticArcs.length > 0 ? (
                    <div className="space-y-2">
                      {analysis.romanticArcs.map((arc, index) => (
                        <div key={index} className="p-2 bg-red-50 rounded-lg">
                          <span className="text-sm">{arc}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm">No romantic storylines detected</p>
                  )}
                </CardContent>
              </Card>

              {/* Key Moments */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Target className="w-5 h-5 text-purple-600" />
                    <span>Pivotal Moments</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {analysis.keyMoments.length > 0 ? (
                    <div className="space-y-3">
                      {analysis.keyMoments.slice(0, 5).map((moment, index) => (
                        <div key={index} className="border-l-4 border-purple-500 pl-3">
                          <div className="font-medium text-sm">Chapter {moment.chapter}</div>
                          <div className="text-sm text-gray-600 mb-1">{moment.event}</div>
                          <div className="text-xs text-purple-600">{moment.impact}</div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm">No key moments recorded yet</p>
                  )}
                </CardContent>
              </Card>

              {/* Recommendations */}
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle>Recommendations</CardTitle>
                  <CardDescription>
                    AI-powered suggestions for relationship development
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {healthScore < 70 && (
                      <Alert>
                        <AlertTriangle className="w-4 h-4" />
                        <AlertDescription>
                          Relationship health is below 70%. Consider reviewing contradictions and inconsistencies.
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    {analysis.romanticArcs.length === 0 && analysis.totalRelationships > 3 && (
                      <Alert>
                        <Heart className="w-4 h-4" />
                        <AlertDescription>
                          No romantic storylines detected. Consider adding romantic tension for deeper character development.
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    {analysis.conflicts.length === 0 && analysis.totalRelationships > 2 && (
                      <Alert>
                        <Zap className="w-4 h-4" />
                        <AlertDescription>
                          No conflicts detected. Adding interpersonal tension can increase story engagement.
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    {analysis.evolutionTrends.length === 0 && (
                      <Alert>
                        <TrendingUp className="w-4 h-4" />
                        <AlertDescription>
                          Relationships appear static. Consider showing character relationship growth over time.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}