-- ============================================================================
-- COMPREHENSIVE RLS POLICIES FOR BOOKSCRIBE AI
-- ============================================================================
-- This script creates proper Row Level Security policies for all tables
-- based on their relationships and access patterns.

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_arcs ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE selection_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE reference_materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE selection_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_bible ENABLE ROW LEVEL SECURITY;
ALTER TABLE editing_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapter_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_snapshots ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_embeddings ENABLE ROW LEVEL SECURITY;
ALTER TABLE writing_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE writing_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE writing_goal_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE series ENABLE ROW LEVEL SECURITY;
ALTER TABLE series_books ENABLE ROW LEVEL SECURITY;
ALTER TABLE collaboration_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE collaboration_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE processing_tasks ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 1. USER PROFILE & SUBSCRIPTION TABLES
-- ============================================================================

-- PROFILES: Users can only access their own profile
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;

CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- USER SUBSCRIPTIONS: Users can only access their own subscription data
DROP POLICY IF EXISTS "Users can view own subscription" ON user_subscriptions;
DROP POLICY IF EXISTS "Users can update own subscription" ON user_subscriptions;
DROP POLICY IF EXISTS "Users can insert own subscription" ON user_subscriptions;

CREATE POLICY "Users can view own subscription" ON user_subscriptions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own subscription" ON user_subscriptions
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own subscription" ON user_subscriptions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- USAGE TRACKING: Users can only view their own usage data
DROP POLICY IF EXISTS "Users can view own usage" ON usage_tracking;
DROP POLICY IF EXISTS "Users can insert own usage" ON usage_tracking;
DROP POLICY IF EXISTS "Users can update own usage" ON usage_tracking;

CREATE POLICY "Users can view own usage" ON usage_tracking
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own usage" ON usage_tracking
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own usage" ON usage_tracking
  FOR UPDATE USING (auth.uid() = user_id);

-- USAGE EVENTS: Users can only view their own usage events
DROP POLICY IF EXISTS "Users can view own usage events" ON usage_events;
DROP POLICY IF EXISTS "Users can insert own usage events" ON usage_events;

CREATE POLICY "Users can view own usage events" ON usage_events
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own usage events" ON usage_events
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- ============================================================================
-- 2. CORE PROJECT TABLES
-- ============================================================================

-- PROJECTS: Users can manage their own projects
DROP POLICY IF EXISTS "Users can view own projects" ON projects;
DROP POLICY IF EXISTS "Users can insert own projects" ON projects;
DROP POLICY IF EXISTS "Users can update own projects" ON projects;
DROP POLICY IF EXISTS "Users can delete own projects" ON projects;

CREATE POLICY "Users can view own projects" ON projects
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own projects" ON projects
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own projects" ON projects
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own projects" ON projects
  FOR DELETE USING (auth.uid() = user_id);

-- ============================================================================
-- 3. PROJECT-RELATED CONTENT TABLES
-- ============================================================================

-- CHAPTERS: Users can manage chapters in their own projects
DROP POLICY IF EXISTS "Users can view own project chapters" ON chapters;
DROP POLICY IF EXISTS "Users can insert own project chapters" ON chapters;
DROP POLICY IF EXISTS "Users can update own project chapters" ON chapters;
DROP POLICY IF EXISTS "Users can delete own project chapters" ON chapters;

CREATE POLICY "Users can view own project chapters" ON chapters
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can insert own project chapters" ON chapters
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can update own project chapters" ON chapters
  FOR UPDATE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can delete own project chapters" ON chapters
  FOR DELETE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- CHARACTERS: Users can manage characters in their own projects
DROP POLICY IF EXISTS "Users can access own project characters" ON characters;

CREATE POLICY "Users can access own project characters" ON characters
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- STORY ARCS: Users can manage story arcs in their own projects
DROP POLICY IF EXISTS "Users can access own project story_arcs" ON story_arcs;

CREATE POLICY "Users can access own project story_arcs" ON story_arcs
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- AGENT LOGS: Users can view AI agent logs for their own projects
DROP POLICY IF EXISTS "Users can access own project agent_logs" ON agent_logs;

CREATE POLICY "Users can access own project agent_logs" ON agent_logs
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- REFERENCE MATERIALS: Users can manage reference materials for their own projects
DROP POLICY IF EXISTS "Users can access own project reference_materials" ON reference_materials;

CREATE POLICY "Users can access own project reference_materials" ON reference_materials
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- STORY BIBLE: Users can manage story bible entries for their own projects
DROP POLICY IF EXISTS "Users can access own project story_bible" ON story_bible;

CREATE POLICY "Users can access own project story_bible" ON story_bible
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- ============================================================================
-- 4. VERSIONING & HISTORY TABLES
-- ============================================================================

-- CHAPTER VERSIONS: Users can access versions of chapters in their own projects
DROP POLICY IF EXISTS "Users can access own chapter versions" ON chapter_versions;

CREATE POLICY "Users can access own chapter versions" ON chapter_versions
  FOR ALL USING (
    chapter_id IN (
      SELECT id FROM chapters WHERE project_id IN (
        SELECT id FROM projects WHERE user_id = auth.uid()
      )
    )
  );

-- PROJECT SNAPSHOTS: Users can access snapshots of their own projects
DROP POLICY IF EXISTS "Users can access own project snapshots" ON project_snapshots;

CREATE POLICY "Users can access own project snapshots" ON project_snapshots
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- EDITING SESSIONS: Users can access their own editing sessions
DROP POLICY IF EXISTS "Users can access own editing sessions" ON editing_sessions;

CREATE POLICY "Users can access own editing sessions" ON editing_sessions
  FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- 5. AI & ANALYTICS TABLES
-- ============================================================================

-- CONTENT EMBEDDINGS: Users can access embeddings for their own projects
DROP POLICY IF EXISTS "Users can access own content embeddings" ON content_embeddings;

CREATE POLICY "Users can access own content embeddings" ON content_embeddings
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- AI SUGGESTIONS: Users can access AI suggestions for their own projects
DROP POLICY IF EXISTS "Users can access own ai_suggestions" ON ai_suggestions;

CREATE POLICY "Users can access own ai_suggestions" ON ai_suggestions
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- SELECTION ANALYTICS: Users can access their own selection analytics
DROP POLICY IF EXISTS "Users can access own selection analytics" ON selection_analytics;

CREATE POLICY "Users can access own selection analytics" ON selection_analytics
  FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- 6. WRITING & PRODUCTIVITY TABLES
-- ============================================================================

-- WRITING SESSIONS: Users can access their own writing sessions
DROP POLICY IF EXISTS "Users can access own writing sessions" ON writing_sessions;

CREATE POLICY "Users can access own writing sessions" ON writing_sessions
  FOR ALL USING (auth.uid() = user_id);

-- WRITING GOALS: Users can manage their own writing goals
DROP POLICY IF EXISTS "Users can access own writing goals" ON writing_goals;

CREATE POLICY "Users can access own writing goals" ON writing_goals
  FOR ALL USING (auth.uid() = user_id);

-- WRITING GOAL PROGRESS: Users can access their own goal progress
DROP POLICY IF EXISTS "Users can access own writing goal progress" ON writing_goal_progress;

CREATE POLICY "Users can access own writing goal progress" ON writing_goal_progress
  FOR ALL USING (
    goal_id IN (SELECT id FROM writing_goals WHERE user_id = auth.uid())
  );

-- NOTIFICATIONS: Users can access their own notifications
DROP POLICY IF EXISTS "Users can access own notifications" ON notifications;

CREATE POLICY "Users can access own notifications" ON notifications
  FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- 7. SERIES MANAGEMENT TABLES
-- ============================================================================

-- SERIES: Users can manage their own series
DROP POLICY IF EXISTS "Users can access own series" ON series;

CREATE POLICY "Users can access own series" ON series
  FOR ALL USING (auth.uid() = user_id);

-- SERIES BOOKS: Users can manage books in their own series
DROP POLICY IF EXISTS "Users can access own series books" ON series_books;

CREATE POLICY "Users can access own series books" ON series_books
  FOR ALL USING (
    series_id IN (SELECT id FROM series WHERE user_id = auth.uid())
  );

-- ============================================================================
-- 8. TEMPLATES & REUSABLE CONTENT
-- ============================================================================

-- SELECTION PROFILES: Users can access their own selection profiles + public ones
DROP POLICY IF EXISTS "Users can access selection profiles" ON selection_profiles;

CREATE POLICY "Users can access selection profiles" ON selection_profiles
  FOR SELECT USING (
    auth.uid() = user_id OR is_public = true
  );

CREATE POLICY "Users can manage own selection profiles" ON selection_profiles
  FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- 9. COLLABORATION TABLES
-- ============================================================================

-- COLLABORATION SESSIONS: Users can access sessions they participate in
DROP POLICY IF EXISTS "Users can access collaboration sessions" ON collaboration_sessions;

CREATE POLICY "Users can access collaboration sessions" ON collaboration_sessions
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid()) OR
    id IN (SELECT session_id FROM collaboration_participants WHERE user_id = auth.uid())
  );

-- COLLABORATION PARTICIPANTS: Users can view participants in their sessions
DROP POLICY IF EXISTS "Users can access collaboration participants" ON collaboration_participants;

CREATE POLICY "Users can access collaboration participants" ON collaboration_participants
  FOR ALL USING (
    session_id IN (
      SELECT id FROM collaboration_sessions WHERE 
      project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
    ) OR auth.uid() = user_id
  );

-- ============================================================================
-- 10. BACKGROUND PROCESSING
-- ============================================================================

-- PROCESSING TASKS: Users can view their own processing tasks
DROP POLICY IF EXISTS "Users can access own processing tasks" ON processing_tasks;

CREATE POLICY "Users can access own processing tasks" ON processing_tasks
  FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- POLICY SUMMARY COMPLETE
-- ============================================================================
