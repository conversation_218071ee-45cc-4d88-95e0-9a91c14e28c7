/**
 * Stripe utility functions for client-side usage
 */

import Stripe from 'stripe'
import { logger } from '@/lib/services/logger';

import { config } from './config'

// Server-side Stripe instance (only use on server)
export const stripe = new Stripe(config.stripe.secretKey, {
  apiVersion: '2025-06-30.basil',
})

// Export price IDs for easy access
export const STRIPE_PRICES = {
  basic: config.stripe.prices.basic,
  pro: config.stripe.prices.pro,
  enterprise: config.stripe.prices.enterprise,
} as const

/**
 * Creates a charge using the Stripe API
 * 
 * @param amount - Amount in cents (e.g., 2000 for $20.00)
 * @param currency - Currency code (e.g., 'usd')
 * @param source - Payment source token from Stripe.js
 * @param description - Description of the charge
 * @returns The charge object from Stripe
 */
export async function createCharge({
  amount,
  currency = 'usd',
  source,
  description,
}: {
  amount: number;
  currency?: string;
  source: string;
  description?: string;
}) {
  try {
    const response = await fetch('/api/payment/charge', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount,
        currency,
        source,
        description,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to process payment');
    }

    return await response.json();
  } catch (error) {
    logger.error('Payment processing error:', error);
    throw error;
  }
}

/**
 * Formats a price from cents to a readable currency string
 * 
 * @param amount - Amount in cents
 * @param currency - Currency code (default: 'usd')
 * @returns Formatted price string
 */
export function formatPrice(amount: number, currency = 'usd') {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  });
  
  return formatter.format(amount / 100);
}