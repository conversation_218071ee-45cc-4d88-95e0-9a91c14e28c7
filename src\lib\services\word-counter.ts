import { logger } from './logger'

/**
 * Service for counting words in generated content
 */
export class WordCounter {
  private static instance: WordCounter
  
  private constructor() {}
  
  static getInstance(): WordCounter {
    if (!WordCounter.instance) {
      WordCounter.instance = new WordCounter()
    }
    return WordCounter.instance
  }

  /**
   * Count words in a text string
   */
  countWords(text: string): number {
    if (!text || typeof text !== 'string') {
      return 0
    }
    
    // Remove extra whitespace and split by word boundaries
    const words = text
      .trim()
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .split(/\s+/) // Split by whitespace
      .filter(word => word.length > 0) // Remove empty strings
    
    return words.length
  }

  /**
   * Count words in structured content (e.g., chapter with metadata)
   */
  countStructuredContent(content: Record<string, unknown>): number {
    let totalWords = 0
    
    const countInObject = (obj: unknown): void => {
      if (typeof obj === 'string') {
        totalWords += this.countWords(obj)
      } else if (Array.isArray(obj)) {
        obj.forEach(item => countInObject(item))
      } else if (obj && typeof obj === 'object') {
        Object.values(obj).forEach(value => countInObject(value))
      }
    }
    
    countInObject(content)
    return totalWords
  }

  /**
   * Estimate token count to word count
   * OpenAI tokens are roughly 0.75 words on average
   */
  estimateWordsFromTokens(tokens: number): number {
    return Math.round(tokens * 0.75)
  }

  /**
   * Estimate tokens from word count
   * Inverse of the above calculation
   */
  estimateTokensFromWords(words: number): number {
    return Math.round(words / 0.75)
  }

  /**
   * Format word count for display
   */
  formatWordCount(count: number): string {
    if (count < 1000) {
      return count.toString()
    } else if (count < 1000000) {
      return `${(count / 1000).toFixed(1)}k`
    } else {
      return `${(count / 1000000).toFixed(2)}M`
    }
  }

  /**
   * Calculate reading time estimate
   * Average reading speed is 200-250 words per minute
   */
  estimateReadingTime(wordCount: number, wordsPerMinute: number = 225): {
    minutes: number
    formatted: string
  } {
    const minutes = Math.ceil(wordCount / wordsPerMinute)
    
    let formatted: string
    if (minutes < 1) {
      formatted = 'Less than 1 minute'
    } else if (minutes === 1) {
      formatted = '1 minute'
    } else if (minutes < 60) {
      formatted = `${minutes} minutes`
    } else {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      if (remainingMinutes === 0) {
        formatted = `${hours} hour${hours > 1 ? 's' : ''}`
      } else {
        formatted = `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes} minutes`
      }
    }
    
    return { minutes, formatted }
  }

  /**
   * Track word usage for a user
   */
  async trackWordUsage(
    userId: string,
    wordCount: number,
    source: string,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    try {
      // Separate tracking for embeddings vs generation
      const isEmbedding = metadata?.taskType && [
        'semantic-search',
        'content-similarity',
        'recommendations',
        'relationship-analysis',
        'theme-extraction'
      ].includes(String(metadata.taskType))
      
      // Log with appropriate category
      logger.info('Word usage tracked', {
        userId,
        wordCount,
        source,
        category: isEmbedding ? 'embedding' : 'generation',
        metadata,
        timestamp: new Date().toISOString()
      })
      
      // Embeddings use much less of the word budget (10% weight)
      const adjustedWordCount = isEmbedding ? Math.ceil(wordCount * 0.1) : wordCount
      
      // Update usage in database
      if (metadata?.updateDatabase !== false) {
        const { trackUsage } = await import('../usage-tracker')
        await trackUsage({
          userId,
          eventType: 'ai_words',
          amount: adjustedWordCount,
          metadata: {
            ...metadata,
            originalWordCount: wordCount,
            isEmbedding
          }
        })
      }
    } catch (error) {
      logger.error('Failed to track word usage', error)
    }
  }

  /**
   * Get remaining words for a user this month
   */
  async getRemainingWords(
    userId: string,
    currentUsage: number,
    monthlyLimit: number
  ): Promise<{
    used: number
    limit: number
    remaining: number
    percentage: number
  }> {
    const remaining = Math.max(0, monthlyLimit - currentUsage)
    const percentage = monthlyLimit > 0 ? (currentUsage / monthlyLimit) * 100 : 0
    
    return {
      used: currentUsage,
      limit: monthlyLimit,
      remaining,
      percentage: Math.min(100, Math.round(percentage))
    }
  }

  /**
   * Check if user has enough words remaining
   */
  canGenerateWords(
    currentUsage: number,
    monthlyLimit: number,
    estimatedWords: number
  ): boolean {
    if (monthlyLimit === -1) {
      return true // Unlimited
    }
    
    return currentUsage + estimatedWords <= monthlyLimit
  }

  /**
   * Get usage statistics for display
   */
  getUsageStats(
    used: number,
    limit: number
  ): {
    formatted: string
    percentage: number
    isUnlimited: boolean
    isNearLimit: boolean
    isOverLimit: boolean
  } {
    const isUnlimited = limit === -1
    const percentage = isUnlimited ? 0 : (used / limit) * 100
    const isNearLimit = percentage >= 80 && percentage < 100
    const isOverLimit = percentage >= 100
    
    const formatted = isUnlimited
      ? `${this.formatWordCount(used)} words used`
      : `${this.formatWordCount(used)} / ${this.formatWordCount(limit)} words`
    
    return {
      formatted,
      percentage: Math.min(100, Math.round(percentage)),
      isUnlimited,
      isNearLimit,
      isOverLimit
    }
  }
}

export const wordCounter = WordCounter.getInstance()