const fs = require('fs');
const path = require('path');

const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];
const EXCLUDE_DIRS = ['node_modules', '.next', 'out', 'dist', '.git'];
const EXCLUDE_FILES = ['logger.ts', 'replace-console-logs.js'];

// Patterns to replace
const patterns = [
  {
    // console.log with single argument
    pattern: /console\.log\(([^)]+)\);?/g,
    replacement: (match, args) => `logger.info(${args});`
  },
  {
    // console.error with single argument
    pattern: /console\.error\(([^)]+)\);?/g,
    replacement: (match, args) => `logger.error(${args});`
  },
  {
    // console.warn with single argument
    pattern: /console\.warn\(([^)]+)\);?/g,
    replacement: (match, args) => `logger.warn(${args});`
  },
  {
    // console.debug with single argument
    pattern: /console\.debug\(([^)]+)\);?/g,
    replacement: (match, args) => `logger.debug(${args});`
  }
];

function shouldProcessFile(filePath) {
  const ext = path.extname(filePath);
  const fileName = path.basename(filePath);
  
  if (!EXTENSIONS.includes(ext)) return false;
  if (EXCLUDE_FILES.includes(fileName)) return false;
  
  for (const excludeDir of EXCLUDE_DIRS) {
    if (filePath.includes(excludeDir)) return false;
  }
  
  return true;
}

function processFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let hasConsoleLog = false;
  
  // Check if file contains any console statements
  if (content.includes('console.')) {
    hasConsoleLog = true;
  }
  
  if (!hasConsoleLog) return;
  
  // Apply replacements
  patterns.forEach(({ pattern, replacement }) => {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement);
      modified = true;
    }
  });
  
  if (modified) {
    // Add logger import if not already present
    if (!content.includes("import { logger }") && !content.includes("logger from")) {
      const importStatement = `import { logger } from '@/lib/services/logger';\n`;
      
      // Try to add after other imports
      const importMatch = content.match(/^import .* from ['"].*['"];?\s*$/m);
      if (importMatch) {
        const lastImportIndex = content.lastIndexOf(importMatch[0]) + importMatch[0].length;
        content = content.slice(0, lastImportIndex) + '\n' + importStatement + content.slice(lastImportIndex);
      } else {
        // Add at the beginning if no imports found
        content = importStatement + '\n' + content;
      }
    }
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Processed: ${filePath}`);
  }
}

function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);
  
  items.forEach(item => {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !EXCLUDE_DIRS.includes(item)) {
      processDirectory(fullPath);
    } else if (stat.isFile() && shouldProcessFile(fullPath)) {
      processFile(fullPath);
    }
  });
}

// Start processing from src directory
const srcPath = path.join(__dirname, '..', 'src');
console.log('🔄 Starting console.log replacement...');
processDirectory(srcPath);
console.log('✅ Console.log replacement complete!');
console.log('⚠️  Please review the changes and test your application.');