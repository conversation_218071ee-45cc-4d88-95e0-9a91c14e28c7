'use client'

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Moon, Sun } from 'lucide-react'

const themes = {
  light: ['writers-sanctuary-light', 'forest-manuscript-light'],
  dark: ['evening-study-dark', 'midnight-ink-dark']
}

export function SimpleThemeToggle() {
  const [isDark, setIsDark] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    const savedTheme = localStorage.getItem('bookscribe-theme')
    const htmlClasses = document.documentElement.className
    setIsDark(savedTheme?.includes('dark') || htmlClasses.includes('dark'))
  }, [])

  const toggleTheme = () => {
    const newIsDark = !isDark
    const themeList = newIsDark ? themes.dark : themes.light
    const newTheme = themeList[0] // Default to first theme in category
    
    // Update DOM
    document.documentElement.className = document.documentElement.className
      .replace(/writers-sanctuary-light|forest-manuscript-light|evening-study-dark|midnight-ink-dark|light|dark/g, '')
      .trim()
    
    document.documentElement.classList.add(newTheme)
    if (newIsDark) {
      document.documentElement.classList.add('dark')
    }
    
    // Update storage
    localStorage.setItem('bookscribe-theme', newTheme)
    
    // Update state
    setIsDark(newIsDark)
    
    // Dispatch event for other components
    window.dispatchEvent(new CustomEvent('theme-change', { detail: { theme: newTheme, isDark: newIsDark } }))
  }

  if (!mounted) {
    return (
      <Button variant="ghost" size="icon" className="h-9 w-9" disabled>
        <div className="h-4 w-4" />
      </Button>
    )
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="h-9 w-9"
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} theme`}
    >
      <Sun className={`h-4 w-4 transition-all ${isDark ? 'rotate-90 scale-0' : 'rotate-0 scale-100'}`} />
      <Moon className={`absolute h-4 w-4 transition-all ${isDark ? 'rotate-0 scale-100' : '-rotate-90 scale-0'}`} />
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}