'use client'

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Moon, Sun } from 'lucide-react'
import { useTheme } from '@/hooks/use-theme'

export function SimpleThemeToggle() {
  const { theme, setTheme, themes, mounted } = useTheme()
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient || !mounted) {
    return (
      <Button variant="ghost" size="icon" className="h-9 w-9" disabled>
        <div className="h-4 w-4" />
      </Button>
    )
  }

  const currentTheme = themes.find(t => t.id === theme)
  const isDark = currentTheme?.mode === 'dark'

  const toggleTheme = () => {
    // Find next theme of opposite mode
    const targetMode = isDark ? 'light' : 'dark'
    const nextTheme = themes.find(t => t.mode === targetMode)
    if (nextTheme) {
      setTheme(nextTheme.id)
    }
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="h-9 w-9"
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} theme`}
    >
      <Sun className={`h-4 w-4 transition-all ${isDark ? 'rotate-90 scale-0' : 'rotate-0 scale-100'}`} />
      <Moon className={`absolute h-4 w-4 transition-all ${isDark ? 'rotate-0 scale-100' : '-rotate-90 scale-0'}`} />
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}