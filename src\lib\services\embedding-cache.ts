import { logger } from './logger'
import { createClient } from '../supabase/client'

interface CachedEmbedding {
  text: string
  embedding: number[]
  model: string
  createdAt: Date
  lastAccessed: Date
}

/**
 * Service for caching embeddings to reduce API calls and costs
 * Embeddings are deterministic, so we can cache them aggressively
 */
export class EmbeddingCacheService {
  private static instance: EmbeddingCacheService
  private memoryCache: Map<string, CachedEmbedding>
  private maxMemoryCacheSize = 10000 // Keep 10k embeddings in memory
  private cacheHitRate = { hits: 0, misses: 0 }
  
  private constructor() {
    this.memoryCache = new Map()
    this.startCleanupTask()
  }
  
  static getInstance(): EmbeddingCacheService {
    if (!EmbeddingCacheService.instance) {
      EmbeddingCacheService.instance = new EmbeddingCacheService()
    }
    return EmbeddingCacheService.instance
  }

  /**
   * Get embedding from cache or null if not found
   */
  async get(text: string, model: string): Promise<number[] | null> {
    const key = this.generateKey(text, model)
    
    // Check memory cache first
    const memoryHit = this.memoryCache.get(key)
    if (memoryHit) {
      memoryHit.lastAccessed = new Date()
      this.cacheHitRate.hits++
      return memoryHit.embedding
    }
    
    // Check database cache
    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('embedding_cache')
        .select('embedding')
        .eq('cache_key', key)
        .single()
      
      if (!error && data) {
        // Add to memory cache
        this.addToMemoryCache(key, {
          text,
          embedding: data.embedding,
          model,
          createdAt: new Date(),
          lastAccessed: new Date()
        })
        
        this.cacheHitRate.hits++
        return data.embedding
      }
    } catch (error) {
      logger.error('Error fetching from embedding cache:', error)
    }
    
    this.cacheHitRate.misses++
    return null
  }

  /**
   * Store embedding in cache
   */
  async set(text: string, model: string, embedding: number[]): Promise<void> {
    const key = this.generateKey(text, model)
    
    // Add to memory cache
    this.addToMemoryCache(key, {
      text,
      embedding,
      model,
      createdAt: new Date(),
      lastAccessed: new Date()
    })
    
    // Store in database asynchronously
    this.storeToDatabaseAsync(key, text, model, embedding)
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    memoryCacheSize: number
    hitRate: number
    totalRequests: number
  } {
    const totalRequests = this.cacheHitRate.hits + this.cacheHitRate.misses
    const hitRate = totalRequests > 0 
      ? (this.cacheHitRate.hits / totalRequests) * 100 
      : 0
    
    return {
      memoryCacheSize: this.memoryCache.size,
      hitRate: Math.round(hitRate),
      totalRequests
    }
  }

  /**
   * Clear cache (useful for testing)
   */
  async clear(): Promise<void> {
    this.memoryCache.clear()
    this.cacheHitRate = { hits: 0, misses: 0 }
    
    try {
      const supabase = createClient()
      await supabase
        .from('embedding_cache')
        .delete()
        .gte('created_at', '1900-01-01') // Delete all
    } catch (error) {
      logger.error('Error clearing embedding cache:', error)
    }
  }

  /**
   * Generate cache key from text and model
   */
  private generateKey(text: string, model: string): string {
    // Simple hash function for consistent keys
    const textHash = this.hashString(text)
    return `${model}_${textHash}`
  }

  /**
   * Hash string to create consistent cache keys
   */
  private hashString(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * Add to memory cache with LRU eviction
   */
  private addToMemoryCache(key: string, value: CachedEmbedding): void {
    // Remove oldest entries if cache is full
    if (this.memoryCache.size >= this.maxMemoryCacheSize) {
      // Find oldest entry
      let oldestKey = ''
      let oldestTime = new Date()
      
      for (const [k, v] of this.memoryCache.entries()) {
        if (v.lastAccessed < oldestTime) {
          oldestTime = v.lastAccessed
          oldestKey = k
        }
      }
      
      if (oldestKey) {
        this.memoryCache.delete(oldestKey)
      }
    }
    
    this.memoryCache.set(key, value)
  }

  /**
   * Store to database asynchronously
   */
  private async storeToDatabaseAsync(
    key: string,
    text: string,
    model: string,
    embedding: number[]
  ): Promise<void> {
    try {
      const supabase = createClient()
      
      // Upsert to handle duplicates
      await supabase
        .from('embedding_cache')
        .upsert({
          cache_key: key,
          text_hash: this.hashString(text),
          model,
          embedding,
          text_length: text.length,
          created_at: new Date().toISOString(),
          last_accessed: new Date().toISOString()
        })
    } catch (error) {
      // Log but don't throw - caching failures shouldn't break the app
      logger.error('Error storing to embedding cache:', error)
    }
  }

  /**
   * Start periodic cleanup of old cache entries
   */
  private startCleanupTask(): void {
    // Clean up old entries every hour
    setInterval(async () => {
      try {
        const supabase = createClient()
        
        // Delete entries not accessed in 30 days
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        
        await supabase
          .from('embedding_cache')
          .delete()
          .lt('last_accessed', thirtyDaysAgo.toISOString())
        
        // Log cache stats
        const stats = this.getStats()
        logger.info('Embedding cache stats:', stats)
      } catch (error) {
        logger.error('Error cleaning embedding cache:', error)
      }
    }, 60 * 60 * 1000) // Every hour
  }

  /**
   * Batch get embeddings (efficient for multiple lookups)
   */
  async batchGet(
    items: Array<{ text: string; model: string }>
  ): Promise<Map<string, number[] | null>> {
    const results = new Map<string, number[] | null>()
    const missingKeys: string[] = []
    
    // Check memory cache first
    for (const item of items) {
      const key = this.generateKey(item.text, item.model)
      const cached = this.memoryCache.get(key)
      
      if (cached) {
        results.set(key, cached.embedding)
        cached.lastAccessed = new Date()
        this.cacheHitRate.hits++
      } else {
        missingKeys.push(key)
        results.set(key, null)
      }
    }
    
    // Batch fetch missing from database
    if (missingKeys.length > 0) {
      try {
        const supabase = createClient()
        const { data, error } = await supabase
          .from('embedding_cache')
          .select('cache_key, embedding')
          .in('cache_key', missingKeys)
        
        if (!error && data) {
          for (const row of data) {
            results.set(row.cache_key, row.embedding)
            this.cacheHitRate.hits++
            
            // Add to memory cache
            const item = items.find(i => 
              this.generateKey(i.text, i.model) === row.cache_key
            )
            if (item) {
              this.addToMemoryCache(row.cache_key, {
                text: item.text,
                embedding: row.embedding,
                model: item.model,
                createdAt: new Date(),
                lastAccessed: new Date()
              })
            }
          }
        }
      } catch (error) {
        logger.error('Error batch fetching from embedding cache:', error)
      }
    }
    
    // Count remaining misses
    for (const [key, value] of results.entries()) {
      if (value === null && !missingKeys.includes(key)) {
        this.cacheHitRate.misses++
      }
    }
    
    return results
  }
}

export const embeddingCache = EmbeddingCacheService.getInstance()