'use client'

import { ThemeToggle } from '@/components/ui/theme-toggle'
import { useTheme } from '@/hooks/use-theme'
import { useEffect, useState } from 'react'

export default function TestThemeDropdownPage() {
  const [mounted, setMounted] = useState(false)
  const { theme, currentTheme, availableThemes, mounted: themesMounted } = useTheme()

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <div>Loading...</div>
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-6">Theme Dropdown Test</h1>
      
      <div className="space-y-4">
        <div className="border rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-2">Theme Toggle Component</h2>
          <ThemeToggle />
        </div>

        <div className="border rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-2">Current Theme Info</h2>
          <p>Next-themes theme: {theme || 'Not set'}</p>
          <p>Current theme ID: {currentTheme || 'Not set'}</p>
          <p>Themes mounted: {themesMounted ? 'Yes' : 'No'}</p>
        </div>

        <div className="border rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-2">Available Themes</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(availableThemes, null, 2)}
          </pre>
        </div>

        <div className="border rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-2">Document Info</h2>
          <p>HTML classes: {typeof document !== 'undefined' ? document.documentElement.className : 'N/A'}</p>
          <p>Data-theme: {typeof document !== 'undefined' ? document.documentElement.getAttribute('data-theme') : 'N/A'}</p>
        </div>
      </div>
    </div>
  )
}