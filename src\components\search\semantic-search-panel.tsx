'use client';

import { useState, useEffect } from 'react';
import { logger } from '@/lib/services/logger';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Search, 
  Sparkles, 
  BookOpen, 
  Users, 
  MapPin, 
  Hash,
  Heart,
  Palette,
  Clock,
  Filter,
  X
} from 'lucide-react';

interface SearchResult {
  id: string;
  content: string;
  metadata: {
    projectId: string;
    chapterId?: string;
    type: 'chapter' | 'character' | 'scene' | 'note' | 'world' | 'plot';
    title?: string;
    tags?: string[];
    relevance: number;
  };
  similarity: number;
}

interface SemanticSearchPanelProps {
  projectId: string;
  onResultSelect?: (result: SearchResult) => void;
}

export function SemanticSearchPanel({ projectId, onResultSelect }: SemanticSearchPanelProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchType, setSearchType] = useState<'general' | 'emotion' | 'theme' | 'character'>('general');
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [selectedEmotion, setSelectedEmotion] = useState('');
  const [selectedCharacter, setSelectedCharacter] = useState('');
  const [characters, setCharacters] = useState<string[]>([]);

  useEffect(() => {
    loadRecentSearches();
    loadCharacters();
  }, [projectId]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadRecentSearches = () => {
    const saved = localStorage.getItem(`semantic_searches_${projectId}`);
    if (saved) {
      setRecentSearches(JSON.parse(saved).slice(0, 5));
    }
  };

  const loadCharacters = async () => {
    // Load characters from story bible
    try {
      const response = await fetch(`/api/projects/${projectId}/story-bible`);
      if (response.ok) {
        const data = await response.json();
        const characterNames = data.character_data?.protagonists?.map((c: { name: string }) => c.name) || [];
        setCharacters(characterNames);
      }
    } catch (error) {
      logger.error('Failed to load characters:', error);
    }
  };

  const saveSearch = (query: string) => {
    const searches = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5);
    setRecentSearches(searches);
    localStorage.setItem(`semantic_searches_${projectId}`, JSON.stringify(searches));
  };

  const handleSearch = async () => {
    if (!searchQuery.trim() && searchType === 'general') return;

    setIsSearching(true);
    saveSearch(searchQuery);

    try {
      let endpoint = '/api/search/semantic';
      const body: Record<string, unknown> = {
        projectId,
        types: selectedTypes.length > 0 ? selectedTypes : undefined,
        tags: selectedTags.length > 0 ? selectedTags : undefined,
        limit: 20
      };

      switch (searchType) {
        case 'general':
          body.query = searchQuery;
          break;
        case 'emotion':
          endpoint = '/api/search/emotion';
          body.emotion = selectedEmotion || searchQuery;
          break;
        case 'theme':
          endpoint = '/api/search/theme';
          body.theme = searchQuery;
          break;
        case 'character':
          endpoint = '/api/search/character-moments';
          body.characterName = selectedCharacter || searchQuery;
          if (selectedEmotion) body.emotion = selectedEmotion;
          break;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      });

      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.results || []);
      }
    } catch (error) {
      logger.error('Search failed:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'chapter': return <BookOpen className="w-4 h-4" />;
      case 'character': return <Users className="w-4 h-4" />;
      case 'world': return <MapPin className="w-4 h-4" />;
      case 'scene': return <Sparkles className="w-4 h-4" />;
      case 'note': return <Hash className="w-4 h-4" />;
      default: return <BookOpen className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'chapter': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'character': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'world': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
      case 'scene': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'note': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
      default: return 'bg-slate-100 text-slate-800 dark:bg-slate-900/20 dark:text-slate-300';
    }
  };

  const highlightMatch = (text: string, query: string) => {
    if (!query) return text;
    
    const parts = text.split(new RegExp(`(${query})`, 'gi'));
    return parts.map((part, i) => 
      part.toLowerCase() === query.toLowerCase() 
        ? <mark key={i} className="bg-yellow-200 dark:bg-yellow-900">{part}</mark>
        : part
    );
  };

  const toggleType = (type: string) => {
    setSelectedTypes(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
  };

  const emotions = [
    'joy', 'sadness', 'anger', 'fear', 'surprise', 
    'love', 'hope', 'despair', 'excitement', 'tension'
  ];

  const themes = [
    'redemption', 'betrayal', 'love', 'loss', 'identity',
    'power', 'sacrifice', 'family', 'friendship', 'revenge'
  ];

  return (
    <div className="h-full flex flex-col">
      <Card className="flex-1 flex flex-col">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Search className="w-5 h-5 mr-2" />
            Semantic Search
          </CardTitle>
          <CardDescription>
            Find content using AI-powered semantic understanding
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col">
          <Tabs value={searchType} onValueChange={(v: string) => setSearchType(v as 'general' | 'emotion' | 'theme' | 'character')}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="emotion">Emotion</TabsTrigger>
              <TabsTrigger value="theme">Theme</TabsTrigger>
              <TabsTrigger value="character">Character</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4">
              <div className="flex space-x-2">
                <Input
                  placeholder="Search for anything in your story..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="flex-1"
                />
                <Button onClick={handleSearch} disabled={isSearching}>
                  <Search className="w-4 h-4" />
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="emotion" className="space-y-4">
              <Select value={selectedEmotion} onValueChange={setSelectedEmotion}>
                <SelectTrigger>
                  <SelectValue placeholder="Select an emotion to search for" />
                </SelectTrigger>
                <SelectContent>
                  {emotions.map(emotion => (
                    <SelectItem key={emotion} value={emotion}>
                      <div className="flex items-center">
                        <Heart className="w-4 h-4 mr-2" />
                        {emotion.charAt(0).toUpperCase() + emotion.slice(1)}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button 
                onClick={handleSearch} 
                disabled={isSearching || !selectedEmotion}
                className="w-full"
              >
                Find {selectedEmotion} Moments
              </Button>
            </TabsContent>

            <TabsContent value="theme" className="space-y-4">
              <div className="flex flex-wrap gap-2 mb-4">
                {themes.map(theme => (
                  <Badge
                    key={theme}
                    variant="outline"
                    className="cursor-pointer"
                    onClick={() => setSearchQuery(theme)}
                  >
                    <Palette className="w-3 h-3 mr-1" />
                    {theme}
                  </Badge>
                ))}
              </div>
              <div className="flex space-x-2">
                <Input
                  placeholder="Search for a theme..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="flex-1"
                />
                <Button onClick={handleSearch} disabled={isSearching}>
                  Search Theme
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="character" className="space-y-4">
              <Select value={selectedCharacter} onValueChange={setSelectedCharacter}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a character" />
                </SelectTrigger>
                <SelectContent>
                  {characters.map(character => (
                    <SelectItem key={character} value={character}>
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-2" />
                        {character}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={selectedEmotion} onValueChange={setSelectedEmotion}>
                <SelectTrigger>
                  <SelectValue placeholder="Optional: Filter by emotion" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any emotion</SelectItem>
                  {emotions.map(emotion => (
                    <SelectItem key={emotion} value={emotion}>
                      {emotion.charAt(0).toUpperCase() + emotion.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button 
                onClick={handleSearch} 
                disabled={isSearching || !selectedCharacter}
                className="w-full"
              >
                Find Character Moments
              </Button>
            </TabsContent>
          </Tabs>

          {/* Filters */}
          <div className="mt-4 space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium flex items-center">
                <Filter className="w-4 h-4 mr-1" />
                Filters
              </h4>
              {(selectedTypes.length > 0 || selectedTags.length > 0) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSelectedTypes([]);
                    setSelectedTags([]);
                  }}
                >
                  Clear all
                </Button>
              )}
            </div>

            <div className="flex flex-wrap gap-2">
              {['chapter', 'character', 'scene', 'note', 'world'].map(type => (
                <Badge
                  key={type}
                  variant={selectedTypes.includes(type) ? 'default' : 'outline'}
                  className="cursor-pointer"
                  onClick={() => toggleType(type)}
                >
                  {getTypeIcon(type)}
                  <span className="ml-1">{type}</span>
                  {selectedTypes.includes(type) && (
                    <X className="w-3 h-3 ml-1" />
                  )}
                </Badge>
              ))}
            </div>
          </div>

          {/* Recent Searches */}
          {recentSearches.length > 0 && searchResults.length === 0 && !isSearching && (
            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2 flex items-center">
                <Clock className="w-4 h-4 mr-1" />
                Recent Searches
              </h4>
              <div className="space-y-1">
                {recentSearches.map((search, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setSearchQuery(search);
                      handleSearch();
                    }}
                    className="text-sm text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-100 text-left w-full p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-800"
                  >
                    {search}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Search Results */}
          <ScrollArea className="flex-1 mt-4">
            {isSearching ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-full" />
                    <Skeleton className="h-3 w-5/6" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-3">
                {searchResults.map((result) => (
                  <Card
                    key={result.id}
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => onResultSelect?.(result)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Badge className={getTypeColor(result.metadata.type)}>
                            {getTypeIcon(result.metadata.type)}
                            <span className="ml-1">{result.metadata.type}</span>
                          </Badge>
                          {result.metadata.title && (
                            <span className="font-medium text-sm">
                              {result.metadata.title}
                            </span>
                          )}
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {Math.round(result.similarity * 100)}% match
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-slate-600 dark:text-slate-400 line-clamp-3">
                        {highlightMatch(result.content, searchQuery)}
                      </p>

                      {result.metadata.tags && result.metadata.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {result.metadata.tags.map((tag, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}

                {searchResults.length === 0 && searchQuery && !isSearching && (
                  <div className="text-center py-8 text-slate-500">
                    <Search className="w-12 h-12 mx-auto mb-2 opacity-20" />
                    <p>No results found for your search.</p>
                    <p className="text-sm mt-1">Try different keywords or filters.</p>
                  </div>
                )}
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}