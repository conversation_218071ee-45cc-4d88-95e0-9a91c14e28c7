/**
 * Monaco Editor Worker Configuration
 * Configures web workers for Monaco Editor when using local installation
 */

import { Environment } from 'monaco-editor'

// Configure Monaco Editor environment for web workers
export function configureMonacoWorkers() {
  if (typeof window === 'undefined') return

  // Set up the Monaco environment
  ;(self as any).MonacoEnvironment = {
    getWorkerUrl: function (moduleId: string, label: string) {
      if (label === 'json') {
        return './json.worker.bundle.js'
      }
      if (label === 'css' || label === 'scss' || label === 'less') {
        return './css.worker.bundle.js'
      }
      if (label === 'html' || label === 'handlebars' || label === 'razor') {
        return './html.worker.bundle.js'
      }
      if (label === 'typescript' || label === 'javascript') {
        return './ts.worker.bundle.js'
      }
      return './editor.worker.bundle.js'
    }
  }
}

// Alternative approach using blob URLs for workers
export function configureMonacoWorkersWithBlob() {
  if (typeof window === 'undefined') return

  ;(self as any).MonacoEnvironment = {
    getWorker: function (moduleId: string, label: string) {
      const getWorkerModule = (moduleUrl: string, fallbackUrl: string) => {
        return new Worker(
          new URL(moduleUrl, import.meta.url),
          {
            name: label,
            type: 'module',
          }
        )
      }

      switch (label) {
        case 'json':
          return getWorkerModule(
            'monaco-editor/esm/vs/language/json/json.worker.js',
            'monaco-editor/esm/vs/language/json/json.worker.js'
          )
        case 'css':
        case 'scss':
        case 'less':
          return getWorkerModule(
            'monaco-editor/esm/vs/language/css/css.worker.js',
            'monaco-editor/esm/vs/language/css/css.worker.js'
          )
        case 'html':
        case 'handlebars':
        case 'razor':
          return getWorkerModule(
            'monaco-editor/esm/vs/language/html/html.worker.js',
            'monaco-editor/esm/vs/language/html/html.worker.js'
          )
        case 'typescript':
        case 'javascript':
          return getWorkerModule(
            'monaco-editor/esm/vs/language/typescript/ts.worker.js',
            'monaco-editor/esm/vs/language/typescript/ts.worker.js'
          )
        default:
          return getWorkerModule(
            'monaco-editor/esm/vs/editor/editor.worker.js',
            'monaco-editor/esm/vs/editor/editor.worker.js'
          )
      }
    }
  }
}

// Simplified worker configuration for basic text editing
export function configureBasicMonacoWorkers() {
  if (typeof window === 'undefined') return

  // Simple configuration that works with Next.js
  ;(self as any).MonacoEnvironment = {
    getWorkerUrl: function (moduleId: string, label: string) {
      // Return a data URL for a simple worker that doesn't do much
      // This prevents Monaco from trying to load external worker files
      return `data:text/javascript;charset=utf-8,${encodeURIComponent(`
        self.onmessage = function() {
          // Basic worker that just acknowledges messages
          self.postMessage({ type: 'ready' });
        };
      `)}`
    }
  }
}
