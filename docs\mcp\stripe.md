# Stripe Agent Toolkit MCP

The Stripe Agent Toolkit MCP provides comprehensive access to Stripe's payment processing APIs, enabling payment management, subscription handling, and financial operations directly within <PERSON>.

## Overview

Stripe MCP enables you to:
- Process payments and refunds
- Manage customers and subscriptions
- Create and manage products and prices
- Handle invoices and billing
- Search Stripe documentation
- Monitor account balance and disputes

## Configuration

### Environment Variables

Add these to your Claude Desktop config:

```json
{
  "mcpServers": {
    "stripe": {
      "command": "npx",
      "args": ["@stripe/mcp"],
      "env": {
        "STRIPE_SECRET_KEY": "sk_test_your_secret_key_here",
        "STRIPE_ACCOUNT": "acct_optional_connected_account"
      }
    }
  }
}
```

### Getting Stripe Credentials

1. **Secret Key**: Available in Stripe Dashboard → Developers → API Keys
   - Use `sk_test_*` for testing
   - Use `sk_live_*` for production
2. **Account ID** (Optional): For connected accounts in Stripe Connect

## Available Tools

### Payment Processing
- **List Payment Intents**: View payment attempts and status
- **Create Refunds**: Process full or partial refunds
- **List Refunds**: View refund history

### Customer Management
- **Create Customer**: Add new customers with metadata
- **List Customers**: Search and filter customer records
- **Update Customer**: Modify customer information

### Subscription Management
- **List Subscriptions**: View active and canceled subscriptions
- **Update Subscription**: Modify subscription plans or quantities
- **Cancel Subscription**: End subscriptions with options

### Product & Pricing
- **Create Product**: Add new products to catalog
- **List Products**: View available products
- **Create Price**: Set pricing for products
- **List Prices**: View all price configurations

### Invoicing
- **Create Invoice**: Generate invoices for customers
- **List Invoices**: View invoice history and status
- **Finalize Invoice**: Complete draft invoices
- **Create Invoice Item**: Add line items to invoices

### Promotional Tools
- **Create Coupon**: Generate discount codes
- **List Coupons**: View available promotions
- **Create Payment Link**: Generate shareable payment links

### Account Management
- **Retrieve Balance**: Check account balance and pending funds
- **List Disputes**: View chargebacks and disputes
- **Update Dispute**: Respond to disputes with evidence

### Documentation
- **Search Documentation**: Find Stripe API and guide information

## Example Usage

### Process a Payment
```
Create a payment intent for $50 USD for customer cus_abc123
```

### Manage Subscriptions
```
List all active subscriptions and show their next billing dates
```

### Create Products
```
Create a new product called "Premium Plan" with a monthly price of $29.99
```

### Handle Refunds
```
Create a partial refund of $25 for payment intent pi_xyz789
```

### Customer Service
```
Look up customer cus_abc123 and show their payment history
```

## Security Best Practices

### API Key Management
- Use test keys during development
- Store production keys securely
- Rotate keys regularly
- Use restricted API keys when possible

### Webhook Security
- Verify webhook signatures
- Use HTTPS endpoints only
- Implement proper error handling

### Data Protection
- Follow PCI compliance guidelines
- Never log sensitive payment data
- Use Stripe's secure tokenization

## Common Use Cases

### E-commerce Integration
- Process one-time payments
- Handle subscription billing
- Manage product catalogs
- Process refunds and exchanges

### SaaS Billing
- Manage subscription tiers
- Handle usage-based billing
- Process upgrades/downgrades
- Monitor churn and revenue

### Marketplace Operations
- Split payments between parties
- Manage connected accounts
- Handle multi-party transactions
- Process marketplace fees

### Financial Reporting
- Track revenue and growth
- Monitor failed payments
- Analyze customer lifetime value
- Generate financial reports

## Error Handling

### Common Error Types
- **Authentication**: Invalid API keys
- **Rate Limits**: Too many requests
- **Card Errors**: Declined payments
- **Validation**: Invalid parameters

### Retry Logic
- Implement exponential backoff
- Handle idempotency keys
- Check Stripe status page for outages

## Testing

### Test Mode
- Use test API keys (sk_test_*)
- Test card numbers available in Stripe docs
- Webhook testing with Stripe CLI

### Common Test Scenarios
- Successful payments
- Declined cards
- 3D Secure authentication
- Subscription lifecycle events

## Webhooks Integration

While MCP doesn't handle webhooks directly, you can:
- Query recent events via API
- Check webhook delivery status
- Retry failed webhook deliveries

## Rate Limits

- Standard rate limit: 100 requests/second
- Bursts allowed up to 1000 requests
- Monitor rate limit headers
- Implement proper backoff strategies

## Links

- [Stripe Agent Toolkit GitHub](https://github.com/stripe/agent-toolkit)
- [Stripe API Documentation](https://stripe.com/docs/api)
- [Stripe Testing Guide](https://stripe.com/docs/testing)