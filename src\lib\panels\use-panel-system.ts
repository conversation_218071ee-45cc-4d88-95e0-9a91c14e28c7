import { useEffect } from 'react'
import { PanelPluginManager } from './plugin-manager'
import { PANEL_PLUGINS } from './panel-registry'

export function usePanelSystem() {
  useEffect(() => {
    const manager = PanelPluginManager.getInstance()
    
    // Register all panels
    PANEL_PLUGINS.forEach(plugin => {
      try {
        manager.registerPlugin(plugin)
      } catch (error) {
        console.error(`Failed to register panel ${plugin.id}:`, error)
      }
    })
  }, [])
}

export function usePanelManager() {
  return PanelPluginManager.getInstance()
}