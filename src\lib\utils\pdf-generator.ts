import { format } from 'date-fns';

interface AnalyticsReportData {
  user: {
    id: string;
    name: string;
    bio?: string;
  };
  dateRange: {
    from: string;
    to: string;
  };
  overview: {
    totalWords: number;
    currentStreak: number;
    avgDailyWords: number;
    activeProjects: number;
  };
  activity: {
    dailyWordCount: Array<{ date: string; value: number }>;
    heatmapData: Array<{ date: string; count: number }>;
  };
  quality: {
    qualityDimensions: Array<{ metric: string; score: number; description: string }>;
  };
  goals: {
    activeGoals: Array<{
      id: string;
      type: string;
      title: string;
      current: number;
      target: number;
      progress: number;
      unit: string;
      deadline: string;
    }>;
  };
  projects: Array<{
    id: string;
    title: string;
    primary_genre: string;
    current_word_count: number;
    target_word_count: number;
  }>;
}

export async function generatePDFReport(data: AnalyticsReportData): Promise<Buffer> {
  // For now, we'll create a simple HTML report and convert it to PDF
  // In production, you'd use a library like puppeteer or jsPDF
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Analytics Report - ${data.user.name}</title>
      <style>
        body {
          font-family: 'Georgia', serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 40px;
          background: #f9f7f4;
        }
        h1 {
          color: #2c1810;
          border-bottom: 2px solid #8b4513;
          padding-bottom: 10px;
          margin-bottom: 30px;
        }
        h2 {
          color: #4a3426;
          margin-top: 30px;
          margin-bottom: 15px;
        }
        h3 {
          color: #6b5d54;
          margin-top: 20px;
          margin-bottom: 10px;
        }
        .header {
          text-align: center;
          margin-bottom: 40px;
        }
        .metric {
          background: white;
          padding: 15px;
          margin: 10px 0;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-value {
          font-size: 24px;
          font-weight: bold;
          color: #8b4513;
        }
        .metric-label {
          color: #666;
          font-size: 14px;
        }
        .grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 20px;
          margin: 20px 0;
        }
        .project {
          background: white;
          padding: 15px;
          border-radius: 8px;
          margin: 10px 0;
        }
        .goal {
          background: white;
          padding: 15px;
          border-radius: 8px;
          margin: 10px 0;
        }
        .progress-bar {
          background: #eee;
          height: 10px;
          border-radius: 5px;
          overflow: hidden;
          margin: 10px 0;
        }
        .progress-fill {
          background: #8b4513;
          height: 100%;
          transition: width 0.3s;
        }
        .quality-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 0;
          border-bottom: 1px solid #eee;
        }
        .footer {
          margin-top: 40px;
          text-align: center;
          color: #666;
          font-size: 12px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Writing Analytics Report</h1>
        <p><strong>${data.user.name}</strong></p>
        <p>${format(new Date(data.dateRange.from), 'MMM d, yyyy')} - ${format(new Date(data.dateRange.to), 'MMM d, yyyy')}</p>
      </div>

      <h2>Overview</h2>
      <div class="grid">
        <div class="metric">
          <div class="metric-value">${data.overview.totalWords.toLocaleString()}</div>
          <div class="metric-label">Total Words Written</div>
        </div>
        <div class="metric">
          <div class="metric-value">${data.overview.currentStreak} days</div>
          <div class="metric-label">Current Writing Streak</div>
        </div>
        <div class="metric">
          <div class="metric-value">${data.overview.avgDailyWords.toLocaleString()}</div>
          <div class="metric-label">Average Daily Words</div>
        </div>
        <div class="metric">
          <div class="metric-value">${data.overview.activeProjects}</div>
          <div class="metric-label">Active Projects</div>
        </div>
      </div>

      <h2>Projects</h2>
      ${data.projects.map(project => `
        <div class="project">
          <h3>${project.title}</h3>
          <p><strong>Genre:</strong> ${project.primary_genre}</p>
          <p><strong>Progress:</strong> ${project.current_word_count.toLocaleString()} / ${project.target_word_count.toLocaleString()} words</p>
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${(project.current_word_count / project.target_word_count * 100).toFixed(1)}%"></div>
          </div>
        </div>
      `).join('')}

      <h2>Active Goals</h2>
      ${data.goals.activeGoals.map(goal => `
        <div class="goal">
          <h3>${goal.title}</h3>
          <p><strong>Progress:</strong> ${goal.current.toLocaleString()} / ${goal.target.toLocaleString()} ${goal.unit}</p>
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${goal.progress}%"></div>
          </div>
          ${goal.deadline ? `<p><strong>Deadline:</strong> ${format(new Date(goal.deadline), 'MMM d, yyyy')}</p>` : ''}
        </div>
      `).join('')}

      <h2>Quality Metrics</h2>
      ${data.quality.qualityDimensions.map(dim => `
        <div class="quality-item">
          <div>
            <strong>${dim.metric}</strong>
            <br><small>${dim.description}</small>
          </div>
          <div class="metric-value">${dim.score}/100</div>
        </div>
      `).join('')}

      <div class="footer">
        <p>Generated on ${format(new Date(), 'MMMM d, yyyy')} by BookScribe AI</p>
      </div>
    </body>
    </html>
  `;

  // For now, return HTML as buffer
  // In production, use puppeteer to convert HTML to PDF
  return Buffer.from(html, 'utf-8');
}