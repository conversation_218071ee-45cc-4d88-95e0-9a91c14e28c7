import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'week' // week, month, year
    const projectId = searchParams.get('project_id')
    
    // Calculate date range
    const endDate = new Date()
    const startDate = new Date()
    
    switch (period) {
      case 'week':
        startDate.setDate(endDate.getDate() - 7)
        break
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1)
        break
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1)
        break
      default:
        startDate.setDate(endDate.getDate() - 7)
    }

    // Get active goals
    let goalsQuery = supabase
      .from('writing_goals')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)

    if (projectId) {
      goalsQuery = goalsQuery.eq('project_id', projectId)
    }

    const { data: goals, error: goalsError } = await goalsQuery

    if (goalsError) {
      console.error('Error fetching goals:', goalsError)
      return NextResponse.json({ error: 'Failed to fetch goals' }, { status: 500 })
    }

    // Get writing sessions for the period
    let sessionsQuery = supabase
      .from('writing_sessions')
      .select('*')
      .eq('user_id', user.id)
      .gte('started_at', startDate.toISOString())
      .lte('started_at', endDate.toISOString())

    if (projectId) {
      sessionsQuery = sessionsQuery.eq('project_id', projectId)
    }

    const { data: sessions, error: sessionsError } = await sessionsQuery

    if (sessionsError) {
      console.error('Error fetching sessions:', sessionsError)
      return NextResponse.json({ error: 'Failed to fetch sessions' }, { status: 500 })
    }

    // Get goal progress for active goals
    const goalIds = goals?.map(g => g.id) || []
    let progressData = []
    
    if (goalIds.length > 0) {
      const { data: progress } = await supabase
        .from('writing_goal_progress')
        .select('*')
        .in('goal_id', goalIds)
        .gte('date', startDate.toISOString().split('T')[0])
        .lte('date', endDate.toISOString().split('T')[0])
        .order('date', { ascending: true })

      progressData = progress || []
    }

    // Calculate daily statistics
    const dailyStats: Record<string, {
      date: string
      wordsWritten: number
      sessionsCount: number
      goalsMet: number
      totalGoals: number
    }> = {}

    // Initialize all days in the period
    const currentDate = new Date(startDate)
    while (currentDate <= endDate) {
      const dateStr = currentDate.toISOString().split('T')[0]!
      dailyStats[dateStr] = {
        date: dateStr,
        wordsWritten: 0,
        sessionsCount: 0,
        goalsMet: 0,
        totalGoals: 0,
      }
      currentDate.setDate(currentDate.getDate() + 1)
    }

    // Aggregate session data
    sessions?.forEach(session => {
      const dateStr = new Date(session.started_at).toISOString().split('T')[0]!
      if (dateStr && dailyStats[dateStr]) {
        dailyStats[dateStr].wordsWritten += session.word_count
        dailyStats[dateStr].sessionsCount += 1
      }
    })

    // Check goal completion for each day
    Object.keys(dailyStats).forEach(dateStr => {
      const dayGoals = goals?.filter(goal => {
        const goalStart = new Date(goal.start_date)
        const goalEnd = goal.end_date ? new Date(goal.end_date) : new Date('2099-12-31')
        const currentDay = new Date(dateStr)
        return currentDay >= goalStart && currentDay <= goalEnd
      }) || []

      if (dateStr && dailyStats[dateStr]) {
        dailyStats[dateStr].totalGoals = dayGoals.length
      }

      dayGoals.forEach(goal => {
        const progress = progressData.find(p => p.goal_id === goal.id && p.date === dateStr)
        if (progress && progress.words_written >= goal.target_words && dateStr && dailyStats[dateStr]) {
          dailyStats[dateStr].goalsMet += 1
        }
      })
    })

    // Calculate summary statistics
    const totalWords = Object.values(dailyStats).reduce((sum, day) => sum + day.wordsWritten, 0)
    const totalSessions = Object.values(dailyStats).reduce((sum, day) => sum + day.sessionsCount, 0)
    const daysWritten = Object.values(dailyStats).filter(day => day.wordsWritten > 0).length
    const daysGoalsMet = Object.values(dailyStats).filter(day => day.totalGoals > 0 && day.goalsMet === day.totalGoals).length
    
    // Calculate current streak
    let currentStreak = 0
    const sortedDates = Object.keys(dailyStats).sort().reverse()
    
    for (const dateStr of sortedDates) {
      const day = dateStr ? dailyStats[dateStr] : undefined
      if (day && day.wordsWritten > 0) {
        currentStreak++
      } else if (dateStr !== new Date().toISOString().split('T')[0]!) {
        // Don't break streak if today has no words yet
        break
      }
    }

    return NextResponse.json({
      period,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      goals,
      dailyStats: Object.values(dailyStats),
      summary: {
        totalWords,
        totalSessions,
        daysWritten,
        daysGoalsMet,
        currentStreak,
        averageWordsPerDay: daysWritten > 0 ? Math.round(totalWords / daysWritten) : 0,
        averageSessionsPerDay: daysWritten > 0 ? (totalSessions / daysWritten).toFixed(1) : '0',
        goalCompletionRate: Object.values(dailyStats).filter(d => d.totalGoals > 0).length > 0
          ? Math.round((daysGoalsMet / Object.values(dailyStats).filter(d => d.totalGoals > 0).length) * 100)
          : 0,
      }
    })
  } catch (error) {
    console.error('Error in progress GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}