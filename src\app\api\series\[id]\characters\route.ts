import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { logger } from '@/lib/services/logger';

export const runtime = 'nodejs';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id: seriesId } = params;
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all characters for this series using the database function
    const { data: characters, error } = await supabase
      .rpc('get_series_characters', {
        p_series_id: seriesId
      });

    if (error) {
      logger.error('Error fetching series characters:', error);
      return NextResponse.json({ error: 'Failed to fetch series characters' }, { status: 500 });
    }

    // Group characters by source type
    const groupedCharacters = {
      native: characters.filter(c => c.source_type === 'native'),
      shared: characters.filter(c => c.source_type === 'shared'),
      variant: characters.filter(c => c.source_type === 'variant')
    };

    return NextResponse.json({ 
      characters: groupedCharacters,
      total: characters.length 
    });
  } catch (error) {
    logger.error('Error in GET /api/series/[id]/characters:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id: seriesId } = params;
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, characterId, targetSeriesId, shareType = 'reference', versionNotes } = body;

    if (action === 'import') {
      // Import a shared character into this series
      if (!characterId) {
        return NextResponse.json({ error: 'Character ID is required' }, { status: 400 });
      }

      // Check if there's an existing share for this character to this series
      const { data: existingShare, error: checkError } = await supabase
        .from('character_shares')
        .select('*')
        .eq('character_id', characterId)
        .eq('target_series_id', seriesId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        logger.error('Error checking existing share:', checkError);
        return NextResponse.json({ error: 'Failed to check existing share' }, { status: 500 });
      }

      if (existingShare) {
        return NextResponse.json({ 
          message: 'Character already shared to this series',
          share: existingShare 
        });
      }

      // Create the share
      const { data: share, error: shareError } = await supabase
        .from('character_shares')
        .insert({
          character_id: characterId,
          source_series_id: body.sourceSeriesId,
          target_series_id: seriesId,
          share_type: shareType,
          version_notes: versionNotes
        })
        .select()
        .single();

      if (shareError) {
        logger.error('Error importing character:', shareError);
        return NextResponse.json({ error: 'Failed to import character' }, { status: 500 });
      }

      return NextResponse.json({ share });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    logger.error('Error in POST /api/series/[id]/characters:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}