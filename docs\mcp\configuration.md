# MCP Configuration Guide

This guide provides detailed configuration examples and setup instructions for all MCP servers in BookScribe.

## Configuration File Location

The MCP configuration file is located at:
```
~/.config/claude-desktop/config.json
```

## Complete Configuration Example

Here's a complete configuration with all MCP servers and their environment variables:

```json
{
  "mcpServers": {
    "sentry": {
      "command": "npx",
      "args": ["@sentry/mcp-server"],
      "env": {
        "SENTRY_ORG": "your-organization-slug",
        "SENTRY_PROJECT": "bookscribe",
        "SENTRY_AUTH_TOKEN": "your-sentry-auth-token"
      }
    },
    "stripe": {
      "command": "npx",
      "args": ["@stripe/mcp"],
      "env": {
        "STRIPE_SECRET_KEY": "sk_test_your_stripe_secret_key_here",
        "STRIPE_ACCOUNT": "acct_optional_connected_account_id"
      }
    },
    "supabase": {
      "command": "npx",
      "args": ["@supabase/mcp-server-supabase"],
      "env": {
        "SUPABASE_URL": "https://your-project.supabase.co",
        "SUPABASE_ANON_KEY": "your-anon-key",
        "SUPABASE_SERVICE_ROLE_KEY": "your-service-role-key"
      }
    },
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp"],
      "env": {
        "PLAYWRIGHT_BROWSER": "chromium",
        "PLAYWRIGHT_HEADLESS": "true",
        "PLAYWRIGHT_TIMEOUT": "30000",
        "PLAYWRIGHT_VIEWPORT": "1280x720"
      }
    },
    "context7": {
      "command": "npx",
      "args": ["@upstash/context7-mcp"],
      "env": {
        "CONTEXT7_MAX_TOKENS": "10000"
      }
    }
  }
}
```

## Environment-Specific Configurations

### Development Environment

For development, use test credentials and relaxed settings:

```json
{
  "mcpServers": {
    "stripe": {
      "command": "npx",
      "args": ["@stripe/mcp"],
      "env": {
        "STRIPE_SECRET_KEY": "sk_test_your_test_key_here",
        "NODE_ENV": "development"
      }
    },
    "supabase": {
      "command": "npx", 
      "args": ["@supabase/mcp-server-supabase"],
      "env": {
        "SUPABASE_URL": "https://your-staging-project.supabase.co",
        "SUPABASE_ANON_KEY": "your-staging-anon-key"
      }
    },
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp"],
      "env": {
        "PLAYWRIGHT_HEADLESS": "false",
        "PLAYWRIGHT_SLOWMO": "1000"
      }
    }
  }
}
```

### Production Environment

For production, use live credentials with enhanced security:

```json
{
  "mcpServers": {
    "stripe": {
      "command": "npx",
      "args": ["@stripe/mcp"],
      "env": {
        "STRIPE_SECRET_KEY": "sk_live_your_live_key_here",
        "NODE_ENV": "production"
      }
    },
    "supabase": {
      "command": "npx",
      "args": ["@supabase/mcp-server-supabase"],
      "env": {
        "SUPABASE_URL": "https://your-production-project.supabase.co",
        "SUPABASE_ANON_KEY": "your-production-anon-key",
        "SUPABASE_SERVICE_ROLE_KEY": "your-production-service-role-key"
      }
    },
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp"],
      "env": {
        "PLAYWRIGHT_HEADLESS": "true",
        "PLAYWRIGHT_TIMEOUT": "10000"
      }
    }
  }
}
```

## Individual Server Setup

### Sentry MCP Setup

1. **Get Sentry Credentials**:
   - Go to Sentry.io → Settings → Account → API → Auth Tokens
   - Create token with scopes: `project:read`, `event:read`, `org:read`

2. **Find Organization/Project Slugs**:
   - Organization: Settings → General → Organization Slug
   - Project: Project Settings → General → Project Slug

3. **Configuration**:
```json
{
  "sentry": {
    "command": "npx",
    "args": ["@sentry/mcp-server"],
    "env": {
      "SENTRY_ORG": "bookscribe-org",
      "SENTRY_PROJECT": "bookscribe-frontend", 
      "SENTRY_AUTH_TOKEN": "sntrys_your_token_here"
    }
  }
}
```

### Stripe MCP Setup

1. **Get API Keys**:
   - Go to Stripe Dashboard → Developers → API Keys
   - Copy Secret Key (starts with `sk_test_` or `sk_live_`)

2. **Configuration**:
```json
{
  "stripe": {
    "command": "npx",
    "args": ["@stripe/mcp"],
    "env": {
      "STRIPE_SECRET_KEY": "sk_test_your_key_here"
    }
  }
}
```

### Supabase MCP Setup

1. **Get Project Credentials**:
   - Go to Supabase Dashboard → Settings → API
   - Copy Project URL and anon key
   - Copy service_role key (for admin operations)

2. **Configuration**:
```json
{
  "supabase": {
    "command": "npx",
    "args": ["@supabase/mcp-server-supabase"],
    "env": {
      "SUPABASE_URL": "https://abcdefghijklmnop.supabase.co",
      "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
  }
}
```

## Security Best Practices

### Environment Variable Management

1. **Never commit secrets to version control**
2. **Use different credentials for different environments**
3. **Rotate API keys regularly**
4. **Use minimal required permissions**

### Key Storage Options

#### Option 1: Direct in Config (Not Recommended for Production)
```json
{
  "env": {
    "API_KEY": "actual-key-here"
  }
}
```

#### Option 2: Environment Variables
```bash
# In your shell profile (.bashrc, .zshrc, etc.)
export STRIPE_SECRET_KEY="sk_test_your_key"
export SUPABASE_URL="https://your-project.supabase.co"
```

```json
{
  "env": {
    "STRIPE_SECRET_KEY": "${STRIPE_SECRET_KEY}",
    "SUPABASE_URL": "${SUPABASE_URL}"
  }
}
```

### Access Control

1. **Limit API key scopes**
2. **Use read-only keys when possible** 
3. **Monitor API key usage**
4. **Implement proper error handling**

## Testing Configuration

### Validation Script

Create a test script to validate your configuration:

```bash
#!/bin/bash
# test-mcp-config.sh

echo "Testing MCP Configuration..."

# Test Stripe
if [ -n "$STRIPE_SECRET_KEY" ]; then
  echo "✅ Stripe key configured"
else
  echo "❌ Stripe key missing"
fi

# Test Supabase
if [ -n "$SUPABASE_URL" ] && [ -n "$SUPABASE_ANON_KEY" ]; then
  echo "✅ Supabase configured"
else
  echo "❌ Supabase configuration incomplete"
fi

# Test Sentry
if [ -n "$SENTRY_AUTH_TOKEN" ]; then
  echo "✅ Sentry configured"
else
  echo "❌ Sentry token missing"
fi
```

### Configuration Validation

Validate your JSON configuration:

```bash
# Check JSON syntax
cat ~/.config/claude-desktop/config.json | jq .

# Verify specific server configuration
cat ~/.config/claude-desktop/config.json | jq '.mcpServers.stripe'
```

## Common Configuration Patterns

### Conditional Server Loading

Load different servers based on environment:

```json
{
  "mcpServers": {
    "stripe": {
      "command": "npx",
      "args": ["@stripe/mcp"],
      "env": {
        "STRIPE_SECRET_KEY": "${NODE_ENV:production?${STRIPE_LIVE_KEY}:${STRIPE_TEST_KEY}}"
      }
    }
  }
}
```

### Shared Environment Variables

Use common environment variables across servers:

```json
{
  "mcpServers": {
    "server1": {
      "env": {
        "NODE_ENV": "${NODE_ENV}",
        "LOG_LEVEL": "${LOG_LEVEL}"
      }
    },
    "server2": {
      "env": {
        "NODE_ENV": "${NODE_ENV}",
        "LOG_LEVEL": "${LOG_LEVEL}"
      }
    }
  }
}
```

## Backup and Recovery

### Configuration Backup

```bash
# Backup current configuration
cp ~/.config/claude-desktop/config.json ~/.config/claude-desktop/config.json.backup

# Create timestamped backup
cp ~/.config/claude-desktop/config.json ~/.config/claude-desktop/config.json.$(date +%Y%m%d_%H%M%S)
```

### Configuration Recovery

```bash
# Restore from backup
cp ~/.config/claude-desktop/config.json.backup ~/.config/claude-desktop/config.json

# Restart Claude Desktop to apply changes
```

## Configuration Updates

### Updating Server Versions

```bash
# Update all MCP servers
npm update -g @sentry/mcp-server @stripe/mcp @supabase/mcp-server-supabase @playwright/mcp @upstash/context7-mcp

# Update specific server
npm update -g @stripe/mcp
```

### Adding New Servers

1. **Add to configuration**
2. **Install dependencies**
3. **Test connectivity**
4. **Update documentation**

### Removing Servers

1. **Remove from configuration**
2. **Restart Claude Desktop**
3. **Optionally uninstall packages**

## Monitoring and Logging

### Enable Debug Logging

```json
{
  "mcpServers": {
    "stripe": {
      "command": "npx",
      "args": ["@stripe/mcp"],
      "env": {
        "DEBUG": "stripe:*",
        "LOG_LEVEL": "debug"
      }
    }
  }
}
```

### Configuration Health Check

Create a health check script:

```bash
#!/bin/bash
# health-check.sh

echo "MCP Configuration Health Check"
echo "==============================="

CONFIG_FILE="$HOME/.config/claude-desktop/config.json"

if [ ! -f "$CONFIG_FILE" ]; then
  echo "❌ Configuration file not found"
  exit 1
fi

echo "✅ Configuration file exists"

# Validate JSON
if jq . "$CONFIG_FILE" > /dev/null 2>&1; then
  echo "✅ JSON syntax valid"
else
  echo "❌ Invalid JSON syntax"
  exit 1
fi

# Check server configurations
SERVERS=$(jq -r '.mcpServers | keys[]' "$CONFIG_FILE")
for server in $SERVERS; do
  echo "📊 Server: $server"
  command=$(jq -r ".mcpServers.$server.command" "$CONFIG_FILE")
  args=$(jq -r ".mcpServers.$server.args[]" "$CONFIG_FILE")
  echo "   Command: $command $args"
done
```

This comprehensive configuration guide should help you set up and manage all MCP servers effectively in your BookScribe project.