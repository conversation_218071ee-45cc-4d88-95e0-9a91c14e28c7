'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { writingSessionTracker } from '@/lib/services/writing-session-tracker'
import { 
  ProgressCelebration, 
  useCelebration as useCelebrationHook,
  celebrations,
  type CelebrationTrigger 
} from '@/components/celebrations/progress-celebration'
import { useAuth } from '@/contexts/auth-context'
import { logger } from '@/lib/services/logger'

interface CelebrationContextValue {
  celebrate: (trigger: CelebrationTrigger) => void
  celebrateGoal: (goalTitle: string) => void
  celebrateChapter: (chapterNumber: number) => void
  celebrateQuality: (score: number) => void
}

const CelebrationContext = createContext<CelebrationContextValue | undefined>(undefined)

export function CelebrationProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()
  const { celebration, celebrate, clearCelebration, CelebrationComponent } = useCelebrationHook()

  useEffect(() => {
    if (!user) return

    // Subscribe to milestone events from the writing session tracker
    const unsubscribe = writingSessionTracker.onMilestone((event) => {
      logger.info('Milestone achieved', event)
      celebrate(event.trigger)
    })

    return unsubscribe
  }, [user, celebrate])

  const celebrateGoal = (goalTitle: string) => {
    celebrate(celebrations.goalCompleted(goalTitle))
  }

  const celebrateChapter = (chapterNumber: number) => {
    celebrate(celebrations.chapterComplete(chapterNumber))
  }

  const celebrateQuality = (score: number) => {
    celebrate(celebrations.qualityAchievement(score))
  }

  return (
    <CelebrationContext.Provider 
      value={{ 
        celebrate, 
        celebrateGoal, 
        celebrateChapter, 
        celebrateQuality 
      }}
    >
      {children}
      {CelebrationComponent}
    </CelebrationContext.Provider>
  )
}

export function useCelebration() {
  const context = useContext(CelebrationContext)
  if (!context) {
    throw new Error('useCelebration must be used within a CelebrationProvider')
  }
  return context
}