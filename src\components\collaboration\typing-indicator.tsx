'use client'

import { useEffect, useState } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { cn } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'

interface TypingUser {
  id: string
  name: string
  email: string
  color: string
  lastTyped: number
}

interface TypingIndicatorProps {
  typingUsers: TypingUser[]
  currentUserId: string
  position?: 'top' | 'bottom'
  className?: string
}

export function TypingIndicator({
  typingUsers,
  currentUserId,
  position = 'bottom',
  className
}: TypingIndicatorProps) {
  const [activeTypingUsers, setActiveTypingUsers] = useState<TypingUser[]>([])
  
  useEffect(() => {
    // Filter out current user and users who haven't typed in 3 seconds
    const now = Date.now()
    const active = typingUsers.filter(
      user => user.id !== currentUserId && (now - user.lastTyped) < 3000
    )
    setActiveTypingUsers(active)
    
    // Set up interval to clean up stale typing indicators
    const interval = setInterval(() => {
      const currentTime = Date.now()
      setActiveTypingUsers(prev => 
        prev.filter(user => (currentTime - user.lastTyped) < 3000)
      )
    }, 1000)
    
    return () => clearInterval(interval)
  }, [typingUsers, currentUserId])
  
  if (activeTypingUsers.length === 0) return null
  
  const getInitials = (name: string, email: string) => {
    if (name && name.trim()) {
      return name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2)
    }
    return email.substring(0, 2).toUpperCase()
  }
  
  const getTypingText = () => {
    if (activeTypingUsers.length === 1) {
      return `${activeTypingUsers[0].name} is typing`
    } else if (activeTypingUsers.length === 2) {
      return `${activeTypingUsers[0].name} and ${activeTypingUsers[1].name} are typing`
    } else {
      const othersCount = activeTypingUsers.length - 2
      return `${activeTypingUsers[0].name}, ${activeTypingUsers[1].name} and ${othersCount} ${othersCount === 1 ? 'other' : 'others'} are typing`
    }
  }
  
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: position === 'bottom' ? 10 : -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: position === 'bottom' ? 10 : -10 }}
        transition={{ duration: 0.2 }}
        className={cn(
          'flex items-center gap-2 px-3 py-2 bg-muted/80 backdrop-blur-sm rounded-lg shadow-sm',
          position === 'top' ? 'mb-2' : 'mt-2',
          className
        )}
      >
        {/* User Avatars */}
        <div className="flex -space-x-2">
          {activeTypingUsers.slice(0, 3).map((user) => (
            <Avatar
              key={user.id}
              className="h-6 w-6 border-2 border-background"
              style={{ borderColor: user.color }}
            >
              <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${user.email}`} />
              <AvatarFallback 
                style={{ backgroundColor: user.color }}
                className="text-[10px] text-white font-medium"
              >
                {getInitials(user.name, user.email)}
              </AvatarFallback>
            </Avatar>
          ))}
          {activeTypingUsers.length > 3 && (
            <div className="h-6 w-6 rounded-full bg-muted-foreground/20 border-2 border-background flex items-center justify-center">
              <span className="text-[10px] font-medium text-muted-foreground">
                +{activeTypingUsers.length - 3}
              </span>
            </div>
          )}
        </div>
        
        {/* Typing Text */}
        <div className="flex items-center gap-1">
          <span className="text-sm text-muted-foreground">
            {getTypingText()}
          </span>
          
          {/* Animated Dots */}
          <div className="flex gap-0.5">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-1 h-1 bg-muted-foreground rounded-full"
                animate={{
                  y: [0, -3, 0],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 1.2,
                  delay: i * 0.1,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}