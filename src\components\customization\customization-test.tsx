/**
 * Customization Test Component
 * Shows live preview of all customization settings
 */

'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTheme } from '@/hooks/use-theme';
import { useTypographySettings } from '@/lib/settings/settings-store';

export function CustomizationTest() {
  const { theme, getCurrentMode } = useTheme();
  const { typography } = useTypographySettings();

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="text-lg">Live Preview</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Settings Display */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <strong>Current Theme:</strong> {theme || 'None'}
          </div>
          <div>
            <strong>Theme Mode:</strong> {getCurrentMode() || 'None'}
          </div>
          <div>
            <strong>Text Size:</strong> {typography.textSize}
          </div>
          <div>
            <strong>Editor Font:</strong> {typography.editorFont}
          </div>
          <div>
            <strong>UI Font:</strong> {typography.uiFont}
          </div>
          <div>
            <strong>Reading Font:</strong> {typography.readingFont}
          </div>
        </div>

        {/* Typography Samples */}
        <div className="space-y-4 p-4 border rounded-lg">
          <h3 className="font-semibold mb-2">Typography Samples</h3>
          
          {/* UI Text Sample */}
          <div className="font-ui">
            <strong>UI Text (Interface):</strong> This is how interface elements will appear with your current font settings.
          </div>
          
          {/* Editor Text Sample */}
          <div className="font-editor">
            <strong>Editor Text (Code/Writing):</strong> 
            <pre className="mt-1 p-2 bg-muted rounded text-sm">
{`function writeStory() {
  return "Once upon a time...";
}`}
            </pre>
          </div>
          
          {/* Reading Text Sample */}
          <div className="font-reading">
            <strong>Reading Text (Content):</strong> 
            <p className="mt-1">
              The quick brown fox jumps over the lazy dog. This is how your reading text will appear in documents and content areas.
            </p>
          </div>
        </div>

        {/* Theme Colors */}
        <div className="space-y-2">
          <h3 className="font-semibold">Theme Colors</h3>
          <div className="grid grid-cols-4 gap-2">
            <div className="h-12 bg-background border rounded flex items-center justify-center text-xs">
              Background
            </div>
            <div className="h-12 bg-card border rounded flex items-center justify-center text-xs">
              Card
            </div>
            <div className="h-12 bg-primary rounded flex items-center justify-center text-xs text-primary-foreground">
              Primary
            </div>
            <div className="h-12 bg-muted border rounded flex items-center justify-center text-xs">
              Muted
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}