'use client'

// Centralized lazy-loaded components export
// This file provides lazy-loaded versions of heavy components to reduce initial bundle size

import dynamic from 'next/dynamic'
import { ComponentProps as _ComponentProps } from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import { Card } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'

// Generic loading component
function LoadingComponent({ message = 'Loading component...' }: { message?: string }) {
  return (
    <Card className="p-8 flex flex-col items-center justify-center space-y-4">
      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      <p className="text-sm text-muted-foreground">{message}</p>
    </Card>
  )
}

// Chart loading component
function ChartLoadingComponent() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-8 w-48" />
      <Skeleton className="h-64 w-full" />
    </div>
  )
}

// Export lazy-loaded chart components
export const LazyPacingAnalyzer = dynamic(
  () => import('@/components/analysis/pacing-analyzer').then(mod => ({ default: mod.PacingAnalyzer })),
  { 
    loading: () => <ChartLoadingComponent />,
    ssr: false 
  }
)

export const LazyCharacterArcVisualizer = dynamic(
  () => import('@/components/analysis/character-arc-visualizer').then(mod => ({ default: mod.CharacterArcVisualizer })),
  { 
    loading: () => <ChartLoadingComponent />,
    ssr: false 
  }
)

export const LazyEmotionalJourneyMapper = dynamic(
  () => import('@/components/analysis/emotional-journey-mapper').then(mod => ({ default: mod.EmotionalJourneyMapper })),
  { 
    loading: () => <ChartLoadingComponent />,
    ssr: false 
  }
)

// Export lazy-loaded modal/dialog components
export const LazyUnifiedExport = dynamic(
  () => import('@/components/export/unified-export').then(mod => ({ default: mod.UnifiedExport })),
  { 
    loading: () => <LoadingComponent message="Loading export options..." />,
    ssr: false 
  }
)

export const LazyTemplateBrowser = dynamic(
  () => import('@/components/templates/template-browser').then(mod => ({ default: mod.TemplateBrowser })),
  { 
    loading: () => <LoadingComponent message="Loading templates..." />,
    ssr: false 
  }
)

export const LazyKeyboardShortcutsModal = dynamic(
  () => import('@/components/ui/keyboard-shortcuts-modal').then(mod => ({ default: mod.KeyboardShortcutsModal })),
  { 
    loading: () => null,
    ssr: false 
  }
)

// Export lazy-loaded analysis panels
export const LazyIntelligentContentAnalyzer = dynamic(
  () => import('@/components/analysis/intelligent-content-analyzer').then(mod => ({ default: mod.IntelligentContentAnalyzer })),
  { 
    loading: () => <LoadingComponent message="Loading AI analyzer..." />,
    ssr: false 
  }
)

export const LazyArcPatternAnalyzer = dynamic(
  () => import('@/components/analysis/arc-pattern-analyzer').then(mod => ({ default: mod.ArcPatternAnalyzer })),
  { 
    loading: () => <LoadingComponent message="Loading pattern analyzer..." />,
    ssr: false 
  }
)

export const LazyCharacterDevelopmentGrid = dynamic(
  () => import('@/components/analysis/character-development-grid').then(mod => ({ default: mod.CharacterDevelopmentGrid })),
  { 
    loading: () => <ChartLoadingComponent />,
    ssr: false 
  }
)

export const LazyPlotHoleDetector = dynamic(
  () => import('@/components/analysis/plot-hole-detector').then(mod => ({ default: mod.PlotHoleDetector })),
  { 
    loading: () => <LoadingComponent message="Analyzing plot..." />,
    ssr: false 
  }
)

export const LazyTimelineView = dynamic(
  () => import('@/components/timeline/timeline-view').then(mod => ({ default: mod.TimelineView })),
  { 
    loading: () => <LoadingComponent message="Loading timeline..." />,
    ssr: false 
  }
)

// Export lazy-loaded editor panels
export const LazyAiChatPanel = dynamic(
  () => import('@/components/editor/ai-chat-assistant').then(mod => ({ default: mod.AiChatAssistant })),
  { 
    loading: () => <LoadingComponent message="Loading AI assistant..." />,
    ssr: false 
  }
)

export const LazyStoryBiblePanel = dynamic(
  () => import('@/components/editor/story-bible-enhanced').then(mod => ({ default: mod.StoryBibleEnhanced })),
  { 
    loading: () => <LoadingComponent message="Loading story bible..." />,
    ssr: false 
  }
)

export const LazyChapterReviewPanel = dynamic(
  () => import('@/components/editor/chapter-review-dialog').then(mod => ({ default: mod.ChapterReviewDialog })),
  { 
    loading: () => <LoadingComponent message="Loading review panel..." />,
    ssr: false 
  }
)

export const LazyVersionHistoryPanel = dynamic(
  () => import('@/components/version-history/version-history-panel').then(mod => ({ default: mod.VersionHistoryPanel })),
  { 
    loading: () => <LoadingComponent message="Loading version history..." />,
    ssr: false 
  }
)

export const LazyReferenceMaterials = dynamic(
  () => import('@/components/references/reference-materials').then(mod => ({ default: mod.ReferenceMaterials })),
  { 
    loading: () => <LoadingComponent message="Loading references..." />,
    ssr: false 
  }
)

// Export lazy-loaded search components
export const LazySemanticSearchPanel = dynamic(
  () => import('@/components/search/semantic-search-panel').then(mod => ({ default: mod.SemanticSearchPanel })),
  { 
    loading: () => <LoadingComponent message="Loading search..." />,
    ssr: false 
  }
)

// Export lazy-loaded onboarding components
export const LazyInteractiveTutorial = dynamic(
  () => import('@/components/onboarding/interactive-tutorial').then(mod => ({ default: mod.InteractiveTutorial })),
  { 
    loading: () => null,
    ssr: false 
  }
)

export const LazyWelcomeTour = dynamic(
  () => import('@/components/onboarding/welcome-tour').then(mod => ({ default: mod.WelcomeTour })),
  { 
    loading: () => null,
    ssr: false 
  }
)

export const LazySampleProjectGenerator = dynamic(
  () => import('@/components/onboarding/sample-project-generator').then(mod => ({ default: mod.SampleProjectGenerator })),
  { 
    loading: () => <LoadingComponent message="Loading sample generator..." />,
    ssr: false 
  }
)

// Export lazy-loaded series management components
export const LazySeriesConsistencyDashboard = dynamic(
  () => import('@/components/series/series-consistency-dashboard').then(mod => ({ default: mod.SeriesConsistencyDashboard })),
  { 
    loading: () => <LoadingComponent message="Loading consistency dashboard..." />,
    ssr: false 
  }
)

export const LazyCharacterContinuityTracker = dynamic(
  () => import('@/components/series/character-continuity-tracker').then(mod => ({ default: mod.CharacterContinuityTracker })),
  { 
    loading: () => <LoadingComponent message="Loading character tracker..." />,
    ssr: false 
  }
)