'use client'

import { useState } from 'react'
import { logger } from '@/lib/services/logger';

import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'

interface Chapter {
  id: string
  chapter_number: number
  title?: string | null
  status: string
  content?: string | null
  outline?: string | null
}

interface GenerateChapterButtonProps {
  projectId: string
  chapters: Chapter[]
}

export function GenerateChapterButton({ projectId, chapters }: GenerateChapterButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [selectedChapter, setSelectedChapter] = useState<string>('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  // Find chapters to generate
  const chaptersToGenerate = chapters?.filter(ch => ch.status === 'planned' && !ch.content) || []

  const handleGenerate = async () => {
    if (!selectedChapter) return
    
    setIsGenerating(true)
    
    try {
      const response = await fetch('/api/agents/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          action: 'write_chapter',
          chapterNumber: parseInt(selectedChapter)
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate chapter')
      }

      setIsDialogOpen(false)
      router.refresh()
      
      // Show success message
      toast({
        title: 'Chapter Generated!',
        description: `Chapter ${selectedChapter} has been generated successfully!`,
      })
      
    } catch (error) {
      logger.error('Chapter generation error:', error);
      toast({
        title: 'Generation Failed',
        description: error instanceof Error ? error.message : 'Failed to generate chapter',
        variant: 'destructive'
      })
    } finally {
      setIsGenerating(false)
    }
  }

  if (chaptersToGenerate.length === 0) {
    return (
      <Button variant="outline" className="w-full" disabled>
        No Chapters Ready to Generate
      </Button>
    )
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full">
          Generate Chapter
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Generate Chapter</DialogTitle>
          <DialogDescription>
            Select which chapter you&apos;d like to generate. The AI will write the full chapter based on the outline.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Chapter to Generate</label>
            <Select value={selectedChapter} onValueChange={setSelectedChapter}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a chapter" />
              </SelectTrigger>
              <SelectContent>
                {chaptersToGenerate.map((chapter) => (
                  <SelectItem key={chapter.id} value={chapter.chapter_number.toString()}>
                    Chapter {chapter.chapter_number}: {chapter.title || 'Untitled'}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {selectedChapter && (
            <div className="p-3 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Chapter Preview</h4>
              {(() => {
                const chapter = chaptersToGenerate.find(ch => ch.chapter_number.toString() === selectedChapter)
                const outline = chapter?.outline ? JSON.parse(chapter.outline) : null
                return (
                  <div className="text-sm space-y-1">
                    <p><strong>Title:</strong> {outline?.title || 'Untitled'}</p>
                    <p><strong>Target Words:</strong> {outline?.targetWordCount?.toLocaleString() || 'Unknown'}</p>
                    <p><strong>Summary:</strong> {outline?.summary?.slice(0, 100) || 'No summary'}...</p>
                  </div>
                )
              })()}
            </div>
          )}
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={() => setIsDialogOpen(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleGenerate} 
              disabled={!selectedChapter || isGenerating}
              className="flex-1"
            >
              {isGenerating ? 'Generating...' : 'Generate Chapter'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}