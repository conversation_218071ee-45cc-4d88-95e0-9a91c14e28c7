import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/db/types'

const createSessionSchema = z.object({
  projectId: z.string().uuid(),
  chapterId: z.string().uuid().optional(),
  wordCount: z.number().int().min(0),
  duration: z.number().int().min(0), // Duration in seconds
  startedAt: z.string().datetime(),
  endedAt: z.string().datetime(),
})

const getSessionsSchema = z.object({
  projectId: z.string().uuid().optional(),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 20),
  offset: z.string().optional().transform(val => val ? parseInt(val) : 0),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const supabase: SupabaseClient<Database> = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const params = getSessionsSchema.parse({
      projectId: searchParams.get('projectId'),
      limit: searchParams.get('limit'),
      offset: searchParams.get('offset'),
      startDate: searchParams.get('startDate'),
      endDate: searchParams.get('endDate'),
    })

    let query = supabase
      .from('writing_sessions')
      .select('*, projects(title)')
      .eq('user_id', user.id)
      .order('started_at', { ascending: false })
      .range(params.offset, params.offset + params.limit - 1)

    if (params.projectId) {
      query = query.eq('project_id', params.projectId)
    }

    if (params.startDate) {
      query = query.gte('started_at', params.startDate)
    }

    if (params.endDate) {
      query = query.lte('started_at', params.endDate)
    }

    const { data: sessions, error } = await query

    if (error) {
      console.error('Error fetching writing sessions:', error)
      return NextResponse.json({ error: 'Failed to fetch writing sessions' }, { status: 500 })
    }

    // Calculate statistics
    let statsQuery = supabase
      .from('writing_sessions')
      .select('word_count, duration')
      .eq('user_id', user.id)

    if (params.projectId) {
      statsQuery = statsQuery.eq('project_id', params.projectId)
    }

    if (params.startDate) {
      statsQuery = statsQuery.gte('started_at', params.startDate)
    }

    if (params.endDate) {
      statsQuery = statsQuery.lte('started_at', params.endDate)
    }

    const { data: allSessions } = await statsQuery

    const stats = {
      totalSessions: allSessions?.length || 0,
      totalWords: allSessions?.reduce((sum: number, s: { word_count: number }) => sum + (s.word_count || 0), 0) || 0,
      totalDuration: allSessions?.reduce((sum: number, s: { duration: number }) => sum + (s.duration || 0), 0) || 0,
      averageWordsPerSession: 0,
      averageDurationPerSession: 0,
      wordsPerMinute: 0,
    }

    if (stats.totalSessions > 0) {
      stats.averageWordsPerSession = Math.round(stats.totalWords / stats.totalSessions)
      stats.averageDurationPerSession = Math.round(stats.totalDuration / stats.totalSessions)
      if (stats.totalDuration > 0) {
        stats.wordsPerMinute = Math.round((stats.totalWords / stats.totalDuration) * 60)
      }
    }

    return NextResponse.json({
      sessions: sessions || [],
      stats,
      limit: params.limit,
      offset: params.offset,
    })
  } catch (error) {
    console.error('Error in writing sessions GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase: SupabaseClient<Database> = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const sessionData = createSessionSchema.parse(body)

    // Verify user owns the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id')
      .eq('id', sessionData.projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found or unauthorized' }, { status: 404 })
    }

    // Create the writing session
    const { data: session, error: sessionError } = await supabase
      .from('writing_sessions')
      .insert({
        user_id: user.id,
        project_id: sessionData.projectId,
        chapter_id: sessionData.chapterId,
        word_count: sessionData.wordCount,
        duration: sessionData.duration,
        started_at: sessionData.startedAt,
        ended_at: sessionData.endedAt,
      })
      .select()
      .single()

    if (sessionError) {
      console.error('Error creating writing session:', sessionError)
      return NextResponse.json({ error: 'Failed to create writing session' }, { status: 500 })
    }

    // Update project total word count
    const { error: updateError } = await supabase
      .from('projects')
      .update({ 
        total_word_count: supabase.rpc('increment_word_count', { 
          project_id: sessionData.projectId, 
          words: sessionData.wordCount 
        }),
        updated_at: new Date().toISOString()
      })
      .eq('id', sessionData.projectId)

    if (updateError) {
      console.error('Error updating project word count:', updateError)
    }

    return NextResponse.json({ session })
  } catch (error) {
    console.error('Error in writing sessions POST:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 })
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase: SupabaseClient<Database> = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('id')

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID required' }, { status: 400 })
    }

    // Get session details before deleting
    const { data: session, error: fetchError } = await supabase
      .from('writing_sessions')
      .select('project_id, word_count')
      .eq('id', sessionId)
      .eq('user_id', user.id)
      .single()

    if (fetchError || !session) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 })
    }

    // Delete the session
    const { error: deleteError } = await supabase
      .from('writing_sessions')
      .delete()
      .eq('id', sessionId)
      .eq('user_id', user.id)

    if (deleteError) {
      console.error('Error deleting writing session:', deleteError)
      return NextResponse.json({ error: 'Failed to delete writing session' }, { status: 500 })
    }

    // Update project word count
    if (session.word_count > 0) {
      const { error: updateError } = await supabase
        .from('projects')
        .update({ 
          total_word_count: supabase.rpc('decrement_word_count', { 
            project_id: session.project_id, 
            words: session.word_count 
          }),
          updated_at: new Date().toISOString()
        })
        .eq('id', session.project_id)

      if (updateError) {
        console.error('Error updating project word count:', updateError)
      }
    }

    return NextResponse.json({ success: true, message: 'Writing session deleted' })
  } catch (error) {
    console.error('Error in writing sessions DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}