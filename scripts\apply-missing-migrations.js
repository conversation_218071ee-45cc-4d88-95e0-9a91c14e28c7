const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function applyMigration(migrationFile) {
  console.log(`\n📄 Applying migration: ${migrationFile}`)
  
  try {
    const migrationPath = path.join('supabase/migrations', migrationFile)
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    // Split by semicolon and execute each statement
    const statements = migrationSQL
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0 && !s.startsWith('--'))
    
    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`  Executing: ${statement.substring(0, 50)}...`)
        const { error } = await supabase.rpc('exec_sql', { sql: statement })
        
        if (error) {
          console.error(`  ❌ Error: ${error.message}`)
          // Continue with other statements
        } else {
          console.log(`  ✅ Success`)
        }
      }
    }
    
    console.log(`✅ Migration ${migrationFile} completed`)
    
  } catch (error) {
    console.error(`❌ Failed to apply migration ${migrationFile}:`, error.message)
  }
}

async function main() {
  console.log('🚀 Applying missing database migrations...')
  
  // Apply migrations in order
  const migrations = [
    '20250119_create_achievements.sql',
    '20250119_create_quality_metrics.sql',
    '20250119_user_analytics.sql',
    '20250119_add_performance_indexes.sql'
  ]
  
  for (const migration of migrations) {
    await applyMigration(migration)
  }
  
  console.log('\n🎉 All migrations applied!')
}

main().catch(console.error)
