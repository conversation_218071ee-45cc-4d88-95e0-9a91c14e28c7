/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300..700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Code+Pro:ital,wght@0,200..900;1,200..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Merriweather:ital,wght@0,300;0,400;0,700;0,900;1,300;1,400;1,700;1,900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import editor layout animations */
@import '../styles/editor-layout.css';

@layer base {
  :root {
    /* Default theme - Writer's Sanctuary Light */
    /* Writer's Sanctuary Theme - Warm Literary Colors */
    --background: 45 50% 97%; /* Warm paper white */
    --foreground: 25 30% 10%; /* Darker charcoal text - increased contrast */
    --card: 42 45% 95%; /* Slightly warmer card background */
    --card-foreground: 25 30% 10%; /* Darker text on cards */
    --popover: 42 45% 95%;
    --popover-foreground: 25 30% 10%; /* Darker popover text */
    --primary: 25 75% 45%; /* Rich amber-brown */
    --primary-foreground: 45 50% 97%;
    --secondary: 40 35% 88%; /* Warm beige */
    --secondary-foreground: 25 30% 10%; /* Darker secondary text */
    --muted: 40 30% 90%; /* Light warm gray */
    --muted-foreground: 25 20% 35%; /* Darker muted text - increased contrast */
    --accent: 35 60% 85%; /* Light amber accent */
    --accent-foreground: 25 30% 10%; /* Darker accent text */
    --destructive: 0 75% 55%; /* Warm red */
    --destructive-foreground: 45 50% 97%;
    --border: 40 25% 85%; /* Warm border */
    --input: 40 25% 90%; /* Input background */
    --ring: 25 75% 45%; /* Focus ring matches primary */
    --radius: 0.75rem; /* Slightly more rounded for warmth */

    /* Literary-specific colors */
    --literary-gold: 45 85% 55%;
    --literary-amber: 35 75% 50%;
    --literary-parchment: 45 40% 92%;
    --literary-ink: 25 40% 15%; /* Darker ink for better contrast */
    --literary-quill: 30 50% 30%; /* Darker quill for better contrast */
    
    /* Status colors - semantic tokens */
    --status-completed: 120 60% 40%; /* Green */
    --status-in-progress: 210 70% 50%; /* Blue */
    --status-draft: 45 90% 50%; /* Yellow/Amber */
    --status-published: 270 60% 55%; /* Purple */
    --status-default: 0 0% 50%; /* Gray */
  }

  .writers-sanctuary-light {
    /* Writer's Sanctuary Theme - Warm Literary Colors */
    --background: 45 50% 97%; /* Warm paper white */
    --foreground: 25 30% 10%; /* Darker charcoal text - increased contrast */
    --card: 42 45% 95%; /* Slightly warmer card background */
    --card-foreground: 25 30% 10%; /* Darker text on cards */
    --popover: 42 45% 95%;
    --popover-foreground: 25 30% 10%; /* Darker popover text */
    --primary: 25 75% 45%; /* Rich amber-brown */
    --primary-foreground: 45 50% 97%;
    --secondary: 40 35% 88%; /* Warm beige */
    --secondary-foreground: 25 30% 10%; /* Darker secondary text */
    --muted: 40 30% 90%; /* Light warm gray */
    --muted-foreground: 25 20% 35%; /* Darker muted text - increased contrast */
    --accent: 35 60% 85%; /* Light amber accent */
    --accent-foreground: 25 30% 10%; /* Darker accent text */
    --destructive: 0 75% 55%; /* Warm red */
    --destructive-foreground: 45 50% 97%;
    --border: 40 25% 85%; /* Warm border */
    --input: 40 25% 90%; /* Input background */
    --ring: 25 75% 45%; /* Focus ring matches primary */
    --radius: 0.75rem; /* Slightly more rounded for warmth */

    /* Literary-specific colors */
    --literary-gold: 45 85% 55%;
    --literary-amber: 35 75% 50%;
    --literary-parchment: 45 40% 92%;
    --literary-ink: 25 40% 15%; /* Darker ink for better contrast */
    --literary-quill: 30 50% 30%; /* Darker quill for better contrast */
    
    /* Status colors - semantic tokens */
    --status-completed: 120 60% 40%; /* Green */
    --status-in-progress: 210 70% 50%; /* Blue */
    --status-draft: 45 90% 50%; /* Yellow/Amber */
    --status-published: 270 60% 55%; /* Purple */
    --status-default: 0 0% 50%; /* Gray */
  }

  .evening-study-dark {
    /* Dark Writer's Sanctuary Theme - Rich Dark Brown Evening Study */
    --background: 25 8% 6%; /* Very dark warm brown #0f0e0d */
    --foreground: 45 20% 98%; /* Brighter cream text for better contrast */
    --card: 25 12% 12%; /* Dark brown card background #1f1c19 */
    --card-foreground: 45 20% 98%; /* Brighter text on cards */
    --popover: 25 12% 12%;
    --popover-foreground: 45 20% 98%; /* Brighter popover text */
    --primary: 35 45% 65%; /* Warm amber accent #D4A574 */
    --primary-foreground: 25 8% 6%;
    --secondary: 25 10% 16%; /* Dark brown secondary #2a2520 */
    --secondary-foreground: 45 20% 98%; /* Brighter secondary text */
    --muted: 25 10% 14%; /* Dark brown muted #242017 */
    --muted-foreground: 35 12% 70%; /* Brighter muted text for better contrast */
    --accent: 35 45% 25%; /* Darker amber accent */
    --accent-foreground: 45 20% 98%; /* Brighter accent text */
    --destructive: 0 65% 55%; /* Warm red */
    --destructive-foreground: 45 20% 98%; /* Brighter destructive text */
    --border: 25 10% 18%; /* Subtle dark brown border #2e2a24 */
    --input: 25 12% 10%; /* Very dark brown input #1a1714 */
    --ring: 35 45% 65%; /* Amber focus ring */

    /* Dark literary colors - matching reference with warm browns */
    --literary-gold: 35 45% 65%; /* #D4A574 */
    --literary-amber: 35 50% 60%; /* Slightly brighter amber */
    --literary-parchment: 25 12% 12%; /* Dark brown parchment */
    --literary-ink: 45 20% 98%; /* Brighter light ink for better contrast */
    --literary-quill: 35 40% 60%; /* Brighter amber quill for better contrast */
    
    /* Status colors - semantic tokens (dark mode) */
    --status-completed: 120 45% 60%; /* Lighter green */
    --status-in-progress: 210 60% 70%; /* Lighter blue */
    --status-draft: 45 80% 65%; /* Lighter amber */
    --status-published: 270 50% 70%; /* Lighter purple */
    --status-default: 0 0% 65%; /* Lighter gray */
  }

  /* Forest Manuscript Light Theme */
  .forest-manuscript-light {
    --background: 120 20% 98%; /* Light green-tinted white #f8faf8 */
    --foreground: 120 25% 15%; /* Darker green-brown for better contrast */
    --card: 120 20% 95%; /* Light green card #f0f5f0 */
    --card-foreground: 120 25% 15%; /* Darker text on cards */
    --popover: 120 20% 95%;
    --popover-foreground: 120 25% 15%; /* Darker popover text */
    --primary: 120 30% 35%; /* Forest green #4a7a4a */
    --primary-foreground: 120 20% 98%;
    --secondary: 120 20% 90%; /* Light green secondary #e8f0e8 */
    --secondary-foreground: 120 25% 15%; /* Darker secondary text */
    --muted: 120 20% 92%; /* Very light green muted */
    --muted-foreground: 120 20% 40%; /* Darker muted text for better contrast */
    --accent: 120 30% 35%; /* Forest green accent */
    --accent-foreground: 120 20% 98%;
    --destructive: 0 65% 55%; /* Red */
    --destructive-foreground: 120 20% 98%;
    --border: 120 20% 88%; /* Light green border */
    --input: 120 20% 95%; /* Light green input */
    --ring: 120 30% 35%; /* Forest green focus ring */

    /* Forest literary colors */
    --literary-gold: 45 50% 55%; /* Golden amber #cc8844 */
    --literary-amber: 45 60% 50%; /* Brighter amber */
    --literary-parchment: 120 20% 95%; /* Light green parchment */
    --literary-ink: 120 25% 15%; /* Darker green ink for better contrast */
    --literary-quill: 120 35% 30%; /* Darker forest green quill for better contrast */
    
    /* Status colors - semantic tokens (forest light) */
    --status-completed: 120 65% 35%; /* Forest green */
    --status-in-progress: 210 65% 45%; /* Forest blue */
    --status-draft: 45 85% 45%; /* Forest amber */
    --status-published: 270 55% 50%; /* Forest purple */
    --status-default: 0 0% 45%; /* Forest gray */
  }

  /* Midnight Ink Dark Theme */
  .midnight-ink-dark {
    --background: 220 40% 8%; /* Deep blue-black #0f1419 */
    --foreground: 0 0% 95%; /* Brighter light gray for better contrast */
    --card: 220 35% 12%; /* Dark blue card #1a1f2e */
    --card-foreground: 0 0% 95%; /* Brighter text on cards */
    --popover: 220 35% 12%;
    --popover-foreground: 0 0% 95%; /* Brighter popover text */
    --primary: 220 100% 70%; /* Bright blue #6699ff */
    --primary-foreground: 220 40% 8%;
    --secondary: 220 30% 16%; /* Dark blue secondary #252a3a */
    --secondary-foreground: 0 0% 95%; /* Brighter secondary text */
    --muted: 220 30% 14%; /* Dark blue muted */
    --muted-foreground: 220 15% 70%; /* Brighter muted text for better contrast */
    --accent: 220 100% 70%; /* Bright blue accent */
    --accent-foreground: 220 40% 8%;
    --destructive: 0 85% 70%; /* Bright red #ff6b6b */
    --destructive-foreground: 220 40% 8%;
    --border: 220 30% 18%; /* Dark blue border */
    --input: 220 35% 10%; /* Very dark blue input */
    --ring: 220 100% 70%; /* Bright blue focus ring */

    /* Midnight literary colors */
    --literary-gold: 35 100% 70%; /* Bright orange #ffa366 */
    --literary-amber: 45 100% 70%; /* Bright yellow #ffcc66 */
    --literary-parchment: 220 35% 12%; /* Dark blue parchment */
    --literary-ink: 0 0% 95%; /* Brighter light gray ink for better contrast */
    --literary-quill: 220 100% 75%; /* Brighter blue quill for better contrast */
    
    /* Status colors - semantic tokens (midnight dark) */
    --status-completed: 120 50% 65%; /* Midnight green */
    --status-in-progress: 220 100% 70%; /* Midnight blue */
    --status-draft: 45 100% 70%; /* Midnight amber */
    --status-published: 270 60% 75%; /* Midnight purple */
    --status-default: 0 0% 70%; /* Midnight gray */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--settings-ui-font, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif);
    font-weight: 400;
    font-size: var(--settings-ui-font-size, 14px);
    line-height: var(--settings-line-height, 1.6);
    letter-spacing: var(--settings-letter-spacing, 0em);
  }

  /* Typography system */
  .font-literary {
    font-family: var(--settings-reading-font, 'Crimson Text', Georgia, serif);
  }

  .font-literary-display {
    font-family: var(--settings-reading-font, 'Crimson Text', Georgia, serif);
    font-weight: 600;
    letter-spacing: -0.025em;
  }

  /* Body text font - applies literary font to general content */
  .font-body {
    font-family: var(--settings-reading-font, 'Crimson Text', Georgia, serif);
    font-size: var(--settings-font-size, 16px);
    line-height: var(--settings-line-height, 1.6);
    letter-spacing: var(--settings-letter-spacing, 0em);
  }

  /* Settings-aware typography classes */
  .font-editor {
    font-family: var(--settings-editor-font, 'JetBrains Mono', 'Courier New', monospace);
    font-size: var(--settings-editor-font-size, 14px);
    line-height: var(--settings-line-height, 1.6);
    letter-spacing: var(--settings-letter-spacing, 0em);
  }

  .font-ui {
    font-family: var(--settings-ui-font, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif);
    font-size: var(--settings-ui-font-size, 14px);
    line-height: var(--settings-line-height, 1.6);
    letter-spacing: var(--settings-letter-spacing, 0em);
  }

  .font-reading {
    font-family: var(--settings-reading-font, 'Crimson Text', Georgia, serif);
    font-size: var(--settings-reading-font-size, 16px);
    line-height: var(--settings-line-height, 1.6);
    letter-spacing: var(--settings-letter-spacing, 0em);
  }
  
  /* Standardized font sizes - respecting user settings */
  .text-xs { 
    font-size: calc(var(--settings-ui-font-size, 14px) * 0.857); /* ~12px at default */
    line-height: 1.5; 
  }
  .text-sm { 
    font-size: var(--settings-ui-font-size, 14px);
    line-height: 1.5; 
  }
  .text-base { 
    font-size: calc(var(--settings-ui-font-size, 14px) * 1.143); /* ~16px at default */
    line-height: 1.6; 
  }
  .text-lg { 
    font-size: calc(var(--settings-ui-font-size, 14px) * 1.286); /* ~18px at default */
    line-height: 1.6; 
  }
  .text-xl { 
    font-size: calc(var(--settings-ui-font-size, 14px) * 1.429); /* ~20px at default */
    line-height: 1.4; 
  }
  .text-2xl { 
    font-size: calc(var(--settings-ui-font-size, 14px) * 1.714); /* ~24px at default */
    line-height: 1.3; 
  }
  .text-3xl { 
    font-size: calc(var(--settings-ui-font-size, 14px) * 2.143); /* ~30px at default */
    line-height: 1.2; 
  }
  .text-4xl { 
    font-size: calc(var(--settings-ui-font-size, 14px) * 2.571); /* ~36px at default */
    line-height: 1.2; 
  }
  
  .font-mono {
    font-family: var(--settings-editor-font, 'JetBrains Mono', 'Courier New', monospace);
  }
  
  /* Headers - use reading font from settings */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--settings-reading-font, 'Crimson Text', Georgia, serif);
    font-weight: 600;
    letter-spacing: var(--settings-letter-spacing, -0.025em);
    margin-bottom: 1rem;
  }
  
  h1 { 
    font-size: calc(var(--settings-reading-font-size, 16px) * 2.25); /* ~36px at default */
    line-height: 1.2; 
  }
  h2 { 
    font-size: calc(var(--settings-reading-font-size, 16px) * 1.875); /* ~30px at default */
    line-height: 1.3; 
  }
  h3 { 
    font-size: calc(var(--settings-reading-font-size, 16px) * 1.5); /* ~24px at default */
    line-height: 1.4; 
  }
  h4 { 
    font-size: calc(var(--settings-reading-font-size, 16px) * 1.25); /* ~20px at default */
    line-height: 1.4; 
  }
  h5 { 
    font-size: calc(var(--settings-reading-font-size, 16px) * 1.125); /* ~18px at default */
    line-height: 1.5; 
  }
  h6 { 
    font-size: var(--settings-reading-font-size, 16px);
    line-height: 1.5; 
  }
  
  /* Form inputs, code, and body text - use editor font from settings */
  input, textarea, select, code, pre {
    font-family: var(--settings-editor-font, 'JetBrains Mono', 'Courier New', monospace);
  }
  
  /* Labels and small text */
  label, .text-xs, .text-sm {
    font-family: var(--settings-ui-font, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif);
    font-weight: 400;
  }
  
  /* Paragraphs and body text */
  p, li, td, dd {
    font-family: var(--settings-ui-font, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif);
  }
  
  /* Navigation and menu items */
  nav, .menu-item, [role="menuitem"] {
    font-family: var(--settings-ui-font, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif);
    font-weight: 500;
  }
  
  /* Dialog and modal titles */
  [role="dialog"] h2, [role="dialog"] h3,
  .modal-title, .dialog-title {
    font-family: var(--settings-ui-font, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif);
    font-weight: 600;
  }
  
  /* Important UI elements */
  .badge, .chip, .tag {
    font-family: var(--settings-ui-font, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif);
  }
  
  /* Prevent text cutoff */
  * {
    overflow-wrap: break-word;
    word-wrap: break-word;
  }
  
  /* Consistent border radius */
  .rounded-xs { border-radius: 0.25rem; }
  .rounded-sm { border-radius: 0.375rem; }
  .rounded-md { border-radius: 0.5rem; }
  .rounded-lg { border-radius: 0.75rem; }
  .rounded-xl { border-radius: 1rem; }
  .rounded-2xl { border-radius: 1.5rem; }

  /* Paper texture background */
  .paper-texture {
    background-image:
      radial-gradient(circle at 1px 1px, rgba(180, 140, 90, 0.15) 1px, transparent 0);
    background-size: 20px 20px;
  }

  /* Dark theme paper texture */
  .dark .paper-texture {
    background-image:
      radial-gradient(circle at 1px 1px, rgba(212, 165, 116, 0.08) 1px, transparent 0);
    background-size: 20px 20px;
  }
  
  /* Consistent spacing */
  .space-y-comfortable > * + * {
    margin-top: 1.5rem;
  }
  
  /* Better scroll containers */
  .scroll-container {
    @apply overflow-y-auto overflow-x-hidden;
    scrollbar-width: thin;
    scrollbar-color: theme('colors.warm.300') transparent;
  }
  
  .scroll-container::-webkit-scrollbar {
    width: 8px;
  }
  
  .scroll-container::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scroll-container::-webkit-scrollbar-thumb {
    @apply bg-warm-300 rounded-full;
  }
  
  .scroll-container::-webkit-scrollbar-thumb:hover {
    @apply bg-warm-400;
  }

  /* Ink drop effect for buttons */
  .ink-drop {
    position: relative;
    overflow: hidden;
  }

  .ink-drop::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transform: translate(-50%, -50%);
    transition: width var(--settings-transition-duration, 0.6s), height var(--settings-transition-duration, 0.6s);
  }

  .ink-drop:hover::before {
    width: 300px;
    height: 300px;
  }

  /* Accessibility classes */
  .reduced-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .high-contrast {
    filter: contrast(var(--settings-contrast-multiplier, 1.5));
  }

  .enhanced-focus *:focus {
    outline: 3px solid var(--primary) !important;
    outline-offset: 2px !important;
  }

  .screen-reader-optimized .sr-only {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
  }
}

/* Modern gradient animations */
@layer utilities {
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 8s ease infinite;
  }
  
  @keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
  
  /* Noise texture effect */
  .noise-texture {
    position: relative;
  }
  
  .noise-texture::before {
    content: "";
    position: absolute;
    inset: 0;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
    opacity: 0.02;
    mix-blend-mode: overlay;
    pointer-events: none;
  }
}

/* Tutorial Highlighting */
.tutorial-highlight {
  position: relative;
  z-index: 35;
  outline: 3px solid hsl(var(--primary));
  outline-offset: 4px;
  border-radius: 0.375rem;
  animation: tutorial-pulse 2s ease-in-out infinite;
}

@keyframes tutorial-pulse {
  0%, 100% {
    outline-color: hsl(var(--primary));
    outline-offset: 4px;
  }
  50% {
    outline-color: hsl(var(--primary) / 0.6);
    outline-offset: 8px;
  }
}

/* Accessibility - Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Focus Visible Improvements */
*:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Smooth Scrolling */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Better Mobile Tap Targets */
@media (max-width: 768px) {
  button, a, input, textarea, select {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Theme-aware utilities */
@layer utilities {
  /* Use theme variables instead of hardcoded colors */
  .theme-card {
    @apply bg-card border-border backdrop-blur-sm;
  }

  .theme-card-elevated {
    @apply bg-card border-border shadow-xl;
  }

  .theme-accent {
    @apply text-primary;
  }

  .theme-accent-bg {
    @apply bg-primary;
  }

  .theme-bg-primary {
    @apply bg-background;
  }

  .theme-bg-secondary {
    @apply bg-secondary;
  }

  .theme-text-primary {
    @apply text-foreground;
  }

  .theme-text-secondary {
    @apply text-muted-foreground;
  }
  
  /* Consistent spacing utilities */
  .spacing-xs { @apply p-2 gap-2; }
  .spacing-sm { @apply p-3 gap-3; }
  .spacing-md { @apply p-4 gap-4; }
  .spacing-lg { @apply p-6 gap-6; }
  .spacing-xl { @apply p-8 gap-8; }
}
