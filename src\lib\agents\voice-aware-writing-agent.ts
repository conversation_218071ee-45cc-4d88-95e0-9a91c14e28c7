import { logger } from '@/lib/services/logger';
import { WritingAgent } from './writing-agent';
import { ChapterContent, ChapterOutline } from './types';
import { VoiceProfileManager } from '@/lib/services/voice-profile-manager';
import { createClient } from '@/lib/supabase/client';
import type { Database } from '@/lib/supabase/types';

type VoiceProfile = Database['public']['Tables']['voice_profiles']['Row'];

export class VoiceAwareWritingAgent extends WritingAgent {
  private voiceProfileManager: VoiceProfileManager;
  private supabase = createClient();

  constructor(context: import('./types').BookContext, contextManager?: import('../services/context-manager').ContextManager) {
    super(context, contextManager);
    this.voiceProfileManager = new VoiceProfileManager();
  }

  async writeChapter(chapterOutline: ChapterOutline): Promise<ChapterContent> {
    // Get relevant voice profiles
    const voiceProfiles = await this.loadVoiceProfiles(chapterOutline);
    
    // Add voice profile context to the writing prompt
    const chapterWithVoiceContext = await super.writeChapter({
      ...chapterOutline,
      voiceGuidance: this.createVoiceGuidance(voiceProfiles)
    } as ChapterOutline);

    // Apply voice consistency post-processing
    if (voiceProfiles.length > 0) {
      const refinedContent = await this.applyVoiceConsistency(
        chapterWithVoiceContext,
        voiceProfiles
      );
      return refinedContent;
    }

    return chapterWithVoiceContext;
  }

  private async loadVoiceProfiles(chapterOutline: ChapterOutline): Promise<VoiceProfile[]> {
    const profiles: VoiceProfile[] = [];

    try {
      // Load project-level voice profile
      if (this.context.projectId) {
        const { data: projectProfiles } = await this.supabase
          .from('voice_profiles')
          .select('*')
          .eq('project_id', this.context.projectId)
          .eq('type', 'author');
        
        if (projectProfiles) {
          profiles.push(...projectProfiles);
        }
      }

      // Load series-level voice profile
      if (this.context.seriesId) {
        const { data: seriesProfiles } = await this.supabase
          .from('voice_profiles')
          .select('*')
          .eq('series_id', this.context.seriesId)
          .eq('type', 'author');
        
        if (seriesProfiles) {
          profiles.push(...seriesProfiles);
        }
      }

      // Load character-specific voice profiles for POV character
      if (chapterOutline.povCharacter) {
        const character = this.context.characters?.find(
          c => c.name === chapterOutline.povCharacter
        );
        
        if (character?.id) {
          const { data: characterProfiles } = await this.supabase
            .from('voice_profiles')
            .select('*')
            .eq('character_id', character.id)
            .eq('type', 'character');
          
          if (characterProfiles) {
            profiles.push(...characterProfiles);
          }
        }
      }

      // Load any global voice profiles
      const { data: globalProfiles } = await this.supabase
        .from('voice_profiles')
        .select('*')
        .eq('is_global', true)
        .eq('user_id', this.context.userId);
      
      if (globalProfiles) {
        profiles.push(...globalProfiles);
      }

    } catch (error) {
      logger.error('Error loading voice profiles:', error);
    }

    // Sort by priority: character > project > series > global
    return profiles.sort((a, b) => {
      const getPriority = (profile: VoiceProfile) => {
        if (profile.character_id) return 4;
        if (profile.project_id) return 3;
        if (profile.series_id) return 2;
        if (profile.is_global) return 1;
        return 0;
      };
      return getPriority(b) - getPriority(a);
    });
  }

  private createVoiceGuidance(voiceProfiles: VoiceProfile[]): string {
    if (voiceProfiles.length === 0) return '';

    const guidance: string[] = ['VOICE PROFILE REQUIREMENTS:'];

    voiceProfiles.forEach(profile => {
      if (profile.patterns && typeof profile.patterns === 'object') {
        const patterns = profile.patterns as any;
        
        guidance.push(`\n${profile.name} (${profile.type}):`);
        
        if (patterns.sentenceStructure) {
          guidance.push(`- Average sentence length: ${patterns.sentenceStructure.averageLength} words`);
          guidance.push(`- Complexity level: ${patterns.sentenceStructure.complexityScore}/10`);
        }
        
        if (patterns.vocabulary) {
          guidance.push(`- Formality level: ${patterns.vocabulary.formalityScore}/10`);
          if (patterns.vocabulary.signatureWords?.length > 0) {
            guidance.push(`- Signature words/phrases: ${patterns.vocabulary.signatureWords.slice(0, 10).join(', ')}`);
          }
        }
        
        if (patterns.style) {
          guidance.push(`- Show vs Tell ratio: ${patterns.style.showVsTell}/10`);
          guidance.push(`- Dialogue ratio: ${(patterns.style.dialogueRatio * 100).toFixed(0)}%`);
          guidance.push(`- Action ratio: ${(patterns.style.actionRatio * 100).toFixed(0)}%`);
        }
        
        if (patterns.tone) {
          guidance.push(`- Emotional range: ${patterns.tone.emotionalRange?.join(', ')}`);
          guidance.push(`- Intensity level: ${patterns.tone.intensity}/10`);
        }
        
        if (patterns.rhythm) {
          guidance.push(`- Pacing score: ${patterns.rhythm.pacingScore}/10`);
        }
      }

      if (profile.sample_texts && profile.sample_texts.length > 0) {
        guidance.push(`\nExample text style:`);
        guidance.push(`"${profile.sample_texts[0].substring(0, 200)}..."`);
      }
    });

    return guidance.join('\n');
  }

  private async applyVoiceConsistency(
    chapter: ChapterContent,
    voiceProfiles: VoiceProfile[]
  ): Promise<ChapterContent> {
    // This is where we would apply post-processing to ensure voice consistency
    // For now, we'll return the chapter as-is, but this could be enhanced
    // with additional AI calls to refine the voice
    
    const primaryProfile = voiceProfiles[0];
    if (!primaryProfile || !primaryProfile.patterns) {
      return chapter;
    }

    // Log voice application for monitoring
    logger.info('Applied voice profiles to chapter', {
      chapterNumber: chapter.chapterNumber,
      profilesUsed: voiceProfiles.map(p => ({ 
        id: p.id, 
        name: p.name, 
        type: p.type 
      }))
    });

    // Store voice consistency metadata
    return {
      ...chapter,
      voiceProfilesApplied: voiceProfiles.map(p => p.id),
      voiceConsistencyScore: primaryProfile.confidence || 0
    } as ChapterContent;
  }

  protected createSystemPrompt(agentName: string, roleDescription: string): string {
    // Get base prompt from parent
    const basePrompt = super.createSystemPrompt(agentName, roleDescription);
    
    // Add voice-specific instructions
    const voiceInstructions = `
VOICE CONSISTENCY REQUIREMENTS:
- Maintain consistent narrative voice throughout the chapter
- Apply character-specific voice patterns for dialogue and POV
- Ensure stylistic elements match established voice profiles
- Balance voice consistency with natural prose flow
`;

    return `${basePrompt}\n\n${voiceInstructions}`;
  }
}