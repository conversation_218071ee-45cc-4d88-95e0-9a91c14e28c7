'use client'

import { useState, useEffect, useCallback } from 'react'
import { toast } from 'sonner'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'

import { KnowledgeGenerator } from './knowledge-generator'
import {
  X,
  BookOpen,
  Users,
  MapPin,
  Lightbulb,
  Plus,
  Edit3,
  Save,
  Feather,
  Heart,
  Sword,
  Globe,
  Clock,
  Target,
  Palette,
  FileText,
  Layers,
  GitBranch,
  Wand2,
  Trash2,
  Search,
  Loader2,
  AlertCircle
} from 'lucide-react'

// Centralized KnowledgeItem interface to avoid duplication
export interface KnowledgeItem {
  id: string
  type: 'character' | 'location' | 'story-arc' | 'theme' | 'conflict' | 'world-building' | 'timeline' | 'setting' | 'plot-device' | 'research' | 'note'
  title: string
  content: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
  category?: string
  importance?: 'low' | 'medium' | 'high'
  connections?: string[] // IDs of related items
  projectId: string
}

// Interface for serialized knowledge items (from localStorage/API)
interface SerializedKnowledgeItem {
  id: string
  type: KnowledgeItem['type']
  title: string
  content: string
  tags: string[]
  createdAt: string
  updatedAt: string
  category?: string
  importance?: 'low' | 'medium' | 'high'
  connections?: string[]
  projectId: string
}

// Interface for items from KnowledgeGenerator (without projectId)
export interface GeneratedKnowledgeItem {
  id: string
  type: KnowledgeItem['type']
  title: string
  content: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
  category?: string
  importance?: 'low' | 'medium' | 'high'
  connections?: string[]
}

interface KnowledgeInputPanelProps {
  projectId: string
  chapterId?: string
  selectedText?: string
  onClose?: () => void
}

export function KnowledgeInputPanel({
  projectId,
  chapterId: _chapterId,
  selectedText,
  onClose
}: KnowledgeInputPanelProps) {
  // State management
  const [activeKnowledgeCategory, setActiveKnowledgeCategory] = useState<string>('all')
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeItem[]>([])
  const [editingItem, setEditingItem] = useState<string | null>(null)
  const [showGenerator, setShowGenerator] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const [newItem, setNewItem] = useState<{
    type: KnowledgeItem['type']
    title: string
    content: string
    tags: string[]
    importance: 'low' | 'medium' | 'high'
  }>({
    type: 'note',
    title: '',
    content: '',
    tags: [],
    importance: 'medium'
  })

  // Initialize with selectedText if provided
  useEffect(() => {
    if (selectedText && selectedText.trim()) {
      setNewItem(prev => ({
        ...prev,
        content: selectedText.trim()
      }))
    }
  }, [selectedText])

  // Load knowledge items from database
  const loadKnowledgeItems = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      // TODO: Replace with actual API call to load from story_bible table
      // For now, keeping local state as fallback
      const savedItems = localStorage.getItem(`knowledge_items_${projectId}`)
      if (savedItems) {
        const serializedItems = JSON.parse(savedItems) as SerializedKnowledgeItem[]
        const items = serializedItems.map((item) => ({
          ...item,
          createdAt: new Date(item.createdAt),
          updatedAt: new Date(item.updatedAt)
        }))
        setKnowledgeItems(items)
      }
    } catch (err) {
      setError('Failed to load knowledge items')
      console.error('Error loading knowledge items:', err)
    } finally {
      setIsLoading(false)
    }
  }, [projectId])

  // Save knowledge items to database
  const saveKnowledgeItems = useCallback(async (items: KnowledgeItem[]) => {
    try {
      // TODO: Replace with actual API call to save to story_bible table
      // For now, using localStorage as fallback
      localStorage.setItem(`knowledge_items_${projectId}`, JSON.stringify(items))
    } catch (err) {
      console.error('Error saving knowledge items:', err)
      toast.error('Failed to save knowledge items')
    }
  }, [projectId])

  // Load items on mount
  useEffect(() => {
    loadKnowledgeItems()
  }, [loadKnowledgeItems])


  // Knowledge categories with comprehensive story elements
  const knowledgeCategories = [
    { id: 'all', label: 'All Items', icon: Layers },
    { id: 'character', label: 'Characters', icon: Users },
    { id: 'location', label: 'Locations', icon: MapPin },
    { id: 'story-arc', label: 'Story Arcs', icon: GitBranch },
    { id: 'theme', label: 'Themes', icon: Heart },
    { id: 'conflict', label: 'Conflicts', icon: Sword },
    { id: 'world-building', label: 'World Building', icon: Globe },
    { id: 'timeline', label: 'Timeline', icon: Clock },
    { id: 'setting', label: 'Settings', icon: Palette },
    { id: 'plot-device', label: 'Plot Devices', icon: Target },
    { id: 'research', label: 'Research', icon: FileText },
    { id: 'note', label: 'General Notes', icon: Lightbulb }
  ]

  // Add knowledge item with database persistence
  const addKnowledgeItem = async () => {
    if (!newItem.title.trim() || !newItem.content.trim()) return

    const item: KnowledgeItem = {
      id: `item_${Date.now()}`,
      ...newItem,
      projectId,
      createdAt: new Date(),
      updatedAt: new Date(),
      connections: []
    }

    const updatedItems = [...knowledgeItems, item]
    setKnowledgeItems(updatedItems)
    await saveKnowledgeItems(updatedItems)

    setNewItem({
      type: 'note',
      title: '',
      content: '',
      tags: [],
      importance: 'medium'
    })

    toast.success('Knowledge item added successfully')
  }

  // Filter and search knowledge items
  const filteredKnowledgeItems = knowledgeItems.filter(item => {
    const matchesCategory = activeKnowledgeCategory === 'all' || item.type === activeKnowledgeCategory
    const matchesSearch = !searchQuery ||
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    return matchesCategory && matchesSearch
  })

  // Handle generated items from AI (convert to our interface)
  const handleGeneratedItems = (generatedItems: GeneratedKnowledgeItem[]) => {
    const itemsWithProjectId: KnowledgeItem[] = generatedItems.map(item => ({
      ...item,
      projectId,
      // Ensure dates are Date objects
      createdAt: item.createdAt instanceof Date ? item.createdAt : new Date(item.createdAt),
      updatedAt: item.updatedAt instanceof Date ? item.updatedAt : new Date(item.updatedAt)
    }))

    const updatedItems = [...knowledgeItems, ...itemsWithProjectId]
    setKnowledgeItems(updatedItems)
    saveKnowledgeItems(updatedItems)
    toast.success(`Added ${generatedItems.length} knowledge items`)
  }

  // Edit existing item
  const handleEditItem = (itemId: string) => {
    const item = knowledgeItems.find(i => i.id === itemId)
    if (item) {
      setNewItem({
        type: item.type,
        title: item.title,
        content: item.content,
        tags: item.tags,
        importance: item.importance || 'medium'
      })
      setEditingItem(itemId)
    }
  }

  // Update existing item
  const handleUpdateItem = async () => {
    if (!editingItem || !newItem.title.trim() || !newItem.content.trim()) return

    const updatedItems = knowledgeItems.map(item =>
      item.id === editingItem
        ? { ...item, ...newItem, updatedAt: new Date() }
        : item
    )

    setKnowledgeItems(updatedItems)
    await saveKnowledgeItems(updatedItems)

    setEditingItem(null)
    setNewItem({
      type: 'note',
      title: '',
      content: '',
      tags: [],
      importance: 'medium'
    })

    toast.success('Knowledge item updated successfully')
  }

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingItem(null)
    setNewItem({
      type: 'note',
      title: '',
      content: '',
      tags: [],
      importance: 'medium'
    })
  }

  // Delete knowledge item
  const handleDeleteItem = async (itemId: string) => {
    const updatedItems = knowledgeItems.filter(item => item.id !== itemId)
    setKnowledgeItems(updatedItems)
    await saveKnowledgeItems(updatedItems)
    toast.success('Knowledge item deleted')
  }

  // Utility functions for styling
  const getTypeIcon = (type: KnowledgeItem['type']) => {
    const category = knowledgeCategories.find(cat => cat.id === type)
    if (category) {
      const IconComponent = category.icon
      return <IconComponent className="h-4 w-4" />
    }
    return <Lightbulb className="h-4 w-4" />
  }

  const getTypeColor = (_type: KnowledgeItem['type']) => {
    // Use muted backgrounds with primary accents for all types
    return 'bg-muted text-muted-foreground border-border'
  }

  const getImportanceColor = (importance: 'low' | 'medium' | 'high') => {
    switch (importance) {
      case 'high': return 'bg-destructive/20 text-destructive border-destructive/30'
      case 'medium': return 'bg-primary/20 text-primary border-primary/30'
      case 'low': return 'bg-muted text-muted-foreground border-border'
      default: return 'bg-muted text-muted-foreground border-border'
    }
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="h-full flex flex-col border-border bg-background/95 backdrop-blur-sm shadow-lg relative">
        <div className="flex-1 flex items-center justify-center">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span className="font-mono">Loading knowledge base...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col border-border bg-background/95 backdrop-blur-sm shadow-lg relative">
      {/* Knowledge Generator Modal */}
      {showGenerator && (
        <KnowledgeGenerator
          projectId={projectId}
          onGenerated={handleGeneratedItems}
          onClose={() => setShowGenerator(false)}
        />
      )}

      {/* Error Alert */}
      {error && (
        <Alert className="m-4 border-destructive/50 bg-destructive/10">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="font-mono text-sm">
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setError(null)}
              className="ml-2 h-auto p-1"
            >
              <X className="h-3 w-3" />
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Header */}
      <div className="border-b border-border p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Feather className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-mono font-semibold text-foreground">
              Knowledge Base
            </h2>
            {knowledgeItems.length === 0 && (
              <Button
                size="sm"
                onClick={() => setShowGenerator(true)}
                className="ml-auto bg-primary hover:bg-primary/90 text-primary-foreground font-mono rounded-none"
              >
                <Wand2 className="h-3 w-3 mr-1" />
                Initialize
              </Button>
            )}
          </div>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose} className="text-muted-foreground hover:text-foreground">
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Search Bar */}
        {knowledgeItems.length > 0 && (
          <div className="mt-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search knowledge items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 font-mono bg-background border-border rounded-none"
              />
            </div>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Knowledge Categories */}
        <div className="border-b border-border p-4">
          <div className="grid grid-cols-2 gap-2 mb-4">
            {knowledgeCategories.map((category) => {
              const IconComponent = category.icon
              const isActive = activeKnowledgeCategory === category.id
              const categoryCount = category.id === 'all'
                ? knowledgeItems.length
                : knowledgeItems.filter(item => item.type === category.id).length

              return (
                <button
                  key={category.id}
                  onClick={() => setActiveKnowledgeCategory(category.id)}
                  className={`flex items-center gap-2 px-3 py-2 rounded-none text-xs font-mono transition-colors ${
                    isActive
                      ? 'bg-primary/20 text-primary border border-primary/50'
                      : 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground border border-border'
                  }`}
                >
                  <IconComponent className="h-3 w-3" />
                  {category.label}
                  <span className="ml-auto text-xs opacity-60">
                    {categoryCount}
                  </span>
                </button>
              )
            })}
          </div>
        </div>

        {/* Knowledge Items */}
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {/* Add New Item Form */}
            <div className="border border-border rounded-none bg-muted p-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <select
                    value={newItem.type}
                    onChange={(e) => setNewItem(prev => ({ ...prev, type: e.target.value as KnowledgeItem['type'] }))}
                    className="px-3 py-2 rounded-none border border-border bg-background text-sm font-mono"
                  >
                    {knowledgeCategories.slice(1).map(category => (
                      <option key={category.id} value={category.id}>{category.label}</option>
                    ))}
                  </select>
                  <select
                    value={newItem.importance}
                    onChange={(e) => setNewItem(prev => ({ ...prev, importance: e.target.value as 'low' | 'medium' | 'high' }))}
                    className="px-3 py-2 rounded-none border border-border bg-background text-sm font-mono"
                  >
                    <option value="low">Low Priority</option>
                    <option value="medium">Medium Priority</option>
                    <option value="high">High Priority</option>
                  </select>
                </div>
                <Input
                  placeholder="Enter character name, location, or item title..."
                  value={newItem.title}
                  onChange={(e) => setNewItem(prev => ({ ...prev, title: e.target.value }))}
                  className="text-sm font-mono bg-background border-border rounded-none"
                />
                <Textarea
                  placeholder="Add detailed description, backstory, or notes about this story element..."
                  value={newItem.content}
                  onChange={(e) => setNewItem(prev => ({ ...prev, content: e.target.value }))}
                  className="min-h-[80px] text-sm font-mono bg-background border-border rounded-none"
                />
                <div className="flex gap-2">
                  <Button
                    onClick={editingItem ? handleUpdateItem : addKnowledgeItem}
                    size="sm"
                    className="bg-primary hover:bg-primary/90 text-primary-foreground font-mono rounded-none"
                    disabled={!newItem.title.trim() || !newItem.content.trim()}
                  >
                    {editingItem ? (
                      <>
                        <Save className="h-4 w-4 mr-1" />
                        Update Item
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-1" />
                        Add to Story
                      </>
                    )}
                  </Button>
                  {editingItem && (
                    <Button
                      onClick={handleCancelEdit}
                      size="sm"
                      variant="outline"
                      className="font-mono rounded-none border-border"
                    >
                      Cancel
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {/* Knowledge Items List */}
            {filteredKnowledgeItems.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                <BookOpen className="h-12 w-12 mx-auto mb-3 opacity-50 text-primary" />
                <p className="font-mono text-base mb-2 text-foreground">
                  No items in {activeKnowledgeCategory === 'all' ? 'knowledge base' : activeKnowledgeCategory}
                </p>
                <p className="text-sm font-mono text-muted-foreground">
                  {searchQuery ? 'Try adjusting your search terms' : 'Start building your story\'s knowledge base'}
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredKnowledgeItems.map((item) => (
                  <div key={item.id} className="border border-border rounded-none bg-card hover:shadow-sm transition-all hover:border-primary/50">
                    <div className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          {getTypeIcon(item.type)}
                          <h4 className="font-mono font-semibold text-card-foreground truncate">{item.title}</h4>
                          <Badge className={`${getTypeColor(item.type)} font-mono text-xs rounded-none`}>
                            {item.type}
                          </Badge>
                          {item.importance && (
                            <Badge className={`${getImportanceColor(item.importance)} font-mono text-xs rounded-none`}>
                              {item.importance}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditItem(item.id)}
                            className="text-muted-foreground hover:text-primary font-mono"
                          >
                            <Edit3 className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteItem(item.id)}
                            className="text-muted-foreground hover:text-destructive font-mono"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <pre className="text-sm text-card-foreground font-mono leading-relaxed whitespace-pre-wrap bg-muted p-3 rounded-none border border-border">
                        {item.content}
                      </pre>
                      {item.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {item.tags.map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs font-mono rounded-none">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                      <div className="flex items-center justify-between mt-3 text-xs text-muted-foreground font-mono">
                        <span>last modified: {item.updatedAt.toLocaleDateString()}</span>
                        <span>id: {item.id.slice(-8)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  )
}
