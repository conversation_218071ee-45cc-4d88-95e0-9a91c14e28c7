#!/usr/bin/env node

/**
 * Security Check Script for BookScribe AI
 * This script checks for security vulnerabilities and provides recommendations
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔒 BookScribe AI Security Check\n');

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Check if npm is available
try {
  execSync('npm --version', { stdio: 'ignore' });
} catch (error) {
  log('❌ npm is not available. Please install Node.js and npm.', 'red');
  process.exit(1);
}

// Run npm audit
log('📊 Running npm audit...', 'blue');
try {
  const auditOutput = execSync('npm audit --json', { encoding: 'utf8' });
  const auditData = JSON.parse(auditOutput);
  
  if (auditData.vulnerabilities && Object.keys(auditData.vulnerabilities).length > 0) {
    log(`⚠️  Found ${Object.keys(auditData.vulnerabilities).length} vulnerabilities:`, 'yellow');
    
    Object.entries(auditData.vulnerabilities).forEach(([pkg, vuln]) => {
      const severity = vuln.severity || 'unknown';
      const color = severity === 'high' || severity === 'critical' ? 'red' : 
                   severity === 'moderate' ? 'yellow' : 'blue';
      log(`  - ${pkg}: ${severity} severity`, color);
    });
    
    // Check for specific EPUB-related vulnerabilities
    const epubVulns = Object.keys(auditData.vulnerabilities).filter(pkg => 
      ['epub', 'tar', 'xml2js', 'zipfile', 'node-pre-gyp'].includes(pkg)
    );
    
    if (epubVulns.length > 0) {
      log('\n📚 EPUB-related vulnerabilities detected:', 'yellow');
      epubVulns.forEach(pkg => {
        log(`  - ${pkg}`, 'yellow');
      });
      log('\n💡 Recommendations:', 'blue');
      log('  1. Review SECURITY-ADVISORY.md for detailed information');
      log('  2. Consider limiting EPUB import functionality');
      log('  3. Monitor EPUB processing logs for unusual activity');
      log('  4. Plan migration to more secure EPUB library');
    }
    
  } else {
    log('✅ No vulnerabilities found!', 'green');
  }
  
} catch (error) {
  if (error.status === 1) {
    // npm audit returns exit code 1 when vulnerabilities are found
    log('⚠️  Vulnerabilities found. Run "npm audit" for details.', 'yellow');
  } else {
    log('❌ Error running npm audit:', 'red');
    console.error(error.message);
  }
}

// Check for outdated packages
log('\n📦 Checking for outdated packages...', 'blue');
try {
  const outdatedOutput = execSync('npm outdated --json', { encoding: 'utf8' });
  if (outdatedOutput.trim()) {
    const outdatedData = JSON.parse(outdatedOutput);
    const outdatedCount = Object.keys(outdatedData).length;
    
    if (outdatedCount > 0) {
      log(`📈 Found ${outdatedCount} outdated packages`, 'yellow');
      
      // Check for security-related packages
      const securityPackages = ['epub', 'tar', 'xml2js', 'zipfile'];
      const outdatedSecurityPackages = Object.keys(outdatedData).filter(pkg => 
        securityPackages.includes(pkg)
      );
      
      if (outdatedSecurityPackages.length > 0) {
        log('🔒 Security-related packages that can be updated:', 'yellow');
        outdatedSecurityPackages.forEach(pkg => {
          const info = outdatedData[pkg];
          log(`  - ${pkg}: ${info.current} → ${info.latest}`, 'yellow');
        });
      }
    }
  } else {
    log('✅ All packages are up to date!', 'green');
  }
} catch (error) {
  // npm outdated returns exit code 1 when outdated packages are found
  if (error.status !== 1) {
    log('⚠️  Could not check for outdated packages', 'yellow');
  }
}

// Check Docker security
log('\n🐳 Docker Security Check...', 'blue');
try {
  execSync('docker --version', { stdio: 'ignore' });
  
  // Check if Dockerfile exists and has security best practices
  const dockerfilePath = path.join(process.cwd(), 'Dockerfile');
  if (fs.existsSync(dockerfilePath)) {
    const dockerfileContent = fs.readFileSync(dockerfilePath, 'utf8');
    
    const securityChecks = [
      {
        check: dockerfileContent.includes('USER '),
        message: 'Non-root user configured'
      },
      {
        check: dockerfileContent.includes('HEALTHCHECK'),
        message: 'Health check configured'
      },
      {
        check: !dockerfileContent.includes('ADD ') || dockerfileContent.includes('COPY '),
        message: 'Using COPY instead of ADD (recommended)'
      }
    ];
    
    securityChecks.forEach(({ check, message }) => {
      log(`  ${check ? '✅' : '⚠️ '} ${message}`, check ? 'green' : 'yellow');
    });
  } else {
    log('  ⚠️  Dockerfile not found', 'yellow');
  }
} catch (error) {
  log('  ⚠️  Docker not available', 'yellow');
}

// Security recommendations
log('\n🛡️  Security Recommendations:', 'blue');
log('  1. Run security checks regularly (weekly)', 'reset');
log('  2. Keep dependencies updated', 'reset');
log('  3. Monitor security advisories', 'reset');
log('  4. Use Docker for isolation', 'reset');
log('  5. Implement proper input validation', 'reset');
log('  6. Review SECURITY-ADVISORY.md regularly', 'reset');

// Quick fix suggestions
log('\n🔧 Quick Actions:', 'blue');
log('  • Run "npm audit fix" to auto-fix some vulnerabilities', 'reset');
log('  • Run "npm update" to update packages to latest versions', 'reset');
log('  • Review and update .env.local with secure configurations', 'reset');

log('\n✨ Security check completed!', 'green');
