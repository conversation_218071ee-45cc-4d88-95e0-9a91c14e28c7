'use client'

import { useState } from 'react'
import { logger } from '@/lib/services/logger';

import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'

interface GenerateStructureButtonProps {
  projectId: string
}

export function GenerateStructureButton({ projectId }: GenerateStructureButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const router = useRouter()

  const handleGenerate = async () => {
    setIsGenerating(true)

    try {
      // Create an AbortController for timeout handling
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 300000) // 5 minute timeout

      const response = await fetch('/api/agents/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          action: 'generate_structure'
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const result = await response.json()
        throw new Error(result.error || `Server error: ${response.status}`)
      }

      const result = await response.json()

      // Refresh the page to show updated content
      router.refresh()

      // Show success message
      alert('Project structure generated successfully!')

    } catch (error) {
      logger.error('Generation error:', error);

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          alert('Generation is taking longer than expected. The process may still be running in the background. Please refresh the page in a few minutes to check if it completed.')
        } else if (error.message.includes('Failed to fetch')) {
          alert('Network error: Unable to connect to the server. Please check your connection and try again.')
        } else {
          alert(`Failed to generate structure: ${error.message}`)
        }
      } else {
        alert('Failed to generate structure: Unknown error')
      }
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <Button 
      onClick={handleGenerate} 
      disabled={isGenerating}
      className="w-full"
    >
      {isGenerating ? 'Generating Story...' : 'Generate Story Structure'}
    </Button>
  )
}