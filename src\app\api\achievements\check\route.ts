import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { authenticateUser, handleRouteError } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const body = await request.json()
    const userId = body.userId || authResult.user.id

    const supabase = await createClient()

    // Call the check_and_unlock_achievements function
    const { data, error } = await supabase
      .rpc('check_and_unlock_achievements', { p_user_id: userId })

    if (error) {
      throw error
    }

    const newlyUnlocked = data?.[0]?.newly_unlocked || []

    // If there are newly unlocked achievements, fetch their details
    let unlockedDetails = []
    if (newlyUnlocked.length > 0) {
      const { data: achievements } = await supabase
        .from('user_achievements')
        .select('*')
        .eq('user_id', userId)
        .in('achievement_id', newlyUnlocked)

      unlockedDetails = achievements || []
    }

    return NextResponse.json({ 
      newlyUnlocked,
      unlockedDetails,
      checked: true 
    })

  } catch (error) {
    return handleRouteError(error, 'Achievement Check')
  }
}