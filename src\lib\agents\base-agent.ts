import { vercelAIClient, VercelAIConfig, StreamingOptions } from '@/lib/ai/vercel-ai-client';
import { logger } from '@/lib/services/logger';
import { BookContext } from './types';
import { AI_TEMPERATURE, AI_MAX_TOKENS, AI_MODELS } from '../config/ai-settings';
import { ErrorContext } from '../services/error-handler';
import { z } from 'zod';
import { aiModelSelector, TASK_TYPES } from '../services/ai-model-selector';
import { wordCounter } from '../services/word-counter';

export abstract class BaseAgent {
  protected aiClient = vercelAIClient;
  protected context: BookContext;

  constructor(context: BookContext) {
    this.context = context;
  }

  protected async createCompletion(
    prompt: string,
    systemPrompt?: string,
    model: string = AI_MODELS.PRIMARY,
    temperature?: number,
    maxTokens?: number,
    streaming?: boolean,
    streamingOptions?: StreamingOptions
  ) {
    // Infer task type from agent name and prompt
    const taskType = aiModelSelector.inferTaskType(
      prompt,
      systemPrompt,
      this.constructor.name
    );
    
    // Select appropriate model based on subscription tier and task type
    const modelSelection = aiModelSelector.selectModel(
      this.context.subscription || null,
      model,
      taskType
    );
    
    const selectedModel = modelSelection.model;
    
    // Log if model was restricted
    if (modelSelection.isRestricted) {
      logger.info('AI model restricted by subscription tier', {
        requested: model,
        selected: selectedModel,
        tier: modelSelection.tierName,
        reason: modelSelection.reason,
        taskType
      });
    }
    
    const errorContext: ErrorContext = {
      operation: `${this.constructor.name}.createCompletion`,
      projectId: this.context.projectId,
      metadata: {
        model: selectedModel,
        requestedModel: model,
        temperature: temperature || AI_TEMPERATURE.BALANCED,
        maxTokens: maxTokens || AI_MAX_TOKENS.STANDARD,
        streaming: !!streaming,
        tierRestricted: modelSelection.isRestricted,
        taskType
      }
    };

    const aiConfig: VercelAIConfig = {
      model: selectedModel,
      temperature: temperature || AI_TEMPERATURE.BALANCED,
      maxTokens: maxTokens || AI_MAX_TOKENS.STANDARD,
      systemPrompt: systemPrompt || 'You are an expert creative writing assistant.'
    };

    if (streaming) {
      const result = await this.aiClient.streamText(prompt, aiConfig, streamingOptions, errorContext);
      // Track word usage for streaming (estimate based on tokens)
      if (result.usage) {
        const estimatedWords = wordCounter.estimateWordsFromTokens(result.usage.totalTokens);
        await this.trackWordUsage(estimatedWords, taskType);
      }
      return result;
    } else {
      const result = await this.aiClient.generateText(prompt, aiConfig, errorContext);
      // Track word usage
      if (result.text) {
        const wordCount = wordCounter.countWords(result.text);
        await this.trackWordUsage(wordCount, taskType);
      }
      return result;
    }
  }

  /**
   * Create a structured completion with schema validation
   */
  protected async createStructuredCompletion<T>(
    prompt: string,
    schema: z.ZodSchema<T>,
    systemPrompt?: string,
    model: string = AI_MODELS.PRIMARY,
    temperature?: number,
    maxTokens?: number,
    streaming?: boolean,
    streamingOptions?: StreamingOptions
  ): Promise<T> {
    // Infer task type from agent name and prompt
    const taskType = aiModelSelector.inferTaskType(
      prompt,
      systemPrompt,
      this.constructor.name
    );
    
    // Select appropriate model based on subscription tier and task type
    const modelSelection = aiModelSelector.selectModel(
      this.context.subscription || null,
      model,
      taskType
    );
    
    const selectedModel = modelSelection.model;
    
    // Log if model was restricted
    if (modelSelection.isRestricted) {
      logger.info('AI model restricted by subscription tier', {
        requested: model,
        selected: selectedModel,
        tier: modelSelection.tierName,
        reason: modelSelection.reason,
        taskType
      });
    }
    
    const errorContext: ErrorContext = {
      operation: `${this.constructor.name}.createStructuredCompletion`,
      projectId: this.context.projectId,
      metadata: {
        model: selectedModel,
        requestedModel: model,
        temperature: temperature || AI_TEMPERATURE.BALANCED,
        maxTokens: maxTokens || AI_MAX_TOKENS.STANDARD,
        streaming: !!streaming,
        hasSchema: true,
        tierRestricted: modelSelection.isRestricted,
        taskType
      }
    };

    const aiConfig: VercelAIConfig = {
      model: selectedModel,
      temperature: temperature || AI_TEMPERATURE.BALANCED,
      maxTokens: maxTokens || AI_MAX_TOKENS.STANDARD,
      systemPrompt: systemPrompt || 'You are an expert creative writing assistant. Respond with valid JSON that matches the required schema.'
    };

    if (streaming) {
      const result = await this.aiClient.streamObject(prompt, schema, aiConfig, streamingOptions, errorContext);
      // Track word usage for structured content
      if (result) {
        const wordCount = wordCounter.countStructuredContent(result as Record<string, unknown>);
        await this.trackWordUsage(wordCount, taskType);
      }
      return result;
    } else {
      const result = await this.aiClient.generateObject(prompt, schema, aiConfig, errorContext);
      // Track word usage for structured content
      if (result) {
        const wordCount = wordCounter.countStructuredContent(result as Record<string, unknown>);
        await this.trackWordUsage(wordCount, taskType);
      }
      return result;
    }
  }

  /**
   * Track word usage for the user
   */
  protected async trackWordUsage(wordCount: number, taskType: string): Promise<void> {
    if (!this.context.userId || !wordCount) {
      return;
    }
    
    await wordCounter.trackWordUsage(
      this.context.userId,
      wordCount,
      this.constructor.name,
      {
        projectId: this.context.projectId,
        taskType,
        agentType: this.constructor.name
      }
    );
  }

  protected createSystemPrompt(role: string, instructions: string): string {
    const settingsContext = this.buildSettingsContext();
    
    return `You are ${role}, an expert AI agent specializing in novel writing.

${instructions}

PROJECT SETTINGS CONTEXT:
${settingsContext}

Always maintain consistency with these project settings and previous agent outputs in the context.`;
  }




  private buildSettingsContext(): string {
    const settings = this.context.settings || this.context.projectSelections;
    
    if (!settings) {
      return 'No project settings available';
    }
    
    return `
GENRE & STYLE:
- Primary Genre: ${settings.primaryGenre}
- Subgenre: ${settings.subgenre || 'None specified'}
- Narrative Voice: ${settings.narrativeVoice}
- Tense: ${settings.tense}
- Writing Style: ${settings.writingStyle}
- Tone: ${settings.tone?.join(', ') || 'None specified'}

STORY STRUCTURE:
- Structure Type: ${settings.structureType}
- Pacing: ${settings.pacingPreference}
- Timeline Complexity: ${settings.timelineComplexity}

WORLD & CHARACTERS:
- Time Period: ${settings.timePeriod}
- World Type: ${settings.worldType}
- Character Complexity: ${settings.characterComplexity}
- Magic/Tech Level: ${settings.magicTechLevel}

THEMES:
- Major Themes: ${settings.majorThemes?.join(', ') || 'None specified'}
- Philosophical Themes: ${settings.philosophicalThemes?.join(', ') || 'None specified'}
- Social Themes: ${settings.socialThemes?.join(', ') || 'None specified'}

TECHNICAL SPECS:
- Target Word Count: ${settings.targetWordCount?.toLocaleString()}
- POV Type: ${settings.povCharacterType}
- Content Rating: ${settings.contentRating}

STORY CONCEPT:
${settings.initialConcept}
    `.trim();
  }

  abstract execute(): Promise<unknown>;
}