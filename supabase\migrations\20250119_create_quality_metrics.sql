-- Create quality_metrics table to store chapter quality analysis results
CREATE TABLE IF NOT EXISTS public.quality_metrics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  chapter_id uuid REFERENCES public.chapters(id) ON DELETE CASCADE,
  project_id uuid REFERENCES public.projects(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Overall quality scores
  overall_score numeric(5,2) CHECK (overall_score >= 0 AND overall_score <= 100),
  
  -- Fundamental quality metrics
  coherence numeric(5,2) CHECK (coherence >= 0 AND coherence <= 100),
  style numeric(5,2) CHECK (style >= 0 AND style <= 100),
  grammar numeric(5,2) CHECK (grammar >= 0 AND grammar <= 100),
  creativity numeric(5,2) CHECK (creativity >= 0 AND creativity <= 100),
  pacing numeric(5,2) CHECK (pacing >= 0 AND pacing <= 100),
  character_consistency numeric(5,2) CHECK (character_consistency >= 0 AND character_consistency <= 100),
  plot_consistency numeric(5,2) CHECK (plot_consistency >= 0 AND plot_consistency <= 100),
  emotional_impact numeric(5,2) CHECK (emotional_impact >= 0 AND emotional_impact <= 100),
  readability numeric(5,2) CHECK (readability >= 0 AND readability <= 100),
  
  -- Bestseller-specific metrics
  show_dont_tell_ratio numeric(5,2) CHECK (show_dont_tell_ratio >= 0 AND show_dont_tell_ratio <= 100),
  sensory_engagement numeric(5,2) CHECK (sensory_engagement >= 0 AND sensory_engagement <= 100),
  dialogue_authenticity numeric(5,2) CHECK (dialogue_authenticity >= 0 AND dialogue_authenticity <= 100),
  hook_strength numeric(5,2) CHECK (hook_strength >= 0 AND hook_strength <= 100),
  pageturner_quality numeric(5,2) CHECK (pageturner_quality >= 0 AND pageturner_quality <= 100),
  literary_merit numeric(5,2) CHECK (literary_merit >= 0 AND literary_merit <= 100),
  market_potential numeric(5,2) CHECK (market_potential >= 0 AND market_potential <= 100),
  memorability numeric(5,2) CHECK (memorability >= 0 AND memorability <= 100),
  
  -- Analysis results
  strengths text[] DEFAULT ARRAY[]::text[],
  weaknesses text[] DEFAULT ARRAY[]::text[],
  suggestions text[] DEFAULT ARRAY[]::text[],
  
  -- Timestamps
  analyzed_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create indexes for performance
CREATE INDEX idx_quality_metrics_chapter_id ON public.quality_metrics(chapter_id);
CREATE INDEX idx_quality_metrics_project_id ON public.quality_metrics(project_id);
CREATE INDEX idx_quality_metrics_user_id ON public.quality_metrics(user_id);
CREATE INDEX idx_quality_metrics_analyzed_at ON public.quality_metrics(analyzed_at DESC);
CREATE INDEX idx_quality_metrics_overall_score ON public.quality_metrics(overall_score DESC);

-- Create a unique constraint to ensure only one quality metric per chapter
CREATE UNIQUE INDEX idx_quality_metrics_chapter_unique ON public.quality_metrics(chapter_id);

-- Enable RLS
ALTER TABLE public.quality_metrics ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own quality metrics" ON public.quality_metrics
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own quality metrics" ON public.quality_metrics
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own quality metrics" ON public.quality_metrics
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own quality metrics" ON public.quality_metrics
  FOR DELETE USING (auth.uid() = user_id);

-- Create aggregated quality metrics table for project-level analysis
CREATE TABLE IF NOT EXISTS public.project_quality_metrics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id uuid REFERENCES public.projects(id) ON DELETE CASCADE UNIQUE,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Aggregated scores (averages across all chapters)
  avg_overall_score numeric(5,2),
  avg_coherence numeric(5,2),
  avg_style numeric(5,2),
  avg_grammar numeric(5,2),
  avg_creativity numeric(5,2),
  avg_pacing numeric(5,2),
  avg_character_consistency numeric(5,2),
  avg_plot_consistency numeric(5,2),
  avg_emotional_impact numeric(5,2),
  avg_readability numeric(5,2),
  avg_show_dont_tell_ratio numeric(5,2),
  avg_sensory_engagement numeric(5,2),
  avg_dialogue_authenticity numeric(5,2),
  avg_hook_strength numeric(5,2),
  avg_pageturner_quality numeric(5,2),
  avg_literary_merit numeric(5,2),
  avg_market_potential numeric(5,2),
  avg_memorability numeric(5,2),
  
  -- Chapter count and last analysis
  chapters_analyzed integer DEFAULT 0,
  last_analyzed_at timestamp with time zone,
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create indexes
CREATE INDEX idx_project_quality_metrics_project_id ON public.project_quality_metrics(project_id);
CREATE INDEX idx_project_quality_metrics_user_id ON public.project_quality_metrics(user_id);

-- Enable RLS
ALTER TABLE public.project_quality_metrics ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own project quality metrics" ON public.project_quality_metrics
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own project quality metrics" ON public.project_quality_metrics
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own project quality metrics" ON public.project_quality_metrics
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own project quality metrics" ON public.project_quality_metrics
  FOR DELETE USING (auth.uid() = user_id);

-- Create function to update project quality metrics
CREATE OR REPLACE FUNCTION update_project_quality_metrics()
RETURNS TRIGGER AS $$
BEGIN
  -- Calculate aggregated metrics for the project
  INSERT INTO public.project_quality_metrics (
    project_id,
    user_id,
    avg_overall_score,
    avg_coherence,
    avg_style,
    avg_grammar,
    avg_creativity,
    avg_pacing,
    avg_character_consistency,
    avg_plot_consistency,
    avg_emotional_impact,
    avg_readability,
    avg_show_dont_tell_ratio,
    avg_sensory_engagement,
    avg_dialogue_authenticity,
    avg_hook_strength,
    avg_pageturner_quality,
    avg_literary_merit,
    avg_market_potential,
    avg_memorability,
    chapters_analyzed,
    last_analyzed_at
  )
  SELECT 
    NEW.project_id,
    NEW.user_id,
    AVG(overall_score),
    AVG(coherence),
    AVG(style),
    AVG(grammar),
    AVG(creativity),
    AVG(pacing),
    AVG(character_consistency),
    AVG(plot_consistency),
    AVG(emotional_impact),
    AVG(readability),
    AVG(show_dont_tell_ratio),
    AVG(sensory_engagement),
    AVG(dialogue_authenticity),
    AVG(hook_strength),
    AVG(pageturner_quality),
    AVG(literary_merit),
    AVG(market_potential),
    AVG(memorability),
    COUNT(*),
    MAX(analyzed_at)
  FROM public.quality_metrics
  WHERE project_id = NEW.project_id
  ON CONFLICT (project_id) DO UPDATE SET
    avg_overall_score = EXCLUDED.avg_overall_score,
    avg_coherence = EXCLUDED.avg_coherence,
    avg_style = EXCLUDED.avg_style,
    avg_grammar = EXCLUDED.avg_grammar,
    avg_creativity = EXCLUDED.avg_creativity,
    avg_pacing = EXCLUDED.avg_pacing,
    avg_character_consistency = EXCLUDED.avg_character_consistency,
    avg_plot_consistency = EXCLUDED.avg_plot_consistency,
    avg_emotional_impact = EXCLUDED.avg_emotional_impact,
    avg_readability = EXCLUDED.avg_readability,
    avg_show_dont_tell_ratio = EXCLUDED.avg_show_dont_tell_ratio,
    avg_sensory_engagement = EXCLUDED.avg_sensory_engagement,
    avg_dialogue_authenticity = EXCLUDED.avg_dialogue_authenticity,
    avg_hook_strength = EXCLUDED.avg_hook_strength,
    avg_pageturner_quality = EXCLUDED.avg_pageturner_quality,
    avg_literary_merit = EXCLUDED.avg_literary_merit,
    avg_market_potential = EXCLUDED.avg_market_potential,
    avg_memorability = EXCLUDED.avg_memorability,
    chapters_analyzed = EXCLUDED.chapters_analyzed,
    last_analyzed_at = EXCLUDED.last_analyzed_at,
    updated_at = timezone('utc'::text, now());
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update project metrics when chapter metrics change
CREATE TRIGGER update_project_quality_metrics_trigger
AFTER INSERT OR UPDATE ON public.quality_metrics
FOR EACH ROW
EXECUTE FUNCTION update_project_quality_metrics();

-- Create view for easy access to latest chapter quality
CREATE VIEW public.latest_chapter_quality AS
SELECT 
  c.id as chapter_id,
  c.project_id,
  c.title as chapter_title,
  c.chapter_number,
  qm.overall_score,
  qm.coherence,
  qm.style,
  qm.grammar,
  qm.creativity,
  qm.pacing,
  qm.character_consistency,
  qm.plot_consistency,
  qm.emotional_impact,
  qm.readability,
  qm.show_dont_tell_ratio,
  qm.sensory_engagement,
  qm.dialogue_authenticity,
  qm.hook_strength,
  qm.pageturner_quality,
  qm.literary_merit,
  qm.market_potential,
  qm.memorability,
  qm.strengths,
  qm.weaknesses,
  qm.suggestions,
  qm.analyzed_at
FROM public.chapters c
LEFT JOIN public.quality_metrics qm ON c.id = qm.chapter_id
ORDER BY c.project_id, c.chapter_number;

-- Grant access to the view
GRANT SELECT ON public.latest_chapter_quality TO authenticated;