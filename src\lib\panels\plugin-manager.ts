import { PanelPlugin, PanelState, PanelLayout, PanelEvent, PanelEventHandler, DEFAULT_LAYOUTS } from './types'

export class PanelPluginManager {
  private static instance: PanelPluginManager | null = null
  private plugins: Map<string, PanelPlugin> = new Map()
  private panelStates: Map<string, PanelState> = new Map()
  private eventHandlers: Map<string, Set<PanelEventHandler>> = new Map()
  private currentLayout: PanelLayout = DEFAULT_LAYOUTS.writing
  private layouts: Map<string, PanelLayout> = new Map()

  private constructor() {
    // Initialize with default layouts
    Object.values(DEFAULT_LAYOUTS).forEach(layout => {
      this.layouts.set(layout.id, layout)
    })
    
    // Load saved state from localStorage
    this.loadPersistedState()
  }

  static getInstance(): PanelPluginManager {
    if (!PanelPluginManager.instance) {
      PanelPluginManager.instance = new PanelPluginManager()
    }
    return PanelPluginManager.instance
  }

  // Plugin registration
  registerPlugin(plugin: PanelPlugin): void {
    if (this.plugins.has(plugin.id)) {
      console.warn(`Plugin with id "${plugin.id}" is already registered`)
      return
    }

    this.plugins.set(plugin.id, plugin)
    
    // Initialize panel state if not exists
    if (!this.panelStates.has(plugin.id)) {
      this.panelStates.set(plugin.id, {
        id: plugin.id,
        visible: false,
        pinned: false,
        position: plugin.defaultPosition,
        width: plugin.defaultWidth,
        height: plugin.defaultHeight
      })
    }

    this.emitEvent({
      source: 'plugin-manager',
      type: 'plugin:registered',
      data: { pluginId: plugin.id },
      timestamp: Date.now()
    })
  }

  unregisterPlugin(id: string): void {
    if (!this.plugins.has(id)) {
      console.warn(`Plugin with id "${id}" is not registered`)
      return
    }

    this.plugins.delete(id)
    this.panelStates.delete(id)

    this.emitEvent({
      source: 'plugin-manager',
      type: 'plugin:unregistered',
      data: { pluginId: id },
      timestamp: Date.now()
    })
  }

  // Panel state management
  togglePanel(id: string): void {
    const state = this.panelStates.get(id)
    if (state) {
      state.visible = !state.visible
      this.savePersistentState()
      
      this.emitEvent({
        source: 'plugin-manager',
        type: state.visible ? 'panel:shown' : 'panel:hidden',
        data: { panelId: id },
        timestamp: Date.now()
      })
    }
  }

  updatePanelState(id: string, updates: Partial<PanelState>): void {
    const state = this.panelStates.get(id)
    if (state) {
      Object.assign(state, updates)
      this.savePersistentState()
      
      this.emitEvent({
        source: 'plugin-manager',
        type: 'panel:updated',
        data: { panelId: id, updates },
        timestamp: Date.now()
      })
    }
  }

  getPanelState(id: string): PanelState | undefined {
    return this.panelStates.get(id)
  }

  getVisiblePanels(): PanelPlugin[] {
    return Array.from(this.plugins.values()).filter(plugin => {
      const state = this.panelStates.get(plugin.id)
      return state?.visible
    })
  }

  // Layout management
  saveLayout(name: string, description?: string): void {
    const layout: PanelLayout = {
      id: `custom-${Date.now()}`,
      name,
      description: description || '',
      panels: Array.from(this.panelStates.values()),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    this.layouts.set(layout.id, layout)
    this.savePersistentState()

    this.emitEvent({
      source: 'plugin-manager',
      type: 'layout:saved',
      data: { layoutId: layout.id, name },
      timestamp: Date.now()
    })
  }

  loadLayout(id: string): void {
    const layout = this.layouts.get(id)
    if (!layout) {
      console.warn(`Layout with id "${id}" not found`)
      return
    }

    this.currentLayout = layout
    
    // Reset all panel states
    this.panelStates.clear()
    
    // Apply layout panel states
    layout.panels.forEach(panelState => {
      if (this.plugins.has(panelState.id)) {
        this.panelStates.set(panelState.id, { ...panelState })
      }
    })

    this.savePersistentState()

    this.emitEvent({
      source: 'plugin-manager',
      type: 'layout:loaded',
      data: { layoutId: id },
      timestamp: Date.now()
    })
  }

  resetToDefaultLayout(): void {
    this.loadLayout('writing')
  }

  getLayouts(): PanelLayout[] {
    return Array.from(this.layouts.values())
  }

  getCurrentLayout(): PanelLayout {
    return this.currentLayout
  }

  // Event system
  on(event: string, handler: PanelEventHandler): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set())
    }
    this.eventHandlers.get(event)!.add(handler)
  }

  off(event: string, handler: PanelEventHandler): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.delete(handler)
    }
  }

  emitEvent(event: PanelEvent): void {
    const handlers = this.eventHandlers.get(event.type)
    if (handlers) {
      handlers.forEach(handler => handler(event))
    }

    // Also emit to wildcard handlers
    const wildcardHandlers = this.eventHandlers.get('*')
    if (wildcardHandlers) {
      wildcardHandlers.forEach(handler => handler(event))
    }
  }

  // Persistence
  private savePersistentState(): void {
    const state = {
      currentLayoutId: this.currentLayout.id,
      panelStates: Array.from(this.panelStates.entries()),
      customLayouts: Array.from(this.layouts.entries())
        .filter(([id]) => !Object.keys(DEFAULT_LAYOUTS).includes(id))
    }

    localStorage.setItem('bookscribe-panel-state', JSON.stringify(state))
  }

  private loadPersistedState(): void {
    try {
      const saved = localStorage.getItem('bookscribe-panel-state')
      if (saved) {
        const state = JSON.parse(saved)
        
        // Restore panel states
        if (state.panelStates) {
          this.panelStates = new Map(state.panelStates)
        }

        // Restore custom layouts
        if (state.customLayouts) {
          state.customLayouts.forEach(([id, layout]: [string, PanelLayout]) => {
            this.layouts.set(id, layout)
          })
        }

        // Restore current layout
        if (state.currentLayoutId && this.layouts.has(state.currentLayoutId)) {
          this.currentLayout = this.layouts.get(state.currentLayoutId)!
        }
      }
    } catch (error) {
      console.error('Failed to load panel state:', error)
    }
  }

  // Utility methods
  getPlugin(id: string): PanelPlugin | undefined {
    return this.plugins.get(id)
  }

  getAllPlugins(): PanelPlugin[] {
    return Array.from(this.plugins.values())
  }

  getPluginsByCategory(category: string): PanelPlugin[] {
    return this.getAllPlugins().filter(plugin => plugin.category === category)
  }
}