-- Migration: Add word usage tracking
-- Description: Adds ai_words_used column to usage_tracking table for word-based subscription tracking

-- Add ai_words_used column to usage_tracking table
ALTER TABLE usage_tracking 
ADD COLUMN IF NOT EXISTS ai_words_used INTEGER DEFAULT 0;

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_period_words 
ON usage_tracking(user_id, period_start, ai_words_used);

-- Update existing records to have default value
UPDATE usage_tracking 
SET ai_words_used = 0 
WHERE ai_words_used IS NULL;

-- Add constraint to ensure non-negative values
ALTER TABLE usage_tracking 
ADD CONSTRAINT check_ai_words_used_non_negative 
CHECK (ai_words_used >= 0);

-- Create a function to get remaining words for a user
CREATE OR REPLACE FUNCTION get_user_remaining_words(p_user_id UUID)
RETURNS TABLE (
  used_words INTEGER,
  monthly_limit INTEGER,
  remaining_words INTEGER,
  tier_name TEXT
) AS $$
DECLARE
  v_period_start TEXT;
  v_used_words INTEGER;
  v_tier_id TEXT;
  v_monthly_limit INTEGER;
BEGIN
  -- Get current period
  v_period_start := TO_CHAR(CURRENT_DATE, 'YYYY-MM');
  
  -- Get used words for current period
  SELECT COALESCE(ut.ai_words_used, 0)
  INTO v_used_words
  FROM usage_tracking ut
  WHERE ut.user_id = p_user_id
  AND ut.period_start = v_period_start;
  
  -- If no record exists, default to 0
  IF v_used_words IS NULL THEN
    v_used_words := 0;
  END IF;
  
  -- Get user's subscription tier
  SELECT COALESCE(us.tier_id, 'starter')
  INTO v_tier_id
  FROM user_subscriptions us
  WHERE us.user_id = p_user_id
  AND us.status = 'active'
  ORDER BY us.created_at DESC
  LIMIT 1;
  
  -- If no active subscription, default to starter
  IF v_tier_id IS NULL THEN
    v_tier_id := 'starter';
  END IF;
  
  -- Get monthly limit based on tier
  v_monthly_limit := CASE v_tier_id
    WHEN 'starter' THEN 10000
    WHEN 'writer' THEN 50000
    WHEN 'author' THEN 150000
    WHEN 'professional' THEN 300000
    WHEN 'studio' THEN 600000
    ELSE 10000 -- Default to starter
  END;
  
  RETURN QUERY
  SELECT 
    v_used_words,
    v_monthly_limit,
    GREATEST(0, v_monthly_limit - v_used_words),
    v_tier_id;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to prevent exceeding word limits
CREATE OR REPLACE FUNCTION check_word_usage_limit()
RETURNS TRIGGER AS $$
DECLARE
  v_remaining RECORD;
BEGIN
  -- Get remaining words for user
  SELECT * INTO v_remaining
  FROM get_user_remaining_words(NEW.user_id);
  
  -- Check if update would exceed limit
  IF NEW.ai_words_used > OLD.ai_words_used AND 
     NEW.ai_words_used > v_remaining.monthly_limit THEN
    RAISE EXCEPTION 'Word usage limit exceeded. Used: %, Limit: %', 
      NEW.ai_words_used, v_remaining.monthly_limit;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS check_word_usage_limit_trigger ON usage_tracking;
CREATE TRIGGER check_word_usage_limit_trigger
BEFORE UPDATE OF ai_words_used ON usage_tracking
FOR EACH ROW
EXECUTE FUNCTION check_word_usage_limit();

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_user_remaining_words(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION check_word_usage_limit() TO authenticated;