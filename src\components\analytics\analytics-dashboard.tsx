"use client";

import { useState } from "react";
import { logger } from '@/lib/services/logger';

import { DateRange } from "react-day-picker";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Download, FileText } from "lucide-react";
import { TimeRangeSelector } from "./time-range-selector";
import { AnalyticsCard } from "./components/analytics-card";
import { ProgressChart } from "./components/progress-chart";
import { HeatmapCalendar } from "./components/heatmap-calendar";
import { GoalTracker } from "./components/goal-tracker";
import { QualityMetrics } from "./components/quality-metrics";
import { QualitySection } from "./sections/quality-section";
import { useAnalyticsData } from "@/hooks/use-analytics-data";
import { EmptyAnalytics } from "@/components/empty-states/empty-analytics";
import { AchievementDisplay } from "@/components/achievements/achievement-display";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Project {
  id: string;
  title: string;
  primary_genre: string;
}

interface AnalyticsDashboardProps {
  userId: string;
  projects: Project[];
}

export function AnalyticsDashboard({ userId, projects }: AnalyticsDashboardProps) {
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [selectedProject, setSelectedProject] = useState<string>("all");
  const [activeTab, setActiveTab] = useState("overview");
  
  const { data, loading, error } = useAnalyticsData({
    userId,
    projectId: selectedProject === "all" ? undefined : selectedProject,
    dateRange,
  });

  const handleExport = async (format: "pdf" | "csv") => {
    try {
      const response = await fetch("/api/analytics/export", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          projectId: selectedProject === "all" ? undefined : selectedProject,
          dateRange,
          format,
          scope: "current",
          data: data,
        }),
      });

      if (!response.ok) {
        throw new Error("Export failed");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `analytics-report-${new Date().toISOString().split("T")[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      logger.error("Export error:", error);
      // In production, show a toast notification
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-muted-foreground">Loading analytics...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-destructive">Error loading analytics</div>
      </div>
    );
  }

  // Show empty state if no projects or no data
  if (!projects.length || !data || (typeof data !== 'object')) {
    return <EmptyAnalytics hasProjects={projects.length > 0} userId={userId} />;
  }

  // Additional safety check for data structure
  const hasValidData = data && (
    (typeof data.totalSessions === 'number' && data.totalSessions > 0) ||
    (typeof data.totalWords === 'number' && data.totalWords > 0) ||
    (Array.isArray(data.dailyWordCount) && data.dailyWordCount.length > 0)
  );

  if (!hasValidData) {
    return <EmptyAnalytics hasProjects={projects.length > 0} userId={userId} />;
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <TimeRangeSelector onRangeChange={setDateRange} />
          
          <Select value={selectedProject} onValueChange={setSelectedProject}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="All projects" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All projects</SelectItem>
              {projects.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  {project.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport("csv")}
          >
            <FileText className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport("pdf")}
          >
            <Download className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      {/* Main Analytics */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="quality">Quality</TabsTrigger>
          <TabsTrigger value="goals">Goals</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <AnalyticsCard
              title="Total Words Written"
              value={data?.totalWords || 0}
              trend={data?.wordsTrend ? {
                value: Math.abs(data.wordsTrend),
                direction: data.wordsTrend > 0 ? 'up' : data.wordsTrend < 0 ? 'down' : 'neutral'
              } : undefined}
              subtitle="vs last period"
            />
            <AnalyticsCard
              title="Writing Streak"
              value={`${data?.currentStreak || 0} days`}
              trend={data?.streakTrend ? {
                value: Math.abs(data.streakTrend),
                direction: data.streakTrend > 0 ? 'up' : data.streakTrend < 0 ? 'down' : 'neutral'
              } : undefined}
            />
            <AnalyticsCard
              title="Average Daily Words"
              value={data?.avgDailyWords || 0}
              trend={data?.avgWordsTrend ? {
                value: Math.abs(data.avgWordsTrend),
                direction: data.avgWordsTrend > 0 ? 'up' : data.avgWordsTrend < 0 ? 'down' : 'neutral'
              } : undefined}
            />
            <AnalyticsCard
              title="Active Projects"
              value={data?.activeProjects || 0}
              trend={data?.projectsTrend ? {
                value: Math.abs(data.projectsTrend),
                direction: data.projectsTrend > 0 ? 'up' : data.projectsTrend < 0 ? 'down' : 'neutral'
              } : undefined}
            />
          </div>

          {/* Daily Word Count Chart */}
          <div className="grid gap-6 lg:grid-cols-2">
            <ProgressChart
              title="Daily Word Count"
              data={data?.dailyWordCount || []}
              type="area"
              color="hsl(var(--primary))"
            />
            
            <HeatmapCalendar
              title="Writing Activity"
              data={data?.heatmapData || []}
            />
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          {/* Writing Patterns */}
          <div className="grid gap-6 lg:grid-cols-2">
            <ProgressChart
              title="Writing by Hour of Day"
              data={data?.hourlyPattern || []}
              type="bar"
              xAxisLabel="Hour"
              yAxisLabel="Words Written"
            />
            <ProgressChart
              title="Writing by Day of Week"
              data={data?.weeklyPattern || []}
              type="bar"
              xAxisLabel="Day"
              yAxisLabel="Words Written"
            />
          </div>

          {/* Session Statistics */}
          <div className="grid gap-4 md:grid-cols-3">
            <AnalyticsCard
              title="Average Session Duration"
              value={`${data?.avgSessionDuration || 0}h`}
            />
            <AnalyticsCard
              title="Words per Session"
              value={data?.avgWordsPerSession || 0}
            />
            <AnalyticsCard
              title="Total Sessions"
              value={data?.totalSessions || 0}
            />
          </div>
        </TabsContent>

        <TabsContent value="projects" className="space-y-6">
          {/* Project Progress */}
          <ProgressChart
            title="Project Progress Over Time"
            data={data?.projectProgress || []}
            lines={data?.projectLines}
            showLegend
            height={400}
          />

          {/* Project Metrics */}
          <div className="grid gap-6 lg:grid-cols-2">
            <ProgressChart
              title="Words by Project"
              data={data?.wordsByProject || []}
              type="bar"
            />
            <QualityMetrics
              metrics={{
                readability: data?.projectQuality?.find(m => m.metric === 'Plot Structure')?.score || 0,
                consistency: data?.projectQuality?.find(m => m.metric === 'Character Development')?.score || 0,
                pacing: data?.projectQuality?.find(m => m.metric === 'Pacing')?.score || 0,
                engagement: data?.projectQuality?.find(m => m.metric === 'Plot Structure')?.score || 0,
                dialogue: data?.projectQuality?.find(m => m.metric === 'Dialogue Quality')?.score || 0,
                description: data?.projectQuality?.find(m => m.metric === 'World Building')?.score || 0
              }}
              overallScore={data?.projectQuality?.length > 0 ? Math.round(data.projectQuality.reduce((sum, m) => sum + m.score, 0) / data.projectQuality.length) : 0}
              showRadar={false}
            />
          </div>
        </TabsContent>

        <TabsContent value="quality" className="space-y-6">
          <QualitySection 
            data={{
              quality: {
                overallScore: data?.qualityDimensions?.length > 0 
                  ? Math.round(data.qualityDimensions.reduce((sum, m) => sum + m.score, 0) / data.qualityDimensions.length)
                  : 0,
                metrics: {
                  readability: data?.qualityDimensions?.find(m => m.metric === 'Readability')?.score || 0,
                  consistency: data?.qualityDimensions?.find(m => m.metric === 'Consistency')?.score || 0,
                  pacing: data?.qualityDimensions?.find(m => m.metric === 'Pacing')?.score || 0,
                  engagement: data?.qualityDimensions?.find(m => m.metric === 'Engagement')?.score || 0,
                  dialogue: data?.qualityDimensions?.find(m => m.metric === 'Dialogue')?.score || 0,
                  description: data?.qualityDimensions?.find(m => m.metric === 'Description')?.score || 0,
                },
                trends: data?.qualityTrends?.map(trend => ({
                  date: trend.date,
                  score: Math.round((trend.readability + trend.consistency + trend.pacing) / 3) // Calculate overall score
                })) || [],
                improvementAreas: data?.improvementAreas || []
              }
            }}
            isLoading={loading}
            selectedProject={selectedProject}
            projects={projects}
          />
        </TabsContent>

        <TabsContent value="goals" className="space-y-6">
          {/* Active Goals */}
          <div className="grid gap-6 lg:grid-cols-2">
            <div>
              <h3 className="text-lg font-semibold mb-4">Active Goals</h3>
              <GoalTracker
                goals={data?.activeGoals || []}
              />
            </div>
            
            <AchievementDisplay
              userId={userId}
              showUnlocked={true}
              showProgress={true}
              maxDisplay={5}
            />
          </div>

          {/* Goal Progress */}
          <ProgressChart
            title="Goal Progress Over Time"
            data={data?.goalProgress || []}
            type="line"
          />
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          {/* AI Insights */}
          <div className="rounded-lg border bg-card p-6">
            <h3 className="text-lg font-semibold mb-4">AI-Generated Insights</h3>
            <div className="space-y-4">
              {data?.insights?.map((insight, index) => (
                <div key={index} className="space-y-2">
                  <h4 className="font-medium">{insight.title}</h4>
                  <p className="text-sm text-muted-foreground">{insight.content}</p>
                  {insight.recommendation && (
                    <div className="rounded-md bg-primary/5 p-3">
                      <p className="text-sm font-medium">Recommendation:</p>
                      <p className="text-sm">{insight.recommendation}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Personalized Tips */}
          <div className="grid gap-4 md:grid-cols-2">
            {data?.tips?.map((tip, index) => (
              <div key={index} className="rounded-lg border bg-card p-4">
                <h4 className="font-medium mb-2">{tip.title}</h4>
                <p className="text-sm text-muted-foreground">{tip.content}</p>
              </div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}