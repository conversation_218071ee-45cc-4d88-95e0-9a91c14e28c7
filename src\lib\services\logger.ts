type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  context?: Record<string, unknown>;
  error?: Error;
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development';
  private logBuffer: LogEntry[] = [];
  private maxBufferSize = 100;

  private log(level: LogLevel, message: string, context?: Record<string, unknown>, error?: Error) {
    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      context,
      error,
    };

    // Store in buffer for potential error reporting
    this.logBuffer.push(entry);
    if (this.logBuffer.length > this.maxBufferSize) {
      this.logBuffer.shift();
    }

    // Only log to console in development
    if (this.isDevelopment) {
      const logMethod = level === 'error' ? console.error : 
                       level === 'warn' ? console.warn : 
                       console.log;
      
      const prefix = `[${level.toUpperCase()}] ${entry.timestamp.toISOString()}`;
      
      if (error) {
        logMethod(prefix, message, context || {}, error);
      } else if (context) {
        logMethod(prefix, message, context);
      } else {
        logMethod(prefix, message);
      }
    }

    // In production, send to error tracking service
    if (!this.isDevelopment && (level === 'error' || level === 'warn')) {
      this.sendToErrorTracking(entry);
    }
  }

  debug(message: string, context?: Record<string, unknown>) {
    this.log('debug', message, context);
  }

  info(message: string, context?: Record<string, unknown>) {
    this.log('info', message, context);
  }

  warn(message: string, context?: Record<string, unknown>) {
    this.log('warn', message, context);
  }

  error(message: string, error?: Error, context?: Record<string, unknown>) {
    this.log('error', message, context, error);
  }

  getRecentLogs(count = 50): LogEntry[] {
    return this.logBuffer.slice(-count);
  }

  clearLogs() {
    this.logBuffer = [];
  }

  private sendToErrorTracking(entry: LogEntry) {
    // Store critical errors locally until external tracking service is configured
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        const errors = JSON.parse(localStorage.getItem('app_errors') || '[]');
        errors.push({
          ...entry,
          timestamp: entry.timestamp.toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href,
        });
        // Keep only last 10 errors
        if (errors.length > 10) {
          errors.splice(0, errors.length - 10);
        }
        localStorage.setItem('app_errors', JSON.stringify(errors));
        
        // If Sentry is configured in the future, add integration here:
        // if (window.Sentry && process.env.NEXT_PUBLIC_SENTRY_DSN) {
        //   window.Sentry.captureException(entry.error || new Error(entry.message), {
        //     level: entry.level,
        //     extra: entry.context
        //   });
        // }
      } catch (e) {
        // Silently fail if localStorage is full or unavailable
      }
    }
  }
}

// Export singleton instance
export const logger = new Logger();

// Export type for use in other files
export type { LogLevel, LogEntry };