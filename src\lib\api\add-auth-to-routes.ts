// List of routes that need authentication
export const routesNeedingAuth = [
  // COMPLETED:
  // '/api/timeline/validate/route.ts',
  // '/api/timeline/autofix/route.ts',
  // '/api/relationships/analyze/route.ts',
  // '/api/relationships/graph/route.ts',
  // '/api/search/semantic/route.ts',
  // '/api/search/theme/route.ts',
  // '/api/search/emotion/route.ts',
  // '/api/search/character-moments/route.ts',
  // '/api/search/related/route.ts',
  // '/api/analytics/selections/route.ts',
  // '/api/analytics/selections/success-patterns/route.ts',
  
  // REMAINING:
  '/api/content-analysis/route.ts',
  '/api/analysis/content/route.ts',
  '/api/profiles/[id]/clone/route.ts',
  '/api/profiles/[id]/favorite/route.ts',
  '/api/projects/[id]/voice-profile/route.ts',
  '/api/references/[id]/route.ts',
  '/api/series/[id]/universe/route.ts',
  '/api/voice-profiles/[id]/route.ts',
  '/api/voice-profiles/[id]/train-from-content/route.ts',
  '/api/agents/edit/route.ts',
  '/api/achievements/stats/route.ts'
];

// Template for adding authentication
export const authTemplate = {
  import: `import { authenticateUser, authenticateUserForProject } from '@/lib/api/auth-helpers';`,
  
  simpleAuth: `  // Check authentication
  const { user, error: authError } = await authenticateUser();
  if (authError) return authError;`,
  
  projectAuth: `  // Check authentication and project access
  const { user, error: authError } = await authenticateUserForProject(projectId);
  if (authError) return authError;`,
  
  paramsProjectAuth: `  const { id: projectId } = await params;
  
  // Check authentication and project access
  const { user, error: authError } = await authenticateUserForProject(projectId);
  if (authError) return authError;`
};