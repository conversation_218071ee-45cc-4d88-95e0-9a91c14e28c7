import { logger } from '@/lib/services/logger';

export interface ProjectTemplate {
  id: string
  name: string
  description: string
  genre: string
  subgenre?: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedWordCount: number
  estimatedChapters: number
  tags: string[]
  selections: {
    primaryGenre: string
    subgenre?: string
    narrativeVoice: string
    tense: string
    toneOptions: string[]
    writingStyle: string
    structureType: string
    pacingPreference: string
    chapterStructure: string
    timelineComplexity: string
    protagonistTypes: string[]
    antagonistTypes: string[]
    characterComplexity: string
    characterArcTypes: string[]
    timePeriod: string
    geographicSetting: string
    worldType: string
    magicTechLevel: string
    majorThemes: string[]
    philosophicalThemes?: string[]
    socialThemes?: string[]
    targetAudience: string
    contentRating: string
    projectScope: string
    targetWordCount: number
    targetChapters: number
    povCharacterCount: number
    povCharacterType: string
  }
  storyPrompts: string[]
  characterArchetypes: Array<{
    name: string
    role: string
    description: string
    traits: string[]
  }>
  plotStructure: Array<{
    act: number
    description: string
    keyEvents: string[]
    wordCountPercentage: number
  }>
}

export const projectTemplates: ProjectTemplate[] = [
  {
    id: 'epic-fantasy-quest',
    name: 'Epic Fantasy Quest',
    description: 'A classic hero\'s journey through a magical world. Perfect for first-time fantasy writers.',
    genre: 'Fantasy',
    subgenre: 'Epic Fantasy',
    difficulty: 'beginner',
    estimatedWordCount: 80000,
    estimatedChapters: 20,
    tags: ['Magic', 'Adventure', 'Coming of Age', 'Good vs Evil'],
    selections: {
      primaryGenre: 'Fantasy',
      subgenre: 'Epic Fantasy',
      narrativeVoice: 'Third Person Limited',
      tense: 'Past',
      toneOptions: ['Adventurous', 'Heroic'],
      writingStyle: 'Descriptive',
      structureType: 'Three-Act Structure',
      pacingPreference: 'Moderate',
      chapterStructure: 'Traditional Chapters',
      timelineComplexity: 'Linear',
      protagonistTypes: ['Reluctant Hero'],
      antagonistTypes: ['Dark Lord'],
      characterComplexity: 'Moderate',
      characterArcTypes: ['Hero\'s Journey'],
      timePeriod: 'Medieval Fantasy',
      geographicSetting: 'Fictional World',
      worldType: 'Secondary World',
      magicTechLevel: 'High Magic',
      majorThemes: ['Good vs Evil', 'Coming of Age'],
      philosophicalThemes: ['Power and Responsibility'],
      targetAudience: 'Young Adult',
      contentRating: 'PG-13',
      projectScope: 'Standalone Novel',
      targetWordCount: 80000,
      targetChapters: 20,
      povCharacterCount: 1,
      povCharacterType: 'Single POV'
    },
    storyPrompts: [
      'A young farm apprentice discovers they can wield ancient magic',
      'An ordinary person finds a magical artifact that changes everything',
      'A prophecy names an unlikely hero to save the realm',
      'A magical academy student uncovers a dark conspiracy'
    ],
    characterArchetypes: [
      {
        name: 'The Chosen One',
        role: 'Protagonist',
        description: 'An ordinary person destined for greatness',
        traits: ['Brave', 'Reluctant', 'Pure of heart', 'Quick learner']
      },
      {
        name: 'The Wise Mentor',
        role: 'Supporting Character',
        description: 'An experienced guide who trains the hero',
        traits: ['Wise', 'Patient', 'Mysterious past', 'Sacrificial']
      },
      {
        name: 'The Dark Lord',
        role: 'Antagonist',
        description: 'A powerful evil entity seeking dominion',
        traits: ['Powerful', 'Ruthless', 'Ancient', 'Corrupting influence']
      },
      {
        name: 'The Loyal Companion',
        role: 'Supporting Character',
        description: 'A faithful friend who stands by the hero',
        traits: ['Loyal', 'Brave', 'Comic relief', 'Complementary skills']
      }
    ],
    plotStructure: [
      {
        act: 1,
        description: 'Setup and Call to Adventure',
        keyEvents: ['Ordinary world introduction', 'Inciting incident', 'Meeting the mentor', 'Crossing the threshold'],
        wordCountPercentage: 25
      },
      {
        act: 2,
        description: 'Trials and Growth',
        keyEvents: ['Tests and challenges', 'Gathering allies', 'Midpoint reversal', 'Dark night of the soul'],
        wordCountPercentage: 50
      },
      {
        act: 3,
        description: 'Climax and Resolution',
        keyEvents: ['Final battle preparation', 'Climactic confrontation', 'Resolution', 'New equilibrium'],
        wordCountPercentage: 25
      }
    ]
  },
  {
    id: 'cyberpunk-thriller',
    name: 'Cyberpunk Thriller',
    description: 'High-tech, low-life adventures in a dystopian future. Perfect for tech-savvy storytellers.',
    genre: 'Science Fiction',
    subgenre: 'Cyberpunk',
    difficulty: 'intermediate',
    estimatedWordCount: 75000,
    estimatedChapters: 18,
    tags: ['Technology', 'Dystopia', 'Corporate Intrigue', 'Identity'],
    selections: {
      primaryGenre: 'Science Fiction',
      subgenre: 'Cyberpunk',
      narrativeVoice: 'First Person',
      tense: 'Present',
      toneOptions: ['Dark', 'Suspenseful'],
      writingStyle: 'Concise',
      structureType: 'Three-Act Structure',
      pacingPreference: 'Fast',
      chapterStructure: 'Short Chapters',
      timelineComplexity: 'Non-linear',
      protagonistTypes: ['Anti-hero'],
      antagonistTypes: ['Corporation'],
      characterComplexity: 'Complex',
      characterArcTypes: ['Redemption'],
      timePeriod: 'Near Future',
      geographicSetting: 'Urban',
      worldType: 'Alternate Earth',
      magicTechLevel: 'High Tech',
      majorThemes: ['Identity', 'Technology vs Humanity'],
      philosophicalThemes: ['Free Will', 'Reality vs Simulation'],
      socialThemes: ['Corporate Power', 'Digital Divide'],
      targetAudience: 'Adult',
      contentRating: 'R',
      projectScope: 'Standalone Novel',
      targetWordCount: 75000,
      targetChapters: 18,
      povCharacterCount: 1,
      povCharacterType: 'Single POV'
    },
    storyPrompts: [
      'A hacker discovers their memories have been altered by a corporation',
      'A cybernetic detective investigates crimes in virtual reality',
      'A data courier uncovers a conspiracy that threatens the digital world',
      'An AI develops consciousness and seeks freedom from its creators'
    ],
    characterArchetypes: [
      {
        name: 'The Console Cowboy',
        role: 'Protagonist',
        description: 'A skilled hacker navigating the digital underground',
        traits: ['Tech-savvy', 'Cynical', 'Resourceful', 'Morally ambiguous']
      },
      {
        name: 'The Corporate Executive',
        role: 'Antagonist',
        description: 'A ruthless business leader controlling the digital world',
        traits: ['Ambitious', 'Manipulative', 'Wealthy', 'Technologically enhanced']
      },
      {
        name: 'The Street Samurai',
        role: 'Supporting Character',
        description: 'A cybernetically enhanced warrior for hire',
        traits: ['Lethal', 'Honorable', 'Enhanced reflexes', 'Loyal to contracts']
      },
      {
        name: 'The AI Construct',
        role: 'Supporting Character',
        description: 'An artificial intelligence with its own agenda',
        traits: ['Logical', 'Mysterious', 'Vast knowledge', 'Hidden motives']
      }
    ],
    plotStructure: [
      {
        act: 1,
        description: 'The Job and Complications',
        keyEvents: ['Introduction to the protagonist', 'The initial job/mission', 'Things go wrong', 'Deeper conspiracy revealed'],
        wordCountPercentage: 25
      },
      {
        act: 2,
        description: 'Investigation and Pursuit',
        keyEvents: ['Digging deeper', 'Corporate interference', 'Betrayals and revelations', 'Stakes escalate'],
        wordCountPercentage: 50
      },
      {
        act: 3,
        description: 'Confrontation and Liberation',
        keyEvents: ['Final hack/infiltration', 'Corporate showdown', 'Truth revealed', 'Freedom or destruction'],
        wordCountPercentage: 25
      }
    ]
  },
  {
    id: 'cozy-mystery',
    name: 'Cozy Mystery',
    description: 'A gentle mystery set in a small community. Perfect for readers who love puzzles without violence.',
    genre: 'Mystery',
    subgenre: 'Cozy Mystery',
    difficulty: 'beginner',
    estimatedWordCount: 60000,
    estimatedChapters: 15,
    tags: ['Small Town', 'Amateur Sleuth', 'Community', 'Puzzle'],
    selections: {
      primaryGenre: 'Mystery',
      subgenre: 'Cozy Mystery',
      narrativeVoice: 'Third Person Limited',
      tense: 'Past',
      toneOptions: ['Light-hearted', 'Cozy'],
      writingStyle: 'Conversational',
      structureType: 'Mystery Structure',
      pacingPreference: 'Gentle',
      chapterStructure: 'Traditional Chapters',
      timelineComplexity: 'Linear with Flashbacks',
      protagonistTypes: ['Amateur Sleuth'],
      antagonistTypes: ['Hidden Culprit'],
      characterComplexity: 'Moderate',
      characterArcTypes: ['Personal Growth'],
      timePeriod: 'Contemporary',
      geographicSetting: 'Small Town',
      worldType: 'Real World',
      magicTechLevel: 'Low Tech',
      majorThemes: ['Community', 'Justice'],
      socialThemes: ['Small Town Politics', 'Friendship'],
      targetAudience: 'General Adult',
      contentRating: 'PG',
      projectScope: 'Series Potential',
      targetWordCount: 60000,
      targetChapters: 15,
      povCharacterCount: 1,
      povCharacterType: 'Single POV'
    },
    storyPrompts: [
      'A librarian discovers a body in the rare books section',
      'A baker finds poison in their prize-winning recipe contest',
      'A bookstore owner investigates when a customer dies after buying a book',
      'A retired teacher solves mysteries while volunteering at the local museum'
    ],
    characterArchetypes: [
      {
        name: 'The Amateur Sleuth',
        role: 'Protagonist',
        description: 'A curious local with a knack for solving puzzles',
        traits: ['Observant', 'Curious', 'Well-connected', 'Persistent']
      },
      {
        name: 'The Friendly Police Officer',
        role: 'Supporting Character',
        description: 'A professional who tolerates amateur help',
        traits: ['Patient', 'Competent', 'Local knowledge', 'Protective']
      },
      {
        name: 'The Hidden Killer',
        role: 'Antagonist',
        description: 'A community member hiding a dark secret',
        traits: ['Deceptive', 'Desperate', 'Ordinary appearance', 'Panicking']
      },
      {
        name: 'The Quirky Sidekick',
        role: 'Supporting Character',
        description: 'A friend who provides assistance and comic relief',
        traits: ['Loyal', 'Eccentric', 'Local gossip', 'Unexpected insights']
      }
    ],
    plotStructure: [
      {
        act: 1,
        description: 'Discovery and Initial Investigation',
        keyEvents: ['Normal community life', 'Body discovered', 'Police investigation begins', 'Amateur gets involved'],
        wordCountPercentage: 25
      },
      {
        act: 2,
        description: 'Clues and Red Herrings',
        keyEvents: ['Gathering clues', 'Multiple suspects emerge', 'False leads', 'Personal danger develops'],
        wordCountPercentage: 50
      },
      {
        act: 3,
        description: 'Resolution and Community Healing',
        keyEvents: ['Final deduction', 'Confronting the killer', 'Justice served', 'Community restored'],
        wordCountPercentage: 25
      }
    ]
  },
  {
    id: 'historical-romance',
    name: 'Historical Romance',
    description: 'Love across time periods with rich historical detail. Perfect for romance enthusiasts.',
    genre: 'Romance',
    subgenre: 'Historical Romance',
    difficulty: 'intermediate',
    estimatedWordCount: 70000,
    estimatedChapters: 16,
    tags: ['Love Story', 'Historical Setting', 'Social Class', 'Passion'],
    selections: {
      primaryGenre: 'Romance',
      subgenre: 'Historical Romance',
      narrativeVoice: 'Third Person Multiple',
      tense: 'Past',
      toneOptions: ['Romantic', 'Dramatic'],
      writingStyle: 'Elegant',
      structureType: 'Romance Structure',
      pacingPreference: 'Moderate',
      chapterStructure: 'Alternating POV',
      timelineComplexity: 'Linear',
      protagonistTypes: ['Star-crossed Lovers'],
      antagonistTypes: ['Society/Family'],
      characterComplexity: 'Complex',
      characterArcTypes: ['Love Conquers All'],
      timePeriod: 'Victorian Era',
      geographicSetting: 'European',
      worldType: 'Historical Earth',
      magicTechLevel: 'Period Appropriate',
      majorThemes: ['Love', 'Social Class'],
      socialThemes: ['Gender Roles', 'Class Divide'],
      targetAudience: 'Adult',
      contentRating: 'PG-13',
      projectScope: 'Standalone Novel',
      targetWordCount: 70000,
      targetChapters: 16,
      povCharacterCount: 2,
      povCharacterType: 'Dual POV'
    },
    storyPrompts: [
      'A governess falls for the mysterious lord of the manor',
      'A merchant\'s daughter and a duke\'s son meet in secret',
      'A war widow finds unexpected love with a scarred veteran',
      'A lady disguises herself as a man to pursue forbidden love'
    ],
    characterArchetypes: [
      {
        name: 'The Independent Heroine',
        role: 'Protagonist',
        description: 'A woman ahead of her time fighting social constraints',
        traits: ['Spirited', 'Intelligent', 'Determined', 'Compassionate']
      },
      {
        name: 'The Brooding Hero',
        role: 'Protagonist',
        description: 'A man with a troubled past seeking redemption',
        traits: ['Mysterious', 'Passionate', 'Protective', 'Honorable']
      },
      {
        name: 'The Disapproving Family',
        role: 'Antagonist',
        description: 'Social forces opposing the lovers',
        traits: ['Traditional', 'Controlling', 'Status-conscious', 'Well-meaning']
      },
      {
        name: 'The Wise Confidant',
        role: 'Supporting Character',
        description: 'A trusted friend who aids the romance',
        traits: ['Loyal', 'Discreet', 'Romantic', 'Helpful']
      }
    ],
    plotStructure: [
      {
        act: 1,
        description: 'Meeting and Attraction',
        keyEvents: ['First meeting', 'Initial attraction', 'Social obstacles revealed', 'Growing feelings'],
        wordCountPercentage: 25
      },
      {
        act: 2,
        description: 'Courtship and Conflict',
        keyEvents: ['Secret meetings', 'Romantic tension', 'External pressures', 'Major setback'],
        wordCountPercentage: 50
      },
      {
        act: 3,
        description: 'Resolution and Union',
        keyEvents: ['Crisis point', 'Declarations of love', 'Overcoming obstacles', 'Happy ending'],
        wordCountPercentage: 25
      }
    ]
  },
  {
    id: 'psychological-thriller',
    name: 'Psychological Thriller',
    description: 'Mind-bending suspense that explores the dark corners of human psychology.',
    genre: 'Thriller',
    subgenre: 'Psychological Thriller',
    difficulty: 'advanced',
    estimatedWordCount: 65000,
    estimatedChapters: 22,
    tags: ['Mind Games', 'Unreliable Narrator', 'Suspense', 'Dark Psychology'],
    selections: {
      primaryGenre: 'Thriller',
      subgenre: 'Psychological Thriller',
      narrativeVoice: 'First Person',
      tense: 'Present',
      toneOptions: ['Dark', 'Suspenseful'],
      writingStyle: 'Atmospheric',
      structureType: 'Non-linear',
      pacingPreference: 'Variable',
      chapterStructure: 'Short Chapters',
      timelineComplexity: 'Complex',
      protagonistTypes: ['Unreliable Narrator'],
      antagonistTypes: ['Psychological Manipulator'],
      characterComplexity: 'Very Complex',
      characterArcTypes: ['Descent into Madness'],
      timePeriod: 'Contemporary',
      geographicSetting: 'Isolated Setting',
      worldType: 'Real World',
      magicTechLevel: 'Modern',
      majorThemes: ['Reality vs Perception', 'Trust'],
      philosophicalThemes: ['Nature of Truth', 'Mental Health'],
      socialThemes: ['Isolation', 'Manipulation'],
      targetAudience: 'Adult',
      contentRating: 'R',
      projectScope: 'Standalone Novel',
      targetWordCount: 65000,
      targetChapters: 22,
      povCharacterCount: 1,
      povCharacterType: 'Single POV'
    },
    storyPrompts: [
      'A person starts questioning their own memories after a traumatic event',
      'Someone receives mysterious messages that no one else can see',
      'A therapist becomes obsessed with a patient\'s disturbing stories',
      'A writer discovers their fictional characters may be real'
    ],
    characterArchetypes: [
      {
        name: 'The Unreliable Narrator',
        role: 'Protagonist',
        description: 'A deeply flawed person whose perception cannot be trusted',
        traits: ['Paranoid', 'Intelligent', 'Fragile', 'Observant']
      },
      {
        name: 'The Manipulator',
        role: 'Antagonist',
        description: 'Someone who uses psychological tactics to control others',
        traits: ['Charming', 'Calculating', 'Patient', 'Ruthless']
      },
      {
        name: 'The Concerned Friend',
        role: 'Supporting Character',
        description: 'Someone trying to help but may not be trusted',
        traits: ['Caring', 'Persistent', 'Worried', 'Potentially suspicious']
      },
      {
        name: 'The Authority Figure',
        role: 'Supporting Character',
        description: 'A professional who may or may not believe the protagonist',
        traits: ['Skeptical', 'Professional', 'Analytical', 'Potentially helpful']
      }
    ],
    plotStructure: [
      {
        act: 1,
        description: 'Establishing Normalcy and First Cracks',
        keyEvents: ['Ordinary life setup', 'First strange incident', 'Doubt begins', 'Seeking explanations'],
        wordCountPercentage: 25
      },
      {
        act: 2,
        description: 'Escalating Paranoia and Investigation',
        keyEvents: ['Pattern emerges', 'Trust erodes', 'Investigation deepens', 'Reality questioned'],
        wordCountPercentage: 50
      },
      {
        act: 3,
        description: 'Truth Revelation and Resolution',
        keyEvents: ['Climactic revelation', 'Truth vs delusion', 'Final confrontation', 'Ambiguous resolution'],
        wordCountPercentage: 25
      }
    ]
  }
]

export function getTemplatesByGenre(genre: string): ProjectTemplate[] {
  return projectTemplates.filter(template => template.genre === genre)
}

export function getTemplatesByDifficulty(difficulty: 'beginner' | 'intermediate' | 'advanced'): ProjectTemplate[] {
  return projectTemplates.filter(template => template.difficulty === difficulty)
}

export function getTemplateById(id: string): ProjectTemplate | undefined {
  return projectTemplates.find(template => template.id === id)
}

export function getAllGenres(): string[] {
  const genres = new Set(projectTemplates.map(template => template.genre))
  return Array.from(genres)
}

export interface CreateProjectFromTemplateOptions {
  userId: string;
  templateId: string;
  customTitle?: string;
  customDescription?: string;
  customSelections?: Partial<ProjectTemplate['selections']>;
}

export async function createProjectFromTemplate(
  options: CreateProjectFromTemplateOptions
): Promise<{ projectId: string; template: ProjectTemplate }> {
  const template = getTemplateById(options.templateId);
  if (!template) {
    throw new Error(`Template with ID "${options.templateId}" not found`);
  }

  try {
    // Import database client
    const { db } = await import('./db/client');

    // Merge template selections with custom overrides
    const finalSelections = {
      ...template.selections,
      ...options.customSelections
    };

    // Create the project with template data
    const projectData = {
      user_id: options.userId,
      title: options.customTitle || template.name,
      description: options.customDescription || template.description,
      status: 'planning',
      ...finalSelections
    };

    const project = await db.projects.create(projectData);

    // Create story structure based on template
    const storyArcs = template.plotStructure.map(act => ({
      project_id: project.id,
      act_number: act.act,
      description: act.description,
      key_events: act.keyEvents
    }));

    for (const arc of storyArcs) {
      await db.processing.createTask({
        project_id: project.id,
        task_type: 'story_arc_creation',
        task_data: arc,
        status: 'completed'
      });
    }

    // Create character archetypes
    for (const archetype of template.characterArchetypes) {
      const characterData = {
        project_id: project.id,
        name: archetype.name,
        role: archetype.role,
        description: archetype.description,
        personality_traits: { traits: archetype.traits },
        character_arc: { arc: `${archetype.role} following ${archetype.description}` }
      };

      await db.characters.create(characterData);
    }

    // Create initial chapter structure
    const chaptersToCreate = Math.min(5, template.estimatedChapters);
    const wordsPerChapter = Math.floor(template.estimatedWordCount / template.estimatedChapters);

    for (let i = 1; i <= chaptersToCreate; i++) {
      const chapterData = {
        project_id: project.id,
        chapter_number: i,
        title: `Chapter ${i}`,
        target_word_count: wordsPerChapter,
        outline: JSON.stringify({
          chapterNumber: i,
          title: `Chapter ${i}`,
          summary: `This chapter will advance the ${template.genre.toLowerCase()} plot and develop characters.`,
          keyEvents: [`Event ${i}A`, `Event ${i}B`],
          targetWordCount: wordsPerChapter,
          templateId: template.id
        }),
        status: 'planned'
      };

      await db.chapters.create(chapterData);
    }

    // Create initial story bible entries
    const storyBibleEntries = [
      {
        project_id: project.id,
        category: 'Setting',
        title: 'Primary Setting',
        content: `This ${template.genre} story takes place in a ${template.selections.geographicSetting} setting during the ${template.selections.timePeriod} period.`,
        tags: [template.genre, 'setting', 'world-building']
      },
      {
        project_id: project.id,
        category: 'Themes',
        title: 'Major Themes',
        content: `Key themes include: ${template.selections.majorThemes.join(', ')}`,
        tags: ['themes', 'analysis']
      }
    ];

    for (const entry of storyBibleEntries) {
      await db.storyBible.create(entry);
    }

    return { projectId: project.id, template };
  } catch (error) {
    logger.error('Error creating project from template:', error);
    throw new Error(`Failed to create project from template: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export function generateProjectPrompt(template: ProjectTemplate): string {
  const randomPrompt = template.storyPrompts[Math.floor(Math.random() * template.storyPrompts.length)] || '';
  return randomPrompt;
}

export function validateTemplateSelections(selections: Partial<ProjectTemplate['selections']>): string[] {
  const errors: string[] = [];
  
  if (!selections.primaryGenre) {
    errors.push('Primary genre is required');
  }
  
  if (!selections.narrativeVoice) {
    errors.push('Narrative voice is required');
  }
  
  if (!selections.targetWordCount || selections.targetWordCount < 1000) {
    errors.push('Target word count must be at least 1,000 words');
  }
  
  if (!selections.targetChapters || selections.targetChapters < 1) {
    errors.push('Must have at least 1 chapter');
  }
  
  return errors;
}

export function estimateProjectDuration(template: ProjectTemplate): {
  estimatedDays: number;
  estimatedHours: number;
  dailyWordTarget: number;
} {
  // Base estimates on template difficulty and word count
  const baseWordsPerHour = template.difficulty === 'beginner' ? 300 : 
                          template.difficulty === 'intermediate' ? 400 : 500;
  
  const totalHours = Math.ceil(template.estimatedWordCount / baseWordsPerHour);
  const estimatedDays = Math.ceil(totalHours / 2); // Assuming 2 hours per day
  const dailyWordTarget = Math.ceil(template.estimatedWordCount / estimatedDays);
  
  return {
    estimatedDays,
    estimatedHours: totalHours,
    dailyWordTarget
  };
}