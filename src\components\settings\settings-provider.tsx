/**
 * Settings Provider
 * Initializes and applies user settings on app load
 */

'use client';

import { useEffect } from 'react';
import { useSettingsStore } from '@/lib/settings/settings-store';

interface SettingsProviderProps {
  children: React.ReactNode;
}

export function SettingsProvider({ children }: SettingsProviderProps) {
  const applySettingsToDocument = useSettingsStore((state) => state.applySettingsToDocument);

  useEffect(() => {
    // Apply settings when the component mounts
    applySettingsToDocument();
  }, [applySettingsToDocument]);

  return <>{children}</>;
}
