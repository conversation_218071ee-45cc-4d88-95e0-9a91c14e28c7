'use client'

import { Check, Sparkles } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatPrice } from '@/lib/subscription'
import type { SubscriptionTier } from '@/lib/subscription'

interface PricingCardProps {
  tier: SubscriptionTier
  isAnnual: boolean
  onSelect: (tier: SubscriptionTier) => void
}

export function PricingCard({ tier, isAnnual, onSelect }: PricingCardProps) {
  const price = isAnnual && tier.price > 0 ? Math.floor(tier.price * 0.8) : tier.price
  const annualSavings = tier.price > 0 ? Math.floor(tier.price * 12 * 0.2) : 0

  return (
    <Card 
      className={`relative h-full transition-all duration-200 hover:shadow-lg ${
        tier.popular 
          ? 'border-primary shadow-md scale-[1.02]' 
          : 'border-border hover:border-primary/50'
      }`}
    >
      {tier.popular && (
        <Badge 
          className="absolute -top-3 left-1/2 -translate-x-1/2 bg-primary text-primary-foreground font-mono"
        >
          <Sparkles className="w-3 h-3 mr-1" />
          MOST POPULAR
        </Badge>
      )}
      
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-literary-display">{tier.name}</CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          {tier.description}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div className="space-y-1">
          <div className="flex items-baseline gap-1">
            <span className="text-4xl font-bold">
              {formatPrice(price, tier.currency).split('.')[0]}
            </span>
            <span className="text-sm text-muted-foreground font-mono">
              /{isAnnual ? 'year' : 'month'}
            </span>
          </div>
          {isAnnual && tier.price > 0 && (
            <p className="text-xs text-primary font-mono">
              Save {formatPrice(annualSavings, tier.currency)}/year
            </p>
          )}
        </div>

        <ul className="space-y-3">
          {tier.features.map((feature, index) => {
            // Special formatting for agent list
            if (feature.includes('AI Agents') && tier.agents) {
              const agentCount = tier.agents.length
              const allAgents = ['Story Architect', 'Character Developer', 'Writing Agent', 'Editor Agent', 'Chapter Planner', 'Adaptive Planning']
              const hasAllAgents = agentCount === allAgents.length
              
              return (
                <li key={index} className="flex items-start gap-3">
                  <Check className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-foreground/90">
                    <span className="font-medium">
                      {hasAllAgents ? 'Access to all AI Agents' : `${agentCount} AI Agents`}
                    </span>
                    {!hasAllAgents && (
                      <div className="text-xs text-muted-foreground mt-1">
                        {tier.agents.join(', ')}
                      </div>
                    )}
                  </div>
                </li>
              )
            }
            
            return (
              <li key={index} className="flex items-start gap-3">
                <Check className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                <span className="text-sm text-foreground/90">{feature}</span>
              </li>
            )
          })}
        </ul>
      </CardContent>
      
      <CardFooter>
        <Button 
          onClick={() => onSelect(tier)}
          variant={tier.popular ? "default" : "outline"}
          className={`w-full font-mono ${
            tier.popular ? 'bg-primary hover:bg-primary/90' : ''
          }`}
        >
          {tier.price === 0 ? 'Start Free' : 'Get Started'}
        </Button>
      </CardFooter>
    </Card>
  )
}