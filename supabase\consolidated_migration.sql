-- BookScribe AI - Consolidated Database Migration
-- This file consolidates all migrations into a single executable script
-- Run this in Supabase SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;

-- Drop existing tables if they exist (in dependency order)
DROP TABLE IF EXISTS collaboration_locks CASCADE;
DROP TABLE IF EXISTS collaboration_changes CASCADE;
DROP TABLE IF EXISTS collaboration_participants CASCADE;
DROP TABLE IF EXISTS collaboration_sessions CASCADE;
DROP TABLE IF EXISTS ai_suggestions CASCADE;
DROP TABLE IF EXISTS task_progress CASCADE;
DROP TABLE IF EXISTS processing_tasks CASCADE;
DROP TABLE IF EXISTS project_collaborators CASCADE;
DROP TABLE IF EXISTS writing_sessions CASCADE;
DROP TABLE IF EXISTS notifications CASCADE;
DROP TABLE IF EXISTS writing_goal_progress CASCADE;
DROP TABLE IF EXISTS writing_goals CASCADE;
DROP TABLE IF EXISTS series_continuity_issues CASCADE;
DROP TABLE IF EXISTS series_universe_rules CASCADE;
DROP TABLE IF EXISTS series_character_arcs CASCADE;
DROP TABLE IF EXISTS series_books CASCADE;
DROP TABLE IF EXISTS series CASCADE;
DROP TABLE IF EXISTS content_embeddings CASCADE;
DROP TABLE IF EXISTS project_snapshots CASCADE;
DROP TABLE IF EXISTS chapter_versions CASCADE;
DROP TABLE IF EXISTS editing_sessions CASCADE;
DROP TABLE IF EXISTS story_bible CASCADE;
DROP TABLE IF EXISTS selection_analytics CASCADE;
DROP TABLE IF EXISTS reference_materials CASCADE;
DROP TABLE IF EXISTS selection_profiles CASCADE;
DROP TABLE IF EXISTS agent_logs CASCADE;
DROP TABLE IF EXISTS chapters CASCADE;
DROP TABLE IF EXISTS characters CASCADE;
DROP TABLE IF EXISTS story_arcs CASCADE;
DROP TABLE IF EXISTS usage_events CASCADE;
DROP TABLE IF EXISTS usage_tracking CASCADE;
DROP TABLE IF EXISTS user_subscriptions CASCADE;
DROP TABLE IF EXISTS profiles CASCADE;
DROP TABLE IF EXISTS projects CASCADE;

-- Create update function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Projects table with comprehensive settings
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Genre & Style Selections
  primary_genre VARCHAR(100),
  subgenre VARCHAR(100),
  custom_genre TEXT,
  narrative_voice VARCHAR(50),
  tense VARCHAR(20),
  tone_options TEXT[],
  writing_style VARCHAR(50),
  custom_style_description TEXT,
  
  -- Story Structure & Pacing
  structure_type VARCHAR(50),
  pacing_preference VARCHAR(50),
  chapter_structure VARCHAR(50),
  timeline_complexity VARCHAR(50),
  custom_structure_notes TEXT,
  
  -- Character & World Building
  protagonist_types TEXT[],
  antagonist_types TEXT[],
  character_complexity VARCHAR(50),
  character_arc_types TEXT[],
  custom_character_concepts TEXT,
  time_period VARCHAR(100),
  geographic_setting VARCHAR(100),
  world_type VARCHAR(100),
  magic_tech_level VARCHAR(50),
  custom_setting_description TEXT,
  
  -- Themes & Content
  major_themes TEXT[],
  philosophical_themes TEXT[],
  social_themes TEXT[],
  custom_themes TEXT,
  target_audience VARCHAR(50),
  content_rating VARCHAR(20),
  content_warnings TEXT[],
  cultural_sensitivity_notes TEXT,
  
  -- Series & Scope
  project_scope VARCHAR(50),
  series_type VARCHAR(50),
  interconnection_level VARCHAR(50),
  custom_scope_description TEXT,
  
  -- Technical Specifications
  target_word_count INTEGER,
  current_word_count INTEGER DEFAULT 0,
  total_word_count INTEGER DEFAULT 0,
  target_chapters INTEGER,
  chapter_count_type VARCHAR(20),
  pov_character_count INTEGER,
  pov_character_type VARCHAR(50),
  
  -- Research & References
  research_needs TEXT[],
  fact_checking_level VARCHAR(50),
  custom_research_notes TEXT,
  
  -- Initial story concept
  initial_concept TEXT,
  
  -- System Fields
  status TEXT DEFAULT 'planning',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Profiles
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT,
  full_name TEXT,
  username VARCHAR(50) UNIQUE,
  avatar_url TEXT,
  bio TEXT,
  location VARCHAR(255),
  website VARCHAR(500),
  stripe_customer_id TEXT,
  writing_goals JSONB DEFAULT '{"daily_words": 1000, "weekly_hours": 10, "genre_focus": "Fiction"}',
  preferences JSONB DEFAULT '{"public_profile": true, "email_notifications": true, "writing_reminders": true, "beta_features": false}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Subscriptions
CREATE TABLE user_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tier_id TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'past_due', 'incomplete')),
  stripe_subscription_id TEXT,
  stripe_customer_id TEXT,
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  cancel_at_period_end BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usage Tracking
CREATE TABLE usage_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  period_start TEXT NOT NULL,
  ai_generations INTEGER DEFAULT 0,
  projects INTEGER DEFAULT 0,
  exports INTEGER DEFAULT 0,
  storage_used NUMERIC DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, period_start)
);

-- Usage Events Log
CREATE TABLE usage_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  amount INTEGER DEFAULT 1,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Story structure and arcs
CREATE TABLE story_arcs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  structure_data JSONB NOT NULL,
  act_number INTEGER,
  description TEXT,
  key_events JSONB,
  plot_points JSONB,
  world_building JSONB,
  timeline JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Characters with comprehensive data
CREATE TABLE characters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  character_id VARCHAR(100) NOT NULL,
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  description TEXT,
  backstory TEXT,
  personality_traits JSONB,
  character_arc JSONB,
  relationships JSONB,
  voice_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(project_id, character_id)
);

-- Chapters with enhanced planning data
CREATE TABLE chapters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  chapter_number INTEGER NOT NULL,
  title TEXT,
  target_word_count INTEGER,
  actual_word_count INTEGER DEFAULT 0,
  outline TEXT,
  content TEXT,
  scenes_data JSONB,
  character_states JSONB,
  status TEXT DEFAULT 'planned',
  ai_notes JSONB,
  pov_character VARCHAR(100),
  plot_advancement JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(project_id, chapter_number)
);

-- AI Agent execution logs
CREATE TABLE agent_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  agent_type TEXT NOT NULL,
  input_data JSONB,
  output_data JSONB,
  execution_time INTEGER,
  status TEXT NOT NULL,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Selection profiles for reuse
CREATE TABLE selection_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT false,
  
  -- All the same selection fields as projects table
  primary_genre VARCHAR(100),
  subgenre VARCHAR(100),
  custom_genre TEXT,
  narrative_voice VARCHAR(50),
  tense VARCHAR(20),
  tone_options TEXT[],
  writing_style VARCHAR(50),
  custom_style_description TEXT,
  structure_type VARCHAR(50),
  pacing_preference VARCHAR(50),
  chapter_structure VARCHAR(50),
  timeline_complexity VARCHAR(50),
  custom_structure_notes TEXT,
  protagonist_types TEXT[],
  antagonist_types TEXT[],
  character_complexity VARCHAR(50),
  character_arc_types TEXT[],
  custom_character_concepts TEXT,
  time_period VARCHAR(100),
  geographic_setting VARCHAR(100),
  world_type VARCHAR(100),
  magic_tech_level VARCHAR(50),
  custom_setting_description TEXT,
  major_themes TEXT[],
  philosophical_themes TEXT[],
  social_themes TEXT[],
  custom_themes TEXT,
  target_audience VARCHAR(50),
  content_rating VARCHAR(20),
  content_warnings TEXT[],
  cultural_sensitivity_notes TEXT,
  project_scope VARCHAR(50),
  series_type VARCHAR(50),
  interconnection_level VARCHAR(50),
  custom_scope_description TEXT,
  target_word_count INTEGER,
  chapter_count_type VARCHAR(20),
  pov_character_count INTEGER,
  pov_character_type VARCHAR(50),
  research_needs TEXT[],
  fact_checking_level VARCHAR(50),
  custom_research_notes TEXT,
  
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reference materials
CREATE TABLE reference_materials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  file_type VARCHAR(50),
  file_url TEXT,
  file_size INTEGER,
  mime_type VARCHAR(100),
  tags TEXT[],
  is_processed BOOLEAN DEFAULT false,
  processing_status VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Selection analytics
CREATE TABLE selection_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  selection_profile_id UUID REFERENCES selection_profiles(id) ON DELETE SET NULL,
  event_type VARCHAR(50),
  selection_data JSONB,
  outcome_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Story Bible table for context management
CREATE TABLE story_bible (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  entry_type VARCHAR(50) NOT NULL,
  entry_key VARCHAR(255) NOT NULL,
  entry_data JSONB NOT NULL,
  chapter_introduced INTEGER,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Editing Sessions (for IDE-like features)
CREATE TABLE editing_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
  selection_start INTEGER,
  selection_end INTEGER,
  selected_text TEXT,
  ai_prompt TEXT,
  ai_response TEXT,
  action_type VARCHAR(50),
  was_applied BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chapter versions for revision tracking
CREATE TABLE chapter_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  title TEXT,
  content TEXT,
  word_count INTEGER DEFAULT 0,
  outline TEXT,
  ai_notes JSONB,
  change_summary TEXT,
  is_auto_save BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(chapter_id, version_number)
);

-- Project-level version snapshots
CREATE TABLE project_snapshots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  snapshot_data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content Embeddings for Semantic Search
CREATE TABLE content_embeddings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  content_type VARCHAR(50) NOT NULL,
  content_id UUID NOT NULL,
  text_content TEXT NOT NULL,
  embedding vector(1536),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Series Management Tables
CREATE TABLE series (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  genre VARCHAR(100),
  target_audience VARCHAR(50),
  overall_arc_description TEXT,
  planned_book_count INTEGER,
  current_book_count INTEGER DEFAULT 0,
  shared_universe_rules JSONB,
  character_continuity JSONB,
  timeline_span VARCHAR(100),
  publication_status VARCHAR(50) DEFAULT 'planning',
  first_published_date DATE,
  last_published_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Series Books Junction Table
CREATE TABLE series_books (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  series_id UUID REFERENCES series(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  book_number INTEGER NOT NULL,
  book_role VARCHAR(50),
  chronological_order INTEGER,
  publication_order INTEGER,
  introduces_characters UUID[],
  concludes_arcs TEXT[],
  sets_up_future JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(series_id, project_id),
  UNIQUE(series_id, book_number)
);

-- Writing Goals
CREATE TABLE writing_goals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  goal_type TEXT NOT NULL CHECK (goal_type IN ('daily', 'weekly', 'monthly', 'project')),
  target_words INTEGER NOT NULL CHECK (target_words > 0),
  start_date DATE NOT NULL,
  end_date DATE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
  CONSTRAINT unique_active_goal UNIQUE NULLS NOT DISTINCT (user_id, project_id, goal_type, is_active)
);

-- Writing Goal Progress
CREATE TABLE writing_goal_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  goal_id UUID NOT NULL REFERENCES writing_goals(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  words_written INTEGER NOT NULL DEFAULT 0,
  sessions_count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
  CONSTRAINT unique_goal_date UNIQUE (goal_id, date)
);

-- Notifications
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB DEFAULT '{}',
  read BOOLEAN DEFAULT FALSE,
  read_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Writing sessions
CREATE TABLE writing_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  chapter_id UUID REFERENCES chapters(id) ON DELETE SET NULL,
  word_count INTEGER NOT NULL DEFAULT 0,
  duration INTEGER NOT NULL DEFAULT 0,
  started_at TIMESTAMPTZ NOT NULL,
  ended_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Suggestions
CREATE TABLE ai_suggestions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
  suggestion_type TEXT NOT NULL CHECK (suggestion_type IN ('plot', 'character', 'dialogue', 'description', 'pacing', 'style', 'general')),
  content TEXT NOT NULL,
  context JSONB DEFAULT '{}',
  accepted BOOLEAN DEFAULT NULL,
  applied_at TIMESTAMPTZ,
  feedback TEXT,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Collaboration Sessions
CREATE TABLE collaboration_sessions (
  id TEXT PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  owner_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  document_content TEXT DEFAULT '',
  document_version INTEGER DEFAULT 1,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Collaboration Participants
CREATE TABLE collaboration_participants (
  session_id TEXT NOT NULL REFERENCES collaboration_sessions(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('owner', 'editor', 'viewer', 'commenter')),
  status TEXT NOT NULL DEFAULT 'offline' CHECK (status IN ('online', 'offline')),
  cursor_position JSONB,
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  last_seen TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (session_id, user_id)
);

-- Create indexes for performance
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_profiles_username ON profiles(username);
CREATE INDEX idx_profiles_stripe_customer ON profiles(stripe_customer_id);
CREATE INDEX idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX idx_usage_tracking_user_period ON usage_tracking(user_id, period_start);
CREATE INDEX idx_usage_events_user_id ON usage_events(user_id);
CREATE INDEX idx_usage_events_type ON usage_events(event_type);
CREATE INDEX idx_chapters_project_id ON chapters(project_id);
CREATE INDEX idx_chapters_status ON chapters(status);
CREATE INDEX idx_characters_project_id ON characters(project_id);
CREATE INDEX idx_story_bible_project_id ON story_bible(project_id);
CREATE INDEX idx_story_bible_entry_type ON story_bible(entry_type);
CREATE INDEX idx_editing_sessions_chapter_id ON editing_sessions(chapter_id);
CREATE INDEX idx_content_embeddings_project_id ON content_embeddings(project_id);
CREATE INDEX idx_content_embeddings_content_type ON content_embeddings(content_type);
CREATE INDEX idx_content_embeddings_content_id ON content_embeddings(content_id);
CREATE INDEX idx_content_embeddings_embedding ON content_embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX idx_series_user_id ON series(user_id);
CREATE INDEX idx_series_publication_status ON series(publication_status);
CREATE INDEX idx_series_books_series_id ON series_books(series_id);
CREATE INDEX idx_series_books_project_id ON series_books(project_id);
CREATE INDEX idx_writing_goals_user_active ON writing_goals(user_id, is_active);
CREATE INDEX idx_writing_goals_project ON writing_goals(project_id) WHERE project_id IS NOT NULL;
CREATE INDEX idx_writing_goal_progress_goal ON writing_goal_progress(goal_id);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_read ON notifications(read);
CREATE INDEX idx_writing_sessions_user_id ON writing_sessions(user_id);
CREATE INDEX idx_writing_sessions_project_id ON writing_sessions(project_id);
CREATE INDEX idx_ai_suggestions_user_id ON ai_suggestions(user_id);
CREATE INDEX idx_ai_suggestions_project_id ON ai_suggestions(project_id);
CREATE INDEX idx_collaboration_sessions_project_id ON collaboration_sessions(project_id);
CREATE INDEX idx_collaboration_participants_user_id ON collaboration_participants(user_id);

-- Enable Row Level Security
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_arcs ENABLE ROW LEVEL SECURITY;
ALTER TABLE characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE selection_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE reference_materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE selection_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_bible ENABLE ROW LEVEL SECURITY;
ALTER TABLE editing_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapter_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_snapshots ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_embeddings ENABLE ROW LEVEL SECURITY;
ALTER TABLE series ENABLE ROW LEVEL SECURITY;
ALTER TABLE series_books ENABLE ROW LEVEL SECURITY;
ALTER TABLE writing_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE writing_goal_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE writing_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE collaboration_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE collaboration_participants ENABLE ROW LEVEL SECURITY;

-- RLS Policies for Projects
CREATE POLICY "Users can view own projects" ON projects FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own projects" ON projects FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own projects" ON projects FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own projects" ON projects FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for Profiles
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS Policies for User Subscriptions
CREATE POLICY "Users can view own subscription" ON user_subscriptions FOR SELECT USING (auth.uid() = user_id);

-- RLS Policies for Usage Tracking
CREATE POLICY "Users can view own usage" ON usage_tracking FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view own usage events" ON usage_events FOR SELECT USING (auth.uid() = user_id);

-- RLS Policies for Project-related tables
CREATE POLICY "Users can access own project data" ON story_arcs FOR ALL USING (
  project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
);

CREATE POLICY "Users can access own project characters" ON characters FOR ALL USING (
  project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
);

CREATE POLICY "Users can access own project chapters" ON chapters FOR ALL USING (
  project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
);

CREATE POLICY "Users can access own agent logs" ON agent_logs FOR ALL USING (
  project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
);

CREATE POLICY "Users can access own profiles" ON selection_profiles FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view public profiles" ON selection_profiles FOR SELECT USING (is_public = true);

CREATE POLICY "Users can access own reference materials" ON reference_materials FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access own analytics" ON selection_analytics FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access own story bible" ON story_bible FOR ALL USING (
  project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
);

CREATE POLICY "Users can access own editing sessions" ON editing_sessions FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access own chapter versions" ON chapter_versions FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access own project snapshots" ON project_snapshots FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own content embeddings" ON content_embeddings FOR SELECT USING (
  EXISTS (SELECT 1 FROM projects p WHERE p.id = content_embeddings.project_id AND p.user_id = auth.uid())
);

CREATE POLICY "Users can insert own content embeddings" ON content_embeddings FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM projects p WHERE p.id = content_embeddings.project_id AND p.user_id = auth.uid())
);

-- RLS Policies for Series
CREATE POLICY "Users can view own series" ON series FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own series" ON series FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own series" ON series FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own series" ON series FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own series books" ON series_books FOR SELECT USING (
  EXISTS (SELECT 1 FROM series s WHERE s.id = series_books.series_id AND s.user_id = auth.uid())
);

-- RLS Policies for Writing Goals
CREATE POLICY "Users can view their own goals" ON writing_goals FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own goals" ON writing_goals FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own goals" ON writing_goals FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own goals" ON writing_goals FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view progress for their goals" ON writing_goal_progress FOR SELECT USING (
  EXISTS (SELECT 1 FROM writing_goals WHERE writing_goals.id = writing_goal_progress.goal_id AND writing_goals.user_id = auth.uid())
);

-- RLS Policies for Notifications
CREATE POLICY "Users can view their own notifications" ON notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their own notifications" ON notifications FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "System can create notifications for users" ON notifications FOR INSERT WITH CHECK (true);

-- RLS Policies for Writing Sessions
CREATE POLICY "Users can view their own writing sessions" ON writing_sessions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own writing sessions" ON writing_sessions FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for AI Suggestions
CREATE POLICY "Users can view their own AI suggestions" ON ai_suggestions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own AI suggestions" ON ai_suggestions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own AI suggestions" ON ai_suggestions FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for Collaboration
CREATE POLICY "Users can view sessions they participate in" ON collaboration_sessions FOR SELECT USING (
  EXISTS (SELECT 1 FROM collaboration_participants WHERE collaboration_participants.session_id = collaboration_sessions.id AND collaboration_participants.user_id = auth.uid())
);

CREATE POLICY "Owners can update their sessions" ON collaboration_sessions FOR UPDATE USING (owner_id = auth.uid());
CREATE POLICY "Users can create sessions" ON collaboration_sessions FOR INSERT WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Participants can view session participants" ON collaboration_participants FOR SELECT USING (
  EXISTS (SELECT 1 FROM collaboration_participants cp WHERE cp.session_id = collaboration_participants.session_id AND cp.user_id = auth.uid())
);

CREATE POLICY "Users can join sessions" ON collaboration_participants FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "Users can update their own participation" ON collaboration_participants FOR UPDATE USING (user_id = auth.uid());

-- Create triggers for updated_at
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_subscriptions_updated_at BEFORE UPDATE ON user_subscriptions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_usage_tracking_updated_at BEFORE UPDATE ON usage_tracking
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_story_arcs_updated_at BEFORE UPDATE ON story_arcs
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_characters_updated_at BEFORE UPDATE ON characters
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_chapters_updated_at BEFORE UPDATE ON chapters
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_selection_profiles_updated_at BEFORE UPDATE ON selection_profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reference_materials_updated_at BEFORE UPDATE ON reference_materials
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_story_bible_updated_at BEFORE UPDATE ON story_bible
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_embeddings_updated_at BEFORE UPDATE ON content_embeddings
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_series_updated_at BEFORE UPDATE ON series
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_series_books_updated_at BEFORE UPDATE ON series_books
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_writing_goals_updated_at BEFORE UPDATE ON writing_goals
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_writing_goal_progress_updated_at BEFORE UPDATE ON writing_goal_progress
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_notifications_updated_at BEFORE UPDATE ON notifications
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_writing_sessions_updated_at BEFORE UPDATE ON writing_sessions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_suggestions_updated_at BEFORE UPDATE ON ai_suggestions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to create profile on user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, avatar_url)
  VALUES (
    new.id,
    new.email,
    new.raw_user_meta_data->>'full_name',
    new.raw_user_meta_data->>'avatar_url'
  );
  RETURN new;
END;
$$;

-- Trigger to automatically create profile on signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Function to search similar content
CREATE OR REPLACE FUNCTION search_similar_content(
  query_embedding vector(1536),
  project_uuid UUID,
  content_type_filter VARCHAR(50) DEFAULT NULL,
  similarity_threshold FLOAT DEFAULT 0.7,
  match_count INT DEFAULT 10
)
RETURNS TABLE (
  id UUID,
  content_type VARCHAR(50),
  content_id UUID,
  text_content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    ce.id,
    ce.content_type,
    ce.content_id,
    ce.text_content,
    ce.metadata,
    1 - (ce.embedding <=> query_embedding) as similarity
  FROM content_embeddings ce
  WHERE
    ce.project_id = project_uuid
    AND (content_type_filter IS NULL OR ce.content_type = content_type_filter)
    AND 1 - (ce.embedding <=> query_embedding) > similarity_threshold
  ORDER BY ce.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Function to update goal progress when a writing session is created
CREATE OR REPLACE FUNCTION update_goal_progress()
RETURNS TRIGGER AS $$
DECLARE
  active_goals RECORD;
  session_date DATE;
BEGIN
  session_date := DATE(NEW.started_at);

  FOR active_goals IN
    SELECT id FROM writing_goals
    WHERE user_id = NEW.user_id
    AND is_active = true
    AND start_date <= session_date
    AND (end_date IS NULL OR end_date >= session_date)
    AND (project_id IS NULL OR project_id = NEW.project_id)
  LOOP
    INSERT INTO writing_goal_progress (goal_id, date, words_written, sessions_count)
    VALUES (active_goals.id, session_date, NEW.word_count, 1)
    ON CONFLICT (goal_id, date)
    DO UPDATE SET
      words_written = writing_goal_progress.words_written + NEW.word_count,
      sessions_count = writing_goal_progress.sessions_count + 1,
      updated_at = timezone('utc'::text, now());
  END LOOP;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update goal progress
CREATE TRIGGER update_goal_progress_on_session
  AFTER INSERT ON writing_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_goal_progress();

-- Function to create chapter version when content changes
CREATE OR REPLACE FUNCTION create_chapter_version()
RETURNS TRIGGER AS $$
DECLARE
  next_version_number INTEGER;
BEGIN
  IF OLD.content IS DISTINCT FROM NEW.content OR OLD.title IS DISTINCT FROM NEW.title THEN
    SELECT COALESCE(MAX(version_number), 0) + 1
    INTO next_version_number
    FROM chapter_versions
    WHERE chapter_id = NEW.id;

    INSERT INTO chapter_versions (
      chapter_id, user_id, version_number, title, content, word_count, outline, ai_notes, change_summary, is_auto_save
    ) VALUES (
      NEW.id, NEW.user_id, next_version_number, OLD.title, OLD.content, OLD.actual_word_count, OLD.outline, OLD.ai_notes,
      CASE
        WHEN OLD.title IS DISTINCT FROM NEW.title AND OLD.content IS DISTINCT FROM NEW.content THEN 'Title and content updated'
        WHEN OLD.title IS DISTINCT FROM NEW.title THEN 'Title updated'
        WHEN OLD.content IS DISTINCT FROM NEW.content THEN 'Content updated'
        ELSE 'Chapter updated'
      END,
      false
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic versioning
CREATE TRIGGER chapter_version_trigger
  BEFORE UPDATE ON chapters
  FOR EACH ROW
  EXECUTE FUNCTION create_chapter_version();

-- Enable Realtime for collaboration tables
ALTER PUBLICATION supabase_realtime ADD TABLE collaboration_participants;
ALTER PUBLICATION supabase_realtime ADD TABLE collaboration_sessions;
