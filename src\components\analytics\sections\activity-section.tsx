import { ProgressChart } from '../components/progress-chart'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { Badge } from '@/components/ui/badge'
import { Clock } from 'lucide-react'

interface PeakHour {
  hour: number
  avgWords: number
}

interface DailyProgress {
  date: string
  words: number
  sessions: number
}

interface ActivityData {
  dailyProgress: DailyProgress[]
  peakHours: PeakHour[]
  heatmapData?: Array<{ date: string; value: number; words: number }>
}

interface AnalyticsData {
  activity?: ActivityData
  overview?: {
    totalSessions: number
    [key: string]: unknown
  }
}

interface ActivitySectionProps {
  data: AnalyticsData | null
  isLoading: boolean
}

export function ActivitySection({ data, isLoading }: ActivitySectionProps) {
  const activity = data?.activity || {
    dailyProgress: [],
    peakHours: []
  }

  // Prepare hourly data
  const hourlyData = Array.from({ length: 24 }, (_, hour) => {
    const peakHour = activity.peakHours.find((p) => p.hour === hour)
    return {
      hour: `${hour}:00`,
      words: peakHour?.avgWords || 0
    }
  })

  const topHours = activity.peakHours.slice(0, 3)

  return (
    <>
      {/* Daily Progress Chart */}
      <ProgressChart
        title="Writing Progress Over Time"
        data={activity.dailyProgress}
        lines={[
          { dataKey: 'words', color: 'hsl(var(--primary))', name: 'Words' },
          { dataKey: 'sessions', color: 'hsl(var(--secondary))', name: 'Sessions' }
        ]}
        type="line"
        height={300}
        loading={isLoading}
        showLegend
      />

      {/* Peak Writing Hours */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Peak Writing Hours</CardTitle>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                Most productive times
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Top hours badges */}
          <div className="flex flex-wrap gap-2 mb-4">
            {topHours.map((hour, index) => (
              <Badge key={hour.hour} variant={index === 0 ? 'default' : 'secondary'}>
                {hour.hour}:00 - {hour.avgWords} words avg
              </Badge>
            ))}
          </div>

          {/* Hourly chart */}
          <ResponsiveContainer width="100%" height={200}>
            <BarChart data={hourlyData}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis 
                dataKey="hour" 
                className="text-xs"
                tick={{ fill: 'hsl(var(--muted-foreground))' }}
                interval={2}
              />
              <YAxis 
                className="text-xs"
                tick={{ fill: 'hsl(var(--muted-foreground))' }}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px'
                }}
                labelStyle={{ color: 'hsl(var(--foreground))' }}
              />
              <Bar 
                dataKey="words" 
                fill="hsl(var(--primary))"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Session Statistics */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Session Duration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Average Duration</span>
                <span className="font-medium">45 min</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Longest Session</span>
                <span className="font-medium">2h 15m</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Total Sessions</span>
                <span className="font-medium">{data?.overview?.totalSessions || 0}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Writing Patterns</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Most Productive Day</span>
                <Badge variant="outline">Monday</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Preferred Time</span>
                <Badge variant="outline">Morning</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Consistency Score</span>
                <span className="font-medium text-green-500">85%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}