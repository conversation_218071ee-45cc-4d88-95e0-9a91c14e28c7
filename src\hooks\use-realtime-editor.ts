'use client'

import { useEffect, useCallback, useRef } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useToast } from '@/hooks/use-toast'
import type { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js'
import type { Database } from '@/lib/db/types'

type Chapter = Database['public']['Tables']['chapters']['Row']
type WritingSession = Database['public']['Tables']['writing_sessions']['Row']

interface UseRealtimeEditorOptions {
  chapterId?: string
  projectId?: string
  userId?: string
  onChapterUpdate?: (chapter: Chapter) => void
  onSessionUpdate?: (session: WritingSession) => void
  onCollaboratorJoin?: (userId: string) => void
  onCollaboratorLeave?: (userId: string) => void
  enabled?: boolean
}

interface PresenceState {
  userId: string
  userName?: string
  cursor?: { line: number; column: number }
  selection?: { start: number; end: number }
  lastSeen: string
}

export function useRealtimeEditor({
  chapterId,
  projectId,
  userId,
  onChapterUpdate,
  onSessionUpdate,
  onCollaboratorJoin,
  onCollaboratorLeave,
  enabled = true
}: UseRealtimeEditorOptions) {
  const supabase = createClient()
  const { toast } = useToast()
  const channelRef = useRef<RealtimeChannel | null>(null)
  const presenceRef = useRef<Record<string, PresenceState>>({})

  // Handle chapter changes
  const handleChapterChange = useCallback((payload: RealtimePostgresChangesPayload<Chapter>) => {
    if (payload.eventType === 'UPDATE' && payload.new) {
      const updatedChapter = payload.new as Chapter
      
      // Only process if it's a different user's update
      if (updatedChapter.last_edited_by !== userId) {
        onChapterUpdate?.(updatedChapter)
        
        // Show toast notification
        toast({
          title: 'Chapter Updated',
          description: 'This chapter has been updated by another user.',
          duration: 3000,
        })
      }
    }
  }, [userId, onChapterUpdate, toast])

  // Handle writing session changes
  const handleSessionChange = useCallback((payload: RealtimePostgresChangesPayload<WritingSession>) => {
    if (payload.new) {
      const session = payload.new as WritingSession
      onSessionUpdate?.(session)
    }
  }, [onSessionUpdate])

  // Handle presence updates
  const handlePresenceSync = useCallback(() => {
    if (!channelRef.current) return
    
    const state = channelRef.current.presenceState()
    const newPresence: Record<string, PresenceState> = {}
    
    Object.entries(state).forEach(([key, presence]) => {
      if (Array.isArray(presence) && presence[0]) {
        const userPresence = presence[0] as PresenceState
        if (userPresence.userId !== userId) {
          newPresence[userPresence.userId] = userPresence
        }
      }
    })
    
    // Check for joins/leaves
    const oldUserIds = Object.keys(presenceRef.current)
    const newUserIds = Object.keys(newPresence)
    
    newUserIds.forEach(id => {
      if (!oldUserIds.includes(id)) {
        onCollaboratorJoin?.(id)
      }
    })
    
    oldUserIds.forEach(id => {
      if (!newUserIds.includes(id)) {
        onCollaboratorLeave?.(id)
      }
    })
    
    presenceRef.current = newPresence
  }, [userId, onCollaboratorJoin, onCollaboratorLeave])

  // Send cursor position
  const sendCursorPosition = useCallback((line: number, column: number) => {
    if (!channelRef.current || !userId) return
    
    channelRef.current.track({
      userId,
      cursor: { line, column },
      lastSeen: new Date().toISOString()
    })
  }, [userId])

  // Send selection
  const sendSelection = useCallback((start: number, end: number) => {
    if (!channelRef.current || !userId) return
    
    channelRef.current.track({
      userId,
      selection: { start, end },
      lastSeen: new Date().toISOString()
    })
  }, [userId])

  // Set up subscriptions
  useEffect(() => {
    if (!enabled || !chapterId) return

    const channelName = projectId 
      ? `editor-${projectId}-${chapterId}`
      : `editor-${chapterId}`

    const channel = supabase.channel(channelName)

    // Subscribe to chapter changes
    channel.on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'chapters',
        filter: `id=eq.${chapterId}`,
      },
      handleChapterChange
    )

    // Subscribe to writing session changes if projectId is provided
    if (projectId) {
      channel.on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'writing_sessions',
          filter: `project_id=eq.${projectId}`,
        },
        handleSessionChange
      )
    }

    // Set up presence
    channel.on('presence', { event: 'sync' }, handlePresenceSync)
    channel.on('presence', { event: 'join' }, handlePresenceSync)
    channel.on('presence', { event: 'leave' }, handlePresenceSync)

    // Subscribe
    channel.subscribe(async (status) => {
      if (status === 'SUBSCRIBED' && userId) {
        await channel.track({
          userId,
          lastSeen: new Date().toISOString()
        })
      }
    })

    channelRef.current = channel

    return () => {
      channel.unsubscribe()
      channelRef.current = null
      presenceRef.current = {}
    }
  }, [
    enabled,
    chapterId,
    projectId,
    userId,
    handleChapterChange,
    handleSessionChange,
    handlePresenceSync,
    supabase
  ])

  return {
    sendCursorPosition,
    sendSelection,
    collaborators: Object.values(presenceRef.current),
  }
}