'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { X, ChevronRight, ChevronLeft, BookOpen, Wand2, Users, BarChart3, Target, CheckCircle2 } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'

interface TutorialStep {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  action?: string
  targetElement?: string
}

const tutorialSteps: TutorialStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to BookScribe AI!',
    description: 'Let\'s take a quick tour to help you get started with your first novel.',
    icon: <BookOpen className="w-6 h-6" />,
  },
  {
    id: 'create-project',
    title: 'Create Your First Project',
    description: 'Click "Create New Project" to start your novel. Our AI will guide you through genre selection, story structure, and more.',
    icon: <Wand2 className="w-6 h-6" />,
    action: 'Highlight the Create New Project button',
    targetElement: '[data-tutorial="create-project"]',
  },
  {
    id: 'ai-agents',
    title: 'Meet Your AI Writing Team',
    description: 'Five specialized AI agents will help you: Story Architect, Character Developer, Chapter Planner, Writing Agent, and Editor.',
    icon: <Users className="w-6 h-6" />,
  },
  {
    id: 'writing-interface',
    title: 'Your Writing Workspace',
    description: 'Write with AI assistance, track your progress, and maintain consistency across your entire novel.',
    icon: <Target className="w-6 h-6" />,
  },
  {
    id: 'analytics',
    title: 'Track Your Progress',
    description: 'Monitor your writing stats, character development, and story arc progression with our analytics tools.',
    icon: <BarChart3 className="w-6 h-6" />,
  },
]

interface InteractiveTutorialProps {
  onComplete: () => void
  onSkip: () => void
}

export function InteractiveTutorial({ onComplete, onSkip }: InteractiveTutorialProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    // Highlight target elements when step changes
    const step = tutorialSteps[currentStep]
    if (step && step.targetElement) {
      const element = document.querySelector(step.targetElement)
      if (element) {
        element.classList.add('tutorial-highlight')
        element.scrollIntoView({ behavior: 'smooth', block: 'center' })
        
        return () => {
          element.classList.remove('tutorial-highlight')
        }
      }
    }
    return undefined
  }, [currentStep])

  const handleNext = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      handleComplete()
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleComplete = () => {
    setIsVisible(false)
    setTimeout(() => {
      onComplete()
    }, 300)
  }

  const handleSkip = () => {
    setIsVisible(false)
    setTimeout(() => {
      onSkip()
    }, 300)
  }

  const progress = ((currentStep + 1) / tutorialSteps.length) * 100
  const step = tutorialSteps[currentStep]

  return (
    <AnimatePresence>
      {isVisible && (
        <>
          {/* Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40"
            onClick={handleSkip}
          />
          
          {/* Tutorial Card */}
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-full max-w-md"
          >
            <Card className="shadow-2xl">
              <CardHeader className="relative">
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-2"
                  onClick={handleSkip}
                  aria-label="Skip tutorial"
                >
                  <X className="h-4 w-4" />
                </Button>
                
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 rounded-lg bg-primary/10 text-primary">
                    {step?.icon}
                  </div>
                  <Progress value={progress} className="flex-1" />
                </div>
                
                <CardTitle>{step?.title}</CardTitle>
                <CardDescription className="text-base mt-2">
                  {step?.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Step counter */}
                <div className="text-sm text-muted-foreground text-center">
                  Step {currentStep + 1} of {tutorialSteps.length}
                </div>
                
                {/* Navigation buttons */}
                <div className="flex justify-between gap-3">
                  <Button
                    variant="outline"
                    onClick={handlePrevious}
                    disabled={currentStep === 0}
                    className="flex-1"
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Previous
                  </Button>
                  
                  {currentStep === tutorialSteps.length - 1 ? (
                    <Button onClick={handleComplete} className="flex-1">
                      <CheckCircle2 className="h-4 w-4 mr-1" />
                      Get Started
                    </Button>
                  ) : (
                    <Button onClick={handleNext} className="flex-1">
                      Next
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  )}
                </div>
                
                {/* Skip button */}
                <button
                  onClick={handleSkip}
                  className="text-sm text-muted-foreground hover:text-foreground transition-colors w-full text-center"
                >
                  Skip tutorial
                </button>
              </CardContent>
            </Card>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}

// Add these styles to your global CSS
const tutorialStyles = `
  .tutorial-highlight {
    position: relative;
    z-index: 35;
    outline: 3px solid hsl(var(--primary));
    outline-offset: 4px;
    border-radius: 0.375rem;
    animation: tutorial-pulse 2s ease-in-out infinite;
  }
  
  @keyframes tutorial-pulse {
    0%, 100% {
      outline-color: hsl(var(--primary));
      outline-offset: 4px;
    }
    50% {
      outline-color: hsl(var(--primary) / 0.6);
      outline-offset: 8px;
    }
  }
`

// Export styles to be added to globals.css
export { tutorialStyles }