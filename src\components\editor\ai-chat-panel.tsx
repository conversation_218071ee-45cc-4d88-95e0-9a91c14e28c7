'use client'

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useEditorStore } from '@/stores/editor-store'
import { Send, X, Bot, User, Trash2 } from 'lucide-react'
import ReactMarkdown from 'react-markdown'

interface AiChatPanelProps {
  projectId: string
  chapterId?: string
}

export function AiChatPanel({ projectId, chapterId }: AiChatPanelProps) {
  const {
    showAiChat,
    toggleAiChat,
    chatHistory,
    addChatMessage,
    clearChatHistory,
    isAiProcessing,
    setAiProcessing,
    selectedText,
    content
  } = useEditorStore()

  const [input, setInput] = useState('')
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (showAiChat && inputRef.current) {
      inputRef.current.focus()
    }
  }, [showAiChat])

  useEffect(() => {
    // Auto-scroll to bottom when new messages are added
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [chatHistory])

  const handleSendMessage = async () => {
    if (!input.trim() || isAiProcessing) return

    const userMessage = input.trim()
    setInput('')
    setAiProcessing(true)

    // Add user message
    addChatMessage('user', userMessage, { selectedText })

    try {
      const response = await fetch('/api/agents/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId,
          chapterId,
          message: userMessage,
          context: {
            selectedText,
            currentContent: content,
            chatHistory: chatHistory.slice(-5) // Send last 5 messages for context
          }
        })
      })

      const data = await response.json()

      if (data.success) {
        addChatMessage('assistant', data.response)
      } else {
        addChatMessage('assistant', 'Sorry, I encountered an error. Please try again.')
      }
    } catch {
      addChatMessage('assistant', 'Sorry, I encountered an error. Please try again.')
    } finally {
      setAiProcessing(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  const suggestedPrompts = [
    "Help me improve this dialogue",
    "Make this scene more dramatic",
    "Suggest character development ideas",
    "How can I improve pacing?",
    "Help with world-building details"
  ]

  if (!showAiChat) return null

  return (
    <Card className="h-full flex flex-col rounded-none border-0 border-l">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2 font-mono">
            <Bot className="h-5 w-5" />
            AI Chat Assistant
          </CardTitle>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={clearChatHistory}
              disabled={chatHistory.length === 0}
              className="rounded-none"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={toggleAiChat} className="rounded-none">
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Chat Messages */}
        <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
          {chatHistory.length === 0 ? (
            <div className="space-y-4">
              <div className="text-center text-muted-foreground mb-4 font-mono">
                <Bot className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Ask me anything about your writing!</p>
              </div>

              <div className="space-y-2">
                <p className="text-xs font-medium text-muted-foreground font-mono">Try these prompts:</p>
                {suggestedPrompts.map((prompt, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start text-xs h-auto p-2 text-left font-mono rounded-none"
                    onClick={() => setInput(prompt)}
                  >
                    {prompt}
                  </Button>
                ))}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {chatHistory.map((message) => (
                <div key={message.id} className="space-y-2">
                  <div className="flex items-center gap-2">
                    {message.role === 'user' ? (
                      <User className="h-4 w-4" />
                    ) : (
                      <Bot className="h-4 w-4" />
                    )}
                    <span className="text-xs font-medium font-mono">
                      {message.role === 'user' ? 'You' : 'AI Assistant'}
                    </span>
                    <span className="text-xs text-muted-foreground font-mono">
                      {formatTimestamp(message.timestamp)}
                    </span>
                  </div>

                  {message.context?.selectedText && (
                    <div className="ml-6 p-2 bg-muted rounded-none text-xs font-mono">
                      <span className="font-medium">Selected: </span>
                      &quot;{message.context.selectedText.slice(0, 100)}...&quot;
                    </div>
                  )}
                  
                  <div className="ml-6 text-sm">
                    {message.role === 'assistant' ? (
                      <div className="prose prose-sm max-w-none font-mono">
                        <ReactMarkdown
                          components={{
                          p: ({ children }) => <p className="mb-2 last:mb-0 font-mono">{children}</p>,
                          ul: ({ children }) => <ul className="list-disc list-inside mb-2 font-mono">{children}</ul>,
                          ol: ({ children }) => <ol className="list-decimal list-inside mb-2 font-mono">{children}</ol>,
                          li: ({ children }) => <li className="mb-1 font-mono">{children}</li>,
                          code: ({ children }) => <code className="bg-muted px-1 py-0.5 rounded-none text-xs font-mono">{children}</code>
                          }}
                        >
                          {message.content}
                        </ReactMarkdown>
                      </div>
                    ) : (
                      <p className="font-mono">{message.content}</p>
                    )}
                  </div>
                </div>
              ))}
              
              {isAiProcessing && (
                <div className="flex items-center gap-2 ml-6 font-mono">
                  <Bot className="h-4 w-4" />
                  <span className="text-sm">Processing...</span>
                </div>
              )}
            </div>
          )}
        </ScrollArea>

        {/* Input Area */}
        <div className="border-t p-4">
          {selectedText && (
            <div className="mb-3 p-2 bg-primary/10 rounded-none text-xs font-mono">
              <span className="font-medium">Selected text: </span>
              &quot;{selectedText.slice(0, 100)}{selectedText.length > 100 ? '...' : ''}&quot;
            </div>
          )}
          
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask about your writing, request edits, or get suggestions..."
              disabled={isAiProcessing}
              className="font-mono rounded-none"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!input.trim() || isAiProcessing}
              size="sm"
              className="rounded-none"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}