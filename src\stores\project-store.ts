import { create } from 'zustand'

import { logger } from '@/lib/services/logger';

interface Project {
  id: string
  title: string
  description: string | null
  primary_genre: string | null
  target_word_count: number | null
  current_word_count: number
  status: string
  created_at: string
  updated_at: string
}

interface Chapter {
  id: string
  chapter_number: number
  title: string | null
  actual_word_count: number
  status: string
  created_at: string
  updated_at: string
}

interface Character {
  id: string
  name: string
  role: string | null
  description: string | null
}

interface ProjectState {
  // Data
  projects: Project[]
  currentProject: (Project & {
    chapters?: Chapter[]
    characters?: Character[]
  }) | null
  
  // Loading states
  isLoading: boolean
  isCreating: boolean
  isSaving: boolean
  
  // Actions
  setProjects: (projects: Project[]) => void
  setCurrentProject: (project: Record<string, unknown>) => void
  addProject: (project: Project) => void
  updateProject: (id: string, updates: Partial<Project>) => void
  deleteProject: (id: string) => void
  
  // Async actions
  loadProjects: () => Promise<void>
  loadProject: (id: string) => Promise<void>
  createProject: (projectData: Record<string, unknown>) => Promise<string | null>
  saveProject: (id: string, updates: Record<string, unknown>) => Promise<boolean>
  
  // UI state
  clearProject: () => void
  setLoading: (loading: boolean) => void
}

export const useProjectStore = create<ProjectState>((set, get) => ({
  // Initial state
  projects: [],
  currentProject: null,
  isLoading: false,
  isCreating: false,
  isSaving: false,
  
  // Synchronous actions
  setProjects: (projects) => set({ projects }),
  
  setCurrentProject: (project) => set({ currentProject: project as unknown as Project & { chapters?: Chapter[]; characters?: Character[]; } }),
  
  addProject: (project) => 
    set((state) => ({ 
      projects: [project, ...state.projects] 
    })),
  
  updateProject: (id, updates) =>
    set((state) => ({
      projects: state.projects.map(p => 
        p.id === id ? { ...p, ...updates } : p
      ),
      currentProject: state.currentProject?.id === id 
        ? { ...state.currentProject, ...updates }
        : state.currentProject
    })),
  
  deleteProject: (id) =>
    set((state) => ({
      projects: state.projects.filter(p => p.id !== id),
      currentProject: state.currentProject?.id === id 
        ? null 
        : state.currentProject
    })),
  
  clearProject: () => set({ currentProject: null }),
  
  setLoading: (isLoading) => set({ isLoading }),
  
  // Async actions
  loadProjects: async () => {
    set({ isLoading: true })
    try {
      const response = await fetch('/api/projects')
      const data = await response.json()
      
      if (response.ok) {
        set({ projects: data.projects || [] })
      } else {
        logger.error('Failed to load projects:', data.error);
      }
    } catch (error) {
      logger.error('Error loading projects:', error);
    } finally {
      set({ isLoading: false })
    }
  },
  
  loadProject: async (id: string) => {
    set({ isLoading: true })
    try {
      const response = await fetch(`/api/projects/${id}`)
      const data = await response.json()
      
      if (response.ok) {
        set({ currentProject: data.project })
      } else {
        logger.error('Failed to load project:', data.error);
      }
    } catch (error) {
      logger.error('Error loading project:', error);
    } finally {
      set({ isLoading: false })
    }
  },
  
  createProject: async (projectData: Record<string, unknown>) => {
    set({ isCreating: true })
    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(projectData)
      })
      
      const data = await response.json()
      
      if (response.ok) {
        const { addProject } = get()
        addProject(data.project)
        return data.project.id
      } else {
        logger.error('Failed to create project:', data.error);
        return null
      }
    } catch (error) {
      logger.error('Error creating project:', error);
      return null
    } finally {
      set({ isCreating: false })
    }
  },
  
  saveProject: async (id: string, updates: Record<string, unknown>) => {
    set({ isSaving: true })
    try {
      const response = await fetch(`/api/projects/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      })
      
      const data = await response.json()
      
      if (response.ok) {
        const { updateProject } = get()
        updateProject(id, data.project)
        return true
      } else {
        logger.error('Failed to save project:', data.error);
        return false
      }
    } catch (error) {
      logger.error('Error saving project:', error);
      return false
    } finally {
      set({ isSaving: false })
    }
  }
}))

// Helper hooks for common patterns
export const useProject = (id?: string) => {
  const { currentProject, loadProject } = useProjectStore()
  
  // Auto-load project if ID changes
  if (id && (!currentProject || currentProject.id !== id)) {
    loadProject(id)
  }
  
  return currentProject
}

export const useProjects = () => {
  const { projects, isLoading, loadProjects } = useProjectStore()
  
  // Auto-load projects if not loaded
  if (projects.length === 0 && !isLoading) {
    loadProjects()
  }
  
  return { projects, isLoading }
}