import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Test with anon key (like the frontend does)
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testProfileAccess() {
  console.log('🧪 Testing profile access with anon key...\n');

  const userId = 'ac3a6991-ee87-4b5f-b461-b133b9410687';

  try {
    // Test direct access (this should fail due to RLS)
    console.log('1. Testing direct profile access (should fail due to RLS)...');
    const { data: directData, error: directError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId);

    if (directError) {
      console.log('❌ Direct access failed (expected):', directError.message);
    } else {
      console.log('⚠️ Direct access succeeded (unexpected):', directData);
    }

    // Test with auth (simulate logged in user)
    console.log('\n2. Testing with simulated auth context...');
    
    // This simulates what happens when a user is logged in
    // In real app, the JWT token would contain the user ID
    console.log('ℹ️ In the real app, this would work because:');
    console.log('   - User logs in and gets JWT token');
    console.log('   - JWT contains user ID');
    console.log('   - RLS policy allows access when auth.uid() = user.id');
    console.log('   - Frontend makes request with Authorization header');

    console.log('\n3. Checking RLS policies...');
    console.log('✅ RLS policies should be:');
    console.log('   - "Users can view own profile" FOR SELECT USING (auth.uid() = id)');
    console.log('   - "Users can update own profile" FOR UPDATE USING (auth.uid() = id)');
    console.log('   - "Users can insert own profile" FOR INSERT WITH CHECK (auth.uid() = id)');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

testProfileAccess();
