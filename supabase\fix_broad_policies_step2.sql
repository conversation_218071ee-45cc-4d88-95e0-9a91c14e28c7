-- ============================================================================
-- STEP 2: CREATE SECURE GRANULAR POLICIES - PART 1
-- ============================================================================
-- Replace broad policies with specific, secure ones

-- ============================================================================
-- CHARACTERS - Granular Permissions
-- ============================================================================
CREATE POLICY "characters_select" ON characters
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "characters_insert" ON characters
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "characters_update" ON characters
  FOR UPDATE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "characters_delete" ON characters
  FOR DELETE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- ============================================================================
-- STORY ARCS - Granular Permissions
-- ============================================================================
CREATE POLICY "story_arcs_select" ON story_arcs
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "story_arcs_insert" ON story_arcs
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "story_arcs_update" ON story_arcs
  FOR UPDATE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "story_arcs_delete" ON story_arcs
  FOR DELETE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- ============================================================================
-- REFERENCE MATERIALS - Granular Permissions
-- ============================================================================
CREATE POLICY "reference_materials_select" ON reference_materials
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "reference_materials_insert" ON reference_materials
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "reference_materials_update" ON reference_materials
  FOR UPDATE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "reference_materials_delete" ON reference_materials
  FOR DELETE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- ============================================================================
-- STORY BIBLE - Granular Permissions
-- ============================================================================
CREATE POLICY "story_bible_select" ON story_bible
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "story_bible_insert" ON story_bible
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "story_bible_update" ON story_bible
  FOR UPDATE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "story_bible_delete" ON story_bible
  FOR DELETE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- ============================================================================
-- SERIES - User-Owned Full Access
-- ============================================================================
CREATE POLICY "series_select" ON series
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "series_insert" ON series
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "series_update" ON series
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "series_delete" ON series
  FOR DELETE USING (auth.uid() = user_id);

-- ============================================================================
-- WRITING GOALS - User-Owned Full Access
-- ============================================================================
CREATE POLICY "writing_goals_select" ON writing_goals
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "writing_goals_insert" ON writing_goals
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "writing_goals_update" ON writing_goals
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "writing_goals_delete" ON writing_goals
  FOR DELETE USING (auth.uid() = user_id);

-- Verify new policies were created
SELECT 
  tablename,
  COUNT(*) as policy_count,
  STRING_AGG(cmd, ', ') as operations
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('characters', 'story_arcs', 'reference_materials', 'story_bible', 'series', 'writing_goals')
GROUP BY tablename
ORDER BY tablename;
