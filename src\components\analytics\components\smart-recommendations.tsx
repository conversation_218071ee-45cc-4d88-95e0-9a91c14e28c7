'use client'

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  AlertCircle, 
  TrendingUp, 
  CheckCircle2, 
  Info,
  Lightbulb,
  Target,
  Zap
} from 'lucide-react'
import { createClient } from '@/lib/supabase/client'

interface Recommendation {
  id: string
  category: string
  title: string
  description: string
  action: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  potentialImpact: string
}

interface SmartRecommendationsProps {
  projectId?: string
  userId: string
}

export function SmartRecommendations({ projectId, userId }: SmartRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<Recommendation[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    fetchRecommendations()
  }, [projectId, userId])

  const fetchRecommendations = async () => {
    try {
      const response = await fetch(`/api/analytics/recommendations?${new URLSearchParams({
        userId,
        ...(projectId && { projectId })
      })}`)
      
      if (response.ok) {
        const data = await response.json()
        setRecommendations(data.recommendations || [])
      }
    } catch (error) {
      console.error('Failed to fetch recommendations:', error)
    } finally {
      setLoading(false)
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <AlertCircle className="h-5 w-5 text-destructive" />
      case 'high':
        return <TrendingUp className="h-5 w-5 text-orange-500" />
      case 'medium':
        return <Info className="h-5 w-5 text-blue-500" />
      case 'low':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />
      default:
        return <Lightbulb className="h-5 w-5 text-muted-foreground" />
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'performance':
        return <Zap className="h-4 w-4" />
      case 'quality':
        return <Target className="h-4 w-4" />
      default:
        return <Lightbulb className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Skeleton key={i} className="h-24 w-full" />
        ))}
      </div>
    )
  }

  if (recommendations.length === 0) {
    return (
      <Card className="p-6 text-center">
        <CheckCircle2 className="h-12 w-12 mx-auto text-green-500 mb-4" />
        <h3 className="text-lg font-semibold mb-2">Great job!</h3>
        <p className="text-muted-foreground">
          No recommendations at this time. Keep up the excellent work!
        </p>
      </Card>
    )
  }

  return (
    <ScrollArea className="h-[600px] pr-4">
      <div className="space-y-4">
        {recommendations.map((rec) => (
          <Card key={rec.id} className="p-4 hover:shadow-md transition-shadow">
            <div className="flex items-start gap-3">
              {getSeverityIcon(rec.severity)}
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  <h4 className="font-semibold">{rec.title}</h4>
                  <Badge variant="outline" className="text-xs">
                    {getCategoryIcon(rec.category)}
                    <span className="ml-1">{rec.category}</span>
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{rec.description}</p>
                <div className="pt-2 space-y-1">
                  <p className="text-sm font-medium">Recommended Action:</p>
                  <p className="text-sm text-primary">{rec.action}</p>
                </div>
                {rec.potentialImpact && (
                  <p className="text-xs text-muted-foreground pt-1">
                    Potential impact: {rec.potentialImpact}
                  </p>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>
    </ScrollArea>
  )
}