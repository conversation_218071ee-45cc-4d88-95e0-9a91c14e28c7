/**
 * Theme Definitions for BookScribe AI
 * Inspired by novelWriter's theme system
 */

export interface ThemeColors {
  // Base colors
  base: string;
  default: string;
  faded: string;
  red: string;
  orange: string;
  yellow: string;
  green: string;
  cyan: string;
  blue: string;
  purple: string;
  
  // Project colors
  root: string;
  folder: string;
  file: string;
  title: string;
  chapter: string;
  scene: string;
  note: string;
  active: string;
  inactive: string;
  disabled: string;
  
  // Palette colors
  window: string;
  windowText: string;
  alternateBase: string;
  text: string;
  tooltipBase: string;
  tooltipText: string;
  button: string;
  buttonText: string;
  brightText: string;
  highlight: string;
  highlightedText: string;
  link: string;
  linkVisited: string;
  accent: string;
  
  // GUI colors
  helpText: string;
  fadedText: string;
  errorText: string;
  
  // Syntax colors
  background: string;
  syntaxText: string;
  line: string;
  headerText: string;
  headerTag: string;
  emphasis: string;
  dialog: string;
  altDialog: string;
  syntaxNote: string;
  hidden: string;
  shortcode: string;
  keyword: string;
  tag: string;
  value: string;
  optional: string;
  spellcheckLine: string;
  errorLine: string;
  replaceTag: string;
  modifier: string;
  textHighlight: string;
}

export interface ThemeDefinition {
  id: string;
  name: string;
  mode: 'light' | 'dark';
  description: string;
  author: string;
  colors: ThemeColors;
}

// Writer's Sanctuary Light (Default)
export const writersSanctuaryLight: ThemeDefinition = {
  id: 'writers-sanctuary-light',
  name: "Writer's Sanctuary",
  mode: 'light',
  description: 'Warm literary theme with paper-like tones',
  author: 'BookScribe AI',
  colors: {
    // Base colors
    base: '#fcfcfc',
    default: '#2d2520',
    faded: '#8b7355',
    red: '#c44536',
    orange: '#d4a574',
    yellow: '#e6c068',
    green: '#7a9471',
    cyan: '#6b9999',
    blue: '#5a7ba6',
    purple: '#a67ba6',
    
    // Project colors
    root: '#5a7ba6',
    folder: '#e6c068',
    file: '#2d2520',
    title: '#7a9471',
    chapter: '#c44536',
    scene: '#5a7ba6',
    note: '#e6c068',
    active: '#7a9471',
    inactive: '#c44536',
    disabled: '#8b7355',
    
    // Palette colors
    window: '#f8f6f3',
    windowText: '#1a1410', /* Darker for better contrast */
    alternateBase: '#f0ede8',
    text: '#1a1410', /* Darker for better contrast */
    tooltipBase: '#ffffc0',
    tooltipText: '#1a1410', /* Darker for better contrast */
    button: '#f0ede8',
    buttonText: '#1a1410', /* Darker for better contrast */
    brightText: '#fcfcfc',
    highlight: '#d4a574',
    highlightedText: '#1a1410', /* Darker for better contrast */
    link: '#5a7ba6',
    linkVisited: '#a67ba6',
    accent: '#d4a574',

    // GUI colors
    helpText: '#6b5d45', /* Darker for better contrast */
    fadedText: '#6b5d45', /* Darker for better contrast */
    errorText: '#c44536',
    
    // Syntax colors
    background: '#fcfcfc',
    syntaxText: '#1a1410', /* Darker for better contrast */
    line: '#1a1410', /* Darker for better contrast */
    headerText: '#7a9471',
    headerTag: '#7a9471',
    emphasis: '#d4a574',
    dialog: '#5a7ba6',
    altDialog: '#c44536',
    syntaxNote: '#e6c068',
    hidden: '#6b5d45', /* Darker for better contrast */
    shortcode: '#7a9471',
    keyword: '#c44536',
    tag: '#7a9471',
    value: '#7a9471',
    optional: '#7a9471',
    spellcheckLine: '#c44536',
    errorLine: '#7a9471',
    replaceTag: '#7a9471',
    modifier: '#5a7ba6',
    textHighlight: '#e6c068'
  }
};

// Evening Study Dark (Default Dark)
export const eveningStudyDark: ThemeDefinition = {
  id: 'evening-study-dark',
  name: 'Evening Study',
  mode: 'dark',
  description: 'Rich dark brown theme for comfortable night writing',
  author: 'BookScribe AI',
  colors: {
    // Base colors
    base: '#1a1714',
    default: '#e8e3dc',
    faded: '#a09c96',
    red: '#ff7a7a',
    orange: '#d4a574',
    yellow: '#e6c068',
    green: '#9bb89b',
    cyan: '#7bb8b8',
    blue: '#7ba6d4',
    purple: '#c49bc4',
    
    // Project colors
    root: '#7ba6d4',
    folder: '#e6c068',
    file: '#e8e3dc',
    title: '#9bb89b',
    chapter: '#ff7a7a',
    scene: '#7ba6d4',
    note: '#e6c068',
    active: '#9bb89b',
    inactive: '#ff7a7a',
    disabled: '#a09c96',
    
    // Palette colors
    window: '#1f1c19',
    windowText: '#f5f2eb', /* Brighter for better contrast */
    alternateBase: '#2a2520',
    text: '#f5f2eb', /* Brighter for better contrast */
    tooltipBase: '#ffffc0',
    tooltipText: '#2d2520',
    button: '#2a2520',
    buttonText: '#f5f2eb', /* Brighter for better contrast */
    brightText: '#ffffff',
    highlight: '#d4a574',
    highlightedText: '#1a1714',
    link: '#7ba6d4',
    linkVisited: '#c49bc4',
    accent: '#d4a574',

    // GUI colors
    helpText: '#b5b0a8', /* Brighter for better contrast */
    fadedText: '#b5b0a8', /* Brighter for better contrast */
    errorText: '#ff7a7a',
    
    // Syntax colors
    background: '#1a1714',
    syntaxText: '#f5f2eb', /* Brighter for better contrast */
    line: '#f5f2eb', /* Brighter for better contrast */
    headerText: '#9bb89b',
    headerTag: '#9bb89b',
    emphasis: '#d4a574',
    dialog: '#7ba6d4',
    altDialog: '#ff7a7a',
    syntaxNote: '#e6c068',
    hidden: '#b5b0a8', /* Brighter for better contrast */
    shortcode: '#9bb89b',
    keyword: '#ff7a7a',
    tag: '#9bb89b',
    value: '#9bb89b',
    optional: '#9bb89b',
    spellcheckLine: '#ff7a7a',
    errorLine: '#9bb89b',
    replaceTag: '#9bb89b',
    modifier: '#7ba6d4',
    textHighlight: '#e6c068'
  }
};

// Forest Manuscript Light
export const forestManuscriptLight: ThemeDefinition = {
  id: 'forest-manuscript-light',
  name: 'Forest Manuscript',
  mode: 'light',
  description: 'Nature-inspired green theme for outdoor writing',
  author: 'BookScribe AI',
  colors: {
    // Base colors
    base: '#f8faf8',
    default: '#2d3d2d',
    faded: '#7a8a7a',
    red: '#c44536',
    orange: '#cc8844',
    yellow: '#ccaa44',
    green: '#4a7a4a',
    cyan: '#4a7a7a',
    blue: '#4a6a8a',
    purple: '#7a4a7a',

    // Project colors
    root: '#4a6a8a',
    folder: '#ccaa44',
    file: '#2d3d2d',
    title: '#4a7a4a',
    chapter: '#c44536',
    scene: '#4a6a8a',
    note: '#ccaa44',
    active: '#4a7a4a',
    inactive: '#c44536',
    disabled: '#7a8a7a',

    // Palette colors
    window: '#f0f5f0',
    windowText: '#1a2a1a', /* Darker for better contrast */
    alternateBase: '#e8f0e8',
    text: '#1a2a1a', /* Darker for better contrast */
    tooltipBase: '#ffffc0',
    tooltipText: '#1a2a1a', /* Darker for better contrast */
    button: '#e8f0e8',
    buttonText: '#1a2a1a', /* Darker for better contrast */
    brightText: '#f8faf8',
    highlight: '#4a7a4a',
    highlightedText: '#f8faf8',
    link: '#4a6a8a',
    linkVisited: '#7a4a7a',
    accent: '#4a7a4a',

    // GUI colors
    helpText: '#5a6a5a', /* Darker for better contrast */
    fadedText: '#5a6a5a', /* Darker for better contrast */
    errorText: '#c44536',

    // Syntax colors
    background: '#f8faf8',
    syntaxText: '#1a2a1a', /* Darker for better contrast */
    line: '#1a2a1a', /* Darker for better contrast */
    headerText: '#4a7a4a',
    headerTag: '#4a7a4a',
    emphasis: '#cc8844',
    dialog: '#4a6a8a',
    altDialog: '#c44536',
    syntaxNote: '#ccaa44',
    hidden: '#5a6a5a', /* Darker for better contrast */
    shortcode: '#4a7a4a',
    keyword: '#c44536',
    tag: '#4a7a4a',
    value: '#4a7a4a',
    optional: '#4a7a4a',
    spellcheckLine: '#c44536',
    errorLine: '#4a7a4a',
    replaceTag: '#4a7a4a',
    modifier: '#4a6a8a',
    textHighlight: '#ccaa44'
  }
};

// Midnight Ink Dark
export const midnightInkDark: ThemeDefinition = {
  id: 'midnight-ink-dark',
  name: 'Midnight Ink',
  mode: 'dark',
  description: 'Deep blue-black theme for late night writing sessions',
  author: 'BookScribe AI',
  colors: {
    // Base colors
    base: '#0f1419',
    default: '#e6e6e6',
    faded: '#8a9199',
    red: '#ff6b6b',
    orange: '#ffa366',
    yellow: '#ffcc66',
    green: '#66cc99',
    cyan: '#66cccc',
    blue: '#6699ff',
    purple: '#cc99ff',

    // Project colors
    root: '#6699ff',
    folder: '#ffcc66',
    file: '#e6e6e6',
    title: '#66cc99',
    chapter: '#ff6b6b',
    scene: '#6699ff',
    note: '#ffcc66',
    active: '#66cc99',
    inactive: '#ff6b6b',
    disabled: '#8a9199',

    // Palette colors
    window: '#1a1f2e',
    windowText: '#f2f2f2', /* Brighter for better contrast */
    alternateBase: '#252a3a',
    text: '#f2f2f2', /* Brighter for better contrast */
    tooltipBase: '#ffffc0',
    tooltipText: '#0f1419',
    button: '#252a3a',
    buttonText: '#f2f2f2', /* Brighter for better contrast */
    brightText: '#ffffff',
    highlight: '#6699ff',
    highlightedText: '#0f1419',
    link: '#6699ff',
    linkVisited: '#cc99ff',
    accent: '#6699ff',

    // GUI colors
    helpText: '#a5adb5', /* Brighter for better contrast */
    fadedText: '#a5adb5', /* Brighter for better contrast */
    errorText: '#ff6b6b',

    // Syntax colors
    background: '#0f1419',
    syntaxText: '#f2f2f2', /* Brighter for better contrast */
    line: '#f2f2f2', /* Brighter for better contrast */
    headerText: '#66cc99',
    headerTag: '#66cc99',
    emphasis: '#ffa366',
    dialog: '#6699ff',
    altDialog: '#ff6b6b',
    syntaxNote: '#ffcc66',
    hidden: '#a5adb5', /* Brighter for better contrast */
    shortcode: '#66cc99',
    keyword: '#ff6b6b',
    tag: '#66cc99',
    value: '#66cc99',
    optional: '#66cc99',
    spellcheckLine: '#ff6b6b',
    errorLine: '#66cc99',
    replaceTag: '#66cc99',
    modifier: '#6699ff',
    textHighlight: '#ffcc66'
  }
};
