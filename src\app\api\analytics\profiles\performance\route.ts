import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { supabase } from '@/lib/db/client';
import { authenticateUser } from '@/lib/api/auth-helpers';

interface AnalyticsEntry {
  id: string;
  user_id: string;
  project_id: string;
  event_type: string;
  selection_data?: unknown;
  outcome_data?: unknown;
  created_at: string;
  projects?: {
    id: string;
    name: string;
    genre?: string;
    status: string;
    word_count?: number;
    target_word_count?: number;
    created_at: string;
    completed_at?: string;
  };
}

interface PerformanceMetrics {
  totalUsage: number;
  projectsCreated: number;
  projectsCompleted: number;
  projectsAbandoned: number;
  averageWordCount: number;
  averageCompletionTime: number;
  successRate: number;
  usageByEventType: { [key: string]: number };
  genreDistribution: { [key: string]: number };
  wordCountDistribution: {
    'novella': number;
    'short-novel': number;
    'standard-novel': number;
    'long-novel': number;
    'epic-novel': number;
  };
  completionTimeDistribution: {
    'under-week': number;
    'week-month': number;
    'month-quarter': number;
    'quarter-year': number;
    'over-year': number;
  };
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, error: authError } = await authenticateUser();
    if (authError) return authError;

    const { searchParams } = new URL(request.url);
    const profileId = searchParams.get('profileId');
    const userId = searchParams.get('userId');
    const timeframe = searchParams.get('timeframe') || 'month';

    // Only allow users to view their own analytics unless they're admin
    if (userId && userId !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (timeframe) {
      case 'week':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(endDate.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(endDate.getMonth() - 1);
    }

    let query = supabase
      .from('selection_analytics')
      .select(`
        *,
        projects(
          id,
          name,
          genre,
          status,
          word_count,
          target_word_count,
          created_at,
          completed_at
        )
      `)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (profileId) {
      query = query.eq('selection_profile_id', profileId);
    } else if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data: analytics, error } = await query;

    if (error) throw error;

    // Get profile information if profileId is provided
    let profile = null;
    if (profileId) {
      const { data: profileData, error: profileError } = await supabase
        .from('selection_profiles')
        .select('*')
        .eq('id', profileId)
        .single();

      if (!profileError) {
        profile = profileData;
      }
    }

    // Calculate performance metrics
    const performance = calculatePerformanceMetrics(analytics || []);

    return NextResponse.json({
      profile,
      timeframe,
      performance,
      analytics: analytics?.map(entry => ({
        id: entry.id,
        userId: entry.user_id,
        projectId: entry.project_id,
        eventType: entry.event_type,
        selectionData: entry.selection_data,
        outcomeData: entry.outcome_data,
        createdAt: new Date(entry.created_at),
        project: entry.projects
      })) || []
    });
  } catch (error) {
    console.error('Profile performance API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

function calculatePerformanceMetrics(analytics: AnalyticsEntry[]): PerformanceMetrics {
  const metrics = {
    totalUsage: 0,
    projectsCreated: 0,
    projectsCompleted: 0,
    projectsAbandoned: 0,
    averageWordCount: 0,
    averageCompletionTime: 0,
    successRate: 0,
    usageByEventType: {} as { [key: string]: number },
    genreDistribution: {} as { [key: string]: number },
    wordCountDistribution: {
      'novella': 0,
      'short-novel': 0,
      'standard-novel': 0,
      'long-novel': 0,
      'epic-novel': 0
    },
    completionTimeDistribution: {
      'under-week': 0,
      'week-month': 0,
      'month-quarter': 0,
      'quarter-year': 0,
      'over-year': 0
    }
  };

  let totalWordCount = 0;
  let totalCompletionTime = 0;
  let completionTimeCount = 0;

  analytics.forEach(entry => {
    metrics.totalUsage++;

    // Count by event type
    const eventType = entry.event_type;
    if (!metrics.usageByEventType[eventType]) {
      metrics.usageByEventType[eventType] = 0;
    }
    metrics.usageByEventType[eventType]++;

    // Project-specific metrics
    if (entry.projects) {
      const project = entry.projects;
      
      if (entry.event_type === 'project_created') {
        metrics.projectsCreated++;
        
        // Genre distribution
        if (project.genre) {
          const genre = project.genre;
          if (!metrics.genreDistribution[genre]) {
            metrics.genreDistribution[genre] = 0;
          }
          metrics.genreDistribution[genre]++;
        }

        // Word count tracking
        if (project.target_word_count) {
          totalWordCount += project.target_word_count;
          
          const wordCountRange = getWordCountRange(project.target_word_count);
          metrics.wordCountDistribution[wordCountRange]++;
        }
      }

      if (entry.event_type === 'project_completed') {
        metrics.projectsCompleted++;

        // Calculate completion time
        if (project.created_at && project.completed_at) {
          const createdDate = new Date(project.created_at);
          const completedDate = new Date(project.completed_at);
          const completionTimeMs = completedDate.getTime() - createdDate.getTime();
          const completionTimeDays = completionTimeMs / (1000 * 60 * 60 * 24);
          
          totalCompletionTime += completionTimeDays;
          completionTimeCount++;

          // Completion time distribution
          const timeRange = getCompletionTimeRange(completionTimeDays);
          metrics.completionTimeDistribution[timeRange]++;
        }
      }

      if (entry.event_type === 'project_abandoned') {
        metrics.projectsAbandoned++;
      }
    }
  });

  // Calculate averages and rates
  if (metrics.projectsCreated > 0) {
    metrics.averageWordCount = Math.round(totalWordCount / metrics.projectsCreated);
  }

  if (completionTimeCount > 0) {
    metrics.averageCompletionTime = Math.round(totalCompletionTime / completionTimeCount);
  }

  if (metrics.projectsCompleted + metrics.projectsAbandoned > 0) {
    metrics.successRate = Math.round(
      (metrics.projectsCompleted / (metrics.projectsCompleted + metrics.projectsAbandoned)) * 10000
    ) / 100;
  }

  return metrics;
}

type WordCountRange = 'novella' | 'short-novel' | 'standard-novel' | 'long-novel' | 'epic-novel';

function getWordCountRange(wordCount: number): WordCountRange {
  if (wordCount < 50000) return 'novella';
  if (wordCount < 80000) return 'short-novel';
  if (wordCount < 120000) return 'standard-novel';
  if (wordCount < 200000) return 'long-novel';
  return 'epic-novel';
}

type CompletionTimeRange = 'under-week' | 'week-month' | 'month-quarter' | 'quarter-year' | 'over-year';

function getCompletionTimeRange(days: number): CompletionTimeRange {
  if (days < 7) return 'under-week';
  if (days < 30) return 'week-month';
  if (days < 90) return 'month-quarter';
  if (days < 365) return 'quarter-year';
  return 'over-year';
}