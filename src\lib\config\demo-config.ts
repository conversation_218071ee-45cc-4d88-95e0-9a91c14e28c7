/**
 * Demo Configuration
 * Provides dummy configuration values for demo mode when environment variables are not set
 */

export const demoConfig = {
  // App Configuration
  NEXT_PUBLIC_APP_URL: 'http://localhost:3000',
  NODE_ENV: 'development' as const,

  // Supabase Configuration (dummy values for demo)
  NEXT_PUBLIC_SUPABASE_URL: 'https://demo.supabase.co',
  NEXT_PUBLIC_SUPABASE_ANON_KEY: 'demo-anon-key-placeholder',
  SUPABASE_SERVICE_ROLE_KEY: 'demo-service-role-key-placeholder',
  SUPABASE_WEBHOOK_SECRET: 'demo-webhook-secret',
  SUPABASE_JWT_SECRET: 'demo-jwt-secret',

  // OpenAI Configuration (dummy values for demo)
  OPENAI_API_KEY: 'demo-openai-key-placeholder',
  OPENAI_ORGANIZATION: 'demo-org',

  // Stripe Configuration (dummy values for demo)
  STRIPE_SECRET_KEY: 'sk_test_demo_stripe_key_placeholder',
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: 'pk_test_demo_stripe_key_placeholder',
  STRIPE_WEBHOOK_SECRET: 'whsec_demo_webhook_secret',

  // Email Configuration (dummy values for demo)
  RESEND_API_KEY: 'demo-resend-key-placeholder',
  SMTP_HOST: 'demo.smtp.host',
  SMTP_PORT: '587',
  SMTP_USER: '<EMAIL>',
  SMTP_PASSWORD: 'demo-password',

  // Analytics Configuration (dummy values for demo)
  NEXT_PUBLIC_GOOGLE_ANALYTICS_ID: 'GA-DEMO-ID',
  NEXT_PUBLIC_POSTHOG_KEY: 'demo-posthog-key',
  NEXT_PUBLIC_POSTHOG_HOST: 'https://demo.posthog.com',

  // Sentry Configuration (dummy values for demo)
  SENTRY_DSN: 'https://<EMAIL>/demo',
  NEXT_PUBLIC_SENTRY_DSN: 'https://<EMAIL>/demo',

  // Feature Flags (demo defaults)
  NEXT_PUBLIC_ENABLE_ANALYTICS: 'false',
  NEXT_PUBLIC_ENABLE_SENTRY: 'false',
  NEXT_PUBLIC_ENABLE_STRIPE: 'false',
  NEXT_PUBLIC_ENABLE_AI_FEATURES: 'false',

  // Rate Limiting (demo values)
  UPSTASH_REDIS_REST_URL: 'https://demo-redis.upstash.io',
  UPSTASH_REDIS_REST_TOKEN: 'demo-redis-token',

  // File Storage (demo values)
  NEXT_PUBLIC_SUPABASE_STORAGE_BUCKET: 'demo-bucket',
  AWS_ACCESS_KEY_ID: 'demo-aws-key',
  AWS_SECRET_ACCESS_KEY: 'demo-aws-secret',
  AWS_REGION: 'us-east-1',
  AWS_S3_BUCKET: 'demo-s3-bucket',

  // Database (demo values)
  DATABASE_URL: 'postgresql://demo:demo@localhost:5432/demo',
  DIRECT_URL: 'postgresql://demo:demo@localhost:5432/demo',

  // Security (demo values)
  NEXTAUTH_SECRET: 'demo-nextauth-secret-placeholder',
  NEXTAUTH_URL: 'http://localhost:3000',

  // API Keys for external services (demo values)
  ANTHROPIC_API_KEY: 'demo-anthropic-key-placeholder',
  XAI_API_KEY: 'demo-xai-key-placeholder',
  GOOGLE_AI_API_KEY: 'demo-google-ai-key-placeholder',

  // Webhook URLs (demo values)
  WEBHOOK_URL: 'http://localhost:3000/api/webhooks',
  STRIPE_WEBHOOK_URL: 'http://localhost:3000/api/webhooks/stripe',
  SUPABASE_WEBHOOK_URL: 'http://localhost:3000/api/webhooks/supabase',

  // Development specific
  VERCEL_URL: 'localhost:3000',
  VERCEL_ENV: 'development',

  // Logging
  LOG_LEVEL: 'info',
  ENABLE_LOGGING: 'true',

  // Performance
  ENABLE_PERFORMANCE_MONITORING: 'false',
  PERFORMANCE_SAMPLE_RATE: '0.1',

  // Content Security
  CONTENT_SECURITY_POLICY: 'default-src \'self\'',
  
  // Demo mode flag
  DEMO_MODE: 'true',
  NEXT_PUBLIC_DEMO_MODE: 'true'
}
