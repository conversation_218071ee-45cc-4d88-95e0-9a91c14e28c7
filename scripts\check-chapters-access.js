const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://xvqeiwrpbzpiqvwuvtpj.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function checkChaptersAccess() {
  console.log('Checking chapters table access...')
  
  try {
    // First, let's see if we can access chapters at all with service role
    const { data: chapters, error: chaptersError } = await supabase
      .from('chapters')
      .select('*')
      .limit(5)
    
    if (chaptersError) {
      console.error('❌ Error accessing chapters with service role:', chaptersError)
    } else {
      console.log('✅ Service role can access chapters:', chapters.length, 'chapters found')
    }

    // Check if the table exists and has the right structure
    const { data: tableInfo, error: tableError } = await supabase
      .from('information_schema.tables')
      .select('*')
      .eq('table_name', 'chapters')
      .eq('table_schema', 'public')
    
    if (tableError) {
      console.error('❌ Error checking table info:', tableError)
    } else {
      console.log('✅ Chapters table exists:', tableInfo.length > 0)
    }

    // Check projects table for the user
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .limit(5)
    
    if (projectsError) {
      console.error('❌ Error accessing projects:', projectsError)
    } else {
      console.log('✅ Found projects:', projects.length)
      if (projects.length > 0) {
        console.log('First project:', projects[0])
        
        // Try to get chapters for this project
        const { data: projectChapters, error: projectChaptersError } = await supabase
          .from('chapters')
          .select('*')
          .eq('project_id', projects[0].id)
        
        if (projectChaptersError) {
          console.error('❌ Error getting chapters for project:', projectChaptersError)
        } else {
          console.log('✅ Chapters for project:', projectChapters.length)
        }
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error)
  }
}

checkChaptersAccess()
