-- Add compound indexes for common JOIN operations to improve performance

-- Index for chapters joined with projects (very common pattern)
CREATE INDEX IF NOT EXISTS idx_chapters_project_user 
ON chapters (project_id, chapter_number) 
INCLUDE (title, status, actual_word_count)
WHERE deleted_at IS NULL;

-- Index for characters joined with projects
CREATE INDEX IF NOT EXISTS idx_characters_project 
ON characters (project_id, role)
INCLUDE (name)
WHERE deleted_at IS NULL;

-- Index for writing sessions analytics queries
CREATE INDEX IF NOT EXISTS idx_writing_sessions_user_date
ON writing_sessions (user_id, started_at DESC)
INCLUDE (project_id, word_count, duration);

-- Index for quality metrics lookups
CREATE INDEX IF NOT EXISTS idx_quality_metrics_project_chapter
ON quality_metrics (project_id, chapter_id, created_at DESC)
INCLUDE (overall_score);

-- Index for user analytics date queries
CREATE INDEX IF NOT EXISTS idx_user_analytics_user_date
ON user_analytics (user_id, date DESC)
INCLUDE (words_written, time_spent, sessions_count);

-- Index for story arcs joined with projects
CREATE INDEX IF NOT EXISTS idx_story_arcs_project_act
ON story_arcs (project_id, act_number);

-- Index for story bible lookups
CREATE INDEX IF NOT EXISTS idx_story_bible_project_type
ON story_bible (project_id, entry_type)
INCLUDE (entry_key);

-- Index for chapter versions history
CREATE INDEX IF NOT EXISTS idx_chapter_versions_chapter_created
ON chapter_versions (chapter_id, created_at DESC)
INCLUDE (version_type, word_count);

-- Index for projects list queries (common dashboard query)
CREATE INDEX IF NOT EXISTS idx_projects_user_updated
ON projects (user_id, updated_at DESC)
INCLUDE (title, status, current_word_count, target_word_count)
WHERE deleted_at IS NULL;

-- Index for reference materials by project
CREATE INDEX IF NOT EXISTS idx_reference_materials_project_type
ON reference_materials (project_id, material_type)
WHERE deleted_at IS NULL;

-- Analyze tables to update statistics after creating indexes
ANALYZE chapters;
ANALYZE characters;
ANALYZE writing_sessions;
ANALYZE quality_metrics;
ANALYZE user_analytics;
ANALYZE story_arcs;
ANALYZE story_bible;
ANALYZE chapter_versions;
ANALYZE projects;
ANALYZE reference_materials;