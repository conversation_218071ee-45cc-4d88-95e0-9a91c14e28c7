import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { logger } from '@/lib/services/logger';

export const runtime = 'nodejs';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id: projectId } = params;
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: plotThreads, error } = await supabase
      .from('plot_threads')
      .select(`
        *,
        started_chapter:chapters!started_chapter_id(
          id,
          title,
          chapter_number
        ),
        resolved_chapter:chapters!resolved_chapter_id(
          id,
          title,
          chapter_number
        ),
        series:series(
          id,
          title
        )
      `)
      .eq('project_id', projectId)
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('Error fetching plot threads:', error);
      return NextResponse.json({ error: 'Failed to fetch plot threads' }, { status: 500 });
    }

    // Fetch character and location details for related IDs
    const characterIds = [...new Set(plotThreads.flatMap(pt => pt.related_characters || []))];
    const locationIds = [...new Set(plotThreads.flatMap(pt => pt.related_locations || []))];

    let characters: Record<string, unknown>[] = [];
    let locations: Record<string, unknown>[] = [];

    if (characterIds.length > 0) {
      const { data } = await supabase
        .from('characters')
        .select('id, name, role')
        .in('id', characterIds);
      characters = data || [];
    }

    if (locationIds.length > 0) {
      const { data } = await supabase
        .from('locations')
        .select('id, name')
        .in('id', locationIds);
      locations = data || [];
    }

    // Map character and location details to plot threads
    const enrichedPlotThreads = plotThreads.map(thread => ({
      ...thread,
      characters: characters.filter(c => 
        thread.related_characters?.includes(c.id as string)
      ),
      locations: locations.filter(l => 
        thread.related_locations?.includes(l.id as string)
      )
    }));

    return NextResponse.json({ plotThreads: enrichedPlotThreads });
  } catch (error) {
    logger.error('Error in GET /api/projects/[id]/plot-threads:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id: projectId } = params;
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify user owns the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.user.id)
      .single();

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    const body = await request.json();
    const {
      name,
      description,
      status = 'setup',
      threadType = 'subplot',
      importance = 'major',
      seriesId,
      startedChapterId,
      resolvedChapterId,
      relatedCharacters = [],
      relatedLocations = [],
      notes = {}
    } = body;

    if (!name?.trim()) {
      return NextResponse.json({ error: 'Plot thread name is required' }, { status: 400 });
    }

    const { data: plotThread, error } = await supabase
      .from('plot_threads')
      .insert({
        project_id: projectId,
        series_id: seriesId,
        name: name.trim(),
        description: description?.trim(),
        status,
        thread_type: threadType,
        importance,
        started_chapter_id: startedChapterId,
        resolved_chapter_id: resolvedChapterId,
        related_characters: relatedCharacters,
        related_locations: relatedLocations,
        notes
      })
      .select(`
        *,
        started_chapter:chapters!started_chapter_id(
          id,
          title,
          chapter_number
        ),
        resolved_chapter:chapters!resolved_chapter_id(
          id,
          title,
          chapter_number
        )
      `)
      .single();

    if (error) {
      logger.error('Error creating plot thread:', error);
      return NextResponse.json({ error: 'Failed to create plot thread' }, { status: 500 });
    }

    return NextResponse.json({ plotThread });
  } catch (error) {
    logger.error('Error in POST /api/projects/[id]/plot-threads:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}