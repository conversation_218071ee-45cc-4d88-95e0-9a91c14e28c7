/**
 * React hooks for accessing synced Stripe data
 */

import { useState, useEffect } from 'react'
import { StripeSyncService, StripeCustomer, StripeSubscription, StripeInvoice } from '@/lib/stripe/sync-service'

/**
 * Hook to get customer data by email
 */
export function useStripeCustomer(email: string | null) {
  const [customer, setCustomer] = useState<StripeCustomer | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!email) {
      setCustomer(null)
      return
    }

    const fetchCustomer = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const customerData = await StripeSyncService.getCustomerByEmail(email)
        setCustomer(customerData)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch customer')
      } finally {
        setLoading(false)
      }
    }

    fetchCustomer()
  }, [email])

  return { customer, loading, error }
}

/**
 * Hook to get customer subscriptions
 */
export function useCustomerSubscriptions(customerId: string | null) {
  const [subscriptions, setSubscriptions] = useState<StripeSubscription[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!customerId) {
      setSubscriptions([])
      return
    }

    const fetchSubscriptions = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const subscriptionData = await StripeSyncService.getCustomerSubscriptions(customerId)
        setSubscriptions(subscriptionData)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch subscriptions')
      } finally {
        setLoading(false)
      }
    }

    fetchSubscriptions()
  }, [customerId])

  return { subscriptions, loading, error }
}

/**
 * Hook to get active subscription for a customer
 */
export function useActiveSubscription(customerId: string | null) {
  const [subscription, setSubscription] = useState<StripeSubscription | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!customerId) {
      setSubscription(null)
      return
    }

    const fetchActiveSubscription = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const subscriptionData = await StripeSyncService.getActiveSubscription(customerId)
        setSubscription(subscriptionData)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch active subscription')
      } finally {
        setLoading(false)
      }
    }

    fetchActiveSubscription()
  }, [customerId])

  return { subscription, loading, error }
}

/**
 * Hook to get customer invoices
 */
export function useCustomerInvoices(customerId: string | null, limit = 10) {
  const [invoices, setInvoices] = useState<StripeInvoice[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!customerId) {
      setInvoices([])
      return
    }

    const fetchInvoices = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const invoiceData = await StripeSyncService.getCustomerInvoices(customerId, limit)
        setInvoices(invoiceData)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch invoices')
      } finally {
        setLoading(false)
      }
    }

    fetchInvoices()
  }, [customerId, limit])

  return { invoices, loading, error }
}

/**
 * Hook to get failed payments (admin use)
 */
export function useFailedPayments(limit = 10) {
  const [failedPayments, setFailedPayments] = useState<StripeInvoice[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchFailedPayments = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const paymentData = await StripeSyncService.getFailedPayments(limit)
        setFailedPayments(paymentData)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch failed payments')
      } finally {
        setLoading(false)
      }
    }

    fetchFailedPayments()
  }, [limit])

  return { failedPayments, loading, error }
}
