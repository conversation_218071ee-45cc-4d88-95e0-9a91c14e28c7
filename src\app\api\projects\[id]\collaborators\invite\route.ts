import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { sendEmail } from '@/lib/email'
import { logger } from '@/lib/services/logger'
import crypto from 'crypto'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { email, role = 'viewer' } = await request.json()

    if (!email || !email.includes('@')) {
      return NextResponse.json({ error: 'Invalid email address' }, { status: 400 })
    }

    if (!['editor', 'viewer'].includes(role)) {
      return NextResponse.json({ error: 'Invalid role' }, { status: 400 })
    }

    const projectId = params.id

    // Check if user owns the project or has manage_team permission
    const { data: project } = await supabase
      .from('projects')
      .select('id, title, user_id')
      .eq('id', projectId)
      .single()

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    const isOwner = project.user_id === user.id

    if (!isOwner) {
      // Check if user has manage_team permission
      const { data: collaborator } = await supabase
        .from('project_collaborators')
        .select('permissions')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single()

      if (!collaborator || !collaborator.permissions?.can_manage_team) {
        return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
      }
    }

    // Check if user is already a collaborator
    const { data: existingCollaborator } = await supabase
      .from('project_collaborators')
      .select('id, status')
      .eq('project_id', projectId)
      .eq('user_id', email) // We'll need to look up by email
      .single()

    if (existingCollaborator) {
      if (existingCollaborator.status === 'active') {
        return NextResponse.json({ error: 'User is already a collaborator' }, { status: 400 })
      } else if (existingCollaborator.status === 'pending') {
        return NextResponse.json({ error: 'Invitation already sent' }, { status: 400 })
      }
    }

    // Check collaborator limit
    const { data: userSubscription } = await supabase
      .from('user_subscriptions')
      .select('tier_id')
      .eq('user_id', project.user_id)
      .eq('status', 'active')
      .single()

    let maxCollaborators = 0
    if (userSubscription) {
      switch (userSubscription.tier_id) {
        case 'professional':
          maxCollaborators = 2
          break
        case 'studio':
          maxCollaborators = 5
          break
      }
    }

    if (maxCollaborators > 0) {
      const { count } = await supabase
        .from('project_collaborators')
        .select('*', { count: 'exact', head: true })
        .eq('project_id', projectId)
        .eq('status', 'active')
        .neq('user_id', project.user_id)

      if ((count || 0) >= maxCollaborators) {
        return NextResponse.json({ 
          error: `Collaborator limit reached. Your plan allows ${maxCollaborators} collaborators.` 
        }, { status: 400 })
      }
    } else {
      return NextResponse.json({ 
        error: 'Your subscription plan does not include collaboration features' 
      }, { status: 400 })
    }

    // Check if invited email has an account
    const { data: invitedUser } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', email)
      .single()

    // Generate invitation token
    const inviteToken = crypto.randomBytes(32).toString('hex')
    const inviteExpiresAt = new Date()
    inviteExpiresAt.setDate(inviteExpiresAt.getDate() + 7) // 7 days expiry

    // Create invitation
    const { data: invitation, error: inviteError } = await supabase
      .from('project_collaborators')
      .insert({
        project_id: projectId,
        user_id: invitedUser?.id || crypto.randomUUID(), // Placeholder ID if user doesn't exist
        invited_by: user.id,
        role,
        status: 'pending',
        invite_token: inviteToken,
        invite_expires_at: inviteExpiresAt.toISOString()
      })
      .select()
      .single()

    if (inviteError) {
      logger.error('Failed to create invitation', inviteError)
      return NextResponse.json({ error: 'Failed to create invitation' }, { status: 500 })
    }

    // Send invitation email
    const inviteUrl = `${process.env.NEXT_PUBLIC_APP_URL}/invite/${inviteToken}`
    
    try {
      await sendEmail({
        to: email,
        subject: `You've been invited to collaborate on "${project.title}"`,
        html: `
          <h2>Collaboration Invitation</h2>
          <p>You've been invited to collaborate on the project "${project.title}" as a${role === 'editor' ? 'n' : ''} ${role}.</p>
          
          <h3>What you can do as a${role === 'editor' ? 'n' : ''} ${role}:</h3>
          ${role === 'editor' ? `
            <ul>
              <li>View and edit all project content</li>
              <li>Create and modify chapters</li>
              <li>Access the Story Bible</li>
              <li>Export the project</li>
            </ul>
          ` : `
            <ul>
              <li>View all project content</li>
              <li>Access the Story Bible</li>
              <li>Export the project</li>
            </ul>
          `}
          
          <p>
            <a href="${inviteUrl}" style="display: inline-block; padding: 12px 24px; background-color: #3B82F6; color: white; text-decoration: none; border-radius: 6px;">
              Accept Invitation
            </a>
          </p>
          
          <p>This invitation will expire in 7 days.</p>
          
          <p>If you don't have a BookScribe account yet, you'll be prompted to create one.</p>
        `,
        text: `
          You've been invited to collaborate on "${project.title}"
          
          Accept the invitation: ${inviteUrl}
          
          This invitation will expire in 7 days.
        `
      })
    } catch (emailError) {
      logger.error('Failed to send invitation email', emailError)
      // Don't fail the request if email fails, invitation is still created
    }

    return NextResponse.json({
      success: true,
      invitation: {
        id: invitation.id,
        email,
        role,
        status: 'pending',
        expires_at: inviteExpiresAt.toISOString()
      }
    })

  } catch (error) {
    logger.error('Error in collaborator invite endpoint', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}