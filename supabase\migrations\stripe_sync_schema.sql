-- Stripe Sync Engine Schema Migration
-- This creates the necessary schema and tables for syncing Stripe data
-- Run this in your Supabase SQL editor or via migration

-- Create the stripe schema
CREATE SCHEMA IF NOT EXISTS stripe;

-- Enable RLS on the stripe schema (optional, for security)
-- ALTER SCHEMA stripe OWNER TO postgres;

-- Create customers table
CREATE TABLE IF NOT EXISTS stripe.customers (
    id TEXT PRIMARY KEY,
    email TEXT,
    name TEXT,
    description TEXT,
    phone TEXT,
    address JSONB,
    shipping JSONB,
    created BIGINT NOT NULL,
    updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    deleted BOOLEAN DEFAULT FALSE,
    balance BIGINT DEFAULT 0,
    delinquent BOOLEAN DEFAULT FALSE,
    currency TEXT,
    default_source TEXT,
    discount JSONB,
    invoice_prefix TEXT,
    invoice_settings JSONB,
    livemode BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    preferred_locales TEXT[],
    tax_exempt TEXT,
    test_clock TEXT,
    raw_data JSONB
);

-- Create subscriptions table
CREATE TABLE IF NOT EXISTS stripe.subscriptions (
    id TEXT PRIMARY KEY,
    customer TEXT,
    status TEXT NOT NULL,
    current_period_start BIGINT NOT NULL,
    current_period_end BIGINT NOT NULL,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    cancel_at BIGINT,
    canceled_at BIGINT,
    created BIGINT NOT NULL,
    updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    ended_at BIGINT,
    start_date BIGINT,
    trial_start BIGINT,
    trial_end BIGINT,
    application_fee_percent DECIMAL,
    billing_cycle_anchor BIGINT,
    collection_method TEXT,
    days_until_due BIGINT,
    default_payment_method TEXT,
    default_source TEXT,
    default_tax_rates JSONB,
    discount JSONB,
    items JSONB,
    latest_invoice TEXT,
    livemode BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    next_pending_invoice_item_invoice BIGINT,
    pause_collection JSONB,
    payment_settings JSONB,
    pending_invoice_item_interval JSONB,
    pending_setup_intent TEXT,
    pending_update JSONB,
    schedule TEXT,
    transfer_data JSONB,
    trial_settings JSONB,
    raw_data JSONB
);

-- Create invoices table
CREATE TABLE IF NOT EXISTS stripe.invoices (
    id TEXT PRIMARY KEY,
    customer TEXT,
    subscription TEXT,
    status TEXT NOT NULL,
    amount_due BIGINT DEFAULT 0,
    amount_paid BIGINT DEFAULT 0,
    amount_remaining BIGINT DEFAULT 0,
    application_fee_amount BIGINT,
    attempt_count BIGINT DEFAULT 0,
    attempted BOOLEAN DEFAULT FALSE,
    auto_advance BOOLEAN DEFAULT TRUE,
    billing_reason TEXT,
    charge TEXT,
    collection_method TEXT,
    created BIGINT NOT NULL,
    updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    currency TEXT,
    custom_fields JSONB,
    customer_address JSONB,
    customer_email TEXT,
    customer_name TEXT,
    customer_phone TEXT,
    customer_shipping JSONB,
    customer_tax_exempt TEXT,
    customer_tax_ids JSONB,
    default_payment_method TEXT,
    default_source TEXT,
    default_tax_rates JSONB,
    description TEXT,
    discount JSONB,
    discounts JSONB,
    due_date BIGINT,
    ending_balance BIGINT,
    footer TEXT,
    hosted_invoice_url TEXT,
    invoice_pdf TEXT,
    last_finalization_error JSONB,
    lines JSONB,
    livemode BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    next_payment_attempt BIGINT,
    number TEXT,
    on_behalf_of TEXT,
    paid BOOLEAN DEFAULT FALSE,
    paid_out_of_band BOOLEAN DEFAULT FALSE,
    payment_intent TEXT,
    payment_settings JSONB,
    period_end BIGINT,
    period_start BIGINT,
    post_payment_credit_notes_amount BIGINT DEFAULT 0,
    pre_payment_credit_notes_amount BIGINT DEFAULT 0,
    quote TEXT,
    receipt_number TEXT,
    rendering_options JSONB,
    starting_balance BIGINT DEFAULT 0,
    statement_descriptor TEXT,
    status_transitions JSONB,
    subscription_details JSONB,
    subtotal BIGINT DEFAULT 0,
    subtotal_excluding_tax BIGINT,
    tax BIGINT,
    test_clock TEXT,
    total BIGINT DEFAULT 0,
    total_discount_amounts JSONB,
    total_excluding_tax BIGINT,
    total_tax_amounts JSONB,
    transfer_data JSONB,
    webhooks_delivered_at BIGINT,
    raw_data JSONB
);

-- Create payment_intents table
CREATE TABLE IF NOT EXISTS stripe.payment_intents (
    id TEXT PRIMARY KEY,
    customer TEXT,
    amount BIGINT NOT NULL,
    amount_capturable BIGINT DEFAULT 0,
    amount_details JSONB,
    amount_received BIGINT DEFAULT 0,
    application TEXT,
    application_fee_amount BIGINT,
    automatic_payment_methods JSONB,
    canceled_at BIGINT,
    cancellation_reason TEXT,
    capture_method TEXT,
    charges JSONB,
    client_secret TEXT,
    confirmation_method TEXT,
    created BIGINT NOT NULL,
    updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    currency TEXT NOT NULL,
    description TEXT,
    invoice TEXT,
    last_payment_error JSONB,
    latest_charge TEXT,
    livemode BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    next_action JSONB,
    on_behalf_of TEXT,
    payment_method TEXT,
    payment_method_options JSONB,
    payment_method_types TEXT[],
    processing JSONB,
    receipt_email TEXT,
    review TEXT,
    setup_future_usage TEXT,
    shipping JSONB,
    statement_descriptor TEXT,
    statement_descriptor_suffix TEXT,
    status TEXT NOT NULL,
    transfer_data JSONB,
    transfer_group TEXT,
    raw_data JSONB
);

-- Create products table
CREATE TABLE IF NOT EXISTS stripe.products (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    attributes TEXT[],
    caption TEXT,
    created BIGINT NOT NULL,
    updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    deactivate_on TEXT[],
    description TEXT,
    images TEXT[],
    livemode BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    package_dimensions JSONB,
    shippable BOOLEAN,
    statement_descriptor TEXT,
    tax_code TEXT,
    type TEXT,
    unit_label TEXT,
    url TEXT,
    raw_data JSONB
);

-- Create prices table
CREATE TABLE IF NOT EXISTS stripe.prices (
    id TEXT PRIMARY KEY,
    product TEXT,
    active BOOLEAN DEFAULT TRUE,
    billing_scheme TEXT,
    created BIGINT NOT NULL,
    updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    currency TEXT NOT NULL,
    custom_unit_amount JSONB,
    livemode BOOLEAN DEFAULT FALSE,
    lookup_key TEXT,
    metadata JSONB DEFAULT '{}',
    nickname TEXT,
    recurring JSONB,
    tax_behavior TEXT,
    tiers JSONB,
    tiers_mode TEXT,
    transform_quantity JSONB,
    type TEXT,
    unit_amount BIGINT,
    unit_amount_decimal TEXT,
    raw_data JSONB
);

-- Create charges table
CREATE TABLE IF NOT EXISTS stripe.charges (
    id TEXT PRIMARY KEY,
    customer TEXT,
    amount BIGINT NOT NULL,
    amount_captured BIGINT DEFAULT 0,
    amount_refunded BIGINT DEFAULT 0,
    application TEXT,
    application_fee TEXT,
    application_fee_amount BIGINT,
    balance_transaction TEXT,
    billing_details JSONB,
    calculated_statement_descriptor TEXT,
    captured BOOLEAN DEFAULT FALSE,
    created BIGINT NOT NULL,
    updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    currency TEXT NOT NULL,
    description TEXT,
    disputed BOOLEAN DEFAULT FALSE,
    failure_balance_transaction TEXT,
    failure_code TEXT,
    failure_message TEXT,
    fraud_details JSONB,
    invoice TEXT,
    livemode BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    on_behalf_of TEXT,
    outcome JSONB,
    paid BOOLEAN DEFAULT FALSE,
    payment_intent TEXT,
    payment_method TEXT,
    payment_method_details JSONB,
    receipt_email TEXT,
    receipt_number TEXT,
    receipt_url TEXT,
    refunded BOOLEAN DEFAULT FALSE,
    refunds JSONB,
    review TEXT,
    shipping JSONB,
    source_transfer TEXT,
    statement_descriptor TEXT,
    statement_descriptor_suffix TEXT,
    status TEXT NOT NULL,
    transfer_data JSONB,
    transfer_group TEXT,
    raw_data JSONB
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_stripe_customers_email ON stripe.customers(email);
CREATE INDEX IF NOT EXISTS idx_stripe_customers_created ON stripe.customers(created);
CREATE INDEX IF NOT EXISTS idx_stripe_customers_updated ON stripe.customers(updated);

CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_customer ON stripe.subscriptions(customer);
CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_status ON stripe.subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_created ON stripe.subscriptions(created);
CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_current_period_end ON stripe.subscriptions(current_period_end);

CREATE INDEX IF NOT EXISTS idx_stripe_invoices_customer ON stripe.invoices(customer);
CREATE INDEX IF NOT EXISTS idx_stripe_invoices_subscription ON stripe.invoices(subscription);
CREATE INDEX IF NOT EXISTS idx_stripe_invoices_status ON stripe.invoices(status);
CREATE INDEX IF NOT EXISTS idx_stripe_invoices_created ON stripe.invoices(created);

CREATE INDEX IF NOT EXISTS idx_stripe_payment_intents_customer ON stripe.payment_intents(customer);
CREATE INDEX IF NOT EXISTS idx_stripe_payment_intents_status ON stripe.payment_intents(status);
CREATE INDEX IF NOT EXISTS idx_stripe_payment_intents_created ON stripe.payment_intents(created);

CREATE INDEX IF NOT EXISTS idx_stripe_products_active ON stripe.products(active);
CREATE INDEX IF NOT EXISTS idx_stripe_products_created ON stripe.products(created);

CREATE INDEX IF NOT EXISTS idx_stripe_prices_product ON stripe.prices(product);
CREATE INDEX IF NOT EXISTS idx_stripe_prices_active ON stripe.prices(active);
CREATE INDEX IF NOT EXISTS idx_stripe_prices_currency ON stripe.prices(currency);

CREATE INDEX IF NOT EXISTS idx_stripe_charges_customer ON stripe.charges(customer);
CREATE INDEX IF NOT EXISTS idx_stripe_charges_payment_intent ON stripe.charges(payment_intent);
CREATE INDEX IF NOT EXISTS idx_stripe_charges_status ON stripe.charges(status);
CREATE INDEX IF NOT EXISTS idx_stripe_charges_created ON stripe.charges(created);

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT USAGE ON SCHEMA stripe TO authenticated;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA stripe TO authenticated;

-- Create a simple function to check if setup was successful
CREATE OR REPLACE FUNCTION stripe.check_setup()
RETURNS TABLE(table_name TEXT, exists BOOLEAN) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.table_name::TEXT,
        (t.table_name IS NOT NULL) as exists
    FROM (
        VALUES 
            ('customers'),
            ('subscriptions'),
            ('invoices'),
            ('payment_intents'),
            ('products'),
            ('prices'),
            ('charges')
    ) AS expected(table_name)
    LEFT JOIN information_schema.tables t 
        ON t.table_schema = 'stripe' 
        AND t.table_name = expected.table_name;
END;
$$ LANGUAGE plpgsql;
