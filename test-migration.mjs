#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const SUPABASE_URL = 'https://xvqeiwrpbzpiqvwuvtpj.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function testConnection() {
  console.log('🔍 Testing Supabase connection...');
  
  try {
    // Test basic connection
    const { data, error } = await supabase
      .from('auth.users')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('❌ Connection failed:', error.message);
    } else {
      console.log('✅ Connection successful');
    }
    
    // Test creating a simple table
    console.log('\n🔧 Testing table creation...');
    
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS test_migration (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;
    
    const { error: createError } = await supabase.rpc('exec_sql', {
      sql_query: createTableSQL
    });
    
    if (createError) {
      console.log('❌ Table creation failed:', createError.message);
    } else {
      console.log('✅ Table creation successful');
    }
    
    // Test inserting data
    console.log('\n📝 Testing data insertion...');
    
    const { error: insertError } = await supabase
      .from('test_migration')
      .insert({ name: 'Test Migration' });
    
    if (insertError) {
      console.log('❌ Data insertion failed:', insertError.message);
    } else {
      console.log('✅ Data insertion successful');
    }
    
    // Clean up
    console.log('\n🧹 Cleaning up test table...');
    
    const dropTableSQL = 'DROP TABLE IF EXISTS test_migration;';
    
    const { error: dropError } = await supabase.rpc('exec_sql', {
      sql_query: dropTableSQL
    });
    
    if (dropError) {
      console.log('❌ Table cleanup failed:', dropError.message);
    } else {
      console.log('✅ Table cleanup successful');
    }
    
    console.log('\n🎉 All tests passed! Ready to run migrations.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testConnection();
