import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { VoiceProfileManager } from '@/lib/services/voice-profile-manager';
import { supabase } from '@/lib/supabase/client';

const voiceProfileManager = new VoiceProfileManager();

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: profileId } = await params;
    const supabaseClient = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { contentType, contentIds, characterName, projectId } = body;

    if (!contentType || !contentIds || !Array.isArray(contentIds)) {
      return NextResponse.json(
        { error: 'ContentType and contentIds array are required' },
        { status: 400 }
      );
    }

    // Check if user owns this profile
    const { data: profile, error: checkError } = await supabase
      .from('voice_profiles')
      .select('user_id, project_id')
      .eq('id', profileId)
      .single();

    if (checkError || !profile || profile.user_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Use provided projectId if available, otherwise use profile's project_id
    const targetProjectId = projectId || profile.project_id;

    let trainingTexts: string[] = [];

    switch (contentType) {
      case 'chapters': {
        // Fetch chapter content
        const { data: chapters, error: chaptersError } = await supabase
          .from('chapters')
          .select('content')
          .in('id', contentIds)
          .eq('project_id', targetProjectId);

        if (chaptersError) throw chaptersError;

        trainingTexts = chapters
          .map(ch => ch.content)
          .filter(content => content && content.length >= 100);
        break;
      }

      case 'character_dialogue': {
        // Fetch chapters with character dialogue
        const { data: chapters, error: chaptersError } = await supabase
          .from('chapters')
          .select('content')
          .eq('project_id', targetProjectId);

        if (chaptersError) throw chaptersError;

        // Extract dialogue for specific character
        const characterPattern = new RegExp(
          `"([^"]*)"[^"]*${characterName}|${characterName}[^"]*"([^"]*)"`,
          'gi'
        );

        chapters.forEach(ch => {
          if (!ch.content) return;
          
          const matches = ch.content.matchAll(characterPattern);
          for (const match of matches) {
            const dialogue = match[1] || match[2];
            if (dialogue && dialogue.length >= 50) {
              trainingTexts.push(dialogue);
            }
          }
        });
        break;
      }

      case 'story_bible': {
        // Fetch story bible entries
        const { data: entries, error: entriesError } = await supabase
          .from('story_bible')
          .select('content')
          .in('id', contentIds)
          .eq('project_id', targetProjectId);

        if (entriesError) throw entriesError;

        trainingTexts = entries
          .map(entry => entry.content)
          .filter(content => content && content.length >= 100);
        break;
      }

      case 'knowledge_base': {
        // Fetch knowledge base items
        const { data: items, error: itemsError } = await supabase
          .from('knowledge_items')
          .select('content')
          .in('id', contentIds)
          .eq('project_id', targetProjectId);

        if (itemsError) throw itemsError;

        trainingTexts = items
          .map(item => item.content)
          .filter(content => content && content.length >= 100);
        break;
      }

      default:
        return NextResponse.json(
          { error: 'Invalid content type' },
          { status: 400 }
        );
    }

    if (trainingTexts.length === 0) {
      return NextResponse.json(
        { error: 'No valid training content found' },
        { status: 400 }
      );
    }

    // Train the profile
    const success = await voiceProfileManager.trainVoiceProfile(
      profileId,
      trainingTexts,
      'project_content'
    );

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to train voice profile' },
        { status: 500 }
      );
    }

    // Fetch updated profile
    const { data: updatedProfile, error: fetchError } = await supabase
      .from('voice_profiles')
      .select('*')
      .eq('id', profileId)
      .single();

    if (fetchError) throw fetchError;

    return NextResponse.json({
      success: true,
      profile: updatedProfile,
      trainedSamples: trainingTexts.length,
      contentType,
    });
  } catch (error) {
    console.error('Error training from content:', error);
    return NextResponse.json(
      { error: 'Failed to train from content' },
      { status: 500 }
    );
  }
}