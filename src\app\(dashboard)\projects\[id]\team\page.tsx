'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { TeamManagement } from '@/components/collaboration/team-management'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ArrowLeft, Users, AlertCircle } from 'lucide-react'
import Link from 'next/link'
import { logger } from '@/lib/services/logger'
import { checkProjectAccess } from '@/lib/auth/collaboration-auth'
import { getUserTier } from '@/lib/subscription'
import type { Database } from '@/lib/supabase/types'

type Project = Database['public']['Tables']['projects']['Row']

export default function ProjectTeamPage() {
  const params = useParams()
  const router = useRouter()
  const projectId = params.id as string
  
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [user, setUser] = useState<any>(null)
  const [subscription, setSubscription] = useState<any>(null)
  const [hasAccess, setHasAccess] = useState(false)
  const [canManageTeam, setCanManageTeam] = useState(false)
  
  const supabase = createClient()
  
  useEffect(() => {
    loadData()
  }, [projectId])
  
  const loadData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Get current user
      const { data: { user: currentUser } } = await supabase.auth.getUser()
      if (!currentUser) {
        router.push('/login')
        return
      }
      setUser(currentUser)
      
      // Check project access
      const permissions = await checkProjectAccess(currentUser.id, projectId)
      if (!permissions.canView) {
        setError('You do not have access to this project')
        setHasAccess(false)
        return
      }
      
      setHasAccess(true)
      setCanManageTeam(permissions.canManageTeam)
      
      // Load project
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single()
      
      if (projectError) throw projectError
      setProject(projectData)
      
      // Get subscription info for the project owner
      const { data: subData } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', projectData.user_id)
        .eq('status', 'active')
        .single()
      
      if (subData) {
        setSubscription(subData)
      }
      
    } catch (err) {
      logger.error('Error loading team page data', err)
      setError('Failed to load team information')
    } finally {
      setLoading(false)
    }
  }
  
  if (loading) {
    return (
      <div className="container max-w-6xl mx-auto p-6 space-y-6">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-64 w-full" />
      </div>
    )
  }
  
  if (error || !hasAccess) {
    return (
      <div className="container max-w-6xl mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || 'You do not have permission to view this page'}
          </AlertDescription>
        </Alert>
        <Link href={`/projects/${projectId}`}>
          <Button variant="ghost" className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Project
          </Button>
        </Link>
      </div>
    )
  }
  
  if (!canManageTeam) {
    return (
      <div className="container max-w-6xl mx-auto p-6">
        <Alert>
          <Users className="h-4 w-4" />
          <AlertDescription>
            Only project owners and users with team management permissions can access this page.
          </AlertDescription>
        </Alert>
        <Link href={`/projects/${projectId}`}>
          <Button variant="ghost" className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Project
          </Button>
        </Link>
      </div>
    )
  }
  
  const userTier = getUserTier(subscription)
  const maxCollaborators = userTier?.limits.collaborators || 0
  
  return (
    <div className="container max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Link href={`/projects/${projectId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Project
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Team Management</h1>
        </div>
      </div>
      
      {/* Team Management Component */}
      {project && user && (
        <TeamManagement
          projectId={projectId}
          projectTitle={project.title}
          userId={user.id}
          maxCollaborators={maxCollaborators}
        />
      )}
    </div>
  )
}