-- Create book_summaries table for storing various marketing materials
CREATE TABLE IF NOT EXISTS book_summaries (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('elevator-pitch', 'back-cover', 'synopsis', 'query-letter', 'amazon-description')),
  content TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure one summary per type per project
  UNIQUE(project_id, type)
);

-- Add RLS policies
ALTER TABLE book_summaries ENABLE ROW LEVEL SECURITY;

-- Policy for users to manage their own summaries
CREATE POLICY "Users can manage their own book summaries"
  ON book_summaries
  FOR ALL
  USING (project_id IN (
    SELECT id FROM projects WHERE user_id = auth.uid()
  ));

-- Create updated_at trigger
CREATE TRIGGER update_book_summaries_updated_at
  BEFORE UPDATE ON book_summaries
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Add index for performance
CREATE INDEX idx_book_summaries_project_id ON book_summaries(project_id);
CREATE INDEX idx_book_summaries_type ON book_summaries(type);