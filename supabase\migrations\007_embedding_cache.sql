-- Migration: Create embedding cache table
-- Description: Creates a table to cache text embeddings for cost optimization

-- Create embedding_cache table
CREATE TABLE IF NOT EXISTS embedding_cache (
  cache_key TEXT PRIMARY KEY,
  text_hash TEXT NOT NULL,
  model TEXT NOT NULL,
  embedding vector(1536) NOT NULL,
  text_length INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for efficient lookups
CREATE INDEX IF NOT EXISTS idx_embedding_cache_model ON embedding_cache(model);
CREATE INDEX IF NOT EXISTS idx_embedding_cache_text_hash ON embedding_cache(text_hash);
CREATE INDEX IF NOT EXISTS idx_embedding_cache_last_accessed ON embedding_cache(last_accessed);

-- Create index for vector similarity search (if needed in future)
CREATE INDEX IF NOT EXISTS idx_embedding_cache_vector 
ON embedding_cache USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);

-- Function to update last_accessed timestamp
CREATE OR REPLACE FUNCTION update_embedding_last_accessed()
RETURNS TRIGGER AS $$
BEGIN
  NEW.last_accessed = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-update last_accessed on read
CREATE TRIGGER update_embedding_cache_last_accessed
BEFORE UPDATE ON embedding_cache
FOR EACH ROW
WHEN (OLD.* IS DISTINCT FROM NEW.*)
EXECUTE FUNCTION update_embedding_last_accessed();

-- Function to clean old cache entries
CREATE OR REPLACE FUNCTION clean_old_embeddings(days_old INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM embedding_cache
  WHERE last_accessed < NOW() - INTERVAL '1 day' * days_old;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT ALL ON embedding_cache TO authenticated;
GRANT EXECUTE ON FUNCTION clean_old_embeddings(INTEGER) TO authenticated;

-- Add RLS policies
ALTER TABLE embedding_cache ENABLE ROW LEVEL SECURITY;

-- Policy: Anyone can read cached embeddings
CREATE POLICY "Embeddings are publicly readable" ON embedding_cache
FOR SELECT USING (true);

-- Policy: Only authenticated users can insert embeddings
CREATE POLICY "Authenticated users can cache embeddings" ON embedding_cache
FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Policy: Only authenticated users can update last_accessed
CREATE POLICY "Authenticated users can update cache" ON embedding_cache
FOR UPDATE USING (auth.role() = 'authenticated');

-- Add comment explaining the table
COMMENT ON TABLE embedding_cache IS 'Cache for text embeddings to reduce API costs. Embeddings are deterministic so can be cached aggressively.';