'use client'

import React, { useState, useCallback, useEffect, useRef } from 'react'

interface UseResizablePanelProps {
  initialWidth?: number
  minWidth?: number
  maxWidth?: number
  onResize?: (width: number) => void
  side?: 'left' | 'right'
  responsive?: boolean
}

export function useResizablePanel({
  initialWidth = 384, // w-96 equivalent
  minWidth = 280,
  maxWidth = 600,
  onResize,
  side = 'right',
  responsive = true
}: UseResizablePanelProps = {}) {
  const [width, setWidth] = useState(initialWidth)
  const [isResizing, setIsResizing] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const startXRef = useRef(0)
  const startWidthRef = useRef(initialWidth)
  const rafRef = useRef<number>()
  const containerRef = useRef<HTMLDivElement>(null)

  // Get responsive constraints based on viewport
  const getResponsiveConstraints = useCallback(() => {
    if (!responsive) return { minWidth, maxWidth }

    const viewportWidth = window.innerWidth
    const responsiveMinWidth = Math.max(minWidth, Math.min(280, viewportWidth * 0.2))
    const responsiveMaxWidth = Math.min(maxWidth, viewportWidth * 0.6)

    return {
      minWidth: responsiveMinWidth,
      maxWidth: responsiveMaxWidth
    }
  }, [minWidth, maxWidth, responsive])

  // Handle pointer down (mouse + touch)
  const handlePointerDown = useCallback((e: React.PointerEvent) => {
    e.preventDefault()
    e.stopPropagation()

    setIsResizing(true)
    setIsDragging(true)
    startXRef.current = e.clientX
    startWidthRef.current = width

    // Add cursor style to body
    document.body.style.cursor = 'col-resize'
    document.body.style.userSelect = 'none'
    document.body.style.pointerEvents = 'none'

    // Capture pointer for better tracking
    if (e.currentTarget instanceof Element) {
      e.currentTarget.setPointerCapture(e.pointerId)
    }
  }, [width])

  // Optimized move handler with RAF
  const handlePointerMove = useCallback((e: PointerEvent) => {
    if (!isResizing) return

    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current)
    }

    rafRef.current = requestAnimationFrame(() => {
      const { minWidth: responsiveMin, maxWidth: responsiveMax } = getResponsiveConstraints()
      const deltaX = side === 'right'
        ? startXRef.current - e.clientX
        : e.clientX - startXRef.current

      const newWidth = Math.min(
        Math.max(startWidthRef.current + deltaX, responsiveMin),
        responsiveMax
      )

      setWidth(newWidth)
      onResize?.(newWidth)
    })
  }, [isResizing, side, getResponsiveConstraints, onResize])

  const handlePointerUp = useCallback((e: PointerEvent) => {
    setIsResizing(false)
    setIsDragging(false)

    // Clean up RAF
    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current)
    }

    // Restore body styles
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
    document.body.style.pointerEvents = ''

    // Release pointer capture
    if (e.target instanceof Element) {
      e.target.releasePointerCapture(e.pointerId)
    }
  }, [])

  // Event listeners with proper cleanup
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('pointermove', handlePointerMove, { passive: false })
      document.addEventListener('pointerup', handlePointerUp)
      document.addEventListener('pointercancel', handlePointerUp)

      return () => {
        document.removeEventListener('pointermove', handlePointerMove)
        document.removeEventListener('pointerup', handlePointerUp)
        document.removeEventListener('pointercancel', handlePointerUp)
      }
    }
    return undefined
  }, [isResizing, handlePointerMove, handlePointerUp])

  // Handle viewport resize
  useEffect(() => {
    if (!responsive) return

    const handleResize = () => {
      const { minWidth: responsiveMin, maxWidth: responsiveMax } = getResponsiveConstraints()
      setWidth(prevWidth => Math.min(Math.max(prevWidth, responsiveMin), responsiveMax))
    }

    window.addEventListener('resize', handleResize, { passive: true })
    return () => window.removeEventListener('resize', handleResize)
  }, [responsive, getResponsiveConstraints])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current)
      }
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
      document.body.style.pointerEvents = ''
    }
  }, [])

  // Enhanced resize handle with better UX
  const ResizeHandle = React.forwardRef<HTMLDivElement>((props, ref) => (
    <div
      ref={ref}
      className={`
        absolute ${side === 'right' ? 'left-0' : 'right-0'} top-0 bottom-0 w-2
        cursor-col-resize touch-none select-none
        hover:bg-primary/30 active:bg-primary/50
        transition-all duration-200 ease-out
        ${isResizing ? 'bg-primary/50 shadow-lg' : 'bg-transparent hover:bg-primary/20'}
        ${isDragging ? 'bg-primary/60' : ''}
        group z-50
      `}
      onPointerDown={handlePointerDown}
      style={{ touchAction: 'none' }}
      {...props}
    >
      {/* Extended hit area */}
      <div className={`
        absolute ${side === 'right' ? 'left-0' : 'right-0'} top-0 bottom-0
        ${side === 'right' ? 'w-6 -translate-x-2' : 'w-6 translate-x-2'}
      `} />

      {/* Visual indicator line */}
      <div className={`
        absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2
        w-0.5 h-12 bg-primary rounded-full
        transition-all duration-200 ease-out
        ${isResizing || isDragging ? 'opacity-100 h-16' : 'opacity-0 group-hover:opacity-60'}
      `} />

      {/* Grip dots */}
      <div className={`
        absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2
        flex flex-col gap-1
        transition-opacity duration-200
        ${isResizing || isDragging ? 'opacity-100' : 'opacity-0 group-hover:opacity-80'}
      `}>
        <div className="w-1 h-1 bg-primary rounded-full" />
        <div className="w-1 h-1 bg-primary rounded-full" />
        <div className="w-1 h-1 bg-primary rounded-full" />
      </div>

      {/* Resize feedback overlay */}
      {isResizing && (
        <div className="absolute inset-0 bg-primary/10 animate-pulse" />
      )}
    </div>
  ))

  // Add display name for debugging
  ResizeHandle.displayName = 'ResizeHandle'

  return {
    width,
    isResizing,
    isDragging,
    ResizeHandle,
    setWidth,
    containerRef,
    // Utility functions
    resetWidth: () => setWidth(initialWidth),
    getConstraints: getResponsiveConstraints
  }
}
