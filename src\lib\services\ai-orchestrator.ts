import { vercelAIClient } from '@/lib/ai/vercel-ai-client';
import { logger } from '@/lib/services/logger';

import { BaseService } from './base-service';
import { AIAgent, WritingTask, ServiceResponse } from './types';
import { AI_MODELS } from '@/lib/config/ai-settings';

export class AIOrchestrator extends BaseService {
  private agents: Map<string, AIAgent> = new Map();
  private taskQueue: WritingTask[] = [];
  private processingTasks: Map<string, WritingTask> = new Map();
  private aiClient = vercelAIClient;

  constructor() {
    super({
      name: 'ai-orchestrator',
      version: '2.0.0',
      status: 'inactive',
      endpoints: ['/api/orchestrator/agents', '/api/orchestrator/tasks'],
      dependencies: [],
      healthCheck: '/api/orchestrator/health'
    });
  }

  async initialize(): Promise<void> {
    this.initializeDefaultAgents();
    this.startTaskProcessor();
    this.isInitialized = true;
    this.setStatus('active');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string; uptime: number }>> {
    const activeAgents = Array.from(this.agents.values()).filter(a => a.status !== 'error').length;
    const queueSize = this.taskQueue.length;
    
    return this.createResponse(true, {
      status: `${activeAgents}/${this.agents.size} agents active, ${queueSize} tasks queued`,
      uptime: Date.now() - (this.isInitialized ? Date.now() - 1000 : Date.now()),
    });
  }

  async shutdown(): Promise<void> {
    this.taskQueue = [];
    this.processingTasks.clear();
    this.setStatus('inactive');
  }

  async registerAgent(agent: AIAgent): Promise<ServiceResponse<string>> {
    return this.withErrorHandling(async () => {
      this.agents.set(agent.id, agent);
      return `Agent ${agent.name} registered successfully`;
    });
  }

  async submitTask(task: WritingTask): Promise<ServiceResponse<string>> {
    return this.withErrorHandling(async () => {
      task.status = 'pending';
      task.createdAt = Date.now();
      this.taskQueue.push(task);
      
      // Try to assign immediately if agent is available
      await this.processNextTask();
      
      return task.id;
    });
  }

  async getTaskStatus(taskId: string): Promise<ServiceResponse<WritingTask | null>> {
    return this.withErrorHandling(async () => {
      const processingTask = this.processingTasks.get(taskId);
      if (processingTask) return processingTask;
      
      const queuedTask = this.taskQueue.find(t => t.id === taskId);
      return queuedTask || null;
    });
  }

  async getAvailableAgents(): Promise<ServiceResponse<AIAgent[]>> {
    return this.withErrorHandling(async () => {
      return Array.from(this.agents.values()).filter(agent => agent.status === 'idle');
    });
  }

  async reassignTask(taskId: string, agentId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const task = this.processingTasks.get(taskId) || 
                  this.taskQueue.find(t => t.id === taskId);
      
      if (!task) throw new Error('Task not found');
      
      const agent = this.agents.get(agentId);
      if (!agent) throw new Error('Agent not found');
      
      if (task.status === 'processing' && task.assignedAgent) {
        const currentAgent = this.agents.get(task.assignedAgent);
        if (currentAgent) {
          currentAgent.status = 'idle';
          currentAgent.currentTask = undefined;
        }
      }
      
      task.assignedAgent = agentId;
      task.status = 'pending';
      
      if (!this.taskQueue.find(t => t.id === taskId)) {
        this.taskQueue.unshift(task);
      }
      
      this.processingTasks.delete(taskId);
      await this.processNextTask();
      
      return true;
    });
  }

  private initializeDefaultAgents(): void {
    const defaultAgents: AIAgent[] = [
      {
        id: 'creative-writer-1',
        type: 'writing',
        name: 'Creative Writer',
        description: 'Specializes in creative writing and narrative generation',
        capabilities: ['dialogue', 'description', 'plot-development', 'character-voice'],
        status: 'idle'
      },
      {
        id: 'prose-editor-1',
        type: 'editing',
        name: 'Prose Editor',
        description: 'Focuses on prose quality and style improvement',
        capabilities: ['grammar', 'style', 'flow', 'clarity'],
        status: 'idle'
      },
      {
        id: 'story-analyst-1',
        type: 'analysis',
        name: 'Story Analyst',
        description: 'Analyzes plot structure and character development',
        capabilities: ['plot-analysis', 'character-arcs', 'pacing', 'consistency'],
        status: 'idle'
      },
      {
        id: 'research-assistant-1',
        type: 'research',
        name: 'Research Assistant',
        description: 'Gathers information and fact-checks content',
        capabilities: ['fact-checking', 'research', 'world-building', 'historical-accuracy'],
        status: 'idle'
      }
    ];

    defaultAgents.forEach(agent => this.agents.set(agent.id, agent));
  }

  private async processNextTask(): Promise<void> {
    if (this.taskQueue.length === 0) return;

    const availableAgents = Array.from(this.agents.values())
      .filter(agent => agent.status === 'idle');

    if (availableAgents.length === 0) return;

    const task = this.taskQueue.shift();
    if (!task) return;

    // Find best agent for task
    const suitableAgent = this.findBestAgent(task, availableAgents);
    if (!suitableAgent) {
      this.taskQueue.unshift(task); // Put back in queue
      return;
    }

    // Assign task to agent
    task.assignedAgent = suitableAgent.id;
    task.status = 'processing';
    
    suitableAgent.status = 'processing';
    suitableAgent.currentTask = {
      id: task.id,
      type: task.type,
      progress: 0,
      startTime: Date.now()
    };

    this.processingTasks.set(task.id, task);

    // Execute task asynchronously
    this.executeTask(task, suitableAgent).catch(error => {
      logger.error(`Task execution failed for ${task.id}:`, error);
      task.status = 'failed';
      suitableAgent.status = 'error';
    });
  }

  private findBestAgent(task: WritingTask, availableAgents: AIAgent[]): AIAgent | null {
    const taskTypeToAgentType: Record<string, string> = {
      'generate': 'writing',
      'edit': 'editing',
      'expand': 'writing',
      'rewrite': 'editing'
    };

    const preferredType = taskTypeToAgentType[task.type] || 'writing';
    
    // First, try to find agent with matching type
    let suitableAgent = availableAgents.find(agent => agent.type === preferredType);
    
    // If no exact match, find any available agent
    if (!suitableAgent) {
      suitableAgent = availableAgents[0];
    }

    return suitableAgent || null;
  }

  private async executeTask(task: WritingTask, agent: AIAgent): Promise<void> {
    try {
      const result = await this.processWithAI(task, agent);
      
      const aiResult = result as { content?: string; metadata?: Record<string, unknown>; quality?: number };
      task.output = {
        content: String(aiResult.content || ''),
        metadata: aiResult.metadata || {},
        quality: Number(aiResult.quality || 0.8)
      };
      task.status = 'completed';
      task.completedAt = Date.now();
      
      agent.status = 'idle';
      agent.currentTask = undefined;
      
      this.processingTasks.delete(task.id);
      
      // Process next task in queue
      setTimeout(() => this.processNextTask(), 100);
      
    } catch {
      task.status = 'failed';
      agent.status = 'error';
      
      setTimeout(() => {
        agent.status = 'idle'; // Reset agent after error cooldown
      }, 30000);
    }
  }

  private async processWithAI(task: WritingTask, agent: AIAgent): Promise<Record<string, unknown>> {
    const systemPrompt = this.getSystemPrompt(agent);
    const userPrompt = this.buildUserPrompt(task);
    const startTime = Date.now();

    try {
      const content = await this.aiClient.generateText(
        userPrompt,
        {
          model: AI_MODELS.PRIMARY,
          temperature: 0.7,
          maxTokens: 2000,
          systemPrompt
        }
      );

      return {
        content,
        metadata: {
          model: AI_MODELS.PRIMARY,
          agent: agent.name,
          processingTime: Date.now() - startTime,
          tokensUsed: content.length // Approximate token count
        },
        quality: this.assessQuality(content, task.type)
      };
    } catch (error) {
      logger.error(`AI processing failed for agent ${agent.name}:`, error);
      throw error;
    }
  }

  private getSystemPrompt(agent: AIAgent): string {
    const basePrompt = `You are ${agent.name}, an AI writing assistant specializing in ${agent.description.toLowerCase()}.`;
    
    const capabilitiesPrompt = `Your key capabilities include: ${agent.capabilities.join(', ')}.`;
    
    const guidelines = `
    Follow these guidelines:
    - Maintain consistency with the established story world and characters
    - Write in a clear, engaging style appropriate for the target audience
    - Focus on quality over quantity
    - Provide specific, actionable content
    - If editing, preserve the author's voice while improving clarity and flow
    `;

    return `${basePrompt}\n\n${capabilitiesPrompt}\n\n${guidelines}`;
  }

  private buildUserPrompt(task: WritingTask): string {
    let prompt = `Task Type: ${task.type}\n`;
    
    if (task.input.content) {
      prompt += `\nContent to work with:\n${task.input.content}\n`;
    }
    
    if (task.input.prompt) {
      prompt += `\nSpecific instructions: ${task.input.prompt}\n`;
    }
    
    if (task.input.context) {
      prompt += `\nContext: ${JSON.stringify(task.input.context, null, 2)}\n`;
    }
    
    if (task.input.requirements) {
      prompt += `\nRequirements:\n${task.input.requirements.map(req => `- ${req}`).join('\n')}\n`;
    }

    return prompt;
  }

  private assessQuality(content: string, taskType: string): number {
    let score = 50; // Base score
    
    // Length check
    if (content.length > 100) score += 10;
    if (content.length > 500) score += 10;
    
    // Basic quality indicators
    const sentences = content.split(/[.!?]+/).filter(s => s.trim());
    if (sentences.length > 1) score += 10;
    
    // Check for varied sentence structure
    const avgSentenceLength = content.split(/\s+/).length / sentences.length;
    if (avgSentenceLength > 8 && avgSentenceLength < 25) score += 10;
    
    // Task-specific quality checks
    if (taskType === 'generate' && content.includes('"')) score += 5; // Has dialogue
    if (taskType === 'edit' && content.length > 0) score += 10; // Has content
    
    return Math.min(100, Math.max(0, score));
  }

  private startTaskProcessor(): void {
    // Process tasks every 2 seconds
    setInterval(() => {
      this.processNextTask();
    }, 2000);
  }
}