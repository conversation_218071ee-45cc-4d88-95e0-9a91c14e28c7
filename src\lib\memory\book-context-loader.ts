import { createClient } from '@/lib/supabase/server'
import { logger } from '@/lib/services/logger';

import type { BookContext, CharacterProfile, StoryStructure } from '@/lib/types/agents'

export class BookContextLoader {
  private supabase = createClient()

  async loadProjectContext(projectId: string): Promise<BookContext | null> {
    try {
      // Load project data
      const { data: project, error: projectError } = await (await this.supabase)
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single()

      if (projectError || !project) {
        return null
      }

      // Load story structure (from story_arcs)
      const { data: storyArcs } = await (await this.supabase)
        .from('story_arcs')
        .select('*')
        .eq('project_id', projectId)
        .order('act_number')

      // Load characters
      const { data: characters } = await (await this.supabase)
        .from('characters')
        .select('*')
        .eq('project_id', projectId)

      // Load story bible
      const { data: storyBibleEntries } = await (await this.supabase)
        .from('story_bible')
        .select('*')
        .eq('project_id', projectId)

      // Load completed chapters
      const { data: chapters } = await (await this.supabase)
        .from('chapters')
        .select('*')
        .eq('project_id', projectId)
        .eq('status', 'complete')
        .order('chapter_number')

      // Reconstruct story structure
      let storyStructure: StoryStructure | undefined
      if (storyArcs && storyArcs.length > 0) {
        storyStructure = {
          title: project.title,
          genre: project.primary_genre || 'Fiction',
          themes: project.major_themes || [],
          acts: storyArcs.map(arc => ({
            number: arc.act_number || 1,
            title: `Act ${arc.act_number}`,
            description: arc.description || '',
            keyEvents: Array.isArray(arc.key_events) ? arc.key_events : [],
            wordCount: Math.round((project.target_word_count || 80000) / storyArcs.length)
          })),
          conflicts: [],
          timeline: []
        }
      }

      // Reconstruct character profiles
      const characterProfiles: CharacterProfile[] = (characters || []).map(char => ({
        id: char.id,
        name: char.name,
        role: char.role as 'protagonist' | 'antagonist' | 'supporting' | 'minor',
        description: char.description || '',
        backstory: char.backstory || '',
        personality: char.personality_traits || { traits: [], motivations: [], fears: [], goals: [] },
        physicalDescription: '',
        relationships: char.relationships || [],
        arc: char.character_arc || { startingPoint: '', transformation: '', endingPoint: '' },
        voiceCharacteristics: []
      }))

      // Reconstruct story bible
      const worldRules: Record<string, string> = {}
      const plotThreads: Array<{ id: string; description: string; status: 'active' | 'resolved' }> = []
      
      storyBibleEntries?.forEach(entry => {
        if (entry.entry_type === 'world_rule') {
          worldRules[entry.entry_key] = entry.entry_data?.value || ''
        }
      })

      // Build context
      const context: BookContext = {
        projectId,
        projectSelections: {
          primaryGenre: project.primary_genre,
          subgenre: project.subgenre,
          customGenre: project.custom_genre,
          narrativeVoice: project.narrative_voice,
          tense: project.tense,
          toneOptions: project.tone_options,
          writingStyle: project.writing_style,
          customStyleDescription: project.custom_style_description,
          structureType: project.structure_type,
          pacingPreference: project.pacing_preference,
          chapterStructure: project.chapter_structure,
          timelineComplexity: project.timeline_complexity,
          customStructureNotes: project.custom_structure_notes,
          protagonistTypes: project.protagonist_types,
          antagonistTypes: project.antagonist_types,
          characterComplexity: project.character_complexity,
          characterArcTypes: project.character_arc_types,
          customCharacterConcepts: project.custom_character_concepts,
          timePeriod: project.time_period,
          geographicSetting: project.geographic_setting,
          worldType: project.world_type,
          magicTechLevel: project.magic_tech_level,
          customSettingDescription: project.custom_setting_description,
          majorThemes: project.major_themes,
          philosophicalThemes: project.philosophical_themes,
          socialThemes: project.social_themes,
          customThemes: project.custom_themes,
          targetAudience: project.target_audience,
          contentRating: project.content_rating,
          contentWarnings: project.content_warnings,
          culturalSensitivityNotes: project.cultural_sensitivity_notes,
          projectScope: project.project_scope,
          seriesType: project.series_type,
          interconnectionLevel: project.interconnection_level,
          customScopeDescription: project.custom_scope_description,
          targetWordCount: project.target_word_count,
          targetChapters: project.target_chapters,
          chapterCountType: project.chapter_count_type,
          povCharacterCount: project.pov_character_count,
          povCharacterType: project.pov_character_type,
          researchNeeds: project.research_needs,
          factCheckingLevel: project.fact_checking_level,
          customResearchNotes: project.custom_research_notes
        },
        storyStructure,
        characters: characterProfiles,
        completedChapters: (chapters || []).map(chapter => ({
          chapterNumber: chapter.chapter_number,
          title: chapter.title || '',
          content: chapter.content || '',
          wordCount: chapter.actual_word_count,
          scenes: [],
          characterVoices: {},
          consistencyNotes: []
        })),
        storyBible: {
          worldRules,
          timeline: [],
          characterStates: {},
          plotThreads
        },
        currentChapter: (chapters?.length || 0) + 1,
        metadata: {
          totalWordCount: project.current_word_count,
          chaptersCompleted: chapters?.length || 0,
          lastUpdated: project.updated_at
        }
      }

      return context
    } catch (error) {
      logger.error('Error loading project context:', error);
      return null
    }
  }

  async saveChapterContent(
    projectId: string,
    chapterNumber: number,
    title: string,
    content: string,
    wordCount: number
  ): Promise<boolean> {
    try {
      const { error } = await (await this.supabase)
        .from('chapters')
        .upsert({
          project_id: projectId,
          chapter_number: chapterNumber,
          title,
          content,
          actual_word_count: wordCount,
          status: 'complete'
        })

      if (error) {
        logger.error('Error saving chapter:', error);
        return false
      }

      // Update project word count
      const { data: chapters } = await (await this.supabase)
        .from('chapters')
        .select('actual_word_count')
        .eq('project_id', projectId)

      const totalWordCount = chapters?.reduce((sum, chapter) => sum + (chapter.actual_word_count || 0), 0) || 0

      await (await this.supabase)
        .from('projects')
        .update({ 
          current_word_count: totalWordCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId)

      return true
    } catch (error) {
      logger.error('Error saving chapter content:', error);
      return false
    }
  }
}