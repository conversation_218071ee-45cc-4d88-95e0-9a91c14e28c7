'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { PanelPluginManager } from './plugin-manager'
import { PanelPlugin, PanelState, PanelLayout, PanelEvent } from './types'

interface PanelContextValue {
  manager: PanelPluginManager
  plugins: PanelPlugin[]
  panelStates: Map<string, PanelState>
  activeLayout: PanelLayout
  layouts: PanelLayout[]
  togglePanel: (id: string) => void
  updatePanelState: (id: string, updates: Partial<PanelState>) => void
  saveLayout: (name: string, description?: string) => void
  loadLayout: (id: string) => void
  resetToDefaultLayout: () => void
}

const PanelContext = createContext<PanelContextValue | null>(null)

export function usePanels() {
  const context = useContext(PanelContext)
  if (!context) {
    throw new Error('usePanels must be used within a PanelProvider')
  }
  return context
}

interface PanelProviderProps {
  children: ReactNode
  initialLayout?: string
}

export function PanelProvider({ children, initialLayout = 'writing' }: PanelProviderProps) {
  const [manager] = useState(() => PanelPluginManager.getInstance())
  const [plugins, setPlugins] = useState<PanelPlugin[]>([])
  const [panelStates, setPanelStates] = useState<Map<string, PanelState>>(new Map())
  const [activeLayout, setActiveLayout] = useState<PanelLayout>(manager.getCurrentLayout())
  const [layouts, setLayouts] = useState<PanelLayout[]>(manager.getLayouts())

  useEffect(() => {
    // Initialize with default layout if specified
    if (initialLayout && initialLayout !== manager.getCurrentLayout().id) {
      manager.loadLayout(initialLayout)
    }

    // Update state from manager
    const updateFromManager = () => {
      setPlugins(manager.getAllPlugins())
      // Get panel states for all plugins
      const states = new Map<string, PanelState>()
      manager.getAllPlugins().forEach(plugin => {
        const state = manager.getPanelState(plugin.id)
        if (state) {
          states.set(plugin.id, state)
        }
      })
      setPanelStates(states)
      setActiveLayout(manager.getCurrentLayout())
      setLayouts(manager.getLayouts())
    }

    // Initial update
    updateFromManager()

    // Subscribe to manager events
    const handleEvent = (event: PanelEvent) => {
      updateFromManager()
    }

    // Subscribe to all events
    manager.on('*', handleEvent)

    return () => {
      manager.off('*', handleEvent)
    }
  }, [manager, initialLayout])

  const contextValue: PanelContextValue = {
    manager,
    plugins,
    panelStates,
    activeLayout,
    layouts,
    togglePanel: (id) => manager.togglePanel(id),
    updatePanelState: (id, updates) => manager.updatePanelState(id, updates),
    saveLayout: (name, description) => manager.saveLayout(name, description),
    loadLayout: (id) => manager.loadLayout(id),
    resetToDefaultLayout: () => manager.resetToDefaultLayout()
  }

  return (
    <PanelContext.Provider value={contextValue}>
      {children}
    </PanelContext.Provider>
  )
}