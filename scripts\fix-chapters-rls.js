const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://xvqeiwrpbzpiqvwuvtpj.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function fixChaptersRLS() {
  console.log('Fixing chapters RLS policies...')
  
  try {
    // Enable RLS on chapters table
    const { error: enableError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE chapters ENABLE ROW LEVEL SECURITY;'
    })
    
    if (enableError) {
      console.log('RLS already enabled or error:', enableError.message)
    } else {
      console.log('✅ RLS enabled on chapters table')
    }

    // Drop existing policies
    const dropPolicies = [
      'DROP POLICY IF EXISTS "Users can manage own chapters" ON chapters;',
      'DROP POLICY IF EXISTS "Users can access own project chapters" ON chapters;'
    ]
    
    for (const sql of dropPolicies) {
      const { error } = await supabase.rpc('exec_sql', { sql })
      if (error) {
        console.log('Policy drop error (expected):', error.message)
      }
    }

    // Create new policies
    const policies = [
      `CREATE POLICY "Users can view own project chapters" ON chapters
        FOR SELECT USING (
          project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
          )
        );`,
      
      `CREATE POLICY "Users can insert own project chapters" ON chapters
        FOR INSERT WITH CHECK (
          project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
          )
        );`,
      
      `CREATE POLICY "Users can update own project chapters" ON chapters
        FOR UPDATE USING (
          project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
          )
        );`,
      
      `CREATE POLICY "Users can delete own project chapters" ON chapters
        FOR DELETE USING (
          project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
          )
        );`
    ]

    for (const [index, sql] of policies.entries()) {
      const { error } = await supabase.rpc('exec_sql', { sql })
      if (error) {
        console.error(`❌ Error creating policy ${index + 1}:`, error.message)
      } else {
        console.log(`✅ Created policy ${index + 1}`)
      }
    }

    console.log('🎉 Chapters RLS policies fixed!')
    
  } catch (error) {
    console.error('❌ Error fixing RLS policies:', error)
  }
}

fixChaptersRLS()
