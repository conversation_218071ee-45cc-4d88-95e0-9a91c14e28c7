'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Heart, 
  Smile, 
  Frown, 
  Zap, 
  AlertTriangle,
  Sunrise,
  TrendingUp,
  BarChart3
} from 'lucide-react';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Area,
  AreaChart,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  BarChart,
  Bar,
} from 'recharts';
import type { EmotionalData } from '@/lib/types/analysis';
import type { ChartTooltipProps } from '@/lib/types/charts';

interface EmotionalJourneyMapperProps {
  emotionalData: EmotionalData[];
  projectId: string;
  className?: string;
}

export function EmotionalJourneyMapper({ emotionalData, className }: EmotionalJourneyMapperProps) {
  const [viewMode, setViewMode] = useState<'journey' | 'intensity' | 'distribution' | 'transitions'>('journey');
  const [selectedChapter, setSelectedChapter] = useState<number | null>(null);
  const [emotionFilter, setEmotionFilter] = useState<string>('all');

  const getEmotionColor = (emotion: string) => {
    const colors = {
      joy: '#22c55e',      // Green
      sadness: '#3b82f6',  // Blue
      anger: '#ef4444',    // Red
      fear: '#8b5cf6',     // Purple
      surprise: '#f59e0b', // Orange
      tension: '#dc2626',  // Dark Red
      hope: '#10b981',     // Emerald
      despair: '#6b7280',  // Gray
    };
    return colors[emotion as keyof typeof colors] || '#6b7280';
  };

  const getEmotionIcon = (emotion: string) => {
    switch (emotion) {
      case 'joy':
      case 'hope':
        return <Smile className="w-4 h-4" />;
      case 'sadness':
      case 'despair':
        return <Frown className="w-4 h-4" />;
      case 'anger':
        return <Zap className="w-4 h-4" />;
      case 'fear':
        return <AlertTriangle className="w-4 h-4" />;
      case 'surprise':
        return <Sunrise className="w-4 h-4" />;
      case 'tension':
        return <TrendingUp className="w-4 h-4" />;
      default:
        return <Heart className="w-4 h-4" />;
    }
  };

  // Calculate overall emotional statistics
  const avgIntensity = emotionalData.reduce((acc, data) => acc + data.intensity, 0) / emotionalData.length;
  const emotionalVariance = emotionalData.reduce((acc, data) => {
    const range = typeof data.emotionalRange === 'number' ? data.emotionalRange : data.emotionalRange.variance;
    return acc + range;
  }, 0) / emotionalData.length;
  
  // Find emotional peaks and valleys
  const emotionalPeaks = emotionalData.filter(data => data.intensity > avgIntensity + 20);
  const emotionalValleys = emotionalData.filter(data => data.intensity < avgIntensity - 20);

  // Emotion distribution analysis
  const emotionTotals = emotionalData.reduce((acc, data) => {
    Object.entries(data.emotions).forEach(([emotion, value]) => {
      acc[emotion] = (acc[emotion] || 0) + value;
    });
    return acc;
  }, {} as Record<string, number>);

  const emotionDistribution = Object.entries(emotionTotals).map(([emotion, total]) => ({
    emotion,
    value: total / emotionalData.length,
    color: getEmotionColor(emotion),
  }));

  // Prepare radar chart data for selected chapter
  const selectedChapterData = selectedChapter 
    ? emotionalData.find(data => data.chapter === selectedChapter)
    : null;

  const radarData = selectedChapterData 
    ? Object.entries(selectedChapterData.emotions).map(([emotion, value]) => ({
        emotion: emotion.charAt(0).toUpperCase() + emotion.slice(1),
        value,
        fullMark: 100,
      }))
    : [];


  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Heart className="w-5 h-5 mr-2" />
            Emotional Journey Analysis
          </CardTitle>
          <CardDescription>
            Track emotional beats, intensity patterns, and reader engagement throughout your story
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-3 bg-red-50 dark:bg-red-950/20 rounded-lg">
              <TrendingUp className="w-5 h-5 mx-auto mb-1 text-red-600" />
              <div className="text-lg font-bold text-red-600">{emotionalPeaks.length}</div>
              <div className="text-xs text-red-600">Emotional Peaks</div>
            </div>
            <div className="text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
              <Frown className="w-5 h-5 mx-auto mb-1 text-blue-600" />
              <div className="text-lg font-bold text-blue-600">{emotionalValleys.length}</div>
              <div className="text-xs text-blue-600">Emotional Valleys</div>
            </div>
            <div className="text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
              <Heart className="w-5 h-5 mx-auto mb-1 text-green-600" />
              <div className="text-lg font-bold text-green-600">{Math.round(avgIntensity)}%</div>
              <div className="text-xs text-green-600">Avg Intensity</div>
            </div>
            <div className="text-center p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
              <BarChart3 className="w-5 h-5 mx-auto mb-1 text-purple-600" />
              <div className="text-lg font-bold text-purple-600">{Math.round(emotionalVariance)}%</div>
              <div className="text-xs text-purple-600">Emotional Range</div>
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center space-x-4 mb-4">
            <Select value={viewMode} onValueChange={(value: 'journey' | 'intensity' | 'distribution' | 'transitions') => setViewMode(value)}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="journey">Emotional Journey</SelectItem>
                <SelectItem value="intensity">Intensity Curve</SelectItem>
                <SelectItem value="distribution">Emotion Distribution</SelectItem>
                <SelectItem value="transitions">Emotional Transitions</SelectItem>
              </SelectContent>
            </Select>

            <Select value={emotionFilter} onValueChange={setEmotionFilter}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Emotions</SelectItem>
                <SelectItem value="joy">Joy</SelectItem>
                <SelectItem value="sadness">Sadness</SelectItem>
                <SelectItem value="anger">Anger</SelectItem>
                <SelectItem value="fear">Fear</SelectItem>
                <SelectItem value="surprise">Surprise</SelectItem>
                <SelectItem value="tension">Tension</SelectItem>
                <SelectItem value="hope">Hope</SelectItem>
                <SelectItem value="despair">Despair</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Main Visualization */}
      <Card>
        <CardHeader>
          <CardTitle>
            {viewMode === 'journey' && 'Emotional Journey Timeline'}
            {viewMode === 'intensity' && 'Emotional Intensity Curve'}
            {viewMode === 'distribution' && 'Emotion Distribution Analysis'}
            {viewMode === 'transitions' && 'Emotional Transition Patterns'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            {viewMode === 'journey' && (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={emotionalData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="chapter" 
                    label={{ value: 'Chapter', position: 'insideBottom', offset: -10 }}
                  />
                  <YAxis 
                    label={{ value: 'Emotion Level', angle: -90, position: 'insideLeft' }}
                    domain={[0, 100]}
                  />
                  <Tooltip
                    content={(props: ChartTooltipProps) => {
                      const { active, payload, label } = props;
                      if (active && payload && payload.length) {
                        const data = payload[0]?.payload;
                        if (!data) return null;
                        return (
                          <div className="bg-white dark:bg-slate-800 p-3 border rounded shadow">
                            <p className="font-medium">Chapter {label}</p>
                            <p className="text-blue-600">Dominant: {String(data.dominantEmotion)}</p>
                            <p className="text-green-600">Intensity: {String(data.intensity)}%</p>
                            <div className="mt-2 space-y-1">
                              {Object.entries((data as Record<string, unknown>).emotions || {}).map(([emotion, value]: [string, unknown]) => (
                                <p key={emotion} style={{ color: getEmotionColor(emotion) }}>
                                  {emotion}: {typeof value === 'number' ? value : String(value)}%
                                </p>
                              ))}
                            </div>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                  {emotionFilter === 'all' ? (
                    <>
                      <Line type="monotone" dataKey="emotions.joy" stroke={getEmotionColor('joy')} strokeWidth={2} name="Joy" />
                      <Line type="monotone" dataKey="emotions.sadness" stroke={getEmotionColor('sadness')} strokeWidth={2} name="Sadness" />
                      <Line type="monotone" dataKey="emotions.anger" stroke={getEmotionColor('anger')} strokeWidth={2} name="Anger" />
                      <Line type="monotone" dataKey="emotions.fear" stroke={getEmotionColor('fear')} strokeWidth={2} name="Fear" />
                      <Line type="monotone" dataKey="emotions.surprise" stroke={getEmotionColor('surprise')} strokeWidth={2} name="Surprise" />
                      <Line type="monotone" dataKey="emotions.tension" stroke={getEmotionColor('tension')} strokeWidth={3} name="Tension" />
                    </>
                  ) : (
                    <Line 
                      type="monotone" 
                      dataKey={`emotions.${emotionFilter}`} 
                      stroke={getEmotionColor(emotionFilter)} 
                      strokeWidth={3} 
                      name={emotionFilter.charAt(0).toUpperCase() + emotionFilter.slice(1)}
                    />
                  )}
                </LineChart>
              </ResponsiveContainer>
            )}

            {viewMode === 'intensity' && (
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={emotionalData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="chapter" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip />
                  <Area 
                    type="monotone" 
                    dataKey="intensity" 
                    stroke="#ef4444" 
                    fill="#ef4444" 
                    fillOpacity={0.3}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="emotionalRange" 
                    stroke="#3b82f6" 
                    strokeWidth={2}
                    name="Emotional Range"
                  />
                </AreaChart>
              </ResponsiveContainer>
            )}

            {viewMode === 'distribution' && (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={emotionDistribution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="emotion" />
                  <YAxis />
                  <Tooltip />
                  <Bar 
                    dataKey="value" 
                    fill="#8884d8"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            )}

            {viewMode === 'transitions' && selectedChapterData && (
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart data={radarData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="emotion" />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} />
                  <Radar 
                    name="Emotions" 
                    dataKey="value" 
                    stroke="#8884d8" 
                    fill="#8884d8" 
                    fillOpacity={0.3}
                  />
                </RadarChart>
              </ResponsiveContainer>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Chapter Selector for Detailed Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Chapter-by-Chapter Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {emotionalData.map((data) => (
              <div
                key={data.chapter}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedChapter === data.chapter
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20'
                    : 'border-slate-200 hover:border-slate-300'
                }`}
                onClick={() => setSelectedChapter(data.chapter)}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">Chapter {data.chapter}</span>
                  <div className="flex items-center space-x-1">
                    {getEmotionIcon(data.dominantEmotion)}
                    <Badge 
                      style={{ 
                        backgroundColor: getEmotionColor(data.dominantEmotion) + '20',
                        color: getEmotionColor(data.dominantEmotion)
                      }}
                      className="text-xs"
                    >
                      {data.dominantEmotion}
                    </Badge>
                  </div>
                </div>
                <div className="text-sm text-slate-600">
                  Intensity: {data.intensity}% | Range: {typeof data.emotionalRange === 'number' ? data.emotionalRange : data.emotionalRange.variance}%
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Emotional Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Emotional Insights & Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {avgIntensity < 40 && (
              <div className="p-3 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  <strong>Low Emotional Intensity:</strong> Consider adding more emotional stakes and character conflicts to increase reader engagement.
                </p>
              </div>
            )}

            {emotionalVariance < 30 && (
              <div className="p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  <strong>Limited Emotional Range:</strong> Your story maintains consistent emotional tone. Consider varying emotional beats for dynamic pacing.
                </p>
              </div>
            )}

            {emotionalPeaks.length < 3 && (
              <div className="p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
                <p className="text-sm text-red-800 dark:text-red-200">
                  <strong>Few Emotional Peaks:</strong> Consider adding more climactic moments to create a more engaging emotional journey.
                </p>
              </div>
            )}

            {emotionalData.filter(d => d.emotions.tension > 70).length > emotionalData.length * 0.6 && (
              <div className="p-3 bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                <p className="text-sm text-purple-800 dark:text-purple-200">
                  <strong>High Tension Story:</strong> Your story maintains high tension. Consider adding moments of relief to give readers emotional breathing room.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}