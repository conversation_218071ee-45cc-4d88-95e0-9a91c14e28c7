import type { ProjectSettings } from '@/lib/types/project-settings';
import { logger } from '@/lib/services/logger';

import { supabase as createSupabaseClient } from '@/lib/supabase';

export async function createProject(settings: ProjectSettings): Promise<string> {
  const supabase = createSupabaseClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  const projectData = {
    user_id: user.id,
    title: settings.projectName,
    description: settings.description,
    
    // Genre & Style Selections
    primary_genre: settings.primaryGenre,
    subgenre: settings.subgenre,
    custom_genre: settings.customGenre,
    narrative_voice: settings.narrativeVoice,
    tense: settings.tense,
    tone_options: settings.tone,
    writing_style: settings.writingStyle,
    custom_style_description: settings.customStyleDescription,
    
    // Story Structure & Pacing
    structure_type: settings.structureType,
    pacing_preference: settings.pacingPreference,
    chapter_structure: settings.chapterStructure,
    timeline_complexity: settings.timelineComplexity,
    custom_structure_notes: settings.customStructureNotes,
    
    // Character & World Building
    protagonist_types: settings.protagonistTypes,
    antagonist_types: settings.antagonistTypes,
    character_complexity: settings.characterComplexity,
    character_arc_types: settings.characterArcTypes,
    custom_character_concepts: settings.customCharacterConcepts,
    time_period: settings.timePeriod,
    geographic_setting: settings.geographicSetting,
    world_type: settings.worldType,
    magic_tech_level: settings.magicTechLevel,
    custom_setting_description: settings.customSettingDescription,
    
    // Themes & Content
    major_themes: settings.majorThemes,
    philosophical_themes: settings.philosophicalThemes,
    social_themes: settings.socialThemes,
    custom_themes: settings.customThemes,
    target_audience: settings.targetAudience,
    content_rating: settings.contentRating,
    content_warnings: settings.contentWarnings,
    cultural_sensitivity_notes: settings.culturalSensitivityNotes,
    
    // Series & Scope
    project_scope: settings.projectScope,
    series_type: settings.seriesType,
    interconnection_level: settings.interconnectionLevel,
    custom_scope_description: settings.customScopeDescription,
    
    // Technical Specifications
    target_word_count: settings.targetWordCount,
    target_chapters: settings.targetChapters,
    chapter_count_type: settings.chapterCountType,
    pov_character_count: settings.povCharacterCount,
    pov_character_type: settings.povCharacterType,
    
    // Research & References
    research_needs: settings.researchNeeds,
    fact_checking_level: settings.factCheckingLevel,
    custom_research_notes: settings.customResearchNotes,
    
    // Initial story concept
    initial_concept: settings.initialConcept,
    
    status: 'planning',
  };

  const { data, error } = await supabase
    .from('projects')
    .insert(projectData)
    .select('id')
    .single();

  if (error) {
    throw error;
  }

  // Trigger AI agent pipeline
  await initializeProjectAgents(data.id, settings);

  return data.id;
}

async function initializeProjectAgents(projectId: string, settings: ProjectSettings) {
  try {
    const response = await fetch('/api/orchestration/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectId,
        projectSelections: settings,
        storyPrompt: settings.initialConcept || settings.description || 'Create an engaging story',
        targetWordCount: settings.targetWordCount,
        targetChapters: settings.targetChapters
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to initialize AI agents');
    }

    return await response.json();
  } catch (error) {
    logger.error('Error initializing AI agents:', error);
    throw error;
  }
}

export async function getProject(id: string) {
  const supabase = createSupabaseClient();
  const { data, error } = await supabase
    .from('projects')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function getUserProjects() {
  const supabase = createSupabaseClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  const { data, error } = await supabase
    .from('projects')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false });

  if (error) {
    throw error;
  }

  return data;
}