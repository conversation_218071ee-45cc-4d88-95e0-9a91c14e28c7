import { SupabaseClient } from '@supabase/supabase-js';
import { BaseRepository, RepositoryResult, RepositoryListResult } from './base-repository';
import { Database } from '@/lib/db/types';

type Chapter = Database['public']['Tables']['chapters']['Row'];
type ChapterInsert = Database['public']['Tables']['chapters']['Insert'];
type ChapterUpdate = Database['public']['Tables']['chapters']['Update'];

export interface ChapterWithContent extends Chapter {
  word_progress?: number;
  quality_score?: number;
}

export class ChapterRepository extends BaseRepository<Chapter> {
  constructor(supabase: SupabaseClient) {
    super(supabase, 'chapters');
  }
  
  /**
   * Find all chapters for a project
   */
  async findByProjectId(
    projectId: string,
    includeContent: boolean = false
  ): Promise<RepositoryListResult<Chapter>> {
    const columns = includeContent 
      ? '*' 
      : 'id, project_id, chapter_number, title, status, actual_word_count, target_word_count, created_at, updated_at';
      
    return this.executeQuery<Chapter[]>((query) =>
      query
        .select(columns)
        .eq('project_id', projectId)
        .is('deleted_at', null)
        .order('chapter_number', { ascending: true })
    );
  }
  
  /**
   * Find chapter by number within a project
   */
  async findByNumber(
    projectId: string,
    chapterNumber: number
  ): Promise<RepositoryResult<Chapter>> {
    return this.executeQuery<Chapter>((query) =>
      query
        .select(this.selectColumns)
        .eq('project_id', projectId)
        .eq('chapter_number', chapterNumber)
        .is('deleted_at', null)
        .single()
    );
  }
  
  /**
   * Get chapter with quality metrics
   */
  async findWithQualityMetrics(
    chapterId: string
  ): Promise<RepositoryResult<ChapterWithContent>> {
    return this.executeQuery<ChapterWithContent>((query) =>
      query
        .select(`
          *,
          quality_metrics (
            overall_score,
            pacing,
            character_consistency,
            emotional_impact,
            created_at
          )
        `)
        .eq('id', chapterId)
        .single()
    );
  }
  
  /**
   * Update chapter content and word count
   */
  async updateContent(
    chapterId: string,
    content: string
  ): Promise<RepositoryResult<Chapter>> {
    const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length;
    
    return this.update(chapterId, {
      content,
      actual_word_count: wordCount,
      status: 'draft',
      updated_at: new Date().toISOString()
    });
  }
  
  /**
   * Get chapters needing generation
   */
  async findChaptersToGenerate(
    projectId: string
  ): Promise<RepositoryListResult<Chapter>> {
    return this.executeQuery<Chapter[]>((query) =>
      query
        .select(this.selectColumns)
        .eq('project_id', projectId)
        .is('deleted_at', null)
        .or('content.is.null,actual_word_count.is.null,actual_word_count.eq.0')
        .order('chapter_number', { ascending: true })
    );
  }
  
  /**
   * Get project word count
   */
  async getTotalWordCount(projectId: string): Promise<RepositoryResult<number>> {
    const { data: chapters, error } = await this.supabase
      .from('chapters')
      .select('actual_word_count')
      .eq('project_id', projectId)
      .is('deleted_at', null);
      
    if (error) {
      return { error: new Error(error.message) };
    }
    
    const total = (chapters || []).reduce(
      (sum, ch) => sum + (ch.actual_word_count || 0), 
      0
    );
    
    return { data: total };
  }
  
  /**
   * Reorder chapters
   */
  async reorderChapters(
    projectId: string,
    chapterOrder: Array<{ id: string; chapter_number: number }>
  ): Promise<RepositoryResult<void>> {
    try {
      // Update each chapter's number
      for (const { id, chapter_number } of chapterOrder) {
        const { error } = await this.supabase
          .from('chapters')
          .update({ 
            chapter_number,
            updated_at: new Date().toISOString()
          })
          .eq('id', id)
          .eq('project_id', projectId);
          
        if (error) {
          return { error: new Error(error.message) };
        }
      }
      
      return { data: undefined };
    } catch (error) {
      return { 
        error: error instanceof Error ? error : new Error('Failed to reorder chapters') 
      };
    }
  }
  
  /**
   * Duplicate a chapter
   */
  async duplicateChapter(
    chapterId: string,
    newChapterNumber: number
  ): Promise<RepositoryResult<Chapter>> {
    // Get original chapter
    const originalResult = await this.findById(chapterId);
    if (originalResult.error || !originalResult.data) {
      return { error: originalResult.error || new Error('Chapter not found') };
    }
    
    const original = originalResult.data;
    
    // Create duplicate
    const duplicateData: Omit<ChapterInsert, 'id' | 'created_at' | 'updated_at'> = {
      project_id: original.project_id,
      chapter_number: newChapterNumber,
      title: `${original.title} (Copy)`,
      content: original.content,
      summary: original.summary,
      notes: original.notes,
      outline: original.outline,
      target_word_count: original.target_word_count,
      actual_word_count: original.actual_word_count,
      status: 'draft',
      ai_notes: {
        ...(original.ai_notes as object || {}),
        duplicated_from: chapterId,
        duplicated_at: new Date().toISOString()
      }
    };
    
    return this.create(duplicateData);
  }
  
  /**
   * Get chapter versions history
   */
  async getChapterVersions(chapterId: string): Promise<RepositoryListResult<{
    id: string;
    version_type: string;
    word_count: number;
    created_at: string;
    metadata: unknown;
  }>> {
    return this.executeQuery((query) =>
      this.supabase
        .from('chapter_versions')
        .select('id, version_type, word_count, created_at, metadata')
        .eq('chapter_id', chapterId)
        .order('created_at', { ascending: false })
        .limit(20)
    );
  }
}