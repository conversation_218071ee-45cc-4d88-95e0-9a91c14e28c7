-- Simple Stripe tables for webhook sync
-- Run this in your Supabase SQL Editor

-- Create stripe_customers table
CREATE TABLE IF NOT EXISTS public.stripe_customers (
    id TEXT PRIMARY KEY,
    email TEXT,
    name TEXT,
    created BIGINT NOT NULL,
    updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    metadata JSONB DEFAULT '{}',
    raw_data JSONB
);

-- Create stripe_subscriptions table
CREATE TABLE IF NOT EXISTS public.stripe_subscriptions (
    id TEXT PRIMARY KEY,
    customer TEXT,
    status TEXT NOT NULL,
    current_period_start BIGINT NOT NULL,
    current_period_end BIGINT NOT NULL,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    created BIGINT NOT NULL,
    updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    metadata JSONB DEFAULT '{}',
    raw_data JSONB
);

-- Create stripe_invoices table
CREATE TABLE IF NOT EXISTS public.stripe_invoices (
    id TEXT PRIMARY KEY,
    customer TEXT,
    subscription TEXT,
    status TEXT NOT NULL,
    amount_paid BIGINT DEFAULT 0,
    amount_due BIGINT DEFAULT 0,
    created BIGINT NOT NULL,
    updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    metadata JSONB DEFAULT '{}',
    raw_data JSONB
);

-- Create stripe_payment_intents table
CREATE TABLE IF NOT EXISTS public.stripe_payment_intents (
    id TEXT PRIMARY KEY,
    customer TEXT,
    amount BIGINT NOT NULL,
    currency TEXT NOT NULL,
    status TEXT NOT NULL,
    created BIGINT NOT NULL,
    updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    metadata JSONB DEFAULT '{}',
    raw_data JSONB
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_stripe_customers_email ON public.stripe_customers(email);
CREATE INDEX IF NOT EXISTS idx_stripe_customers_created ON public.stripe_customers(created);

CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_customer ON public.stripe_subscriptions(customer);
CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_status ON public.stripe_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_created ON public.stripe_subscriptions(created);

CREATE INDEX IF NOT EXISTS idx_stripe_invoices_customer ON public.stripe_invoices(customer);
CREATE INDEX IF NOT EXISTS idx_stripe_invoices_subscription ON public.stripe_invoices(subscription);
CREATE INDEX IF NOT EXISTS idx_stripe_invoices_status ON public.stripe_invoices(status);
CREATE INDEX IF NOT EXISTS idx_stripe_invoices_created ON public.stripe_invoices(created);

CREATE INDEX IF NOT EXISTS idx_stripe_payment_intents_customer ON public.stripe_payment_intents(customer);
CREATE INDEX IF NOT EXISTS idx_stripe_payment_intents_status ON public.stripe_payment_intents(status);
CREATE INDEX IF NOT EXISTS idx_stripe_payment_intents_created ON public.stripe_payment_intents(created);

-- Enable RLS (Row Level Security) if needed
-- ALTER TABLE public.stripe_customers ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.stripe_subscriptions ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.stripe_invoices ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.stripe_payment_intents ENABLE ROW LEVEL SECURITY;

-- Grant permissions to authenticated users (adjust as needed)
-- GRANT SELECT ON public.stripe_customers TO authenticated;
-- GRANT SELECT ON public.stripe_subscriptions TO authenticated;
-- GRANT SELECT ON public.stripe_invoices TO authenticated;
-- GRANT SELECT ON public.stripe_payment_intents TO authenticated;
