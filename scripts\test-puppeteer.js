#!/usr/bin/env node

/**
 * Test Puppeteer Installation
 * Verifies that P<PERSON>peteer is working correctly for PDF generation
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function testPuppeteer() {
  console.log('🧪 Testing Puppeteer installation...');
  
  let browser;
  try {
    // Launch browser
    console.log('🚀 Launching browser...');
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });
    
    console.log('✅ Browser launched successfully');
    
    // Create a new page
    console.log('📄 Creating new page...');
    const page = await browser.newPage();
    
    // Set content
    await page.setContent(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>BookScribe AI - Puppeteer Test</title>
          <style>
            body {
              font-family: 'Times New Roman', serif;
              margin: 40px;
              line-height: 1.6;
              color: #333;
            }
            .header {
              text-align: center;
              border-bottom: 2px solid #8B4513;
              padding-bottom: 20px;
              margin-bottom: 30px;
            }
            .content {
              max-width: 600px;
              margin: 0 auto;
            }
            .success {
              background: #d4edda;
              border: 1px solid #c3e6cb;
              color: #155724;
              padding: 15px;
              border-radius: 5px;
              margin: 20px 0;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>📚 BookScribe AI</h1>
            <h2>Puppeteer Test Document</h2>
          </div>
          
          <div class="content">
            <div class="success">
              <h3>✅ Success!</h3>
              <p>Puppeteer is working correctly and can generate PDFs.</p>
            </div>
            
            <h3>System Information:</h3>
            <ul>
              <li><strong>Platform:</strong> ${process.platform}</li>
              <li><strong>Node.js Version:</strong> ${process.version}</li>
              <li><strong>Test Date:</strong> ${new Date().toISOString()}</li>
            </ul>
            
            <h3>Features Tested:</h3>
            <ul>
              <li>✅ Browser launch</li>
              <li>✅ Page creation</li>
              <li>✅ HTML content rendering</li>
              <li>✅ CSS styling</li>
              <li>✅ PDF generation</li>
            </ul>
            
            <p><em>This PDF was generated automatically by BookScribe AI's Puppeteer integration.</em></p>
          </div>
        </body>
      </html>
    `);
    
    console.log('✅ Page content set successfully');
    
    // Generate PDF
    console.log('📄 Generating PDF...');
    const testDir = path.join(__dirname, '..', 'test-output');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    
    const pdfPath = path.join(testDir, 'puppeteer-test.pdf');
    await page.pdf({
      path: pdfPath,
      format: 'A4',
      printBackground: true,
      margin: {
        top: '20mm',
        right: '20mm',
        bottom: '20mm',
        left: '20mm'
      }
    });
    
    console.log('✅ PDF generated successfully');
    console.log(`📁 PDF saved to: ${pdfPath}`);
    
    // Test screenshot capability
    console.log('📸 Testing screenshot capability...');
    const screenshotPath = path.join(testDir, 'puppeteer-test.png');
    await page.screenshot({
      path: screenshotPath,
      fullPage: true
    });
    
    console.log('✅ Screenshot generated successfully');
    console.log(`📁 Screenshot saved to: ${screenshotPath}`);
    
    // Get browser version
    const version = await browser.version();
    console.log(`🌐 Browser version: ${version}`);
    
    await browser.close();
    
    console.log('\n🎉 All tests passed! Puppeteer is working correctly.');
    console.log('📋 Summary:');
    console.log('   ✅ Browser launch: Success');
    console.log('   ✅ PDF generation: Success');
    console.log('   ✅ Screenshot capture: Success');
    console.log(`   📁 Test files saved in: ${testDir}`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Puppeteer test failed:', error.message);
    console.error('\n🔧 Troubleshooting tips:');
    console.error('   1. Ensure all system dependencies are installed');
    console.error('   2. Try running: npm run install:system-deps');
    console.error('   3. On Linux, you may need: sudo apt-get install -y libnss3 libatk-bridge2.0-0 libdrm2 libgtk-3-0 libgbm1');
    console.error('   4. Check if Chrome/Chromium is properly installed');
    
    return false;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run test if called directly
if (require.main === module) {
  testPuppeteer()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testPuppeteer };
