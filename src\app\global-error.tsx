'use client';

// Temporarily disable Sen<PERSON> to eliminate OpenTelemetry conflicts
// import * as Sen<PERSON> from "@sentry/nextjs";
import Error from "next/error";
import { useEffect } from "react";


export default function GlobalError({ 
  error, 
  reset 
}: { 
  error: Error & { digest?: string; message?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Temporarily disable Sen<PERSON> to eliminate OpenTelemetry conflicts
    // Sentry.captureException(error);
    console.error('Global error:', error);
  }, [error]);

  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center p-8">
          <div className="max-w-md text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">
              Application Error
            </h1>
            <p className="text-gray-600 mb-6">
              Something went wrong. Please try refreshing the page.
            </p>
            <div className="space-x-4">
              <button
                onClick={reset}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                Try Again
              </button>
              <button
                onClick={() => window.location.href = '/'}
                className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
              >
                Go Home
              </button>
            </div>
            {process.env.NODE_ENV === 'development' && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-sm">
                  Technical Details
                </summary>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-2 overflow-auto">
                  {error?.message || 'Unknown error'}
                </pre>
              </details>
            )}
          </div>
        </div>
      </body>
    </html>
  );
}