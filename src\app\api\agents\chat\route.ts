import { NextResponse } from 'next/server'
import { vercelAIClient } from '@/lib/ai/vercel-ai-client'
import { withAIRoute, AuthenticatedRequest, getValidatedData } from '@/lib/api/middleware'
import { aiChatRequestSchema } from '@/lib/api/validation-schemas'
import { AI_MODELS, AI_TEMPERATURE } from '@/lib/config/ai-settings'
import { logger } from '@/lib/services/logger'

interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

export const POST = withAIRoute(
  async (request: AuthenticatedRequest) => {
    const { projectId, chapterId, message, context } = getValidatedData(request)

    // Get project if projectId provided
    let project = null
    if (projectId) {
      const { data, error } = await request.supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .eq('user_id', request.user.id)
        .single()
      
      if (error || !data) {
        return NextResponse.json({ error: 'Project not found' }, { status: 404 })
      }
      project = data
    }

    // Get current chapter context if available
    let chapterContext = ''
    if (chapterId) {
      const { data: chapter } = await request.supabase
        .from('chapters')
        .select('*')
        .eq('id', chapterId)
        .single()
      
      if (chapter) {
        chapterContext = `Current Chapter: ${chapter.chapter_number} - "${chapter.title}"\n`
        if (chapter.content) {
          chapterContext += `Current content: ${chapter.content.slice(-1000)}...\n`
        }
      }
    }

    // Build context-aware system prompt
    const systemPrompt = `You are an expert AI writing assistant for BookScribe AI, helping authors write novels. You provide helpful, specific advice about writing, editing, character development, plot structure, and creative techniques.

PROJECT CONTEXT:
${project ? `- Title: ${project.title || 'Untitled Project'}
- Genre: ${project.primary_genre || 'Not specified'}${project.secondary_genre ? ` (${project.secondary_genre})` : ''}
- Writing Style: ${project.writing_style || 'Not specified'}
- Narrative Voice: ${project.narrative_voice || 'Not specified'}
- Target Audience: ${project.target_audience || 'Not specified'}
- Target Word Count: ${project.target_word_count?.toLocaleString() || 'Not specified'} words` : 'No project context available'}

${chapterContext}

${context?.selectedText ? `SELECTED TEXT: "${context.selectedText}"\n` : ''}

GUIDELINES:
- Provide specific, actionable advice
- Stay in character as a writing mentor
- Reference the project context when relevant
- If the user selected text, focus on that specific content
- Offer concrete suggestions rather than generic advice
- Help with character consistency, plot development, and writing quality
- Be encouraging and constructive

Remember: You're helping create a ${project?.primary_genre || 'genre'} novel with ${project?.narrative_voice || 'narrative'} perspective.`

    // Build conversation history
    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt }
    ]

    // Add recent chat history for context
    if (context?.chatHistory) {
      context.chatHistory.forEach((msg: ChatMessage) => {
        messages.push({
          role: msg.role,
          content: msg.content
        })
      })
    }

    // Add current message
    messages.push({ role: 'user', content: message })

    // Check if client wants streaming
    const acceptsStreaming = request.headers.get('accept') === 'text/event-stream'
    
    try {
      if (acceptsStreaming) {
        // Create streaming response
        const encoder = new TextEncoder()
        const stream = new ReadableStream({
          async start(controller) {
            try {
              await vercelAIClient.streamTextWithFallback(
                messages[messages.length - 1].content,
                {
                  systemPrompt: systemPrompt,
                  model: AI_MODELS.PRIMARY,
                  temperature: AI_TEMPERATURE.BALANCED || 0.8,
                  maxTokens: 1500
                },
                {
                  onToken: (token) => {
                    const data = `data: ${JSON.stringify({ token })}\n\n`
                    controller.enqueue(encoder.encode(data))
                  },
                  onComplete: (content) => {
                    const data = `data: ${JSON.stringify({ done: true, content })}\n\n`
                    controller.enqueue(encoder.encode(data))
                    controller.close()
                  },
                  onError: (error) => {
                    logger.error('Chat streaming error:', error)
                    const data = `data: ${JSON.stringify({ error: error.message })}\n\n`
                    controller.enqueue(encoder.encode(data))
                    controller.close()
                  }
                }
              )
            } catch (error) {
              logger.error('Chat generation failed:', error)
              const data = `data: ${JSON.stringify({ error: 'Generation failed' })}\n\n`
              controller.enqueue(encoder.encode(data))
              controller.close()
            }
          }
        })
        
        return new Response(stream, {
          headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
          },
        })
      } else {
        // Non-streaming response with fallback
        const aiResponse = await vercelAIClient.generateTextWithFallback(
          messages[messages.length - 1].content,
          {
            systemPrompt: systemPrompt,
            model: AI_MODELS.PRIMARY,
            temperature: AI_TEMPERATURE.BALANCED || 0.8,
            maxTokens: 1500
          }
        )

        if (!aiResponse) {
          return NextResponse.json({ error: 'No response from AI' }, { status: 500 })
        }

        return NextResponse.json({
          success: true,
          response: aiResponse
        })
      }
    } catch (error) {
      logger.error('Chat generation error:', error)
      return NextResponse.json(
        { error: 'Failed to generate response' }, 
        { status: 500 }
      )
    }
  },
  'chat',
  aiChatRequestSchema
)