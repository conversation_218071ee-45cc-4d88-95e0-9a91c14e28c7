/**
 * Unified Theme Hook for BookScribe AI
 * Combines direct CSS control with next-themes integration
 */

'use client'

import { useTheme as useNextTheme } from 'next-themes'
import { useState, useEffect, useCallback } from 'react'
import { 
  ThemeId, 
  themeConfigs, 
  applyTheme, 
  getCurrentTheme, 
  switchTheme as switchThemeDirect, 
  getAllThemes, 
  getThemesByMode,
  initializeTheme 
} from '@/lib/themes/theme-applier'
import { 
  getThemeById, 
  getDefaultTheme, 
  applyThemeToDocument,
  type ThemeDefinition 
} from '@/lib/themes/theme-registry'

export interface UnifiedTheme {
  // State
  theme: string | undefined
  resolvedTheme: string | undefined
  systemTheme: 'light' | 'dark' | undefined
  currentTheme: ThemeId | null
  currentThemeData: ThemeDefinition | null
  mounted: boolean
  
  // Theme data
  themeConfigs: typeof themeConfigs
  availableThemes: ThemeDefinition[]
  
  // Actions
  setTheme: (theme: string) => void
  switchTheme: (themeId: ThemeId) => void
  switchToNextTheme: () => void
  switchToPreviousTheme: () => void
  toggleMode: () => void
  
  // Utilities
  isThemeActive: (themeId: string) => boolean
  getCurrentMode: () => 'light' | 'dark' | null
  isDark: () => boolean
  isLight: () => boolean
  getThemeColor: (colorKey: keyof ThemeDefinition['colors'], fallback?: string) => string
  getThemeDisplayName: () => string
  getThemeDescription: () => string
  getThemesByMode: (mode: 'light' | 'dark') => ThemeDefinition[]
}

export function useTheme(): UnifiedTheme {
  const nextTheme = useNextTheme()
  const [currentTheme, setCurrentTheme] = useState<ThemeId | null>(null)
  const [currentThemeData, setCurrentThemeData] = useState<ThemeDefinition | null>(null)
  const [mounted, setMounted] = useState(false)

  // Initialize theme on mount
  useEffect(() => {
    // Initialize CSS-based theme if not already done
    if (typeof document !== 'undefined' && !currentTheme) {
      initializeTheme()
      setCurrentTheme(getCurrentTheme())
    }
    setMounted(true)
  }, [currentTheme])

  // Sync theme data when theme changes
  useEffect(() => {
    if (!mounted) return

    let themeData: ThemeDefinition | null = null
    const theme = nextTheme.theme

    if (theme === 'system') {
      // Use system theme
      const systemMode = nextTheme.systemTheme === 'dark' ? 'dark' : 'light'
      themeData = getDefaultTheme(systemMode)
    } else if (theme) {
      // Try to get theme from registry first
      themeData = getThemeById(theme)
      
      // If not in registry, check CSS-based themes
      if (!themeData && theme in themeConfigs) {
        const cssTheme = themeConfigs[theme as ThemeId]
        themeData = {
          id: theme,
          name: cssTheme.name,
          description: cssTheme.description || '',
          mode: cssTheme.mode,
          colors: {} as Record<string, string> // CSS themes don't have color objects
        }
      }
      
      // Fallback to default if theme not found
      if (!themeData) {
        const mode = theme.includes('dark') ? 'dark' : 'light'
        themeData = getDefaultTheme(mode)
      }
    }

    setCurrentThemeData(themeData)

    // Apply theme to document
    if (themeData) {
      // Apply registry theme if it has colors
      if ('colors' in themeData && Object.keys(themeData.colors).length > 0) {
        applyThemeToDocument(themeData)
      }
      // Apply CSS theme if it's in themeConfigs
      if (themeData.id in themeConfigs) {
        switchThemeDirect(themeData.id as ThemeId)
        setCurrentTheme(themeData.id as ThemeId)
      }
    }
  }, [nextTheme.theme, nextTheme.systemTheme, mounted])

  // Get all available themes (combine both systems)
  const getAvailableThemes = useCallback((): ThemeDefinition[] => {
    const registryThemes = getAllThemes()
    const cssThemes = Object.entries(themeConfigs).map(([id, config]) => ({
      id,
      name: config.name,
      description: config.description || '',
      mode: config.mode,
      colors: {} as Record<string, string>
    }))
    
    // Deduplicate by ID
    const themeMap = new Map<string, ThemeDefinition>()
    const allThemes = registryThemes.concat(cssThemes)
    allThemes.forEach(theme => {
      if (!themeMap.has(theme.id)) {
        themeMap.set(theme.id, theme)
      }
    })
    
    return Array.from(themeMap.values())
  }, [])

  // Get themes by mode
  const getThemesByModeUnified = useCallback((mode: 'light' | 'dark'): ThemeDefinition[] => {
    return getAvailableThemes().filter(theme => theme.mode === mode)
  }, [getAvailableThemes])

  // Switch theme (works with both systems)
  const switchTheme = useCallback((themeId: ThemeId | string) => {
    // Apply to next-themes
    nextTheme.setTheme(themeId)
    
    // Apply CSS theme if applicable
    if (themeId in themeConfigs) {
      switchThemeDirect(themeId as ThemeId)
      setCurrentTheme(themeId as ThemeId)
    }
  }, [nextTheme])

  // Switch to next theme in the same mode
  const switchToNextTheme = useCallback(() => {
    if (!currentThemeData) return

    const themesInMode = getThemesByModeUnified(currentThemeData.mode)
    const currentIndex = themesInMode.findIndex(t => t.id === currentThemeData.id)
    const nextIndex = (currentIndex + 1) % themesInMode.length
    const nextTheme = themesInMode[nextIndex]

    if (nextTheme) {
      switchTheme(nextTheme.id)
    }
  }, [currentThemeData, getThemesByModeUnified, switchTheme])

  // Switch to previous theme in the same mode
  const switchToPreviousTheme = useCallback(() => {
    if (!currentThemeData) return

    const themesInMode = getThemesByModeUnified(currentThemeData.mode)
    const currentIndex = themesInMode.findIndex(t => t.id === currentThemeData.id)
    const prevIndex = currentIndex === 0 ? themesInMode.length - 1 : currentIndex - 1
    const prevTheme = themesInMode[prevIndex]

    if (prevTheme) {
      switchTheme(prevTheme.id)
    }
  }, [currentThemeData, getThemesByModeUnified, switchTheme])

  // Toggle between light and dark modes
  const toggleMode = useCallback(() => {
    if (!currentThemeData) return

    const oppositeMode = currentThemeData.mode === 'light' ? 'dark' : 'light'
    const defaultTheme = getDefaultTheme(oppositeMode)
    switchTheme(defaultTheme.id)
  }, [currentThemeData, switchTheme])

  // Utility functions
  const getCurrentMode = useCallback((): 'light' | 'dark' | null => {
    return currentThemeData?.mode || null
  }, [currentThemeData])

  const isDark = useCallback((): boolean => {
    return getCurrentMode() === 'dark'
  }, [getCurrentMode])

  const isLight = useCallback((): boolean => {
    return getCurrentMode() === 'light'
  }, [getCurrentMode])

  const isThemeActive = useCallback((themeId: string): boolean => {
    return nextTheme.theme === themeId || currentTheme === themeId
  }, [nextTheme.theme, currentTheme])

  const getThemeColor = useCallback((
    colorKey: keyof ThemeDefinition['colors'], 
    fallback: string = '#000000'
  ): string => {
    if (!currentThemeData || !currentThemeData.colors) return fallback
    return currentThemeData.colors[colorKey] || fallback
  }, [currentThemeData])

  const getThemeDisplayName = useCallback((): string => {
    return currentThemeData?.name || 'Unknown Theme'
  }, [currentThemeData])

  const getThemeDescription = useCallback((): string => {
    return currentThemeData?.description || ''
  }, [currentThemeData])

  return {
    // State
    theme: nextTheme.theme,
    resolvedTheme: nextTheme.resolvedTheme,
    systemTheme: nextTheme.systemTheme,
    currentTheme,
    currentThemeData,
    mounted,
    
    // Theme data
    themeConfigs,
    availableThemes: getAvailableThemes(),
    
    // Actions
    setTheme: nextTheme.setTheme,
    switchTheme,
    switchToNextTheme,
    switchToPreviousTheme,
    toggleMode,
    
    // Utilities
    isThemeActive,
    getCurrentMode,
    isDark,
    isLight,
    getThemeColor,
    getThemeDisplayName,
    getThemeDescription,
    getThemesByMode: getThemesByModeUnified
  }
}

// Export for backwards compatibility
export { useTheme as useBookScribeTheme }
export { useTheme as useSimpleTheme }