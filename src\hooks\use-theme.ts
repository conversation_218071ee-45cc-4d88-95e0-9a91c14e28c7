/**
 * Unified Theme Hook for BookScribe AI
 * Simple wrapper around next-themes with BookScribe-specific utilities
 */

'use client'

import { useTheme as useNextTheme } from 'next-themes'
import { useEffect, useState } from 'react'

export interface BookScribeTheme {
  // State
  theme: string | undefined
  resolvedTheme: string | undefined
  mounted: boolean
  
  // Actions
  setTheme: (theme: string) => void
  
  // Theme data
  themes: Array<{
    id: string
    name: string
    mode: 'light' | 'dark'
  }>
  
  // Utilities
  isThemeActive: (themeId: string) => boolean
  getCurrentMode: () => 'light' | 'dark' | null
  isDark: () => boolean
  isLight: () => boolean
}

// Theme configurations
export const themeConfigs = [
  {
    id: 'writers-sanctuary-light',
    name: "Writer's Sanctuary",
    mode: 'light' as const
  },
  {
    id: 'forest-manuscript-light',
    name: 'Forest Manuscript',
    mode: 'light' as const
  },
  {
    id: 'evening-study-dark',
    name: 'Evening Study',
    mode: 'dark' as const
  },
  {
    id: 'midnight-ink-dark',
    name: 'Midnight Ink',
    mode: 'dark' as const
  }
]

export function useTheme(): BookScribeTheme {
  const nextTheme = useNextTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Get current mode based on theme
  const getCurrentMode = (): 'light' | 'dark' | null => {
    if (!mounted || !nextTheme.theme) return null
    
    const currentTheme = themeConfigs.find(t => t.id === nextTheme.theme)
    return currentTheme?.mode || null
  }

  const isDark = (): boolean => {
    return getCurrentMode() === 'dark'
  }

  const isLight = (): boolean => {
    return getCurrentMode() === 'light'
  }

  const isThemeActive = (themeId: string): boolean => {
    return nextTheme.theme === themeId
  }

  return {
    // State
    theme: nextTheme.theme,
    resolvedTheme: nextTheme.resolvedTheme,
    mounted,
    
    // Actions
    setTheme: nextTheme.setTheme,
    
    // Theme data
    themes: themeConfigs,
    
    // Utilities
    isThemeActive,
    getCurrentMode,
    isDark,
    isLight
  }
}

// Export for backwards compatibility
export { useTheme as useBookScribeTheme }
export { useTheme as useSimpleTheme }