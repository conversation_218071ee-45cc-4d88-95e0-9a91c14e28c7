import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { authenticateUser, handleRouteError } from '@/lib/auth'
import { format } from 'date-fns'

export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const type = searchParams.get('type') || 'overview' // overview, hourly, weekly, daily

    const supabase = await createClient()

    // Build base query
    let query = supabase
      .from('writing_sessions')
      .select('*')
      .eq('user_id', authResult.user.id)

    if (projectId) {
      query = query.eq('project_id', projectId)
    }

    if (startDate) {
      query = query.gte('created_at', startDate)
    }

    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    const { data: sessions, error } = await query.order('created_at', { ascending: false })

    if (error) {
      throw error
    }

    const response: any = {
      sessions,
      count: sessions.length
    }

    // Calculate different analytics based on type
    switch (type) {
      case 'overview':
        const totalWords = sessions.reduce((sum, s) => sum + (s.word_count || 0), 0)
        const totalDuration = sessions.reduce((sum, s) => sum + (s.duration || 0), 0)
        const avgSessionDuration = sessions.length > 0 ? Math.round(totalDuration / sessions.length / 60) : 0
        const avgWordsPerSession = sessions.length > 0 ? Math.round(totalWords / sessions.length) : 0

        response.overview = {
          totalWords,
          totalSessions: sessions.length,
          totalDuration: Math.round(totalDuration / 60), // in minutes
          avgSessionDuration, // in minutes
          avgWordsPerSession,
          avgWordsPerMinute: totalDuration > 0 ? Math.round(totalWords / (totalDuration / 60)) : 0
        }
        break

      case 'hourly':
        const hourlyData = new Array(24).fill(0).map((_, hour) => ({
          hour: `${hour}:00`,
          words: 0,
          sessions: 0
        }))

        sessions.forEach(session => {
          const hour = new Date(session.created_at).getHours()
          hourlyData[hour].words += session.word_count || 0
          hourlyData[hour].sessions += 1
        })

        response.hourlyPattern = hourlyData.map(h => ({
          date: h.hour,
          value: h.words
        }))
        break

      case 'weekly':
        const weeklyData = new Array(7).fill(0).map((_, day) => ({
          day: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][day],
          words: 0,
          sessions: 0
        }))

        sessions.forEach(session => {
          const day = new Date(session.created_at).getDay()
          weeklyData[day].words += session.word_count || 0
          weeklyData[day].sessions += 1
        })

        response.weeklyPattern = weeklyData.map(w => ({
          date: w.day,
          value: w.words
        }))
        break

      case 'daily':
        const dailyMap = new Map<string, { words: number; sessions: number }>()
        
        sessions.forEach(session => {
          const date = format(new Date(session.created_at), 'yyyy-MM-dd')
          const existing = dailyMap.get(date) || { words: 0, sessions: 0 }
          dailyMap.set(date, {
            words: existing.words + (session.word_count || 0),
            sessions: existing.sessions + 1
          })
        })

        response.dailyData = Array.from(dailyMap.entries())
          .map(([date, data]) => ({
            date: format(new Date(date), 'MMM dd'),
            value: data.words,
            sessions: data.sessions
          }))
          .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        break
    }

    // Calculate writing streak
    const uniqueDays = new Set(
      sessions.map(s => format(new Date(s.created_at), 'yyyy-MM-dd'))
    )
    const sortedDays = Array.from(uniqueDays).sort().reverse()
    let streak = 0
    const today = format(new Date(), 'yyyy-MM-dd')
    
    for (let i = 0; i < sortedDays.length; i++) {
      const expectedDate = format(
        new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        'yyyy-MM-dd'
      )
      if (sortedDays[i] === expectedDate) {
        streak++
      } else {
        break
      }
    }

    response.streak = streak

    return NextResponse.json(response)

  } catch (error) {
    return handleRouteError(error, 'Session Analytics')
  }
}

export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const body = await request.json()
    const { projectId, wordCount, duration, sessionType = 'writing' } = body

    if (!projectId || wordCount === undefined) {
      return NextResponse.json({ 
        error: 'projectId and wordCount are required' 
      }, { status: 400 })
    }

    const supabase = await createClient()

    // Create new writing session
    const { data: session, error } = await supabase
      .from('writing_sessions')
      .insert({
        user_id: authResult.user.id,
        project_id: projectId,
        word_count: wordCount,
        duration: duration || 0,
        session_type: sessionType,
      })
      .select()
      .single()

    if (error) {
      throw error
    }

    return NextResponse.json({ session })

  } catch (error) {
    return handleRouteError(error, 'Create Session')
  }
}