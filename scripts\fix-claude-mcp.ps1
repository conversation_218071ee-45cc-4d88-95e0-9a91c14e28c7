# Simple script to fix Claude MCP configuration
Write-Host "Fixing Claude MCP configuration..." -ForegroundColor Green

$configPath = "$env:APPDATA\Claude\claude_desktop_config.json"
$stripeKey = "sk_test_51RZJ6GBnzE6hSOXRKNpXVPjT5MK67bpb5gkT9KWwdRlIhPYxYTElSpYdfmgIKeH0kIrcjMyvfwNut9QI8w55Qdfq00b2InGXAY"

$config = @{
    mcpServers = @{
        supabase = @{
            command = "npx"
            args = @("-y", "@supabase/mcp-server-supabase", "--read-only", "--project-ref=xvqeiwrpbzpiqvwuvtpj")
            env = @{
                SUPABASE_ACCESS_TOKEN = "REPLACE_WITH_YOUR_SUPABASE_TOKEN"
            }
        }
        stripe = @{
            command = "npx"
            args = @("-y", "@stripe/mcp")
            env = @{
                STRIPE_SECRET_KEY = $stripeKey
            }
        }
        playwright = @{
            command = "npx"
            args = @("-y", "@playwright/mcp")
            env = @{
                PLAYWRIGHT_BROWSER = "chromium"
                PLAYWRIGHT_HEADLESS = "true"
                PLAYWRIGHT_TIMEOUT = "30000"
                PLAYWRIGHT_VIEWPORT = "1280x720"
            }
        }
        context7 = @{
            command = "npx"
            args = @("-y", "@upstash/context7-mcp")
            env = @{
                CONTEXT7_MAX_TOKENS = "10000"
            }
        }
    }
}

$jsonConfig = $config | ConvertTo-Json -Depth 10
$jsonConfig | Set-Content -Path $configPath -Encoding UTF8

Write-Host "Claude config updated!" -ForegroundColor Green
Write-Host ""
Write-Host "IMPORTANT: You still need to:" -ForegroundColor Yellow
Write-Host "1. Get your Supabase personal access token from https://supabase.com/dashboard/account/tokens" -ForegroundColor Cyan
Write-Host "2. Edit the config file at: $configPath" -ForegroundColor Cyan
Write-Host "3. Replace 'REPLACE_WITH_YOUR_SUPABASE_TOKEN' with your actual token" -ForegroundColor Cyan
Write-Host "4. Restart Claude Desktop" -ForegroundColor Cyan
