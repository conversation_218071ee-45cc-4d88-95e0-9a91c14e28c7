# PowerShell script to start <PERSON> Des<PERSON>op with environment variables loaded
# This ensures MCP servers can access your .env.local variables

param(
    [string]$EnvFile = ".env.local"
)

Write-Host "🚀 Starting Claude Desktop with MCP environment variables..." -ForegroundColor Green

# Check if .env.local exists
if (-not (Test-Path $EnvFile)) {
    Write-Host "❌ Error: $EnvFile not found!" -ForegroundColor Red
    Write-Host "Please create your .env.local file with the required MCP variables." -ForegroundColor Yellow
    Write-Host "See .env.example for reference." -ForegroundColor Yellow
    exit 1
}

# Function to load environment variables from .env file
function Load-EnvFile {
    param([string]$Path)
    
    Write-Host "📁 Loading environment variables from $Path..." -ForegroundColor Blue
    
    Get-Content $Path | ForEach-Object {
        if ($_ -match '^([^#][^=]+)=(.*)$') {
            $name = $matches[1].Trim()
            $value = $matches[2].Trim()
            
            # Remove quotes if present
            if ($value -match '^"(.*)"$' -or $value -match "^'(.*)'$") {
                $value = $matches[1]
            }
            
            # Set environment variable
            [Environment]::SetEnvironmentVariable($name, $value, "Process")
            Write-Host "  ✓ Set $name" -ForegroundColor Gray
        }
    }
}

# Load environment variables
try {
    Load-EnvFile -Path $EnvFile
    Write-Host "✅ Environment variables loaded successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Error loading environment variables: $_" -ForegroundColor Red
    exit 1
}

# Verify critical MCP variables are set
$requiredVars = @(
    "SENTRY_AUTH_TOKEN",
    "STRIPE_SECRET_KEY", 
    "SUPABASE_ACCESS_TOKEN"
)

$missingVars = @()
foreach ($var in $requiredVars) {
    if (-not [Environment]::GetEnvironmentVariable($var, "Process")) {
        $missingVars += $var
    }
}

if ($missingVars.Count -gt 0) {
    Write-Host "⚠️  Warning: Missing MCP environment variables:" -ForegroundColor Yellow
    $missingVars | ForEach-Object { Write-Host "  - $_" -ForegroundColor Yellow }
    Write-Host "Some MCP servers may not work properly." -ForegroundColor Yellow
}

# Find Claude Desktop executable
$claudePaths = @(
    "$env:LOCALAPPDATA\Programs\Claude\Claude.exe",
    "$env:PROGRAMFILES\Claude\Claude.exe",
    "$env:PROGRAMFILES(X86)\Claude\Claude.exe"
)

$claudeExe = $null
foreach ($path in $claudePaths) {
    if (Test-Path $path) {
        $claudeExe = $path
        break
    }
}

if (-not $claudeExe) {
    Write-Host "❌ Error: Claude Desktop not found!" -ForegroundColor Red
    Write-Host "Please install Claude Desktop from https://claude.ai/download" -ForegroundColor Yellow
    exit 1
}

Write-Host "🎯 Starting Claude Desktop..." -ForegroundColor Green
Write-Host "Path: $claudeExe" -ForegroundColor Gray

# Start Claude Desktop with environment variables
try {
    Start-Process -FilePath $claudeExe -WindowStyle Normal
    Write-Host "✅ Claude Desktop started successfully!" -ForegroundColor Green
    Write-Host "🔨 MCP servers should now be available with your environment variables." -ForegroundColor Cyan
} catch {
    Write-Host "❌ Error starting Claude Desktop: $_" -ForegroundColor Red
    exit 1
}
