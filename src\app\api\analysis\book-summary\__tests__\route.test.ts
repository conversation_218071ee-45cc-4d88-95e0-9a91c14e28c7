import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { POST } from '../route';
import { createClient } from '@/lib/supabase/server';
import OpenAI from 'openai';
import { authenticateUser } from '@/lib/auth/auth-utils';

// Mock dependencies
jest.mock('@/lib/supabase/server');
jest.mock('openai');
jest.mock('@/lib/auth/auth-utils');
jest.mock('@/lib/config', () => ({
  config: {
    openai: { apiKey: 'test-key' },
  },
}));

const mockSupabase = {
  from: jest.fn(),
};

const mockOpenAI = {
  chat: {
    completions: {
      create: jest.fn(),
    },
  },
};

describe('Book Summary API Route', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (createClient as jest.Mock).mockResolvedValue(mockSupabase);
    (OpenAI as jest.MockedClass<typeof OpenAI>).mockImplementation(() => mockOpenAI as any);
    (authenticateUser as jest.Mock).mockResolvedValue({ id: 'test-user-id' });
  });

  describe('POST /api/analysis/book-summary', () => {
    const mockProjectData = {
      id: 'test-project-id',
      title: 'Epic Fantasy Novel',
      description: 'A tale of magic and adventure',
      primary_genre: 'fantasy',
      target_audience: 'young-adult',
      current_word_count: 75000,
      target_word_count: 80000,
    };

    const mockChapters = [
      {
        id: 'ch1',
        title: 'The Beginning',
        content: 'Chapter 1 content...',
        actual_word_count: 4000,
      },
      {
        id: 'ch2', 
        title: 'The Journey',
        content: 'Chapter 2 content...',
        actual_word_count: 4200,
      },
    ];

    const mockCharacters = [
      {
        id: 'char1',
        name: 'Hero',
        role: 'protagonist',
        description: 'Brave young wizard',
      },
      {
        id: 'char2',
        name: 'Villain',
        role: 'antagonist',
        description: 'Dark sorcerer',
      },
    ];

    beforeEach(() => {
      // Setup default mock responses
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'projects') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockProjectData,
                  error: null,
                }),
              }),
            }),
          };
        }
        if (table === 'chapters') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                  data: mockChapters,
                  error: null,
                }),
              }),
            }),
          };
        }
        if (table === 'characters') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockResolvedValue({
                data: mockCharacters,
                error: null,
              }),
            }),
          };
        }
        return {
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }),
        };
      });
    });

    it('should generate an elevator pitch summary', async () => {
      const mockSummary = 'A young wizard must save the kingdom from an ancient evil in this thrilling fantasy adventure.';
      
      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [{
          message: {
            content: mockSummary,
          },
        }],
      });

      const request = new NextRequest('http://localhost:3000/api/analysis/book-summary', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          summaryType: 'elevator-pitch',
          targetAudience: 'general',
          tone: 'professional',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.summary).toBe(mockSummary);
      expect(data.type).toBe('elevator-pitch');
      expect(data.wordCount).toBe(mockSummary.split(' ').length);
    });

    it('should generate a back cover blurb', async () => {
      const mockBlurb = `In a world where magic is forbidden...

Young Aria discovers she possesses extraordinary powers that could either save her kingdom or destroy it.

As an ancient evil awakens, Aria must master her abilities and unite unlikely allies to face a threat that has slumbered for a thousand years.

Will she embrace her destiny, or will fear consume her?

An epic tale of courage, sacrifice, and the power of believing in yourself.`;

      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [{
          message: {
            content: mockBlurb,
          },
        }],
      });

      const request = new NextRequest('http://localhost:3000/api/analysis/book-summary', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          summaryType: 'back-cover',
          targetAudience: 'young-adult',
          tone: 'dramatic',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.summary).toBe(mockBlurb);
      expect(data.type).toBe('back-cover');
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({
          messages: expect.arrayContaining([
            expect.objectContaining({
              content: expect.stringContaining('back cover blurb'),
            }),
          ]),
        })
      );
    });

    it('should include project metadata in response', async () => {
      const request = new NextRequest('http://localhost:3000/api/analysis/book-summary', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          summaryType: 'synopsis',
        }),
      });

      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: 'Test synopsis' } }],
      });

      const response = await POST(request);
      const data = await response.json();

      expect(data.projectTitle).toBe('Epic Fantasy Novel');
      expect(data.genre).toBe('fantasy');
      expect(data.wordCount).toBe(75000);
    });

    it('should handle missing project', async () => {
      mockSupabase.from.mockImplementation(() => ({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }),
        }),
      }));

      const request = new NextRequest('http://localhost:3000/api/analysis/book-summary', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'non-existent-id',
          summaryType: 'elevator-pitch',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toContain('Project not found');
    });

    it('should handle authentication failure', async () => {
      (authenticateUser as jest.Mock).mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/analysis/book-summary', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          summaryType: 'elevator-pitch',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toContain('Unauthorized');
    });

    it('should validate required fields', async () => {
      const request = new NextRequest('http://localhost:3000/api/analysis/book-summary', {
        method: 'POST',
        body: JSON.stringify({
          // Missing projectId
          summaryType: 'elevator-pitch',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('required');
    });

    it('should validate summary type', async () => {
      const request = new NextRequest('http://localhost:3000/api/analysis/book-summary', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          summaryType: 'invalid-type',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Invalid summary type');
    });

    it('should handle OpenAI API errors', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(
        new Error('OpenAI API error')
      );

      const request = new NextRequest('http://localhost:3000/api/analysis/book-summary', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          summaryType: 'elevator-pitch',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('generate summary');
    });

    it('should use appropriate temperature for different summary types', async () => {
      // Test creative summary type
      const creativeRequest = new NextRequest('http://localhost:3000/api/analysis/book-summary', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          summaryType: 'amazon-description',
          tone: 'humorous',
        }),
      });

      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: 'Creative summary' } }],
      });

      await POST(creativeRequest);

      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({
          temperature: 0.8, // Higher for creative
        })
      );

      // Test professional summary type
      const professionalRequest = new NextRequest('http://localhost:3000/api/analysis/book-summary', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          summaryType: 'synopsis',
          tone: 'professional',
        }),
      });

      await POST(professionalRequest);

      expect(mockOpenAI.chat.completions.create).toHaveBeenLastCalledWith(
        expect.objectContaining({
          temperature: 0.5, // Lower for professional
        })
      );
    });

    it('should cache summaries to avoid regeneration', async () => {
      // First request
      const request1 = new NextRequest('http://localhost:3000/api/analysis/book-summary', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          summaryType: 'elevator-pitch',
          useCache: true,
        }),
      });

      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: 'Cached summary' } }],
      });

      const response1 = await POST(request1);
      const data1 = await response1.json();

      expect(data1.fromCache).toBe(false);

      // TODO: Implement actual caching logic
      // Second request should use cache
      // const request2 = new NextRequest('http://localhost:3000/api/analysis/book-summary', {
      //   method: 'POST',
      //   body: JSON.stringify({
      //     projectId: 'test-project-id',
      //     summaryType: 'elevator-pitch',
      //     useCache: true,
      //   }),
      // });

      // const response2 = await POST(request2);
      // const data2 = await response2.json();

      // expect(data2.fromCache).toBe(true);
      // expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(1);
    });
  });
});