import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { getTimelineValidator } from '@/lib/timeline/timeline-instances';
import { authenticateUserForProject } from '@/lib/api/auth-helpers';

export async function POST(request: NextRequest) {
  try {
    const { projectId } = await request.json();
    
    // Check authentication and project access
    const { user, error: authError } = await authenticateUserForProject(projectId);
    if (authError) return authError;

    const validator = getTimelineValidator(projectId);

    const validation = await validator.validateTimeline();
    const analysis = validator.getTimelineAnalysis();
    
    return NextResponse.json({
      success: true,
      validation,
      analysis
    });

  } catch (error) {
    console.error('Error validating timeline:', error);
    return NextResponse.json(
      { error: 'Failed to validate timeline' },
      { status: 500 }
    );
  }
}