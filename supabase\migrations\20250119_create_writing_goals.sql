-- Create writing_goals table for tracking user goals and achievements
CREATE TABLE IF NOT EXISTS public.writing_goals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
  
  -- Goal details
  goal_type TEXT NOT NULL CHECK (goal_type IN ('word_count', 'streak', 'quality', 'milestone', 'chapter', 'custom')),
  title TEXT NOT NULL,
  description TEXT,
  
  -- Target and progress
  target_value NUMERIC NOT NULL,
  current_value NUMERIC DEFAULT 0,
  unit TEXT NOT NULL DEFAULT 'words', -- words, days, score, percent, chapters, etc.
  
  -- Dates
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deadline TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  
  -- Status and metadata
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused', 'failed', 'archived')),
  is_recommended BOOLEAN DEFAULT false,
  difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),
  
  -- Additional metadata for flexibility
  metadata JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create writing_goal_progress table for tracking progress history
CREATE TABLE IF NOT EXISTS public.writing_goal_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  goal_id UUID NOT NULL REFERENCES public.writing_goals(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Progress update
  value NUMERIC NOT NULL,
  progress_date DATE NOT NULL DEFAULT CURRENT_DATE,
  notes TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_writing_goals_user_id ON public.writing_goals(user_id);
CREATE INDEX idx_writing_goals_project_id ON public.writing_goals(project_id);
CREATE INDEX idx_writing_goals_status ON public.writing_goals(status);
CREATE INDEX idx_writing_goals_goal_type ON public.writing_goals(goal_type);
CREATE INDEX idx_writing_goal_progress_goal_id ON public.writing_goal_progress(goal_id);
CREATE INDEX idx_writing_goal_progress_date ON public.writing_goal_progress(progress_date);

-- Enable RLS
ALTER TABLE public.writing_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.writing_goal_progress ENABLE ROW LEVEL SECURITY;

-- RLS policies for writing_goals
CREATE POLICY "Users can view their own goals" ON public.writing_goals
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own goals" ON public.writing_goals
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own goals" ON public.writing_goals
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own goals" ON public.writing_goals
  FOR DELETE USING (auth.uid() = user_id);

-- RLS policies for writing_goal_progress
CREATE POLICY "Users can view their own goal progress" ON public.writing_goal_progress
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can track their own goal progress" ON public.writing_goal_progress
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own goal progress" ON public.writing_goal_progress
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own goal progress" ON public.writing_goal_progress
  FOR DELETE USING (auth.uid() = user_id);

-- Function to update goal progress
CREATE OR REPLACE FUNCTION update_goal_progress(
  p_goal_id UUID,
  p_value NUMERIC,
  p_notes TEXT DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
  -- Update current value in goals table
  UPDATE public.writing_goals
  SET 
    current_value = p_value,
    updated_at = NOW(),
    status = CASE 
      WHEN p_value >= target_value AND status = 'active' THEN 'completed'
      ELSE status
    END,
    completed_at = CASE
      WHEN p_value >= target_value AND completed_at IS NULL THEN NOW()
      ELSE completed_at
    END
  WHERE id = p_goal_id AND user_id = auth.uid();
  
  -- Insert progress record
  INSERT INTO public.writing_goal_progress (goal_id, user_id, value, notes)
  VALUES (p_goal_id, auth.uid(), p_value, p_notes);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate streak goals automatically
CREATE OR REPLACE FUNCTION calculate_writing_streak_goal_progress() RETURNS VOID AS $$
DECLARE
  v_goal RECORD;
  v_streak_count INT;
BEGIN
  -- For each active streak goal
  FOR v_goal IN 
    SELECT id, user_id, project_id
    FROM public.writing_goals
    WHERE goal_type = 'streak' 
    AND status = 'active'
    AND user_id = auth.uid()
  LOOP
    -- Calculate current streak
    WITH consecutive_days AS (
      SELECT 
        created_at::date as write_date,
        created_at::date - ROW_NUMBER() OVER (ORDER BY created_at::date) * INTERVAL '1 day' as grp
      FROM public.writing_sessions
      WHERE user_id = v_goal.user_id
      AND (v_goal.project_id IS NULL OR project_id = v_goal.project_id)
      AND created_at >= CURRENT_DATE - INTERVAL '90 days'
      GROUP BY created_at::date
    ),
    streaks AS (
      SELECT 
        grp,
        COUNT(*) as streak_length,
        MAX(write_date) as last_date
      FROM consecutive_days
      GROUP BY grp
    )
    SELECT COALESCE(MAX(streak_length), 0) INTO v_streak_count
    FROM streaks
    WHERE last_date >= CURRENT_DATE - INTERVAL '1 day';
    
    -- Update goal progress
    PERFORM update_goal_progress(v_goal.id, v_streak_count);
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;