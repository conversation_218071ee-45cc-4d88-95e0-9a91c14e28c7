import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { GenerateStructureButton } from '@/components/project/generate-structure-button'
import { GenerateChapterButton } from '@/components/project/generate-chapter-button'
import { ExportProjectButton } from '@/components/project/export-project-button'
import { QuickExportPresets } from '@/components/export/quick-export-presets'
import { BookOpen, Brain } from 'lucide-react'

export default async function ProjectPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')
  
  const { data: project, error } = await supabase
    .from('projects')
    .select('*')
    .eq('id', id)
    .eq('user_id', user.id)
    .single()
  
  if (error || !project) notFound()
  
  const { data: chapters } = await supabase
    .from('chapters')
    .select('*')
    .eq('project_id', id)
    .order('chapter_number')
  
  const hasCompletedChapters = chapters?.some(ch => ch.status === 'complete' || ch.content) || false
  
  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/dashboard">
              <Button variant="ghost">← Back to Dashboard</Button>
            </Link>
            <h1 className="text-2xl font-bold">{project.title}</h1>
          </div>
          <div className="flex gap-2">
            <Link href={`/projects/${id}/write`}>
              <Button>Write</Button>
            </Link>
            <Link href={`/projects/${id}/agents`}>
              <Button variant="outline">
                <Brain className="h-4 w-4 mr-2" />
                AI Agents
              </Button>
            </Link>
            <Button variant="outline">Settings</Button>
          </div>
        </div>
      </header>
      
      <main className="container py-8">
        <div className="grid gap-8 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Project Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="font-semibold">Genre</h4>
                    <p className="text-muted-foreground">{project.primary_genre}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Target Word Count</h4>
                    <p className="text-muted-foreground">{project.target_word_count?.toLocaleString()}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Current Progress</h4>
                    <p className="text-muted-foreground">
                      {project.current_word_count.toLocaleString()} words ({Math.round((project.current_word_count / (project.target_word_count || 1)) * 100)}%)
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Status</h4>
                    <p className="text-muted-foreground capitalize">{project.status}</p>
                  </div>
                </div>
                {project.description && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">Description</h4>
                    <p className="text-muted-foreground">{project.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Chapters</CardTitle>
                <CardDescription>
                  {chapters?.length || 0} chapters
                </CardDescription>
              </CardHeader>
              <CardContent>
                {chapters && chapters.length > 0 ? (
                  <div className="space-y-2">
                    {chapters.map((chapter) => (
                      <div key={chapter.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <h4 className="font-medium">
                            Chapter {chapter.chapter_number}: {chapter.title || 'Untitled'}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {chapter.actual_word_count} words • {chapter.status}
                          </p>
                        </div>
                        <Link href={`/projects/${id}/write?chapter=${chapter.id}`}>
                          <Button variant="ghost" size="sm">Edit</Button>
                        </Link>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground mb-4">No chapters yet</p>
                    <Link href={`/projects/${id}/write`}>
                      <Button>Start Writing</Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Link href={`/projects/${id}/write`} className="block">
                  <Button className="w-full">Continue Writing</Button>
                </Link>
                <Link href={`/projects/${id}/overview`} className="block">
                  <Button variant="outline" className="w-full">
                    <BookOpen className="h-4 w-4 mr-2" />
                    Book Overview
                  </Button>
                </Link>
                {project.status === 'planning' && (
                  <GenerateStructureButton projectId={id} />
                )}
                {project.status === 'writing' && (
                  <GenerateChapterButton projectId={id} chapters={chapters || []} />
                )}
                <Link href={`/projects/${id}/story-bible`} className="block">
                  <Button variant="outline" className="w-full">Edit Story Bible</Button>
                </Link>
                <QuickExportPresets
                  projectId={id}
                  projectTitle={project.title}
                  chapters={chapters?.map(ch => ({
                    id: ch.id,
                    chapter_number: ch.chapter_number,
                    title: ch.title || undefined,
                    content: ch.content || undefined,
                    word_count: ch.actual_word_count || 0,
                  }))}
                  variant="dropdown"
                />
                <ExportProjectButton 
                  projectId={id} 
                  projectTitle={project.title} 
                  hasCompletedChapters={hasCompletedChapters}
                />
              </CardContent>
            </Card>
            
            {hasCompletedChapters && (
              <Card>
                <CardHeader>
                  <CardTitle>Export Presets</CardTitle>
                  <CardDescription>
                    One-click exports for common formats
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <QuickExportPresets
                    projectId={id}
                    projectTitle={project.title}
                    chapters={chapters?.map(ch => ({
                      id: ch.id,
                      chapter_number: ch.chapter_number,
                      title: ch.title || undefined,
                      content: ch.content || undefined,
                      word_count: ch.actual_word_count || 0,
                    }))}
                    variant="cards"
                  />
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Project Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <h4 className="text-sm font-medium">Writing Style</h4>
                  <p className="text-sm text-muted-foreground">{project.writing_style}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Narrative Voice</h4>
                  <p className="text-sm text-muted-foreground">{project.narrative_voice}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Structure</h4>
                  <p className="text-sm text-muted-foreground">{project.structure_type}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}