'use client'

import { lazy, ComponentType } from 'react'
import { 
  FileText, 
  Brain, 
  BookOpen, 
  Users, 
  MessageSquare,
  BarChart3,
  GitBranch,
  History,
  Target,
  Lightbulb,
  Mic
} from 'lucide-react'
import { PanelPlugin, PanelProps } from './types'

// Lazy load panel adapters
const ChaptersPanel = lazy(() => import('@/components/editor/panel-adapters').then(m => ({ default: m.ChaptersPanelAdapter })))
const KnowledgePanel = lazy(() => import('@/components/editor/panel-adapters').then(m => ({ default: m.KnowledgePanelAdapter })))
const AIChatPanel = lazy(() => import('@/components/editor/panel-adapters').then(m => ({ default: m.AiChatPanelAdapter })))
const StoryBiblePanel = lazy(() => import('@/components/editor/panel-adapters').then(m => ({ default: m.StoryBiblePanelAdapter })))
const VoiceAnalysisPanel = lazy(() => import('@/components/editor/panel-adapters').then(m => ({ default: m.VoiceAnalysisPanelAdapter })))

// Lazy load additional panel components
const VoiceConsistencyPanel = lazy(() => import('@/components/editor/voice-consistency-checker').then(m => ({ default: m.VoiceConsistencyChecker })))

// Placeholder components for panels that don't exist yet
const PlaceholderPanel = ({ name }: { name: string }) => (
  <div className="p-4 text-center text-muted-foreground">
    <p>{name} panel coming soon...</p>
  </div>
)

const CharacterArcsPanel = () => <PlaceholderPanel name="Character Arcs" />
const PlotStructurePanel = () => <PlaceholderPanel name="Plot Structure" />
const VersionHistoryPanel = () => <PlaceholderPanel name="Version History" />
const WritingStatsPanel = () => <PlaceholderPanel name="Writing Stats" />
const SeriesToolsPanel = () => <PlaceholderPanel name="Series Tools" />
const ConsistencyPanel = () => <PlaceholderPanel name="Consistency Checker" />

// Panel definitions
export const PANEL_DEFINITIONS: Omit<PanelPlugin, 'component'>[] = [
  {
    id: 'chapters',
    name: 'Chapters',
    description: 'Navigate and manage your book chapters',
    icon: FileText,
    category: 'writing',
    defaultPosition: 'left',
    defaultWidth: 280,
    minWidth: 200,
    maxWidth: 400,
    showInMenu: true
  },
  {
    id: 'knowledge',
    name: 'Knowledge Base',
    description: 'Story bible and reference materials',
    icon: Brain,
    category: 'writing',
    defaultPosition: 'right',
    defaultWidth: 320,
    minWidth: 280,
    maxWidth: 500,
    showInMenu: true
  },
  {
    id: 'ai-chat',
    name: 'AI Assistant',
    description: 'Chat with AI for writing help',
    icon: MessageSquare,
    category: 'ai',
    defaultPosition: 'right',
    defaultWidth: 350,
    minWidth: 300,
    maxWidth: 500,
    canFloat: true,
    showInMenu: true
  },
  {
    id: 'story-bible',
    name: 'Story Bible',
    description: 'Track characters, locations, and world details',
    icon: BookOpen,
    category: 'writing',
    defaultPosition: 'left',
    defaultWidth: 300,
    minWidth: 250,
    maxWidth: 450,
    showInMenu: true
  },
  {
    id: 'character-arcs',
    name: 'Character Arcs',
    description: 'Visualize and track character development',
    icon: Users,
    category: 'analysis',
    defaultPosition: 'right',
    defaultWidth: 400,
    minWidth: 350,
    maxWidth: 600,
    canFloat: true,
    showInMenu: true
  },
  {
    id: 'plot-structure',
    name: 'Plot Structure',
    description: 'Analyze story structure and pacing',
    icon: GitBranch,
    category: 'analysis',
    defaultPosition: 'bottom',
    defaultHeight: 300,
    minHeight: 200,
    maxHeight: 500,
    showInMenu: true
  },
  {
    id: 'voice-analysis',
    name: 'Voice Analysis',
    description: 'Analyze and maintain consistent voice',
    icon: Mic,
    category: 'analysis',
    defaultPosition: 'right',
    defaultWidth: 350,
    minWidth: 300,
    maxWidth: 500,
    showInMenu: true
  },
  {
    id: 'voice-consistency',
    name: 'Voice Consistency',
    description: 'Check writing consistency against voice profiles',
    icon: Target,
    category: 'analysis',
    defaultPosition: 'right',
    defaultWidth: 350,
    minWidth: 300,
    maxWidth: 450,
    showInMenu: true
  },
  {
    id: 'version-history',
    name: 'Version History',
    description: 'Track changes and restore previous versions',
    icon: History,
    category: 'tools',
    defaultPosition: 'right',
    defaultWidth: 300,
    minWidth: 250,
    maxWidth: 400,
    canFloat: true,
    showInMenu: true
  },
  {
    id: 'writing-stats',
    name: 'Writing Statistics',
    description: 'Track your writing progress and metrics',
    icon: BarChart3,
    category: 'tools',
    defaultPosition: 'bottom',
    defaultHeight: 250,
    minHeight: 150,
    maxHeight: 400,
    showInMenu: true
  },
  {
    id: 'series-tools',
    name: 'Series Tools',
    description: 'Manage series continuity and connections',
    icon: Target,
    category: 'series',
    defaultPosition: 'right',
    defaultWidth: 350,
    minWidth: 300,
    maxWidth: 500,
    showInMenu: true
  },
  {
    id: 'consistency',
    name: 'Consistency Checker',
    description: 'Check for inconsistencies in your story',
    icon: Lightbulb,
    category: 'analysis',
    defaultPosition: 'right',
    defaultWidth: 320,
    minWidth: 280,
    maxWidth: 450,
    canFloat: true,
    showInMenu: true
  }
]

// Panel component mapping
export const PANEL_COMPONENTS: Record<string, ComponentType<PanelProps>> = {
  chapters: ChaptersPanel as any,
  knowledge: KnowledgePanel as any,
  'ai-chat': AIChatPanel as any,
  'story-bible': StoryBiblePanel as any,
  'character-arcs': CharacterArcsPanel as any,
  'plot-structure': PlotStructurePanel as any,
  'voice-analysis': VoiceAnalysisPanel as any,
  'voice-consistency': VoiceConsistencyPanel as any,
  'version-history': VersionHistoryPanel as any,
  'writing-stats': WritingStatsPanel as any,
  'series-tools': SeriesToolsPanel as any,
  consistency: ConsistencyPanel as any
}

// Create full panel plugins
export const PANEL_PLUGINS: PanelPlugin[] = PANEL_DEFINITIONS.map(def => ({
  ...def,
  component: PANEL_COMPONENTS[def.id]
}))