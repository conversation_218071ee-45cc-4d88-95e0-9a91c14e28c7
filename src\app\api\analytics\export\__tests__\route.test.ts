import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { POST } from '../route';
import { createClient } from '@/lib/supabase/server';
import { generatePDFReport } from '@/lib/utils/pdf-generator';
import { generateCSVData } from '@/lib/utils/csv-generator';

// Mock dependencies
jest.mock('@/lib/supabase/server');
jest.mock('@/lib/utils/pdf-generator');
jest.mock('@/lib/utils/csv-generator');
jest.mock('@/lib/auth/auth-utils', () => ({
  authenticateUser: jest.fn(),
}));

const mockSupabase = {
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        gte: jest.fn(() => ({
          lte: jest.fn(() => ({
            order: jest.fn(() => ({
              data: [],
              error: null,
            })),
          })),
        })),
        single: jest.fn(() => ({
          data: { id: 'test-project', title: 'Test Project' },
          error: null,
        })),
      })),
    })),
  })),
};

describe('Analytics Export API Route', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (createClient as jest.Mock).mockResolvedValue(mockSupabase);
  });

  describe('POST /api/analytics/export', () => {
    it('should export analytics as PDF', async () => {
      const mockPDFBuffer = Buffer.from('mock pdf content');
      (generatePDFReport as jest.Mock).mockResolvedValue(mockPDFBuffer);

      const request = new NextRequest('http://localhost:3000/api/analytics/export', {
        method: 'POST',
        body: JSON.stringify({
          userId: 'test-user-id',
          projectId: 'test-project-id',
          dateRange: {
            from: new Date('2024-01-01'),
            to: new Date('2024-01-31'),
          },
          format: 'pdf',
        }),
      });

      const response = await POST(request);
      const buffer = await response.arrayBuffer();

      expect(response.status).toBe(200);
      expect(response.headers.get('Content-Type')).toBe('application/pdf');
      expect(response.headers.get('Content-Disposition')).toContain('analytics-report');
      expect(Buffer.from(buffer)).toEqual(mockPDFBuffer);
    });

    it('should export analytics as CSV', async () => {
      const mockCSVData = 'Date,Words Written,Sessions\n2024-01-01,1500,2';
      (generateCSVData as jest.Mock).mockResolvedValue(mockCSVData);

      const request = new NextRequest('http://localhost:3000/api/analytics/export', {
        method: 'POST',
        body: JSON.stringify({
          userId: 'test-user-id',
          format: 'csv',
          scope: 'all',
        }),
      });

      const response = await POST(request);
      const text = await response.text();

      expect(response.status).toBe(200);
      expect(response.headers.get('Content-Type')).toBe('text/csv');
      expect(response.headers.get('Content-Disposition')).toContain('.csv');
      expect(text).toBe(mockCSVData);
    });

    it('should use provided data when available', async () => {
      const providedData = {
        overview: { totalWords: 50000 },
        dailyProgress: [{ date: '2024-01-01', words: 1500 }],
      };

      const request = new NextRequest('http://localhost:3000/api/analytics/export', {
        method: 'POST',
        body: JSON.stringify({
          userId: 'test-user-id',
          format: 'pdf',
          data: providedData,
        }),
      });

      await POST(request);

      expect(generatePDFReport).toHaveBeenCalledWith(
        expect.objectContaining({
          overview: providedData.overview,
          dailyProgress: providedData.dailyProgress,
        })
      );
    });

    it('should fetch data from database when not provided', async () => {
      const mockWritingSessions = [
        { id: 1, words_written: 1000, created_at: '2024-01-01' },
        { id: 2, words_written: 1500, created_at: '2024-01-02' },
      ];

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            gte: jest.fn().mockReturnValue({
              lte: jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                  data: mockWritingSessions,
                  error: null,
                }),
              }),
            }),
          }),
        }),
      });

      const request = new NextRequest('http://localhost:3000/api/analytics/export', {
        method: 'POST',
        body: JSON.stringify({
          userId: 'test-user-id',
          format: 'csv',
        }),
      });

      await POST(request);

      expect(mockSupabase.from).toHaveBeenCalledWith('writing_sessions');
      expect(generateCSVData).toHaveBeenCalledWith(
        expect.objectContaining({
          writingSessions: mockWritingSessions,
        })
      );
    });

    it('should handle project-specific exports', async () => {
      const request = new NextRequest('http://localhost:3000/api/analytics/export', {
        method: 'POST',
        body: JSON.stringify({
          userId: 'test-user-id',
          projectId: 'specific-project-id',
          format: 'pdf',
        }),
      });

      await POST(request);

      // Verify project data was fetched
      expect(mockSupabase.from).toHaveBeenCalledWith('projects');
      expect(generatePDFReport).toHaveBeenCalledWith(
        expect.objectContaining({
          project: expect.objectContaining({
            id: 'test-project',
            title: 'Test Project',
          }),
        })
      );
    });

    it('should handle missing required fields', async () => {
      const request = new NextRequest('http://localhost:3000/api/analytics/export', {
        method: 'POST',
        body: JSON.stringify({
          // Missing userId
          format: 'pdf',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBeDefined();
    });

    it('should handle invalid format', async () => {
      const request = new NextRequest('http://localhost:3000/api/analytics/export', {
        method: 'POST',
        body: JSON.stringify({
          userId: 'test-user-id',
          format: 'invalid-format',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('format');
    });

    it('should handle database errors gracefully', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            gte: jest.fn().mockReturnValue({
              lte: jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                  data: null,
                  error: new Error('Database error'),
                }),
              }),
            }),
          }),
        }),
      });

      const request = new NextRequest('http://localhost:3000/api/analytics/export', {
        method: 'POST',
        body: JSON.stringify({
          userId: 'test-user-id',
          format: 'pdf',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBeDefined();
    });

    it('should handle PDF generation errors', async () => {
      (generatePDFReport as jest.Mock).mockRejectedValue(new Error('PDF generation failed'));

      const request = new NextRequest('http://localhost:3000/api/analytics/export', {
        method: 'POST',
        body: JSON.stringify({
          userId: 'test-user-id',
          format: 'pdf',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('generate report');
    });

    it('should include correct date range in filename', async () => {
      const request = new NextRequest('http://localhost:3000/api/analytics/export', {
        method: 'POST',
        body: JSON.stringify({
          userId: 'test-user-id',
          format: 'pdf',
          dateRange: {
            from: new Date('2024-01-01'),
            to: new Date('2024-01-31'),
          },
        }),
      });

      const response = await POST(request);

      const contentDisposition = response.headers.get('Content-Disposition');
      expect(contentDisposition).toContain('2024-01-01_to_2024-01-31');
    });
  });
});