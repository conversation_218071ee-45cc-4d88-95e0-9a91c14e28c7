import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { logger } from '@/lib/services/logger';

export const runtime = 'nodejs';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id: projectId } = params;
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: locations, error } = await supabase
      .from('locations')
      .select(`
        *,
        parent_location:locations!parent_location_id(
          id,
          name
        ),
        series:series(
          id,
          title
        ),
        universe:universes(
          id,
          name
        )
      `)
      .eq('project_id', projectId)
      .order('name', { ascending: true });

    if (error) {
      logger.error('Error fetching locations:', error);
      return NextResponse.json({ error: 'Failed to fetch locations' }, { status: 500 });
    }

    return NextResponse.json({ locations });
  } catch (error) {
    logger.error('Error in GET /api/projects/[id]/locations:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id: projectId } = params;
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify user owns the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.user.id)
      .single();

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    const body = await request.json();
    const {
      name,
      description,
      parentLocationId,
      locationType = 'other',
      features = [],
      significance,
      isShareable = false,
      seriesId,
      universeId
    } = body;

    if (!name?.trim()) {
      return NextResponse.json({ error: 'Location name is required' }, { status: 400 });
    }

    const { data: location, error } = await supabase
      .from('locations')
      .insert({
        project_id: projectId,
        series_id: seriesId,
        universe_id: universeId,
        name: name.trim(),
        description: description?.trim(),
        parent_location_id: parentLocationId,
        location_type: locationType,
        features,
        significance: significance?.trim(),
        is_shareable: isShareable
      })
      .select(`
        *,
        parent_location:locations!parent_location_id(
          id,
          name
        )
      `)
      .single();

    if (error) {
      logger.error('Error creating location:', error);
      return NextResponse.json({ error: 'Failed to create location' }, { status: 500 });
    }

    return NextResponse.json({ location });
  } catch (error) {
    logger.error('Error in POST /api/projects/[id]/locations:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}