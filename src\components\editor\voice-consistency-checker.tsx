'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  Mic,
  RefreshCw,
  Target,
  TrendingUp,
  Lightbulb
} from 'lucide-react'
import { toast } from 'sonner'

interface VoiceConsistencyCheckerProps {
  content: string
  projectId: string
  chapterId?: string
  voiceProfileId?: string
  onSuggestionApply?: (suggestion: string) => void
}

interface ConsistencyResult {
  consistencyScore: number
  suggestions: Array<{
    text: string
    severity: 'info' | 'warning' | 'error'
    location?: {
      start: number
      end: number
    }
  }>
  deviations: Array<{
    metric: string
    expected: number
    actual: number
    difference: number
  }>
}

export function VoiceConsistencyChecker({
  content,
  projectId,
  chapterId,
  voiceProfileId,
  onSuggestionApply
}: VoiceConsistencyCheckerProps) {
  const [isChecking, setIsChecking] = useState(false)
  const [result, setResult] = useState<ConsistencyResult | null>(null)
  const [selectedProfileId, setSelectedProfileId] = useState(voiceProfileId)

  const checkConsistency = async () => {
    if (!selectedProfileId) {
      toast.error('Please select a voice profile first')
      return
    }

    if (!content || content.length < 100) {
      toast.error('Please write at least 100 characters to check consistency')
      return
    }

    setIsChecking(true)
    try {
      const response = await fetch('/api/analysis/voice-consistency', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content,
          profileId: selectedProfileId,
          projectId,
          chapterId
        })
      })

      if (!response.ok) {
        throw new Error('Failed to check voice consistency')
      }

      const data = await response.json()
      setResult(data)
      
      if (data.consistencyScore < 70) {
        toast.warning('Voice consistency is below target. Review suggestions.')
      } else {
        toast.success('Voice consistency check complete!')
      }
    } catch (error) {
      console.error('Error checking consistency:', error)
      toast.error('Failed to check voice consistency')
    } finally {
      setIsChecking(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="h-5 w-5 text-green-600" />
    if (score >= 60) return <AlertCircle className="h-5 w-5 text-yellow-600" />
    return <AlertTriangle className="h-5 w-5 text-red-600" />
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'error': return 'destructive'
      case 'warning': return 'default'
      default: return 'secondary'
    }
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Mic className="h-5 w-5" />
              Voice Consistency
            </CardTitle>
            <CardDescription>
              Check if your writing matches the established voice profile
            </CardDescription>
          </div>
          <Button
            size="sm"
            variant={result && result.consistencyScore < 70 ? 'destructive' : 'outline'}
            onClick={checkConsistency}
            disabled={isChecking || !content}
          >
            {isChecking ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Checking...
              </>
            ) : (
              <>
                <Target className="h-4 w-4 mr-2" />
                Check Consistency
              </>
            )}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden">
        {result ? (
          <div className="space-y-4 h-full flex flex-col">
            {/* Score Overview */}
            <div className="flex items-center justify-between p-4 bg-warm-50 rounded-lg">
              <div className="flex items-center gap-3">
                {getScoreIcon(result.consistencyScore)}
                <div>
                  <p className="text-sm font-medium">Overall Consistency</p>
                  <p className={`text-2xl font-bold ${getScoreColor(result.consistencyScore)}`}>
                    {result.consistencyScore}%
                  </p>
                </div>
              </div>
              <Progress 
                value={result.consistencyScore} 
                className="w-32"
              />
            </div>

            {/* Deviations */}
            {result.deviations && result.deviations.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Metric Deviations
                </h4>
                <div className="grid grid-cols-2 gap-2">
                  {result.deviations.map((deviation, idx) => (
                    <div key={idx} className="p-3 border rounded-lg">
                      <p className="text-xs text-muted-foreground">{deviation.metric}</p>
                      <div className="flex items-baseline gap-2 mt-1">
                        <span className="text-sm font-medium">{deviation.actual}</span>
                        <span className="text-xs text-muted-foreground">
                          (target: {deviation.expected})
                        </span>
                      </div>
                      <div className="flex items-center gap-1 mt-1">
                        {deviation.difference > 0 ? (
                          <TrendingUp className="h-3 w-3 text-red-500" />
                        ) : (
                          <TrendingUp className="h-3 w-3 text-green-500 rotate-180" />
                        )}
                        <span className="text-xs">
                          {Math.abs(deviation.difference)}% {deviation.difference > 0 ? 'higher' : 'lower'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <Separator />

            {/* Suggestions */}
            <div className="flex-1 overflow-hidden">
              <h4 className="text-sm font-medium flex items-center gap-2 mb-3">
                <Lightbulb className="h-4 w-4" />
                Suggestions ({result.suggestions.length})
              </h4>
              <ScrollArea className="h-full">
                <div className="space-y-2 pr-4">
                  {result.suggestions.length === 0 ? (
                    <Alert>
                      <CheckCircle className="h-4 w-4" />
                      <AlertDescription>
                        Excellent! Your writing closely matches the voice profile.
                      </AlertDescription>
                    </Alert>
                  ) : (
                    result.suggestions.map((suggestion, idx) => (
                      <Alert key={idx} variant={suggestion.severity === 'error' ? 'destructive' : 'default'}>
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1">
                            <AlertDescription className="text-sm">
                              {suggestion.text}
                            </AlertDescription>
                            {suggestion.location && onSuggestionApply && (
                              <Button
                                size="sm"
                                variant="ghost"
                                className="mt-2 h-7 text-xs"
                                onClick={() => onSuggestionApply(suggestion.text)}
                              >
                                Apply Suggestion
                              </Button>
                            )}
                          </div>
                          <Badge variant={getSeverityColor(suggestion.severity)}>
                            {suggestion.severity}
                          </Badge>
                        </div>
                      </Alert>
                    ))
                  )}
                </div>
              </ScrollArea>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <Mic className="h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-sm text-muted-foreground">
              Click "Check Consistency" to analyze your writing
              <br />
              against the selected voice profile
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}