#!/usr/bin/env tsx

/**
 * Create simple Stripe tables using Supabase client
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

async function createStripeTables() {
  console.log('🚀 Creating Stripe tables...')
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  
  if (!supabaseUrl || !serviceRoleKey) {
    console.error('❌ Missing required environment variables')
    process.exit(1)
  }
  
  const supabase = createClient(supabaseUrl, serviceRoleKey)
  
  // Test basic connection first
  console.log('🔍 Testing connection...')
  try {
    const { data: _testData, error: testError } = await supabase
      .from('projects')
      .select('id')
      .limit(1)
    
    if (testError && !testError.message.includes('relation') && !testError.message.includes('does not exist')) {
      throw testError
    }
    console.log('✅ Connection successful')
  } catch (error) {
    console.error('❌ Connection failed:', error)
    process.exit(1)
  }
  
  // Create tables by trying to insert/upsert with proper structure
  const tables = [
    {
      name: 'stripe_customers',
      testData: {
        id: 'test_customer',
        email: '<EMAIL>',
        name: 'Test Customer',
        created: Math.floor(Date.now() / 1000),
        updated: Math.floor(Date.now() / 1000),
        metadata: {},
        raw_data: {}
      }
    },
    {
      name: 'stripe_subscriptions',
      testData: {
        id: 'test_subscription',
        customer: 'test_customer',
        status: 'active',
        current_period_start: Math.floor(Date.now() / 1000),
        current_period_end: Math.floor(Date.now() / 1000) + 2592000, // +30 days
        cancel_at_period_end: false,
        created: Math.floor(Date.now() / 1000),
        updated: Math.floor(Date.now() / 1000),
        metadata: {},
        raw_data: {}
      }
    },
    {
      name: 'stripe_invoices',
      testData: {
        id: 'test_invoice',
        customer: 'test_customer',
        subscription: 'test_subscription',
        status: 'paid',
        amount_paid: 2000,
        amount_due: 0,
        created: Math.floor(Date.now() / 1000),
        updated: Math.floor(Date.now() / 1000),
        metadata: {},
        raw_data: {}
      }
    },
    {
      name: 'stripe_payment_intents',
      testData: {
        id: 'test_payment_intent',
        customer: 'test_customer',
        amount: 2000,
        currency: 'usd',
        status: 'succeeded',
        created: Math.floor(Date.now() / 1000),
        updated: Math.floor(Date.now() / 1000),
        metadata: {},
        raw_data: {}
      }
    }
  ]
  
  console.log('📊 Creating/verifying tables...')
  let successCount = 0
  
  for (const table of tables) {
    try {
      // Try to insert test data - this will create the table if it doesn't exist
      const { error: insertError } = await supabase
        .from(table.name)
        .upsert(table.testData)
      
      if (insertError) {
        console.log(`  ❌ ${table.name}: ${insertError.message}`)
      } else {
        console.log(`  ✅ ${table.name}: Table ready`)
        successCount++
        
        // Clean up test data
        await supabase
          .from(table.name)
          .delete()
          .eq('id', table.testData.id)
      }
    } catch (error) {
      console.log(`  ❌ ${table.name}: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
  
  console.log(`\n📊 Summary: ${successCount}/${tables.length} tables ready`)
  
  if (successCount === tables.length) {
    console.log('\n✅ All Stripe tables are ready!')
    console.log('🔗 Next steps:')
    console.log('  1. Configure Stripe webhooks to point to: /api/stripe/webhook')
    console.log('  2. Add your STRIPE_WEBHOOK_SECRET to .env.local')
    console.log('  3. Test with some webhook events')
    console.log('  4. Check the admin dashboard at /admin/stripe')
  } else {
    console.log('\n⚠️  Some tables could not be created automatically')
    console.log('💡 Manual setup required:')
    console.log('  1. Go to your Supabase dashboard')
    console.log('  2. Navigate to SQL Editor')
    console.log('  3. Run the SQL from: supabase/migrations/simple_stripe_tables.sql')
  }
}

createStripeTables().catch(console.error)
