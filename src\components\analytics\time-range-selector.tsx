"use client";

import { useState } from "react";
import { CalendarIcon } from "lucide-react";
import { format, subDays, startOfDay, endOfDay } from "date-fns";
import { DateRange } from "react-day-picker";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type TimeRangePreset = 
  | "today"
  | "yesterday"
  | "last7days"
  | "last30days"
  | "last90days"
  | "last365days"
  | "thisWeek"
  | "thisMonth"
  | "thisQuarter"
  | "thisYear"
  | "allTime"
  | "custom";

interface TimeRangeSelectorProps {
  onRangeChange: (range: DateRange | undefined) => void;
  className?: string;
  showCustom?: boolean;
  defaultPreset?: TimeRangePreset;
}

export function TimeRangeSelector({
  onRangeChange,
  className,
  showCustom = true,
  defaultPreset = "last30days",
}: TimeRangeSelectorProps) {
  const [selectedPreset, setSelectedPreset] = useState<TimeRangePreset>(defaultPreset);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  const getPresetRange = (preset: TimeRangePreset): DateRange | undefined => {
    const now = new Date();
    const today = startOfDay(now);
    const todayEnd = endOfDay(now);

    switch (preset) {
      case "today":
        return { from: today, to: todayEnd };
      case "yesterday":
        return { from: subDays(today, 1), to: subDays(todayEnd, 1) };
      case "last7days":
        return { from: subDays(today, 6), to: todayEnd };
      case "last30days":
        return { from: subDays(today, 29), to: todayEnd };
      case "last90days":
        return { from: subDays(today, 89), to: todayEnd };
      case "last365days":
        return { from: subDays(today, 364), to: todayEnd };
      case "thisWeek":
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        return { from: weekStart, to: todayEnd };
      case "thisMonth":
        return { from: new Date(today.getFullYear(), today.getMonth(), 1), to: todayEnd };
      case "thisQuarter":
        const quarter = Math.floor(today.getMonth() / 3);
        const quarterStart = new Date(today.getFullYear(), quarter * 3, 1);
        return { from: quarterStart, to: todayEnd };
      case "thisYear":
        return { from: new Date(today.getFullYear(), 0, 1), to: todayEnd };
      case "allTime":
        return undefined;
      case "custom":
        return dateRange;
      default:
        return undefined;
    }
  };

  const handlePresetChange = (preset: TimeRangePreset) => {
    setSelectedPreset(preset);
    if (preset !== "custom") {
      const range = getPresetRange(preset);
      setDateRange(range);
      onRangeChange(range);
    }
  };

  const handleCustomRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    if (selectedPreset === "custom") {
      onRangeChange(range);
    }
  };

  const presetLabels: Record<TimeRangePreset, string> = {
    today: "Today",
    yesterday: "Yesterday",
    last7days: "Last 7 days",
    last30days: "Last 30 days",
    last90days: "Last 90 days",
    last365days: "Last 365 days",
    thisWeek: "This week",
    thisMonth: "This month",
    thisQuarter: "This quarter",
    thisYear: "This year",
    allTime: "All time",
    custom: "Custom range",
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Select value={selectedPreset} onValueChange={handlePresetChange}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select time range" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="today">Today</SelectItem>
          <SelectItem value="yesterday">Yesterday</SelectItem>
          <SelectItem value="last7days">Last 7 days</SelectItem>
          <SelectItem value="last30days">Last 30 days</SelectItem>
          <SelectItem value="last90days">Last 90 days</SelectItem>
          <SelectItem value="last365days">Last 365 days</SelectItem>
          <SelectItem value="thisWeek">This week</SelectItem>
          <SelectItem value="thisMonth">This month</SelectItem>
          <SelectItem value="thisQuarter">This quarter</SelectItem>
          <SelectItem value="thisYear">This year</SelectItem>
          <SelectItem value="allTime">All time</SelectItem>
          {showCustom && <SelectItem value="custom">Custom range</SelectItem>}
        </SelectContent>
      </Select>

      {selectedPreset === "custom" && showCustom && (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "justify-start text-left font-normal",
                !dateRange && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {dateRange?.from ? (
                dateRange.to ? (
                  <>
                    {format(dateRange.from, "LLL dd, y")} -{" "}
                    {format(dateRange.to, "LLL dd, y")}
                  </>
                ) : (
                  format(dateRange.from, "LLL dd, y")
                )
              ) : (
                <span>Pick a date range</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={dateRange?.from}
              selected={dateRange}
              onSelect={handleCustomRangeChange}
              numberOfMonths={2}
            />
          </PopoverContent>
        </Popover>
      )}
    </div>
  );
}