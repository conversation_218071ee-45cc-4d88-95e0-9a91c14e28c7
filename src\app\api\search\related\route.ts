import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import ServiceManager from '@/lib/services/service-manager';
import { SemanticSearchService } from '@/lib/services/semantic-search';
import { authenticateUserForProject } from '@/lib/api/auth-helpers';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sceneDescription, projectId, type } = body;

    if (!sceneDescription || !projectId) {
      return NextResponse.json(
        { error: 'Scene description and projectId are required' },
        { status: 400 }
      );
    }

    // Check authentication and project access
    const { user, error: authError } = await authenticateUserForProject(projectId);
    if (authError) return authError;

    const serviceManager = ServiceManager.getInstance();
    await serviceManager.initialize();
    
    const searchService = await serviceManager.getService('semantic-search');

    if (!searchService) {
      return NextResponse.json(
        { error: 'Semantic search service not available' },
        { status: 503 }
      );
    }

    let searchResult;

    switch (type) {
      case 'related_scenes':
        searchResult = await (searchService as SemanticSearchService).findRelatedScenes(
          sceneDescription,
          projectId
        );
        break;

      case 'similar_content':
        searchResult = await (searchService as SemanticSearchService).search(sceneDescription, {
          projectId,
          types: ['scene', 'chapter'],
          limit: 10
        });
        break;

      default:
        // Default to related scenes
        searchResult = await (searchService as SemanticSearchService).findRelatedScenes(
          sceneDescription,
          projectId
        );
    }

    if (!searchResult.success) {
      return NextResponse.json(
        { error: searchResult.error || 'Related content search failed' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      results: searchResult.data,
      sceneDescription,
      type: type || 'related_scenes'
    });

  } catch (error) {
    console.error('Related content search API error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false 
      },
      { status: 500 }
    );
  }
}