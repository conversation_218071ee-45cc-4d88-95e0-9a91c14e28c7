'use client'

import { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import confetti from 'canvas-confetti'
import { Trophy, Star, Zap, Target, Flame, BookOpen } from 'lucide-react'
import { cn } from '@/lib/utils'

interface CelebrationTrigger {
  id: string
  type: 'word_count' | 'streak' | 'chapter' | 'quality' | 'goal' | 'achievement'
  title: string
  message: string
  icon: React.ElementType
  level: 'small' | 'medium' | 'large'
  value?: number
}

interface ProgressCelebrationProps {
  trigger?: CelebrationTrigger
  onComplete?: () => void
}

export function ProgressCelebration({ trigger, onComplete }: ProgressCelebrationProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (trigger) {
      setIsVisible(true)
      triggerCelebration(trigger.level)
      
      const timer = setTimeout(() => {
        setIsVisible(false)
        onComplete?.()
      }, 3500)
      
      return () => clearTimeout(timer)
    }
  }, [trigger, onComplete])

  const triggerCelebration = (level: 'small' | 'medium' | 'large') => {
    const defaults = {
      spread: 360,
      ticks: 100,
      gravity: 0.5,
      decay: 0.94,
      startVelocity: 30,
      shapes: ['star'],
      colors: ['FFE400', 'FFBD00', 'E89400', 'FFCA6C', 'FDFFB8']
    }

    switch (level) {
      case 'small':
        confetti({
          ...defaults,
          particleCount: 40,
          scalar: 0.75,
          shapes: ['circle'],
        })
        break
        
      case 'medium':
        confetti({
          ...defaults,
          particleCount: 100,
          scalar: 1.2,
          drift: 0.5,
        })
        break
        
      case 'large':
        // Fire multiple bursts
        const count = 200
        const defaultProps = {
          ...defaults,
          origin: { y: 0.7 },
        }

        function fire(particleRatio: number, opts: any) {
          confetti({
            ...defaultProps,
            ...opts,
            particleCount: Math.floor(count * particleRatio)
          })
        }

        fire(0.25, {
          spread: 26,
          startVelocity: 55,
        })
        fire(0.2, {
          spread: 60,
        })
        fire(0.35, {
          spread: 100,
          decay: 0.91,
          scalar: 0.8
        })
        fire(0.1, {
          spread: 120,
          startVelocity: 25,
          decay: 0.92,
          scalar: 1.2
        })
        fire(0.1, {
          spread: 120,
          startVelocity: 45,
        })
        break
    }
  }

  if (!trigger) return null

  const Icon = trigger.icon

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="fixed inset-0 z-50 flex items-center justify-center pointer-events-none"
        >
          <motion.div
            initial={{ y: 50 }}
            animate={{ y: 0 }}
            exit={{ y: -50 }}
            transition={{ type: "spring", damping: 15 }}
            className={cn(
              "bg-background/95 backdrop-blur-sm border-2 rounded-2xl shadow-2xl p-8 text-center max-w-md mx-4",
              trigger.level === 'large' && "border-primary",
              trigger.level === 'medium' && "border-blue-500",
              trigger.level === 'small' && "border-muted-foreground"
            )}
          >
            <motion.div
              animate={{ 
                rotate: [0, -10, 10, -10, 10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="inline-flex"
            >
              <div className={cn(
                "p-4 rounded-full mb-4",
                trigger.level === 'large' && "bg-primary/20",
                trigger.level === 'medium' && "bg-blue-500/20",
                trigger.level === 'small' && "bg-muted"
              )}>
                <Icon className={cn(
                  "h-16 w-16",
                  trigger.level === 'large' && "text-primary",
                  trigger.level === 'medium' && "text-blue-500",
                  trigger.level === 'small' && "text-muted-foreground"
                )} />
              </div>
            </motion.div>
            
            <motion.h2 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className={cn(
                "text-2xl font-bold mb-2",
                trigger.level === 'large' && "text-primary",
              )}
            >
              {trigger.title}
            </motion.h2>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-muted-foreground"
            >
              {trigger.message}
            </motion.p>
            
            {trigger.value && (
              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.6, type: "spring" }}
                className="mt-4"
              >
                <span className={cn(
                  "text-5xl font-bold",
                  trigger.level === 'large' && "text-primary",
                  trigger.level === 'medium' && "text-blue-500",
                  trigger.level === 'small' && "text-foreground"
                )}>
                  {trigger.value.toLocaleString()}
                </span>
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Hook to trigger celebrations
export function useCelebration() {
  const [celebration, setCelebration] = useState<CelebrationTrigger | undefined>()

  const celebrate = (trigger: CelebrationTrigger) => {
    setCelebration(trigger)
  }

  const clearCelebration = () => {
    setCelebration(undefined)
  }

  return {
    celebration,
    celebrate,
    clearCelebration,
    CelebrationComponent: (
      <ProgressCelebration 
        trigger={celebration} 
        onComplete={clearCelebration}
      />
    )
  }
}

// Preset celebrations
export const celebrations = {
  firstWords: (): CelebrationTrigger => ({
    id: 'first-words',
    type: 'word_count',
    title: 'First Words!',
    message: 'You\'ve started your writing journey!',
    icon: BookOpen,
    level: 'medium',
    value: 100
  }),
  
  thousandWords: (count: number): CelebrationTrigger => ({
    id: 'thousand-words',
    type: 'word_count',
    title: 'Word Milestone!',
    message: `You've written ${count.toLocaleString()} words!`,
    icon: Zap,
    level: count >= 10000 ? 'large' : 'medium',
    value: count
  }),
  
  streakMilestone: (days: number): CelebrationTrigger => ({
    id: 'streak-milestone',
    type: 'streak',
    title: 'Writing Streak!',
    message: `${days} days of consistent writing!`,
    icon: Flame,
    level: days >= 30 ? 'large' : days >= 7 ? 'medium' : 'small',
    value: days
  }),
  
  chapterComplete: (chapterNumber: number): CelebrationTrigger => ({
    id: 'chapter-complete',
    type: 'chapter',
    title: 'Chapter Complete!',
    message: `Chapter ${chapterNumber} is finished!`,
    icon: Star,
    level: 'medium',
    value: chapterNumber
  }),
  
  qualityAchievement: (score: number): CelebrationTrigger => ({
    id: 'quality-achievement',
    type: 'quality',
    title: 'Quality Writing!',
    message: `${score}% quality score achieved!`,
    icon: Trophy,
    level: score >= 95 ? 'large' : score >= 85 ? 'medium' : 'small',
    value: score
  }),
  
  goalCompleted: (goalTitle: string): CelebrationTrigger => ({
    id: 'goal-completed',
    type: 'goal',
    title: 'Goal Achieved!',
    message: goalTitle,
    icon: Target,
    level: 'medium'
  }),
  
  achievementUnlocked: (title: string, tier: string): CelebrationTrigger => ({
    id: 'achievement-unlocked',
    type: 'achievement',
    title: 'Achievement Unlocked!',
    message: title,
    icon: Trophy,
    level: tier === 'platinum' ? 'large' : tier === 'gold' ? 'medium' : 'small'
  })
}