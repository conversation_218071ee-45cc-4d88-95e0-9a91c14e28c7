import { EventEmitter } from 'events';
import { WritingAgent } from '../agents/writing-agent';
import { ContextManager } from './context-manager';
import { BookContext, ChapterOutline, ChapterContent } from '../agents/types';
import { AI_CONCURRENCY, AI_RETRY_CONFIG } from '../config/ai-settings';
import { qualityAnalyzer } from './quality-analyzer';
import { consistencyValidator } from './consistency-validator';

interface BatchTask {
  id: string;
  chapterOutline: ChapterOutline;
  priority: number;
  retryCount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  result?: ChapterContent;
  error?: string;
}

interface BatchProgress {
  total: number;
  completed: number;
  failed: number;
  processing: number;
  estimatedTimeRemaining: number;
  averageProcessingTime: number;
}

interface BatchResult {
  successful: ChapterContent[];
  failed: { chapterNumber: number; error: string }[];
  totalTime: number;
  qualityMetrics: {
    averageScore: number;
    lowestScore: number;
    highestScore: number;
  };
}

export class BatchChapterProcessor extends EventEmitter {
  private tasks: Map<string, BatchTask> = new Map();
  private processingTasks: Set<string> = new Set();
  private contextManager: ContextManager;
  private maxConcurrent: number;
  private processingTimes: number[] = [];
  private isProcessing = false;
  private isPaused = false;

  constructor(
    private context: BookContext,
    maxConcurrent = AI_CONCURRENCY.MAX_CONCURRENT_CHAPTERS
  ) {
    super();
    this.contextManager = new ContextManager();
    this.maxConcurrent = maxConcurrent;
  }

  /**
   * Add chapters to the batch processing queue
   */
  addChapters(chapters: ChapterOutline[], priority = 1): void {
    chapters.forEach((chapter, index) => {
      const taskId = `chapter-${chapter.number}-${Date.now()}`;
      this.tasks.set(taskId, {
        id: taskId,
        chapterOutline: chapter,
        priority: priority + (chapters.length - index) / chapters.length, // Higher priority for earlier chapters
        retryCount: 0,
        status: 'pending'
      });
    });

    this.emit('batch:updated', this.getProgress());
  }

  /**
   * Start processing the batch
   */
  async processBatch(): Promise<BatchResult> {
    if (this.isProcessing) {
      throw new Error('Batch processing already in progress');
    }

    this.isProcessing = true;
    this.isPaused = false;
    const startTime = Date.now();

    this.emit('batch:started', this.getProgress());

    // Initialize services
    await this.contextManager.initialize();
    if (qualityAnalyzer.getStatus() !== 'active') {
      await qualityAnalyzer.initialize();
    }
    if (consistencyValidator.getStatus() !== 'active') {
      await consistencyValidator.initialize();
    }

    // Process tasks
    while (this.hasPendingTasks() && !this.isPaused) {
      await this.processNextBatch();
    }

    this.isProcessing = false;

    // Compile results
    const successful: ChapterContent[] = [];
    const failed: { chapterNumber: number; error: string }[] = [];
    let totalQualityScore = 0;
    let lowestScore = 100;
    let highestScore = 0;

    this.tasks.forEach(task => {
      if (task.status === 'completed' && task.result) {
        successful.push(task.result);
        
        // Extract quality score if available
        const qualityScore = (task.result as ChapterContent & { qualityScore?: number }).qualityScore || 75;
        totalQualityScore += qualityScore;
        lowestScore = Math.min(lowestScore, qualityScore);
        highestScore = Math.max(highestScore, qualityScore);
      } else if (task.status === 'failed') {
        failed.push({
          chapterNumber: task.chapterOutline.number,
          error: task.error || 'Unknown error'
        });
      }
    });

    const result: BatchResult = {
      successful,
      failed,
      totalTime: Date.now() - startTime,
      qualityMetrics: {
        averageScore: successful.length > 0 ? totalQualityScore / successful.length : 0,
        lowestScore: successful.length > 0 ? lowestScore : 0,
        highestScore: successful.length > 0 ? highestScore : 0
      }
    };

    this.emit('batch:completed', result);
    return result;
  }

  /**
   * Pause batch processing
   */
  pause(): void {
    this.isPaused = true;
    this.emit('batch:paused', this.getProgress());
  }

  /**
   * Resume batch processing
   */
  resume(): void {
    if (!this.isProcessing) {
      throw new Error('No batch processing in progress');
    }
    this.isPaused = false;
    this.emit('batch:resumed', this.getProgress());
  }

  /**
   * Cancel batch processing
   */
  cancel(): void {
    this.isPaused = true;
    this.isProcessing = false;
    this.tasks.forEach(task => {
      if (task.status === 'pending' || task.status === 'processing') {
        task.status = 'failed';
        task.error = 'Cancelled by user';
      }
    });
    this.emit('batch:cancelled', this.getProgress());
  }

  /**
   * Get current progress
   */
  getProgress(): BatchProgress {
    const tasks = Array.from(this.tasks.values());
    const completed = tasks.filter(t => t.status === 'completed').length;
    const failed = tasks.filter(t => t.status === 'failed').length;
    const processing = tasks.filter(t => t.status === 'processing').length;
    const pending = tasks.filter(t => t.status === 'pending').length;

    const averageTime = this.processingTimes.length > 0
      ? this.processingTimes.reduce((a, b) => a + b, 0) / this.processingTimes.length
      : 30000; // Default 30 seconds

    return {
      total: tasks.length,
      completed,
      failed,
      processing,
      estimatedTimeRemaining: pending * averageTime,
      averageProcessingTime: averageTime
    };
  }

  /**
   * Process the next batch of chapters
   */
  private async processNextBatch(): Promise<void> {
    // Get tasks that can be processed
    const availableSlots = this.maxConcurrent - this.processingTasks.size;
    if (availableSlots <= 0) {
      // Wait for a slot to open
      await this.waitForSlot();
      return;
    }

    // Get next tasks to process
    const nextTasks = this.getNextTasks(availableSlots);
    if (nextTasks.length === 0) {
      // Wait for processing tasks to complete
      if (this.processingTasks.size > 0) {
        await this.waitForSlot();
      }
      return;
    }

    // Start processing tasks
    const promises = nextTasks.map(task => this.processTask(task));
    await Promise.race(promises);
  }

  /**
   * Process a single task
   */
  private async processTask(task: BatchTask): Promise<void> {
    const startTime = Date.now();
    task.status = 'processing';
    this.processingTasks.add(task.id);

    this.emit('task:started', {
      taskId: task.id,
      chapterNumber: task.chapterOutline.number,
      title: task.chapterOutline.title
    });

    try {
      // Create writing agent with context
      const writingAgent = new WritingAgent(this.context, this.contextManager);
      
      // Generate chapter
      const chapterContent = await this.generateWithRetry(
        () => writingAgent.writeChapter(task.chapterOutline),
        task
      );

      // Validate quality
      if (qualityAnalyzer.getStatus() === 'active') {
        const qualityResult = await qualityAnalyzer.analyzeContentQuality(
          chapterContent.content,
          'chapter',
          {
            previousContent: this.getPreviousChapterContent(task.chapterOutline.number),
            characterProfiles: this.context.characters,
            storyContext: this.context.storyStructure
          }
        );

        if (qualityResult.success && qualityResult.data) {
          (chapterContent as ChapterContent & { qualityScore?: number }).qualityScore = 
            qualityResult.data.metrics.overall;

          // Add quality feedback
          if (qualityResult.data.suggestions.length > 0) {
            chapterContent.continuityNotes.push(
              ...qualityResult.data.suggestions.map(s => `Quality: ${s}`)
            );
          }
        }
      }

      // Check consistency
      if (consistencyValidator.getStatus() === 'active') {
        const consistencyResult = await consistencyValidator.checkNewContent(
          chapterContent.content,
          'chapter',
          this.context
        );

        if (consistencyResult.success && consistencyResult.data) {
          const criticalIssues = consistencyResult.data.filter(
            issue => issue.severity === 'critical' || issue.severity === 'high'
          );

          if (criticalIssues.length > 0) {
            // Add warnings but don't fail
            chapterContent.continuityWarnings = criticalIssues.map(
              issue => `${issue.type}: ${issue.description}`
            );
          }
        }
      }

      // Update context with new chapter
      if (!this.context.completedChapters) {
        this.context.completedChapters = [];
      }
      this.context.completedChapters.push(chapterContent);

      // Mark as completed
      task.status = 'completed';
      task.result = chapterContent;

      const processingTime = Date.now() - startTime;
      this.processingTimes.push(processingTime);
      if (this.processingTimes.length > 10) {
        this.processingTimes.shift(); // Keep only last 10
      }

      this.emit('task:completed', {
        taskId: task.id,
        chapterNumber: task.chapterOutline.number,
        processingTime,
        wordCount: chapterContent.wordCount
      });

    } catch (error) {
      task.status = 'failed';
      task.error = error instanceof Error ? error.message : 'Unknown error';

      this.emit('task:failed', {
        taskId: task.id,
        chapterNumber: task.chapterOutline.number,
        error: task.error,
        retryCount: task.retryCount
      });

    } finally {
      this.processingTasks.delete(task.id);
      this.emit('batch:progress', this.getProgress());
    }
  }

  /**
   * Generate with retry logic
   */
  private async generateWithRetry<T>(
    generateFn: () => Promise<T>,
    task: BatchTask
  ): Promise<T> {
    const maxRetries = AI_RETRY_CONFIG.MAX_RETRIES;
    let lastError: Error | undefined;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await generateFn();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        task.retryCount = attempt + 1;

        if (attempt < maxRetries) {
          // Calculate backoff delay
          const delay = Math.min(
            AI_RETRY_CONFIG.INITIAL_DELAY * Math.pow(AI_RETRY_CONFIG.BACKOFF_MULTIPLIER, attempt),
            AI_RETRY_CONFIG.MAX_DELAY
          );

          this.emit('task:retry', {
            taskId: task.id,
            chapterNumber: task.chapterOutline.number,
            attempt: attempt + 1,
            delay,
            error: lastError.message
          });

          await this.delay(delay);
        }
      }
    }

    throw lastError || new Error('Max retries exceeded');
  }

  /**
   * Get next tasks to process
   */
  private getNextTasks(count: number): BatchTask[] {
    const pendingTasks = Array.from(this.tasks.values())
      .filter(task => task.status === 'pending')
      .sort((a, b) => b.priority - a.priority);

    return pendingTasks.slice(0, count);
  }

  /**
   * Wait for a processing slot to open
   */
  private async waitForSlot(): Promise<void> {
    if (this.processingTasks.size === 0) return;

    return new Promise(resolve => {
      const checkSlot = () => {
        if (this.processingTasks.size < this.maxConcurrent || this.processingTasks.size === 0) {
          this.off('task:completed', checkSlot);
          this.off('task:failed', checkSlot);
          resolve();
        }
      };

      this.on('task:completed', checkSlot);
      this.on('task:failed', checkSlot);
    });
  }

  /**
   * Check if there are pending tasks
   */
  private hasPendingTasks(): boolean {
    return Array.from(this.tasks.values()).some(task => task.status === 'pending');
  }

  /**
   * Get previous chapter content for context
   */
  private getPreviousChapterContent(chapterNumber: number): string | undefined {
    if (!this.context.completedChapters) return undefined;

    const previousChapter = this.context.completedChapters
      .filter(ch => ch.chapterNumber === chapterNumber - 1)
      .sort((a, b) => b.chapterNumber - a.chapterNumber)[0];

    return previousChapter?.content;
  }

  /**
   * Delay helper
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clear all tasks
   */
  clearTasks(): void {
    this.tasks.clear();
    this.processingTasks.clear();
    this.processingTimes = [];
    this.emit('batch:cleared');
  }

  /**
   * Get detailed task status
   */
  getTaskStatus(): {
    pending: BatchTask[];
    processing: BatchTask[];
    completed: BatchTask[];
    failed: BatchTask[];
  } {
    const tasks = Array.from(this.tasks.values());
    
    return {
      pending: tasks.filter(t => t.status === 'pending'),
      processing: tasks.filter(t => t.status === 'processing'),
      completed: tasks.filter(t => t.status === 'completed'),
      failed: tasks.filter(t => t.status === 'failed')
    };
  }
}

// Export factory function
export function createBatchProcessor(
  context: BookContext,
  maxConcurrent?: number
): BatchChapterProcessor {
  return new BatchChapterProcessor(context, maxConcurrent);
}