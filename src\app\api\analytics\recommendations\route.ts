import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { authenticateUser, handleRouteError } from '@/lib/auth'
import { vercelAIClient } from '@/lib/ai/vercel-ai-client'
import { AI_MODELS, TASK_TYPES } from '@/lib/config/ai-settings'
import { aiModelSelector } from '@/lib/services/ai-model-selector'
import { embeddingCache } from '@/lib/services/embedding-cache'
import { z } from 'zod'

interface BookRecommendation {
  id: string
  bookTitle: string
  category: 'readability' | 'consistency' | 'engagement' | 'dialogue'
  severity: 'critical' | 'high' | 'medium' | 'low'
  issue: string
  recommendation: string
  currentScore: number
  targetScore: number
}

interface ProjectQualityData {
  projectId: string
  projectTitle: string
  overallScore: number
  readability: number
  consistency: number
  engagement: number
  dialogue: number
  pacing: number
  creativity: number
}

export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')
    const seriesId = searchParams.get('seriesId')

    const supabase = await createClient()

    let projectsData: ProjectQualityData[] = []

    if (seriesId) {
      // Handle series recommendations
      const { data: seriesProjects, error: seriesError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          project_quality_metrics (
            overall_score,
            avg_readability,
            avg_consistency,
            avg_engagement,
            avg_dialogue_authenticity,
            avg_pacing,
            avg_creativity,
            created_at
          )
        `)
        .eq('series_id', seriesId)
        .eq('user_id', authResult.user.id)

      if (seriesError) throw seriesError

      projectsData = seriesProjects?.map(project => ({
        projectId: project.id,
        projectTitle: project.title,
        overallScore: project.project_quality_metrics?.[0]?.overall_score || 0,
        readability: project.project_quality_metrics?.[0]?.avg_readability || 0,
        consistency: project.project_quality_metrics?.[0]?.avg_consistency || 0,
        engagement: project.project_quality_metrics?.[0]?.avg_engagement || 0,
        dialogue: project.project_quality_metrics?.[0]?.avg_dialogue_authenticity || 0,
        pacing: project.project_quality_metrics?.[0]?.avg_pacing || 0,
        creativity: project.project_quality_metrics?.[0]?.avg_creativity || 0,
      })) || []

    } else if (projectId) {
      // Single project detailed recommendations
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          project_quality_metrics (
            overall_score,
            avg_readability,
            avg_consistency,
            avg_engagement,
            avg_dialogue_authenticity,
            avg_pacing,
            avg_creativity,
            feedback,
            improvement_suggestions
          )
        `)
        .eq('id', projectId)
        .eq('user_id', authResult.user.id)
        .single()

      if (projectError) throw projectError

      if (project) {
        projectsData = [{
          projectId: project.id,
          projectTitle: project.title,
          overallScore: project.project_quality_metrics?.[0]?.overall_score || 0,
          readability: project.project_quality_metrics?.[0]?.avg_readability || 0,
          consistency: project.project_quality_metrics?.[0]?.avg_consistency || 0,
          engagement: project.project_quality_metrics?.[0]?.avg_engagement || 0,
          dialogue: project.project_quality_metrics?.[0]?.avg_dialogue_authenticity || 0,
          pacing: project.project_quality_metrics?.[0]?.avg_pacing || 0,
          creativity: project.project_quality_metrics?.[0]?.avg_creativity || 0,
        }]
      }

    } else {
      // All projects - get quality data for all user projects
      const { data: allProjects, error: allError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          project_quality_metrics (
            overall_score,
            avg_readability,
            avg_consistency,
            avg_engagement,
            avg_dialogue_authenticity,
            avg_pacing,
            avg_creativity,
            created_at
          )
        `)
        .eq('user_id', authResult.user.id)

      if (allError) throw allError

      projectsData = allProjects?.map(project => ({
        projectId: project.id,
        projectTitle: project.title,
        overallScore: project.project_quality_metrics?.[0]?.overall_score || 0,
        readability: project.project_quality_metrics?.[0]?.avg_readability || 0,
        consistency: project.project_quality_metrics?.[0]?.avg_consistency || 0,
        engagement: project.project_quality_metrics?.[0]?.avg_engagement || 0,
        dialogue: project.project_quality_metrics?.[0]?.avg_dialogue_authenticity || 0,
        pacing: project.project_quality_metrics?.[0]?.avg_pacing || 0,
        creativity: project.project_quality_metrics?.[0]?.avg_creativity || 0,
      })) || []
    }

    // Filter projects that have quality data
    const projectsWithQuality = projectsData.filter(p => p.overallScore > 0)

    if (projectsWithQuality.length === 0) {
      return NextResponse.json({
        recommendations: [],
        message: 'No quality data available for recommendations'
      })
    }

    // Generate recommendations based on selection type
    const recommendations = projectId 
      ? await generateDetailedRecommendations(projectsWithQuality[0])
      : await generatePriorityRecommendations(projectsWithQuality)

    return NextResponse.json({
      recommendations,
      projectsAnalyzed: projectsWithQuality.length,
      selectionType: projectId ? 'single' : seriesId ? 'series' : 'all'
    })

  } catch (error) {
    return handleRouteError(error, 'Smart Recommendations')
  }
}

// Generate detailed recommendations for a single project (up to 4 per category)
async function generateDetailedRecommendations(project: ProjectQualityData): Promise<BookRecommendation[]> {
  const recommendations: BookRecommendation[] = []
  const categories = [
    { key: 'readability', score: project.readability, icon: '📖' },
    { key: 'consistency', score: project.consistency, icon: '🔄' },
    { key: 'engagement', score: project.engagement, icon: '✨' },
    { key: 'dialogue', score: project.dialogue, icon: '💬' }
  ]

  // Use embeddings to find similar successful patterns
  const successPatterns = await findSuccessPatterns(project)
  
  for (const category of categories) {
    if (category.score < 80) { // Only show recommendations for scores below 80
      const categoryRecommendations = await generateCategoryRecommendations(
        project,
        category.key as any,
        4, // Up to 4 recommendations per category for single project
        successPatterns
      )
      recommendations.push(...categoryRecommendations)
    }
  }

  return recommendations.sort((a, b) => {
    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
    return severityOrder[b.severity] - severityOrder[a.severity]
  })
}

// Generate priority recommendations across projects (1 per book per category, max 4 total)
async function generatePriorityRecommendations(projects: ProjectQualityData[]): Promise<BookRecommendation[]> {
  const allRecommendations: BookRecommendation[] = []

  // Get the worst scoring category for each project
  for (const project of projects) {
    const categories = [
      { key: 'readability', score: project.readability },
      { key: 'consistency', score: project.consistency },
      { key: 'engagement', score: project.engagement },
      { key: 'dialogue', score: project.dialogue }
    ]

    // Find the category with the lowest score that's below 80
    const worstCategory = categories
      .filter(c => c.score < 80)
      .sort((a, b) => a.score - b.score)[0]

    if (worstCategory) {
      const recommendations = await generateCategoryRecommendations(
        project,
        worstCategory.key as any,
        1 // Only 1 recommendation per project for "all projects" view
      )
      allRecommendations.push(...recommendations)
    }
  }

  // Sort by severity and return top 4
  return allRecommendations
    .sort((a, b) => {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
      return severityOrder[b.severity] - severityOrder[a.severity]
    })
    .slice(0, 4)
}

async function generateCategoryRecommendations(
  project: ProjectQualityData,
  category: 'readability' | 'consistency' | 'engagement' | 'dialogue',
  maxRecommendations: number,
  successPatterns?: Array<{ pattern: string; score: number }>
): Promise<BookRecommendation[]> {
  const score = project[category]
  const severity = getSeverity(score)
  
  const recommendations: BookRecommendation[] = []
  
  // Generate specific recommendations based on category and score
  let categoryRecommendations = getCategorySpecificRecommendations(category, score, project.projectTitle)
  
  // Enhance recommendations with success patterns if available
  if (successPatterns && successPatterns.length > 0) {
    categoryRecommendations = categoryRecommendations.map((rec, index) => {
      const relevantPattern = successPatterns.find(p => 
        p.pattern.toLowerCase().includes(category.toLowerCase())
      )
      
      if (relevantPattern && relevantPattern.score > score) {
        return {
          ...rec,
          recommendation: `${rec.recommendation} Studies show that books with ${Math.round(relevantPattern.score)}+ ${category} scores often ${relevantPattern.pattern}.`
        }
      }
      return rec
    })
  }
  
  // Take up to maxRecommendations
  for (let i = 0; i < Math.min(categoryRecommendations.length, maxRecommendations); i++) {
    recommendations.push({
      id: `${project.projectId}-${category}-${i}`,
      bookTitle: project.projectTitle,
      category,
      severity,
      issue: categoryRecommendations[i].issue,
      recommendation: categoryRecommendations[i].recommendation,
      currentScore: Math.round(score),
      targetScore: Math.min(100, Math.round(score + 15)) // Target 15 point improvement
    })
  }

  return recommendations
}

function getSeverity(score: number): 'critical' | 'high' | 'medium' | 'low' {
  if (score < 50) return 'critical'
  if (score < 65) return 'high'
  if (score < 75) return 'medium'
  return 'low'
}

function getCategorySpecificRecommendations(
  category: string,
  score: number,
  bookTitle: string
): Array<{ issue: string; recommendation: string }> {
  switch (category) {
    case 'readability':
      return [
        {
          issue: 'Complex sentence structures making content hard to follow',
          recommendation: 'Break long sentences into shorter ones. Aim for 15-20 words per sentence on average.'
        },
        {
          issue: 'Overuse of technical or complex vocabulary',
          recommendation: 'Replace complex words with simpler alternatives where possible without losing meaning.'
        },
        {
          issue: 'Dense paragraphs overwhelming readers',
          recommendation: 'Break up long paragraphs. Each paragraph should focus on one main idea.'
        },
        {
          issue: 'Passive voice reducing clarity',
          recommendation: 'Use active voice more frequently to make your writing more direct and engaging.'
        }
      ]

    case 'consistency':
      return [
        {
          issue: 'Character behavior inconsistencies across chapters',
          recommendation: 'Create detailed character sheets and refer to them regularly to maintain consistent personalities.'
        },
        {
          issue: 'Timeline or plot continuity issues',
          recommendation: 'Develop a comprehensive timeline and plot outline to track events and maintain logical flow.'
        },
        {
          issue: 'Inconsistent tone or writing style',
          recommendation: 'Establish a style guide for your narrative voice and review chapters for tone consistency.'
        },
        {
          issue: 'World-building rule violations or contradictions',
          recommendation: 'Create a world-building bible documenting all rules, settings, and logic systems.'
        }
      ]

    case 'engagement':
      return [
        {
          issue: 'Weak chapter openings failing to hook readers',
          recommendation: 'Start each chapter with action, dialogue, or compelling questions to immediately engage readers.'
        },
        {
          issue: 'Insufficient conflict or tension throughout the story',
          recommendation: 'Increase stakes and add obstacles for characters. Every scene should have some form of conflict.'
        },
        {
          issue: 'Lack of sensory details making scenes feel flat',
          recommendation: 'Incorporate more sensory descriptions (sight, sound, smell, touch, taste) to immerse readers.'
        },
        {
          issue: 'Characters\' emotions told rather than shown',
          recommendation: 'Show character emotions through actions, dialogue, and physical reactions rather than stating them.'
        }
      ]

    case 'dialogue':
      return [
        {
          issue: 'All characters sound the same in dialogue',
          recommendation: 'Develop unique speech patterns, vocabulary, and mannerisms for each character.'
        },
        {
          issue: 'Dialogue feels unnatural or stilted',
          recommendation: 'Read dialogue aloud to ensure it sounds natural. Use contractions and interruptions.'
        },
        {
          issue: 'Too much exposition delivered through dialogue',
          recommendation: 'Balance dialogue with action and narrative. Avoid info-dumping through character speech.'
        },
        {
          issue: 'Missing dialogue tags causing confusion',
          recommendation: 'Use clear dialogue tags and action beats to help readers follow conversations.'
        }
      ]

    default:
      return [
        {
          issue: 'General quality improvement needed',
          recommendation: 'Review and revise content to improve overall writing quality.'
        }
      ]
  }
}

// Find successful patterns using embeddings
async function findSuccessPatterns(
  project: ProjectQualityData
): Promise<Array<{ pattern: string; score: number }>> {
  try {
    // Create a query embedding for finding similar successful projects
    const queryText = `High quality book with excellent ${project.overallScore < 70 ? 'improvement areas' : 'strengths'} in readability, consistency, engagement, and dialogue`
    
    // Get embedding for the query
    const modelSelection = aiModelSelector.selectModel(
      null, // Use default tier for embeddings
      AI_MODELS.EMBEDDING,
      'recommendations' // This is a recommendations task
    )
    
    // Check cache first
    const cachedEmbedding = await embeddingCache.get(queryText, modelSelection.model)
    let embedding: number[]
    
    if (cachedEmbedding) {
      embedding = cachedEmbedding
    } else {
      // Generate new embedding
      embedding = await vercelAIClient.generateEmbedding(queryText)
      
      // Cache for future use
      await embeddingCache.set(queryText, modelSelection.model, embedding)
    }
    
    // Here we would normally search a vector database for similar patterns
    // For now, return static success patterns
    return [
      { pattern: 'use shorter sentences and clearer paragraph breaks', score: 85 },
      { pattern: 'maintain consistent character voice throughout chapters', score: 88 },
      { pattern: 'start chapters with engaging hooks and end with cliffhangers', score: 90 },
      { pattern: 'use unique speech patterns and vocabulary for each character', score: 87 }
    ]
  } catch (error) {
    // Log error but don't fail the recommendations
    console.error('Error finding success patterns:', error)
    return []
  }
}