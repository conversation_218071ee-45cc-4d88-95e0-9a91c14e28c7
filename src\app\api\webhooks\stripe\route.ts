import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { headers } from 'next/headers'
import { stripe } from '@/lib/stripe'
import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'
import { config } from '@/lib/config'
import type Stripe from 'stripe'

const webhookSecret = config.stripe.webhookSecret

// Validation schemas for webhook payloads
const checkoutSessionMetadataSchema = z.object({
  userId: z.string().uuid(),
  tierId: z.string()
})

const subscriptionSchema = z.object({
  id: z.string(),
  customer: z.string(),
  status: z.enum(['active', 'canceled', 'past_due', 'unpaid', 'incomplete', 'trialing']),
  cancel_at_period_end: z.boolean()
})

// No custom interface needed - using standard Stripe.Invoice with proper property access

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const headersList = await headers()
    const signature = headersList.get('stripe-signature')!

    let event
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (_error) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
    }

    const supabase = await createClient()

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object
        
        // Validate metadata
        try {
          const metadata = checkoutSessionMetadataSchema.parse({
            userId: session.metadata?.userId,
            tierId: session.metadata?.tierId
          })
          
          const userId = metadata.userId
          const tierId = metadata.tierId

          // Create subscription record
          const subscription = await stripe.subscriptions.retrieve(session.subscription as string, {
            expand: ['items']
          })
          
          // Validate subscription data
          const validatedSub = subscriptionSchema.parse(subscription)
          
          // Get period dates from the first subscription item
          const firstItem = subscription.items.data[0]
          if (!firstItem) {
            throw new Error('No subscription items found')
          }
          
          await supabase.from('user_subscriptions').insert({
            user_id: userId,
            tier_id: tierId,
            status: 'active',
            stripe_subscription_id: validatedSub.id,
            stripe_customer_id: validatedSub.customer,
            current_period_start: new Date(firstItem.current_period_start * 1000),
            current_period_end: new Date(firstItem.current_period_end * 1000),
            cancel_at_period_end: validatedSub.cancel_at_period_end
          })

          // Subscription created successfully
        } catch (_error) {
          // Invalid checkout session data
        }
        break
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        
        try {
          // Validate subscription data
          const validatedSub = subscriptionSchema.parse(subscription)
          
          // Get period dates from the first subscription item
          const firstItem = subscription.items.data[0]
          if (!firstItem) {
            throw new Error('No subscription items found')
          }
          
          await supabase
            .from('user_subscriptions')
            .update({
              status: validatedSub.status,
              current_period_start: new Date(firstItem.current_period_start * 1000),
              current_period_end: new Date(firstItem.current_period_end * 1000),
              cancel_at_period_end: validatedSub.cancel_at_period_end
            })
            .eq('stripe_subscription_id', validatedSub.id)
        } catch (_error) {
          // Invalid subscription data
        }

        // Subscription updated
        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        
        await supabase
          .from('user_subscriptions')
          .update({ status: 'canceled' })
          .eq('stripe_subscription_id', subscription.id)

        // Subscription canceled
        break
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        
        // Reset usage for new billing period  
        if (invoice.billing_reason === 'subscription_cycle' && invoice.parent?.subscription_details?.subscription) {
          const subscriptionRef = invoice.parent.subscription_details.subscription
          const subscriptionId = typeof subscriptionRef === 'string' ? subscriptionRef : subscriptionRef.id
          const subscription = await stripe.subscriptions.retrieve(subscriptionId)
          const { data: userSub } = await supabase
            .from('user_subscriptions')
            .select('user_id')
            .eq('stripe_subscription_id', subscription.id)
            .single()

          if (userSub) {
            const periodStart = new Date().toISOString().slice(0, 7)
            await supabase
              .from('usage_tracking')
              .upsert({
                user_id: userSub.user_id,
                period_start: periodStart,
                ai_generations: 0,
                projects: 0,
                exports: 0,
                storage_used: 0
              })
          }
        }
        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        
        if (invoice.parent?.subscription_details?.subscription) {
          const subscriptionRef = invoice.parent.subscription_details.subscription
          const subscriptionId = typeof subscriptionRef === 'string' ? subscriptionRef : subscriptionRef.id
          
          await supabase
            .from('user_subscriptions')
            .update({ status: 'past_due' })
            .eq('stripe_subscription_id', subscriptionId)
        }

        // Payment failed
        break
      }

      default:
        // Unhandled event type
    }

    return NextResponse.json({ received: true })
  } catch (_error) {
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}