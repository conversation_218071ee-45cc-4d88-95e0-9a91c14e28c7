'use client'

import { Suspense } from 'react'
import { cn } from '@/lib/utils'
import { PanelWrapper, usePanels, PanelProps } from '@/lib/panels'
import { Loader2 } from 'lucide-react'

interface PanelLayoutProps {
  projectId: string
  userId?: string
  chapterId?: string
  content?: string
  selectedText?: string
  seriesId?: string
  metadata?: Record<string, any>
  children: React.ReactNode
  className?: string
}

export function PanelLayout({
  projectId,
  userId,
  chapterId,
  content,
  selectedText,
  seriesId,
  metadata,
  children,
  className
}: PanelLayoutProps) {
  const { plugins, panelStates } = usePanels()

  // Get visible panels by position
  const leftPanels = plugins.filter(p => {
    const state = panelStates.get(p.id)
    return state?.visible && state.position === 'left'
  })

  const rightPanels = plugins.filter(p => {
    const state = panelStates.get(p.id)
    return state?.visible && state.position === 'right'
  })

  const bottomPanels = plugins.filter(p => {
    const state = panelStates.get(p.id)
    return state?.visible && state.position === 'bottom'
  })

  const floatingPanels = plugins.filter(p => {
    const state = panelStates.get(p.id)
    return state?.visible && state.position === 'floating'
  })

  // Create common panel props
  const createPanelProps = (): Omit<PanelProps, 'onClose' | 'onAction' | 'onSettingsChange'> => ({
    projectId,
    userId,
    chapterId,
    content,
    selectedText,
    seriesId,
    metadata
  })

  const panelProps = createPanelProps()

  return (
    <div className={cn('flex h-full', className)}>
      {/* Left panels */}
      {leftPanels.length > 0 && (
        <div className="flex">
          {leftPanels.map(plugin => (
            <Suspense
              key={plugin.id}
              fallback={
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-4 w-4 animate-spin" />
                </div>
              }
            >
              <PanelWrapper plugin={plugin} panelProps={panelProps} />
            </Suspense>
          ))}
        </div>
      )}

      {/* Main content area */}
      <div className="flex-1 flex flex-col">
        {/* Editor area */}
        <div className="flex-1 flex">
          {/* Main content */}
          <div className="flex-1">
            {children}
          </div>

          {/* Right panels */}
          {rightPanels.length > 0 && (
            <div className="flex">
              {rightPanels.map(plugin => (
                <Suspense
                  key={plugin.id}
                  fallback={
                    <div className="flex items-center justify-center p-4">
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </div>
                  }
                >
                  <PanelWrapper plugin={plugin} panelProps={panelProps} />
                </Suspense>
              ))}
            </div>
          )}
        </div>

        {/* Bottom panels */}
        {bottomPanels.length > 0 && (
          <div className="flex flex-col">
            {bottomPanels.map(plugin => (
              <Suspense
                key={plugin.id}
                fallback={
                  <div className="flex items-center justify-center p-4">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                }
              >
                <PanelWrapper plugin={plugin} panelProps={panelProps} />
              </Suspense>
            ))}
          </div>
        )}
      </div>

      {/* Floating panels */}
      {floatingPanels.map(plugin => (
        <Suspense
          key={plugin.id}
          fallback={
            <div className="fixed inset-0 flex items-center justify-center bg-black/20">
              <Loader2 className="h-8 w-8 animate-spin text-white" />
            </div>
          }
        >
          <PanelWrapper 
            plugin={plugin} 
            panelProps={panelProps}
            className="fixed z-40"
          />
        </Suspense>
      ))}
    </div>
  )
}