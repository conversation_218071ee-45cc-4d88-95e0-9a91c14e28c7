import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { config } from '@/lib/config'
import Stripe from 'stripe'

// Initialize Stripe client
const stripe = new Stripe(config.stripe.secretKey, {
  apiVersion: '2025-06-30.basil',
})

// Initialize Supabase client with service role
const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey)

// Helper function to sync Stripe data to Supabase
async function syncStripeData(event: Stripe.Event) {
  const { type, data } = event
  const object = data.object as Stripe.Customer | Stripe.Subscription | Stripe.Invoice | Stripe.PaymentIntent

  try {
    switch (type) {
      case 'customer.created':
      case 'customer.updated':
        const customer = object as Stripe.Customer;
        await supabase
          .from('stripe_customers')
          .upsert({
            id: customer.id,
            email: customer.email,
            name: customer.name,
            created: customer.created,
            updated: Math.floor(Date.now() / 1000),
            metadata: customer.metadata || {},
            raw_data: customer
          })
        break

      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        const subscription = object as Stripe.Subscription;
        await supabase
          .from('stripe_subscriptions')
          .upsert({
            id: subscription.id,
            customer: subscription.customer as string,
            status: subscription.status,
            current_period_start: (subscription as Stripe.Subscription & { current_period_start: number }).current_period_start,
            current_period_end: (subscription as Stripe.Subscription & { current_period_end: number }).current_period_end,
            cancel_at_period_end: (subscription as Stripe.Subscription & { cancel_at_period_end: boolean }).cancel_at_period_end,
            created: subscription.created,
            updated: Math.floor(Date.now() / 1000),
            metadata: subscription.metadata || {},
            raw_data: subscription
          })
        break

      case 'invoice.created':
      case 'invoice.updated':
      case 'invoice.payment_succeeded':
      case 'invoice.payment_failed':
        const invoice = object as Stripe.Invoice;
        await supabase
          .from('stripe_invoices')
          .upsert({
            id: invoice.id,
            customer: invoice.customer as string,
            subscription: (invoice as Stripe.Invoice & { subscription: string }).subscription,
            status: invoice.status,
            amount_paid: invoice.amount_paid || 0,
            amount_due: invoice.amount_due || 0,
            created: invoice.created,
            updated: Math.floor(Date.now() / 1000),
            metadata: invoice.metadata || {},
            raw_data: invoice
          })
        break

      case 'payment_intent.succeeded':
      case 'payment_intent.payment_failed':
        const paymentIntent = object as Stripe.PaymentIntent;
        await supabase
          .from('stripe_payment_intents')
          .upsert({
            id: paymentIntent.id,
            customer: paymentIntent.customer as string,
            amount: paymentIntent.amount,
            currency: paymentIntent.currency,
            status: paymentIntent.status,
            created: paymentIntent.created,
            updated: Math.floor(Date.now() / 1000),
            metadata: paymentIntent.metadata || {},
            raw_data: paymentIntent
          })
        break

      default:
        console.log(`Unhandled event type: ${type}`)
    }
  } catch (error) {
    console.error(`Error syncing ${type}:`, error)
    throw error
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the raw body as text
    const body = await request.text()

    // Get Stripe signature from headers
    const stripeSignature = request.headers.get('stripe-signature')

    if (!stripeSignature) {
      console.error('Missing stripe-signature header')
      return NextResponse.json(
        { error: 'Missing stripe-signature header' },
        { status: 400 }
      )
    }

    console.log('📥 Processing Stripe webhook...')

    // Verify webhook signature
    let event: Stripe.Event
    try {
      event = stripe.webhooks.constructEvent(
        body,
        stripeSignature,
        config.stripe.webhookSecret
      )
    } catch (err) {
      console.error('Webhook signature verification failed:', err)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Sync the data to Supabase
    await syncStripeData(event)

    console.log(`✅ Stripe webhook processed successfully: ${event.type}`)

    return NextResponse.json(
      { received: true, type: event.type },
      { status: 200 }
    )

  } catch (error) {
    console.error('❌ Error processing Stripe webhook:', error)

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Disable body parsing for raw webhook data
export const runtime = 'nodejs'
