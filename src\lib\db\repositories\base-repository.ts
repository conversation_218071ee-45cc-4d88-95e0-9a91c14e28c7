import { SupabaseClient } from '@supabase/supabase-js';
import { logger } from '@/lib/services/logger';

export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasMore: boolean;
}

export interface RepositoryResult<T> {
  data?: T;
  error?: Error;
}

export interface RepositoryListResult<T> {
  data?: T[];
  error?: Error;
}

export abstract class BaseRepository<T extends { id: string }> {
  protected supabase: SupabaseClient;
  protected tableName: string;
  protected selectColumns: string = '*';
  
  constructor(supabase: SupabaseClient, tableName: string) {
    this.supabase = supabase;
    this.tableName = tableName;
  }
  
  /**
   * Find a record by ID
   */
  async findById(id: string): Promise<RepositoryResult<T>> {
    try {
      const { data, error } = await this.supabase
        .from(this.tableName)
        .select(this.selectColumns)
        .eq('id', id)
        .single();
        
      if (error) {
        logger.error(`Error finding ${this.tableName} by id`, { error, id });
        return { error: new Error(error.message) };
      }
      
      return { data };
    } catch (error) {
      logger.error(`Unexpected error in findById`, { error, tableName: this.tableName, id });
      return { error: error instanceof Error ? error : new Error('Unknown error') };
    }
  }
  
  /**
   * Find all records matching criteria
   */
  async findAll(criteria?: Partial<T>): Promise<RepositoryListResult<T>> {
    try {
      let query = this.supabase
        .from(this.tableName)
        .select(this.selectColumns);
        
      // Apply criteria filters
      if (criteria) {
        Object.entries(criteria).forEach(([key, value]) => {
          if (value !== undefined) {
            query = query.eq(key, value);
          }
        });
      }
      
      const { data, error } = await query;
      
      if (error) {
        logger.error(`Error finding all ${this.tableName}`, { error, criteria });
        return { error: new Error(error.message) };
      }
      
      return { data: data || [] };
    } catch (error) {
      logger.error(`Unexpected error in findAll`, { error, tableName: this.tableName });
      return { error: error instanceof Error ? error : new Error('Unknown error') };
    }
  }
  
  /**
   * Find records with pagination
   */
  async findPaginated(
    criteria?: Partial<T>,
    options: PaginationOptions = {}
  ): Promise<RepositoryResult<PaginatedResult<T>>> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = options;
      
      const offset = (page - 1) * limit;
      
      // Build query
      let query = this.supabase
        .from(this.tableName)
        .select(this.selectColumns, { count: 'exact' });
        
      // Apply criteria filters
      if (criteria) {
        Object.entries(criteria).forEach(([key, value]) => {
          if (value !== undefined) {
            query = query.eq(key, value);
          }
        });
      }
      
      // Apply sorting and pagination
      query = query
        .order(sortBy, { ascending: sortOrder === 'asc' })
        .range(offset, offset + limit - 1);
        
      const { data, error, count } = await query;
      
      if (error) {
        logger.error(`Error in paginated find`, { error, criteria, options });
        return { error: new Error(error.message) };
      }
      
      const total = count || 0;
      const totalPages = Math.ceil(total / limit);
      
      return {
        data: {
          data: data || [],
          total,
          page,
          limit,
          totalPages,
          hasMore: page < totalPages
        }
      };
    } catch (error) {
      logger.error(`Unexpected error in findPaginated`, { error, tableName: this.tableName });
      return { error: error instanceof Error ? error : new Error('Unknown error') };
    }
  }
  
  /**
   * Create a new record
   */
  async create(data: Omit<T, 'id' | 'created_at' | 'updated_at'>): Promise<RepositoryResult<T>> {
    try {
      const { data: created, error } = await this.supabase
        .from(this.tableName)
        .insert(data)
        .select(this.selectColumns)
        .single();
        
      if (error) {
        logger.error(`Error creating ${this.tableName}`, { error, data });
        return { error: new Error(error.message) };
      }
      
      return { data: created };
    } catch (error) {
      logger.error(`Unexpected error in create`, { error, tableName: this.tableName });
      return { error: error instanceof Error ? error : new Error('Unknown error') };
    }
  }
  
  /**
   * Update a record
   */
  async update(
    id: string, 
    data: Partial<Omit<T, 'id' | 'created_at' | 'updated_at'>>
  ): Promise<RepositoryResult<T>> {
    try {
      const updateData = {
        ...data,
        updated_at: new Date().toISOString()
      };
      
      const { data: updated, error } = await this.supabase
        .from(this.tableName)
        .update(updateData)
        .eq('id', id)
        .select(this.selectColumns)
        .single();
        
      if (error) {
        logger.error(`Error updating ${this.tableName}`, { error, id, data });
        return { error: new Error(error.message) };
      }
      
      return { data: updated };
    } catch (error) {
      logger.error(`Unexpected error in update`, { error, tableName: this.tableName });
      return { error: error instanceof Error ? error : new Error('Unknown error') };
    }
  }
  
  /**
   * Delete a record (soft delete if supported)
   */
  async delete(id: string, soft: boolean = true): Promise<RepositoryResult<void>> {
    try {
      if (soft && this.supportsSoftDelete()) {
        const { error } = await this.supabase
          .from(this.tableName)
          .update({ deleted_at: new Date().toISOString() })
          .eq('id', id);
          
        if (error) {
          logger.error(`Error soft deleting ${this.tableName}`, { error, id });
          return { error: new Error(error.message) };
        }
      } else {
        const { error } = await this.supabase
          .from(this.tableName)
          .delete()
          .eq('id', id);
          
        if (error) {
          logger.error(`Error hard deleting ${this.tableName}`, { error, id });
          return { error: new Error(error.message) };
        }
      }
      
      return { data: undefined };
    } catch (error) {
      logger.error(`Unexpected error in delete`, { error, tableName: this.tableName });
      return { error: error instanceof Error ? error : new Error('Unknown error') };
    }
  }
  
  /**
   * Bulk create records
   */
  async bulkCreate(
    items: Array<Omit<T, 'id' | 'created_at' | 'updated_at'>>
  ): Promise<RepositoryListResult<T>> {
    try {
      const { data, error } = await this.supabase
        .from(this.tableName)
        .insert(items)
        .select(this.selectColumns);
        
      if (error) {
        logger.error(`Error bulk creating ${this.tableName}`, { error, count: items.length });
        return { error: new Error(error.message) };
      }
      
      return { data: data || [] };
    } catch (error) {
      logger.error(`Unexpected error in bulkCreate`, { error, tableName: this.tableName });
      return { error: error instanceof Error ? error : new Error('Unknown error') };
    }
  }
  
  /**
   * Count records matching criteria
   */
  async count(criteria?: Partial<T>): Promise<RepositoryResult<number>> {
    try {
      let query = this.supabase
        .from(this.tableName)
        .select('*', { count: 'exact', head: true });
        
      // Apply criteria filters
      if (criteria) {
        Object.entries(criteria).forEach(([key, value]) => {
          if (value !== undefined) {
            query = query.eq(key, value);
          }
        });
      }
      
      const { count, error } = await query;
      
      if (error) {
        logger.error(`Error counting ${this.tableName}`, { error, criteria });
        return { error: new Error(error.message) };
      }
      
      return { data: count || 0 };
    } catch (error) {
      logger.error(`Unexpected error in count`, { error, tableName: this.tableName });
      return { error: error instanceof Error ? error : new Error('Unknown error') };
    }
  }
  
  /**
   * Check if a record exists
   */
  async exists(id: string): Promise<boolean> {
    const result = await this.count({ id } as Partial<T>);
    return result.data === 1;
  }
  
  /**
   * Override to specify if the table supports soft delete
   */
  protected supportsSoftDelete(): boolean {
    return true; // Most tables in BookScribe support soft delete
  }
  
  /**
   * Execute a custom query (for complex operations)
   */
  protected async executeQuery<R>(
    queryBuilder: <Q>(query: Q) => Q
  ): Promise<RepositoryResult<R>> {
    try {
      const baseQuery = this.supabase.from(this.tableName);
      const query = queryBuilder(baseQuery);
      const { data, error } = await query;
      
      if (error) {
        logger.error(`Error in custom query`, { error, tableName: this.tableName });
        return { error: new Error(error.message) };
      }
      
      return { data };
    } catch (error) {
      logger.error(`Unexpected error in executeQuery`, { error, tableName: this.tableName });
      return { error: error instanceof Error ? error : new Error('Unknown error') };
    }
  }
}