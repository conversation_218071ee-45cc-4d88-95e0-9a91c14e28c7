'use client'

import { useEffect, useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Users, Wifi, WifiOff } from 'lucide-react'
import { cn } from '@/lib/utils'

interface Collaborator {
  userId: string
  userName?: string
  cursor?: { line: number; column: number }
  lastSeen: string
}

interface RealtimeIndicatorProps {
  isConnected: boolean
  collaborators: Collaborator[]
  className?: string
}

export function RealtimeIndicator({ 
  isConnected, 
  collaborators = [],
  className 
}: RealtimeIndicatorProps) {
  const [showPulse, setShowPulse] = useState(false)

  useEffect(() => {
    if (isConnected) {
      setShowPulse(true)
      const timer = setTimeout(() => setShowPulse(false), 2000)
      return () => clearTimeout(timer)
    }
  }, [isConnected])

  const getInitials = (userName?: string, userId?: string) => {
    if (userName) {
      return userName.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
    }
    return userId?.slice(0, 2).toUpperCase() || '??'
  }

  const getCollaboratorColor = (index: number) => {
    const colors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-purple-500',
      'bg-orange-500',
      'bg-pink-500',
      'bg-yellow-500',
    ]
    return colors[index % colors.length]
  }

  return (
    <div className={cn("flex items-center gap-3", className)}>
      {/* Connection Status */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center gap-2">
              <div className="relative">
                {isConnected ? (
                  <Wifi className="h-4 w-4 text-green-600" />
                ) : (
                  <WifiOff className="h-4 w-4 text-muted-foreground" />
                )}
                {showPulse && isConnected && (
                  <div className="absolute inset-0 -m-1">
                    <div className="h-6 w-6 rounded-full bg-green-600 animate-ping opacity-75" />
                  </div>
                )}
              </div>
              <Badge 
                variant={isConnected ? "default" : "secondary"}
                className={cn(
                  "text-xs",
                  isConnected && "bg-green-100 text-green-800 hover:bg-green-100"
                )}
              >
                {isConnected ? 'Live' : 'Offline'}
              </Badge>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-xs">
              {isConnected 
                ? 'Real-time sync is active' 
                : 'Working offline - changes will sync when connected'
              }
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* Collaborators */}
      {collaborators.length > 0 && (
        <>
          <div className="h-4 w-px bg-border" />
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <div className="flex -space-x-2">
              {collaborators.slice(0, 3).map((collaborator, index) => (
                <TooltipProvider key={collaborator.userId}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Avatar className="h-6 w-6 border-2 border-background">
                        <AvatarFallback 
                          className={cn(
                            "text-xs text-white",
                            getCollaboratorColor(index)
                          )}
                        >
                          {getInitials(collaborator.userName, collaborator.userId)}
                        </AvatarFallback>
                      </Avatar>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-xs space-y-1">
                        <p className="font-medium">
                          {collaborator.userName || `User ${collaborator.userId.slice(0, 8)}`}
                        </p>
                        {collaborator.cursor && (
                          <p className="text-muted-foreground">
                            Line {collaborator.cursor.line}, Col {collaborator.cursor.column}
                          </p>
                        )}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ))}
              {collaborators.length > 3 && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                        <span className="text-xs text-muted-foreground">
                          +{collaborators.length - 3}
                        </span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">
                        {collaborators.length - 3} more {collaborators.length - 3 === 1 ? 'person' : 'people'} editing
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  )
}