#!/usr/bin/env node

/**
 * Service Connection Diagnostic Script
 * Checks all external service connections and reports status
 */

const chalk = require('chalk');

// Check if we're in demo mode
const isDemoMode = process.env.NEXT_PUBLIC_DEMO_MODE === 'true';
const isDevBypass = process.env.NEXT_PUBLIC_DEV_BYPASS_AUTH === 'true';

console.log(chalk.blue.bold('\n🔍 BookScribe Service Connection Check\n'));

// Check environment variables
console.log(chalk.yellow('📋 Environment Variables:'));
console.log(`  Demo Mode: ${isDemoMode ? chalk.red('ENABLED') : chalk.green('DISABLED')}`);
console.log(`  Dev Auth Bypass: ${isDevBypass ? chalk.yellow('ENABLED') : chalk.green('DISABLED')}`);
console.log(`  Environment: ${process.env.NODE_ENV || 'development'}\n`);

// Check Supabase
console.log(chalk.yellow('🗄️  Supabase Configuration:'));
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnon = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseService = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log(`  URL: ${supabaseUrl ? chalk.green('✓ Set') : chalk.red('✗ Missing')}`);
console.log(`  Anon Key: ${supabaseAnon ? chalk.green('✓ Set') : chalk.red('✗ Missing')}`);
console.log(`  Service Key: ${supabaseService && supabaseService !== 'your_supabase_service_role_key' ? chalk.green('✓ Set') : chalk.red('✗ Missing or placeholder')}`);

// Check OpenAI
console.log(chalk.yellow('\n🤖 OpenAI Configuration:'));
const openaiKey = process.env.OPENAI_API_KEY;
console.log(`  API Key: ${openaiKey && openaiKey !== 'your_openai_api_key' ? chalk.green('✓ Set') : chalk.red('✗ Missing or placeholder')}`);

// Check Stripe
console.log(chalk.yellow('\n💳 Stripe Configuration:'));
const stripeSecret = process.env.STRIPE_SECRET_KEY;
const stripePublishable = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
const stripeWebhook = process.env.STRIPE_WEBHOOK_SECRET;
const stripePrices = {
  basic: process.env.STRIPE_PRICE_ID_BASIC,
  pro: process.env.STRIPE_PRICE_ID_PRO,
  enterprise: process.env.STRIPE_PRICE_ID_ENTERPRISE
};

console.log(`  Secret Key: ${stripeSecret ? chalk.green('✓ Set') : chalk.red('✗ Missing')}`);
console.log(`  Publishable Key: ${stripePublishable ? chalk.green('✓ Set') : chalk.red('✗ Missing')}`);
console.log(`  Webhook Secret: ${stripeWebhook && stripeWebhook !== 'whsec_your_stripe_webhook_secret' ? chalk.green('✓ Set') : chalk.red('✗ Missing or placeholder')}`);
console.log(`  Price IDs:`);
console.log(`    - Basic: ${stripePrices.basic && !stripePrices.basic.includes('your_') ? chalk.green('✓ Set') : chalk.red('✗ Missing or placeholder')}`);
console.log(`    - Pro: ${stripePrices.pro && !stripePrices.pro.includes('your_') ? chalk.green('✓ Set') : chalk.red('✗ Missing or placeholder')}`);
console.log(`    - Enterprise: ${stripePrices.enterprise && !stripePrices.enterprise.includes('your_') ? chalk.green('✓ Set') : chalk.red('✗ Missing or placeholder')}`);

// Summary
console.log(chalk.blue.bold('\n📊 Summary:\n'));

const criticalMissing = [];
if (!supabaseService || supabaseService === 'your_supabase_service_role_key') {
  criticalMissing.push('Supabase Service Role Key');
}
if (!openaiKey || openaiKey === 'your_openai_api_key') {
  criticalMissing.push('OpenAI API Key');
}

if (criticalMissing.length > 0) {
  console.log(chalk.red.bold('❌ Critical services not configured:'));
  criticalMissing.forEach(service => {
    console.log(chalk.red(`   - ${service}`));
  });
  console.log(chalk.yellow('\n⚠️  The application will not function properly without these services.'));
  console.log(chalk.yellow('   Please update your .env.local file with the correct values.\n'));
} else if (isDemoMode) {
  console.log(chalk.yellow('⚠️  Demo mode is enabled. Using mock data instead of real services.'));
  console.log(chalk.yellow('   Set NEXT_PUBLIC_DEMO_MODE=false to use real services.\n'));
} else {
  console.log(chalk.green.bold('✅ All critical services are configured!'));
  console.log(chalk.green('   The application should be fully functional.\n'));
}

// Instructions
if (criticalMissing.length > 0) {
  console.log(chalk.cyan('📝 Setup Instructions:\n'));
  
  if (criticalMissing.includes('Supabase Service Role Key')) {
    console.log(chalk.cyan('1. Get Supabase Service Role Key:'));
    console.log('   - Go to your Supabase project dashboard');
    console.log('   - Navigate to Settings → API');
    console.log('   - Copy the "service_role" key (secret)');
    console.log('   - Update SUPABASE_SERVICE_ROLE_KEY in .env.local\n');
  }
  
  if (criticalMissing.includes('OpenAI API Key')) {
    console.log(chalk.cyan('2. Get OpenAI API Key:'));
    console.log('   - Go to https://platform.openai.com/api-keys');
    console.log('   - Create a new secret key');
    console.log('   - Update OPENAI_API_KEY in .env.local\n');
  }
  
  console.log(chalk.cyan('3. Restart your development server after updating .env.local\n'));
}

process.exit(criticalMissing.length > 0 ? 1 : 0);