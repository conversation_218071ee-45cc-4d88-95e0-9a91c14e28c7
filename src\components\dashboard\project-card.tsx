"use client"

import { memo, useMemo } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Calendar, FileText, Users, Target, BookOpen, Edit } from 'lucide-react'
import Link from 'next/link'

interface Project {
  id: string
  title: string
  description: string | null
  primary_genre: string | null
  target_word_count: number | null
  current_word_count: number
  status: string
  created_at: string
  updated_at: string
  chapters?: Array<{
    id: string
    chapter_number: number
    title: string | null
    actual_word_count: number
    status: string
  }>
  characters?: Array<{
    id: string
    name: string
    role: string | null
  }>
}

interface ProjectCardProps {
  project: Project
  onEdit?: () => void
  onDelete?: () => void
}

const statusColors = {
  planning: 'bg-warm-100 text-warm-800 border-warm-200',
  writing: 'bg-literary-parchment text-literary-ink border-literary-amber',
  editing: 'bg-amber-100 text-amber-800 border-amber-200',
  complete: 'bg-literary-gold/20 text-literary-quill border-literary-gold',
  paused: 'bg-warm-200 text-warm-700 border-warm-300'
}

const statusLabels = {
  planning: 'Planning',
  writing: 'Writing',
  editing: 'Editing', 
  complete: 'Complete',
  paused: 'Paused'
}

const ProjectCardComponent = function ProjectCard({ project, onEdit, onDelete }: ProjectCardProps) {
  // Memoize expensive calculations
  const progress = useMemo(() => {
    return project.target_word_count 
      ? Math.min((project.current_word_count / project.target_word_count) * 100, 100)
      : 0
  }, [project.current_word_count, project.target_word_count])

  const chapterStats = useMemo(() => {
    const completedChapters = project.chapters?.filter(ch => ch.status === 'complete').length || 0
    const totalChapters = project.chapters?.length || 0
    return { completedChapters, totalChapters }
  }, [project.chapters])
  
  const formatDate = useMemo(() => (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }, [])

  const getStatusColor = (status: string) => {
    return `${statusColors[status as keyof typeof statusColors] || statusColors.planning} border`
  }

  const getStatusLabel = (status: string) => {
    return statusLabels[status as keyof typeof statusLabels] || 'Planning'
  }

  return (
    <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-warm-200 bg-white/90 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg font-literary font-semibold truncate text-warm-800">
              {project.title}
            </CardTitle>
            <div className="flex items-center gap-2 mt-2">
              <Badge className={getStatusColor(project.status)}>
                {getStatusLabel(project.status)}
              </Badge>
              {project.primary_genre && (
                <Badge variant="outline" className="text-xs border-warm-300 text-warm-700 bg-warm-50">
                  {project.primary_genre}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {project.description && (
          <p className="text-sm text-warm-600 line-clamp-2 mt-3 font-literary">
            {project.description}
          </p>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress Section */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="flex items-center gap-1">
              <Target className="h-4 w-4" />
              Word Count
            </span>
            <span className="font-medium">
              {project.current_word_count.toLocaleString()}
              {project.target_word_count && (
                <span className="text-muted-foreground">
                  /{project.target_word_count.toLocaleString()}
                </span>
              )}
            </span>
          </div>
          
          {project.target_word_count && (
            <Progress value={progress} className="h-2" />
          )}
          
          {progress > 0 && (
            <div className="text-xs text-muted-foreground text-right">
              {progress.toFixed(1)}% complete
            </div>
          )}
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
              <FileText className="h-4 w-4" />
            </div>
            <div className="font-medium">{chapterStats.completedChapters}/{chapterStats.totalChapters}</div>
            <div className="text-xs text-muted-foreground">Chapters</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
              <Users className="h-4 w-4" />
            </div>
            <div className="font-medium">{project.characters?.length || 0}</div>
            <div className="text-xs text-muted-foreground">Characters</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
              <Calendar className="h-4 w-4" />
            </div>
            <div className="font-medium text-xs">{formatDate(project.updated_at)}</div>
            <div className="text-xs text-muted-foreground">Updated</div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button asChild className="flex-1" size="sm">
            <Link href={`/projects/${project.id}`} className="flex items-center gap-1">
              <BookOpen className="h-4 w-4" />
              Open Project
            </Link>
          </Button>
          
          {project.status !== 'planning' && (
            <Button asChild variant="outline" size="sm">
              <Link href={`/projects/${project.id}/write`} className="flex items-center gap-1">
                <Edit className="h-4 w-4" />
                Write
              </Link>
            </Button>
          )}
        </div>
        
        {/* Quick Actions (visible on hover) */}
        {(onEdit || onDelete) && (
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
            {onEdit && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onEdit} 
                className="flex-1"
                aria-label={`Edit details for ${project.title}`}
              >
                Edit Details
              </Button>
            )}
            {onDelete && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onDelete} 
                className="text-destructive hover:text-destructive"
                aria-label={`Delete project ${project.title}`}
              >
                Delete
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Memoize the component to prevent unnecessary re-renders when project data hasn't changed
export const ProjectCard = memo(ProjectCardComponent)