# BookScribe AI - LLM/Agent Development Guide

## Project Overview

**BookScribe AI** is an AI-powered novel writing IDE that helps authors create epic novels with AI agents that maintain context across hundreds of thousands of words. The system uses a multi-agent architecture with specialized AI agents for different aspects of novel writing.

### Core Purpose
- **Primary Function**: AI-assisted novel writing with context-aware agents
- **Target Users**: Authors, writers, and creative professionals
- **Key Value**: Maintains narrative consistency across long-form content (100k+ words)
- **Design Philosophy**: Writer's Sanctuary theme - warm, literary atmosphere with paper-like tones

## Technology Stack

### Frontend
- **Framework**: Next.js 15+ with App Router
- **UI Library**: Shadcn/ui with custom @21stdev design system
- **Styling**: Tailwind CSS with CSS custom properties
- **Theme System**: Multi-theme support with Writer's Sanctuary as default
- **State Management**: Zustand for client state, React Context for providers

### Backend & Database
- **Database**: Supabase (PostgreSQL) with real-time capabilities
- **Authentication**: Supa<PERSON> Auth with JWT tokens
- **File Storage**: Supabase Storage for documents and assets
- **API**: Next.js API routes with TypeScript

### AI & Agents
- **Primary AI**: OpenAI GPT-4.1/GPT-o4 mini
- **Agent Framework**: Custom multi-agent orchestration system
- **Context Management**: Advanced context-aware writing assistance

### Development Tools
- **Language**: TypeScript (strict mode, avoid 'any' types)
- **Testing**: Jest + Playwright for E2E testing
- **Browser Automation**: Puppeteer + Playwright available
- **Package Manager**: npm (use package managers, never edit package files directly)
- **Deployment**: Vercel (frontend) + Supabase (backend)

## Architecture Overview

### Multi-Agent System
The core of BookScribe is a sophisticated multi-agent architecture:

1. **Story Architect Agent**: Creates comprehensive story structures and plot points
2. **Character Developer Agent**: Develops detailed character profiles and arcs
3. **Chapter Planner Agent**: Plans chapter outlines and scene structures
4. **Writing Agent**: Generates actual chapter content with context awareness
5. **Prose Editor Agent**: Focuses on prose quality and style improvement
6. **Story Analyst Agent**: Analyzes plot structure and character development
7. **Adaptive Planning Agent**: Adjusts plans based on user changes

### Agent Orchestration
- **Handoff-based Pipeline**: Agents pass work between each other
- **Concurrent Processing**: Up to 3 agents can work simultaneously
- **Context Sharing**: All agents share BookContext for consistency
- **Quality Assessment**: Built-in quality scoring and validation

## Standard Workflow

1. **Information Gathering**: Always use codebase-retrieval tool first to understand existing code
2. **Planning**: Create detailed plans in docs/ folder, maintain checklists in docs/PRD.md
3. **Approach Selection**: Consider 2-3 approaches, pick the best one that fits guardrails
4. **User Approval**: Get sign-off on plans before implementation
5. **Implementation**: Simple, efficient changes - maintain existing files vs building new
6. **Modularity**: Keep files under 700 lines, use shared utilities
7. **Testing**: Always suggest writing/updating tests after code changes

## Project Structure

```
BookScribe/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── (marketing)/        # Marketing pages (landing, demo)
│   │   ├── (app)/             # Main application pages
│   │   ├── api/               # API routes
│   │   └── globals.css        # Global styles with theme definitions
│   ├── components/            # React components
│   │   ├── ui/               # Shadcn/ui base components
│   │   ├── settings/         # Settings and customization components
│   │   ├── demo/             # Demo components (use mock data)
│   │   └── theme-provider.tsx # Theme system provider
│   ├── lib/                  # Core utilities and services
│   │   ├── agents/           # AI agent implementations
│   │   ├── services/         # Business logic services
│   │   ├── themes/           # Theme system (Writer's Sanctuary, etc.)
│   │   ├── settings/         # Settings management
│   │   ├── supabase/         # Database client configuration
│   │   ├── auth/             # Authentication utilities
│   │   └── utils.ts          # Shared utilities
│   ├── contexts/             # React contexts (auth, settings)
│   ├── hooks/                # Custom React hooks
│   └── types/                # TypeScript type definitions
├── docs/                     # Comprehensive documentation
│   ├── PRD.md               # Product Requirements Document
│   ├── DEVELOPMENT_GUIDE.md  # Development setup guide
│   ├── API_DOCUMENTATION.md  # API reference
│   ├── SUPABASE_IMPLEMENTATION.md # Database guide
│   └── mcp/                 # MCP server documentation
├── supabase/                # Supabase configuration
│   ├── functions/           # Edge Functions
│   └── migrations/          # Database migrations
└── scripts/                 # Build and utility scripts
```

## Environment Setup

### Required Environment Variables
```bash
# Supabase (Database & Auth)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI (Required for AI agents)
OPENAI_API_KEY=your_openai_api_key

# Stripe (Payments)
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# Optional: Development
NEXT_PUBLIC_DEV_BYPASS_AUTH=false  # Set to true for development
NEXT_PUBLIC_DEMO_MODE=true         # For demo mode
```

### Development Commands
```bash
# Development
npm run dev                    # Start development server
npm run dev:demo              # Start in demo mode

# Testing
npm run test                  # Run Jest tests
npm run test:e2e             # Run Playwright E2E tests
npm run test:puppeteer       # Run Puppeteer tests

# Building
npm run build                # Production build
npm run build:analyze        # Build with bundle analysis

# Database
npm run generate:types       # Generate Supabase types
```

## Theme System

### Writer's Sanctuary Design Theme
- **Primary Theme**: Warm paper tones, literary aesthetics
- **Typography**: Serif fonts for reading, monospace for code
- **Color Palette**: Warm browns, creams, and muted earth tones
- **Inspiration**: Notion, Scrivener, Medium, Substack

### Theme Implementation
- **Location**: `src/lib/themes/` and `src/app/globals.css`
- **Themes Available**: Writer's Sanctuary (light/dark), Forest Manuscript, Evening Study, Midnight Ink
- **Customization**: `/customization` page with live preview
- **Settings**: Separate theme selection from functional settings

## AI Agents System

### Agent Architecture
All agents extend `BaseAgent` class and share `BookContext` for consistency:

```typescript
interface BookContext {
  projectId: string;
  settings: ProjectSettings;
  projectSelections: ProjectSettings;
  storyPrompt?: string;
  targetWordCount?: number;
  targetChapters?: number;
}
```

### Core Agents

#### 1. Story Architect Agent (`src/lib/agents/story-architect.ts`)
- **Purpose**: Creates comprehensive story structures and plot points
- **Input**: Project selections, story prompt, target metrics
- **Output**: Detailed story structure with themes, conflicts, and arcs
- **Tools**: OpenAI GPT-4.1 with structured JSON output

#### 2. Character Developer Agent (`src/lib/agents/character-developer.ts`)
- **Purpose**: Develops detailed character profiles and relationship webs
- **Input**: Story structure, character requirements
- **Output**: Character profiles with personality, backstory, arcs, and voice
- **Tools**: OpenAI GPT-4.1 with character consistency validation

#### 3. Chapter Planner Agent (`src/lib/agents/chapter-planner.ts`)
- **Purpose**: Plans chapter outlines and scene structures
- **Input**: Story structure, character profiles, target word count
- **Output**: Detailed chapter outlines with scenes, conflicts, and pacing
- **Tools**: OpenAI GPT-4.1 with narrative structure analysis

#### 4. Writing Agent (`src/lib/agents/writing-agent.ts`)
- **Purpose**: Generates actual chapter content with context awareness
- **Input**: Chapter outline, character states, previous chapters
- **Output**: Complete chapter content with dialogue, description, and narrative
- **Tools**: OpenAI GPT-4.1 with context memory and consistency checking

#### 5. Adaptive Planning Agent (`src/lib/agents/adaptive-planning-agent.ts`)
- **Purpose**: Adjusts plans based on user changes and story evolution
- **Input**: User modifications, current story state
- **Output**: Recommended adjustments to maintain consistency
- **Tools**: OpenAI GPT-o4 mini with analysis and recommendation functions

### Agent Orchestration (`src/lib/agents/advanced-orchestrator.ts`)
- **Concurrent Processing**: Up to 3 agents work simultaneously
- **Task Queue**: Manages dependencies between agent tasks
- **Handoff System**: Agents pass context and results to each other
- **Error Handling**: Automatic retry and fallback mechanisms
- **Progress Tracking**: Real-time status updates and completion metrics

### Agent Communication
- **Shared Context**: All agents access the same BookContext
- **Memory System**: Context-aware memory for long-form consistency
- **Quality Assessment**: Built-in scoring for generated content
- **Validation**: Cross-agent consistency checking

## Available Tools & Integrations

### AI & Language Models
- **OpenAI SDK**: GPT-4.1, GPT-o4 mini for primary AI operations
- **Google Gemini**: Alternative AI model for validation and perspectives
- **Custom Prompts**: Specialized prompts for each agent type
- **Context Management**: Advanced context-aware processing

### Browser Automation & Scraping
- **Playwright**: Full browser automation for E2E testing and scraping
  - Headless and headed modes
  - Multiple browser support (Chromium, Firefox, Safari)
  - Screenshot and PDF generation capabilities
- **Puppeteer**: Additional browser automation option
  - Web scraping and content extraction
  - Dynamic content handling

### Database & Storage
- **Supabase Client**: Real-time database operations
- **Supabase Storage**: File upload and management
- **Real-time Subscriptions**: Live data updates
- **Edge Functions**: Serverless backend processing

### Development Tools
- **TypeScript**: Strict typing throughout (avoid 'any' types)
- **ESLint**: Code quality and consistency
- **Jest**: Unit testing framework
- **Bundle Analyzer**: Performance optimization
- **Madge**: Dependency analysis

### External Services
- **Stripe**: Payment processing and subscription management
- **Sentry**: Error monitoring and performance tracking
- **Vercel**: Deployment and hosting platform

## Development Guidelines

### Code Quality Standards
1. **TypeScript First**: Use proper typing, avoid 'any' types
2. **File Size Limit**: Keep files under 700 lines
3. **Modular Design**: Use shared utilities and components
4. **Minimal Comments**: Code should be self-documenting
5. **Error Handling**: Comprehensive error boundaries and validation

### Package Management Rules
- **Always use package managers** (npm install/uninstall)
- **Never edit package.json directly** unless for configuration
- **Use exact versions** when specified by user
- **Prefer stable dependencies** over bleeding edge

### Authentication & Security
- **Supabase Auth**: JWT-based authentication system
- **Row Level Security**: Database-level access control
- **Environment Variables**: Secure credential management
- **Admin Controls**: Role-based access for admin features

### UI/UX Principles
- **Writer's Sanctuary Theme**: Consistent warm, literary aesthetic
- **Accessibility**: High contrast options, screen reader support
- **Responsive Design**: Mobile-first approach
- **Performance**: Optimized loading and rendering

## User Preferences & Design Principles

### Core Preferences
- **No Package Version Changes**: Avoid changing versions without strong evidence
- **Proper TypeScript**: No 'any' types, comprehensive typing throughout
- **Mock Data for Demos**: Demo components use simulated data, not real generation
- **Writer's Sanctuary Theme**: Consistent application across entire codebase
- **Preserve Existing Functionality**: Don't remove features without explicit permission

### Design Inspiration
- **Writing Tools**: Notion, Scrivener, Medium, Substack aesthetics
- **Theme System**: Based on novelWriter project color schemes
- **Typography**: Serif fonts for reading, monospace for code/inputs
- **Layout**: Resizable panels, GitHub-style formatting

### Settings Architecture
- **Separation of Concerns**: Visual customization separate from functional settings
- **Theme Controls**: Dedicated /customization page for themes
- **Settings Modal**: Typography, editor, and accessibility preferences only
- **Live Preview**: Real-time preview of all customization changes

## Best Practices for Agents

### When Working on BookScribe
1. **Always use codebase-retrieval first** to understand existing implementations
2. **Respect the Writer's Sanctuary theme** - maintain warm, literary aesthetics
3. **Use package managers** - never edit package.json directly
4. **Preserve existing features** - ask before removing functionality
5. **Follow TypeScript standards** - proper typing, no 'any' types
6. **Test suggestions** - always recommend testing after code changes

### Agent Collaboration Guidelines
1. **Share Context**: Use BookContext for consistency across agents
2. **Quality Assessment**: Validate output quality before handoff
3. **Error Recovery**: Implement retry mechanisms for failed operations
4. **Progress Tracking**: Emit progress events for user feedback
5. **Memory Management**: Maintain context for long-form consistency

### Common Patterns
- **Authentication**: Use Supabase Auth with JWT tokens
- **Database**: Supabase client with real-time capabilities
- **Styling**: Tailwind CSS with CSS custom properties
- **State**: Zustand for complex state, React Context for providers
- **Forms**: React Hook Form with Zod validation

## Troubleshooting & Common Issues

### Development Environment
- **Windows Users**: Use PowerShell, `Remove-Item -Recurse -Force` instead of `rm -rf`
- **Node Memory**: Use `NODE_OPTIONS=--max-old-space-size=4096` for builds
- **Turbopack**: Completely removed from project, don't use

### Database Issues
- **Connection**: Check Supabase environment variables
- **Types**: Regenerate types with `npm run generate:types`
- **Migrations**: Use Supabase CLI for database changes

### AI Agent Issues
- **Context Limits**: Monitor token usage and context window
- **Rate Limits**: Implement proper retry mechanisms
- **Quality Control**: Validate agent outputs before use

## Documentation References

### Key Documentation Files
- `docs/PRD.md` - Product Requirements Document
- `docs/DEVELOPMENT_GUIDE.md` - Complete setup guide
- `docs/API_DOCUMENTATION.md` - API reference
- `docs/SUPABASE_IMPLEMENTATION.md` - Database patterns
- `docs/AI_SDK_RESEARCH_2025.md` - AI implementation guide
- `docs/mcp/` - MCP server documentation

### External Resources
- [Next.js App Router](https://nextjs.org/docs/app)
- [Supabase Documentation](https://supabase.com/docs)
- [OpenAI API Reference](https://platform.openai.com/docs)
- [Shadcn/ui Components](https://ui.shadcn.com)
- [Playwright Testing](https://playwright.dev)

---

**Remember**: BookScribe is a sophisticated AI-powered writing tool. Always maintain the literary aesthetic, respect existing functionality, and ensure AI agents work together seamlessly to help authors create amazing novels.
