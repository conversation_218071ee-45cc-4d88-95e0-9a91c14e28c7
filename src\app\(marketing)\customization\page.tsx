/**
 * Customization Page
 * Comprehensive visual customization hub for BookScribe AI
 */

'use client';

import { CustomizationHub } from '@/components/customization/customization-hub'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { SettingsButton } from '@/components/settings/settings-modal'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Palette, Settings } from 'lucide-react'
import Link from 'next/link'
import { useAuth } from '@/contexts/auth-context'

export default function CustomizationPage() {
  // Use optional auth since this is a marketing page
  let user = null;
  try {
    const auth = useAuth();
    user = auth.user;
  } catch (_error) {
    // Auth context not available in marketing layout, which is fine
    user = null;
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-background dark:from-background dark:via-background/95 dark:to-background">
      {/* Header */}
      <header className="border-b border-border bg-background/80 backdrop-blur-xl">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Home
                </Button>
              </Link>
              <div className="flex items-center gap-2">
                <Palette className="h-6 w-6 text-primary" />
                <h1 className="text-xl font-bold" style={{ fontFamily: 'Crimson Text, serif' }}>
                  BookScribe AI Customization
                </h1>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <ThemeToggle />
              <SettingsButton />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Introduction */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4" style={{ fontFamily: 'Crimson Text, serif' }}>
              Customize Your Writing Experience
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Choose from curated themes or create your own custom combinations with our comprehensive customization system.
              Personalize every aspect of your writing environment to match your creative process.
            </p>
          </div>

          {/* Features Section */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div className="text-center p-6 rounded-lg bg-card border">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                <Palette className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Curated Themes</h3>
              <p className="text-sm text-muted-foreground">
                Choose from carefully crafted default themes or browse your saved custom theme collections.
              </p>
            </div>

            <div className="text-center p-6 rounded-lg bg-card border">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                <Settings className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Custom Settings</h3>
              <p className="text-sm text-muted-foreground">
                Fine-tune typography, colors, and layout preferences to create your perfect writing environment.
              </p>
            </div>

            <div className="text-center p-6 rounded-lg bg-card border">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                <span className="text-primary font-bold text-xl">Aa</span>
              </div>
              <h3 className="font-semibold mb-2">Save & Share</h3>
              <p className="text-sm text-muted-foreground">
                Premium users can save unlimited custom themes and access them across all devices for a consistent experience.
              </p>
            </div>
          </div>

          {/* Customization Hub */}
          <CustomizationHub />

          {/* Features */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 rounded-lg bg-card border">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                <Palette className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Visual Themes</h3>
              <p className="text-sm text-muted-foreground">
                Choose from carefully crafted light and dark themes, each designed for different writing moods and environments.
              </p>
            </div>

            <div className="text-center p-6 rounded-lg bg-card border">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                <Settings className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Smart Preferences</h3>
              <p className="text-sm text-muted-foreground">
                Automatically adapts to your system preferences or manually select your preferred theme mode and style.
              </p>
            </div>

            <div className="text-center p-6 rounded-lg bg-card border">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                <svg className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="font-semibold mb-2">Writer-Focused</h3>
              <p className="text-sm text-muted-foreground">
                Every theme is crafted with writers in mind, optimizing readability and reducing eye strain for long writing sessions.
              </p>
            </div>
          </div>

          {/* Call to Action - Only show for non-authenticated users */}
          {!user && (
            <div className="mt-16 text-center">
              <div className="bg-card border rounded-lg p-8 max-w-2xl mx-auto">
                <h3 className="text-xl font-semibold mb-4" style={{ fontFamily: 'Crimson Text, serif' }}>
                  Ready to Start Writing?
                </h3>
                <p className="text-muted-foreground mb-6">
                  Customize your perfect writing environment and begin your creative journey with BookScribe AI.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/demo">
                    <Button size="lg" className="w-full sm:w-auto">
                      Try Demo
                    </Button>
                  </Link>
                  <Link href="/signup">
                    <Button variant="outline" size="lg" className="w-full sm:w-auto">
                      Get Started Free
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t border-border bg-background/50 backdrop-blur-sm mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-sm text-muted-foreground">
            <p>© 2024 BookScribe AI. Crafted for writers, by writers.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
