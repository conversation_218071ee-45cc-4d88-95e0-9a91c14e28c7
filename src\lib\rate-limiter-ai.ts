import { RateLimiter } from './rate-limiter';

// Specific rate limiters for AI endpoints
export const aiGenerationLimiter = new RateLimiter({ 
  interval: 15 * 60 * 1000, // 15 minutes
  uniqueTokenPerInterval: 100 // Max unique users per 15 minutes
});
export const aiChatLimiter = new RateLimiter({ 
  interval: 15 * 60 * 1000, // 15 minutes
  uniqueTokenPerInterval: 200 // Max unique users per 15 minutes - more lenient for chat
});
export const aiAnalysisLimiter = new RateLimiter({ 
  interval: 15 * 60 * 1000, // 15 minutes
  uniqueTokenPerInterval: 150 // Max unique users per 15 minutes
});

// Rate limit configurations for different AI operations
export const AI_RATE_LIMITS = {
  // Content generation: 5 requests per 15 minutes (was 10 per hour)
  generation: {
    requests: 5,
    windowMs: 15 * 60 * 1000, // 15 minutes
    limiter: aiGenerationLimiter
  },
  // Chat interactions: 20 requests per 15 minutes (was 30 per hour)
  chat: {
    requests: 20,
    windowMs: 15 * 60 * 1000, // 15 minutes
    limiter: aiChatLimiter
  },
  // Analysis operations: 10 requests per 15 minutes (was 20 per hour)
  analysis: {
    requests: 10,
    windowMs: 15 * 60 * 1000, // 15 minutes
    limiter: aiAnalysisLimiter
  }
} as const;

/**
 * Get the appropriate rate limiter for an AI operation type
 */
export function getAIRateLimiter(operationType: keyof typeof AI_RATE_LIMITS) {
  return AI_RATE_LIMITS[operationType];
}

/**
 * Create a rate limit response with proper headers
 */
export function createAIRateLimitResponse(retryAfter: number) {
  return new Response(
    JSON.stringify({
      error: 'Too many AI requests. Please try again later.',
      retryAfter,
      message: 'AI generation limits help ensure fair usage and service availability for all users.'
    }),
    {
      status: 429,
      headers: {
        'Content-Type': 'application/json',
        'Retry-After': retryAfter.toString(),
        'X-RateLimit-Limit': '10',
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': new Date(Date.now() + retryAfter * 1000).toISOString()
      }
    }
  );
}