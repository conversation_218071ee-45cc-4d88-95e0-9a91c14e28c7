# PowerShell script to update <PERSON> Desktop config with actual environment variables
# This replaces ${VAR} placeholders with actual values from .env.local

param(
    [string]$EnvFile = ".env.local"
)

Write-Host "Updating Claude Desktop MCP configuration..." -ForegroundColor Green

# Check if .env.local exists
if (-not (Test-Path $EnvFile)) {
    Write-Host "Error: $EnvFile not found!" -ForegroundColor Red
    Write-Host "Please create your .env.local file with the required MCP variables." -ForegroundColor Yellow
    exit 1
}

# Function to load environment variables from .env file
function Load-EnvVars {
    param([string]$Path)
    
    $envVars = @{}
    Get-Content $Path | ForEach-Object {
        if ($_ -match '^([^#][^=]+)=(.*)$') {
            $name = $matches[1].Trim()
            $value = $matches[2].Trim()
            
            # Remove quotes if present
            if ($value -match '^"(.*)"$' -or $value -match "^'(.*)'$") {
                $value = $matches[1]
            }
            
            $envVars[$name] = $value
        }
    }
    return $envVars
}

# Load environment variables
$envVars = Load-EnvVars -Path $EnvFile
Write-Host "📁 Loaded environment variables from $EnvFile" -ForegroundColor Blue

# Claude Desktop config path
$configPath = "$env:APPDATA\Claude\claude_desktop_config.json"

if (-not (Test-Path $configPath)) {
    Write-Host "❌ Error: Claude Desktop config not found at $configPath" -ForegroundColor Red
    exit 1
}

# Read current config
$configContent = Get-Content $configPath -Raw
Write-Host "📖 Read existing Claude Desktop configuration" -ForegroundColor Blue

# Replace environment variable placeholders with actual values
$updatedConfig = $configContent

foreach ($key in $envVars.Keys) {
    $placeholder = "`${$key}"
    $value = $envVars[$key]
    
    if ($updatedConfig -match [regex]::Escape($placeholder)) {
        $updatedConfig = $updatedConfig -replace [regex]::Escape($placeholder), $value
        Write-Host "  ✅ Replaced $placeholder with actual value" -ForegroundColor Gray
    }
}

# Handle default values (e.g., ${VAR:-default})
$updatedConfig = $updatedConfig -replace '\$\{([^}]+):-([^}]+)\}', '$2'

# Write updated config back
$updatedConfig | Set-Content -Path $configPath -Encoding UTF8
Write-Host "💾 Updated Claude Desktop configuration" -ForegroundColor Green

Write-Host "`n✅ Configuration updated successfully!" -ForegroundColor Green
Write-Host "🔄 Please restart Claude Desktop to apply changes" -ForegroundColor Yellow

# Verify critical variables are set
$criticalVars = @("SUPABASE_ACCESS_TOKEN", "STRIPE_SECRET_KEY")
$missingVars = @()

foreach ($var in $criticalVars) {
    if (-not $envVars.ContainsKey($var) -or [string]::IsNullOrEmpty($envVars[$var])) {
        $missingVars += $var
    }
}

if ($missingVars.Count -gt 0) {
    Write-Host "`n⚠️  Warning: Missing critical environment variables:" -ForegroundColor Yellow
    $missingVars | ForEach-Object { Write-Host "  - $_" -ForegroundColor Yellow }
    Write-Host "Some MCP servers may not work properly." -ForegroundColor Yellow
    Write-Host "`nTo fix:" -ForegroundColor Cyan
    Write-Host "1. Add missing variables to $EnvFile" -ForegroundColor Cyan
    Write-Host "2. Run this script again" -ForegroundColor Cyan
    Write-Host "3. Restart Claude Desktop" -ForegroundColor Cyan
}
