-- =====================================================
-- CREATE PROJECT_QUALITY_METRICS TABLE (FIXED VERSION)
-- This table aggregates quality metrics at the project level
-- =====================================================

-- Create aggregated quality metrics table for project-level analysis
CREATE TABLE IF NOT EXISTS public.project_quality_metrics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id uuid REFERENCES public.projects(id) ON DELETE CASCADE UNIQUE,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Aggregated scores (averages across all chapters)
  avg_overall_score numeric(5,2),
  avg_coherence numeric(5,2),
  avg_style numeric(5,2),
  avg_grammar numeric(5,2),
  avg_creativity numeric(5,2),
  avg_pacing numeric(5,2),
  avg_character_consistency numeric(5,2),
  avg_plot_consistency numeric(5,2),
  avg_emotional_impact numeric(5,2),
  avg_readability numeric(5,2),
  avg_show_dont_tell_ratio numeric(5,2),
  avg_sensory_engagement numeric(5,2),
  avg_dialogue_authenticity numeric(5,2),
  avg_hook_strength numeric(5,2),
  avg_pageturner_quality numeric(5,2),
  avg_literary_merit numeric(5,2),
  avg_market_potential numeric(5,2),
  avg_memorability numeric(5,2),
  
  -- Additional fields that the API expects
  overall_score numeric(5,2),
  avg_consistency numeric(5,2),
  avg_engagement numeric(5,2),
  feedback text,
  improvement_suggestions text[],
  
  -- Chapter count and last analysis
  chapters_analyzed integer DEFAULT 0,
  last_analyzed_at timestamp with time zone,
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_project_quality_metrics_project_id ON public.project_quality_metrics(project_id);
CREATE INDEX IF NOT EXISTS idx_project_quality_metrics_user_id ON public.project_quality_metrics(user_id);

-- Enable RLS
ALTER TABLE public.project_quality_metrics ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (drop first if they exist)
DROP POLICY IF EXISTS "Users can view their own project quality metrics" ON public.project_quality_metrics;
CREATE POLICY "Users can view their own project quality metrics" ON public.project_quality_metrics
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own project quality metrics" ON public.project_quality_metrics;
CREATE POLICY "Users can insert their own project quality metrics" ON public.project_quality_metrics
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own project quality metrics" ON public.project_quality_metrics;
CREATE POLICY "Users can update their own project quality metrics" ON public.project_quality_metrics
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own project quality metrics" ON public.project_quality_metrics;
CREATE POLICY "Users can delete their own project quality metrics" ON public.project_quality_metrics
  FOR DELETE USING (auth.uid() = user_id);

-- Create function to update project quality metrics
CREATE OR REPLACE FUNCTION update_project_quality_metrics()
RETURNS TRIGGER AS $$
BEGIN
  -- Calculate aggregated metrics for the project
  INSERT INTO public.project_quality_metrics (
    project_id,
    user_id,
    overall_score,
    avg_overall_score,
    avg_coherence,
    avg_style,
    avg_grammar,
    avg_creativity,
    avg_pacing,
    avg_character_consistency,
    avg_plot_consistency,
    avg_emotional_impact,
    avg_readability,
    avg_consistency,
    avg_engagement,
    avg_show_dont_tell_ratio,
    avg_sensory_engagement,
    avg_dialogue_authenticity,
    avg_hook_strength,
    avg_pageturner_quality,
    avg_literary_merit,
    avg_market_potential,
    avg_memorability,
    chapters_analyzed,
    last_analyzed_at
  )
  SELECT 
    NEW.project_id,
    NEW.user_id,
    AVG(overall_score),
    AVG(overall_score),
    AVG(coherence),
    AVG(style),
    AVG(grammar),
    AVG(creativity),
    AVG(pacing),
    AVG(character_consistency),
    AVG(plot_consistency),
    AVG(emotional_impact),
    AVG(readability),
    AVG(plot_consistency), -- Using plot_consistency as consistency fallback
    AVG(emotional_impact), -- Using emotional_impact as engagement fallback
    AVG(show_dont_tell_ratio),
    AVG(sensory_engagement),
    AVG(dialogue_authenticity),
    AVG(hook_strength),
    AVG(pageturner_quality),
    AVG(literary_merit),
    AVG(market_potential),
    AVG(memorability),
    COUNT(*),
    MAX(analyzed_at)
  FROM public.quality_metrics
  WHERE project_id = NEW.project_id
  ON CONFLICT (project_id) DO UPDATE SET
    overall_score = EXCLUDED.overall_score,
    avg_overall_score = EXCLUDED.avg_overall_score,
    avg_coherence = EXCLUDED.avg_coherence,
    avg_style = EXCLUDED.avg_style,
    avg_grammar = EXCLUDED.avg_grammar,
    avg_creativity = EXCLUDED.avg_creativity,
    avg_pacing = EXCLUDED.avg_pacing,
    avg_character_consistency = EXCLUDED.avg_character_consistency,
    avg_plot_consistency = EXCLUDED.avg_plot_consistency,
    avg_emotional_impact = EXCLUDED.avg_emotional_impact,
    avg_readability = EXCLUDED.avg_readability,
    avg_consistency = EXCLUDED.avg_consistency,
    avg_engagement = EXCLUDED.avg_engagement,
    avg_show_dont_tell_ratio = EXCLUDED.avg_show_dont_tell_ratio,
    avg_sensory_engagement = EXCLUDED.avg_sensory_engagement,
    avg_dialogue_authenticity = EXCLUDED.avg_dialogue_authenticity,
    avg_hook_strength = EXCLUDED.avg_hook_strength,
    avg_pageturner_quality = EXCLUDED.avg_pageturner_quality,
    avg_literary_merit = EXCLUDED.avg_literary_merit,
    avg_market_potential = EXCLUDED.avg_market_potential,
    avg_memorability = EXCLUDED.avg_memorability,
    chapters_analyzed = EXCLUDED.chapters_analyzed,
    last_analyzed_at = EXCLUDED.last_analyzed_at,
    updated_at = timezone('utc'::text, now());
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update project metrics when chapter metrics change
DROP TRIGGER IF EXISTS update_project_quality_metrics_trigger ON public.quality_metrics;
CREATE TRIGGER update_project_quality_metrics_trigger
AFTER INSERT OR UPDATE ON public.quality_metrics
FOR EACH ROW
EXECUTE FUNCTION update_project_quality_metrics();

-- Success message
SELECT 'SUCCESS: project_quality_metrics table created and configured!' as status;
