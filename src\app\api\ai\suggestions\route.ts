import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { authenticateUser, handleRouteError } from '@/lib/auth'
import { z } from 'zod'
import { aiLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter'

// Validation schemas
const suggestionQuerySchema = z.object({
  project_id: z.string().uuid().optional(),
  chapter_id: z.string().uuid().optional(),
  suggestion_type: z.enum(['plot', 'character', 'dialogue', 'description', 'pacing', 'style', 'general']).optional(),
  accepted: z.boolean().optional(),
  limit: z.number().int().positive().max(100).default(50).optional(),
  offset: z.number().int().min(0).default(0).optional()
})

const createSuggestionSchema = z.object({
  project_id: z.string().uuid().optional(),
  chapter_id: z.string().uuid().optional(),
  suggestion_type: z.enum(['plot', 'character', 'dialogue', 'description', 'pacing', 'style', 'general']),
  content: z.string().min(1),
  context: z.record(z.unknown()).optional(),
  metadata: z.record(z.unknown()).optional()
})

const updateSuggestionSchema = z.object({
  accepted: z.boolean().optional(),
  feedback: z.string().optional(),
  rating: z.number().min(1).max(5).optional(),
  applied_at: z.string().datetime().optional()
})

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    const { searchParams } = new URL(request.url)
    const queryParams = {
      project_id: searchParams.get('project_id') || undefined,
      chapter_id: searchParams.get('chapter_id') || undefined,
      suggestion_type: searchParams.get('suggestion_type') || undefined,
      accepted: searchParams.get('accepted') ? searchParams.get('accepted') === 'true' : undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined
    }

    const validatedQuery = suggestionQuerySchema.parse(queryParams)

    const supabase = await createClient()
    
    // Build query
    let query = supabase
      .from('ai_suggestions')
      .select(`
        *,
        projects (
          id,
          title
        ),
        chapters (
          id,
          title,
          chapter_number
        )
      `, { count: 'exact' })
      .eq('user_id', authResult.user.id)
      .order('created_at', { ascending: false })

    // Apply filters
    if (validatedQuery.project_id) {
      query = query.eq('project_id', validatedQuery.project_id)
    }
    if (validatedQuery.chapter_id) {
      query = query.eq('chapter_id', validatedQuery.chapter_id)
    }
    if (validatedQuery.suggestion_type) {
      query = query.eq('suggestion_type', validatedQuery.suggestion_type)
    }
    if (validatedQuery.accepted !== undefined) {
      query = query.eq('accepted', validatedQuery.accepted)
    }

    // Apply pagination
    const limit = validatedQuery.limit || 50
    const offset = validatedQuery.offset || 0
    query = query.range(offset, offset + limit - 1)

    const { data: suggestions, error, count } = await query

    if (error) {
      console.error('Error fetching AI suggestions:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Calculate statistics
    const stats = {
      total: count || 0,
      accepted: suggestions?.filter(s => s.accepted === true).length || 0,
      rejected: suggestions?.filter(s => s.accepted === false).length || 0,
      pending: suggestions?.filter(s => s.accepted === null).length || 0,
      avgRating: suggestions?.length 
        ? suggestions.reduce((sum, s) => sum + (s.rating || 0), 0) / suggestions.filter(s => s.rating).length || 0
        : 0
    }

    return NextResponse.json({
      suggestions: suggestions || [],
      stats,
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid query parameters',
        details: error.errors
      }, { status: 400 })
    }

    return handleRouteError(error, 'AI Suggestions GET')
  }
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for AI suggestions (30 suggestions per hour)
    const clientIP = getClientIP(request)
    const rateLimitResult = aiLimiter.check(30, clientIP)
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset)
    }

    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    const body = await request.json()
    const validatedData = createSuggestionSchema.parse(body)

    const supabase = await createClient()

    // Verify project ownership if project_id is provided
    if (validatedData.project_id) {
      const { data: project } = await supabase
        .from('projects')
        .select('id')
        .eq('id', validatedData.project_id)
        .eq('user_id', authResult.user.id)
        .single()

      if (!project) {
        return NextResponse.json({ error: 'Project not found' }, { status: 404 })
      }
    }

    // If chapter_id is provided, verify it belongs to the project
    if (validatedData.chapter_id) {
      const { data: chapter } = await supabase
        .from('chapters')
        .select('id')
        .eq('id', validatedData.chapter_id)
        .eq('project_id', validatedData.project_id!)
        .single()

      if (!chapter) {
        return NextResponse.json({ error: 'Chapter not found in project' }, { status: 404 })
      }
    }

    // Create AI suggestion
    const { data: suggestion, error } = await supabase
      .from('ai_suggestions')
      .insert({
        ...validatedData,
        user_id: authResult.user.id,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating AI suggestion:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ suggestion }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid suggestion data',
        details: error.errors
      }, { status: 400 })
    }

    return handleRouteError(error, 'AI Suggestions POST')
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    const searchParams = request.nextUrl.searchParams
    const suggestionId = searchParams.get('id')

    if (!suggestionId) {
      return NextResponse.json({ error: 'Suggestion ID is required' }, { status: 400 })
    }

    const body = await request.json()
    const validatedData = updateSuggestionSchema.parse(body)

    const supabase = await createClient()

    // If accepting the suggestion, set applied_at
    if (validatedData.accepted === true && !validatedData.applied_at) {
      validatedData.applied_at = new Date().toISOString()
    }

    const { data: suggestion, error } = await supabase
      .from('ai_suggestions')
      .update(validatedData)
      .eq('id', suggestionId)
      .eq('user_id', authResult.user.id)
      .select()
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Suggestion not found' }, { status: 404 })
      }
      console.error('Error updating AI suggestion:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ suggestion })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid update data',
        details: error.errors
      }, { status: 400 })
    }

    return handleRouteError(error, 'AI Suggestions PATCH')
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    const searchParams = request.nextUrl.searchParams
    const suggestionId = searchParams.get('id')

    if (!suggestionId) {
      return NextResponse.json({ error: 'Suggestion ID is required' }, { status: 400 })
    }

    const supabase = await createClient()

    const { error } = await supabase
      .from('ai_suggestions')
      .delete()
      .eq('id', suggestionId)
      .eq('user_id', authResult.user.id)

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Suggestion not found' }, { status: 404 })
      }
      console.error('Error deleting AI suggestion:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ success: true, message: 'Suggestion deleted' })

  } catch (error) {
    return handleRouteError(error, 'AI Suggestions DELETE')
  }
}