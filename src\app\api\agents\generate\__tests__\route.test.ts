import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { POST } from '../route';
import { createClient } from '@/lib/supabase/server';
import { authenticateUser } from '@/lib/auth/auth-utils';
import { StoryArchitect } from '@/lib/agents/story-architect';
import { CharacterDeveloper } from '@/lib/agents/character-developer';
import { ChapterPlanner } from '@/lib/agents/chapter-planner';
import { WritingAgent } from '@/lib/agents/writing-agent';

// Mock dependencies
jest.mock('@/lib/supabase/server');
jest.mock('@/lib/auth/auth-utils');
jest.mock('@/lib/agents/story-architect');
jest.mock('@/lib/agents/character-developer');
jest.mock('@/lib/agents/chapter-planner');
jest.mock('@/lib/agents/writing-agent');

const mockSupabase = {
  from: jest.fn(),
};

describe('Agents Generate API Route', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (createClient as jest.Mock).mockResolvedValue(mockSupabase);
    (authenticateUser as jest.Mock).mockResolvedValue({ id: 'test-user-id' });
  });

  describe('POST /api/agents/generate', () => {
    const mockProject = {
      id: 'test-project-id',
      user_id: 'test-user-id',
      title: 'Test Novel',
      primary_genre: 'fantasy',
      target_audience: 'young-adult',
      writing_style: 'descriptive',
      narrative_voice: 'third-person',
      tense: 'past',
      pacing: 'medium',
      violence_level: 'moderate',
      romance_level: 'low',
      profanity_level: 'mild',
      target_word_count: 80000,
      target_chapters: 20,
    };

    beforeEach(() => {
      // Setup default mock responses
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'projects') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockProject,
                  error: null,
                }),
              }),
            }),
            update: jest.fn().mockReturnValue({
              eq: jest.fn().mockResolvedValue({
                data: mockProject,
                error: null,
              }),
            }),
          };
        }
        if (table === 'agent_logs') {
          return {
            insert: jest.fn().mockResolvedValue({
              data: { id: 'log-id' },
              error: null,
            }),
          };
        }
        return {
          insert: jest.fn().mockResolvedValue({ data: null, error: null }),
          select: jest.fn().mockResolvedValue({ data: [], error: null }),
        };
      });
    });

    it('should generate story structure', async () => {
      const mockStructure = {
        title: 'Epic Quest',
        premise: 'A hero saves the world',
        genre: 'fantasy',
        themes: ['courage', 'friendship'],
        acts: [],
        conflicts: [],
        timeline: [],
        worldBuilding: { setting: {}, rules: [], history: [] },
        plotPoints: [],
      };

      (StoryArchitect as jest.MockedClass<typeof StoryArchitect>).mockImplementation(() => ({
        execute: jest.fn().mockResolvedValue(mockStructure),
      } as any));

      const request = new NextRequest('http://localhost:3000/api/agents/generate', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          agentType: 'story-architect',
          context: { storyPrompt: 'A fantasy adventure' },
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.result).toEqual(mockStructure);
      expect(StoryArchitect).toHaveBeenCalled();
    });

    it('should generate characters', async () => {
      const mockCharacters = {
        protagonists: [{ id: '1', name: 'Hero', role: 'protagonist' }],
        antagonists: [{ id: '2', name: 'Villain', role: 'antagonist' }],
        supporting: [],
        relationships: [],
      };

      (CharacterDeveloper as jest.MockedClass<typeof CharacterDeveloper>).mockImplementation(() => ({
        execute: jest.fn().mockResolvedValue(mockCharacters),
      } as any));

      const request = new NextRequest('http://localhost:3000/api/agents/generate', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          agentType: 'character-developer',
          context: {
            storyStructure: { title: 'Test', premise: 'Test premise' },
          },
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.result).toEqual(mockCharacters);
      expect(CharacterDeveloper).toHaveBeenCalled();
    });

    it('should generate chapter outlines', async () => {
      const mockOutlines = {
        chapters: [
          {
            number: 1,
            title: 'The Beginning',
            summary: 'Story starts',
            wordCountTarget: 4000,
            scenes: [],
          },
        ],
        totalWordCount: 80000,
        estimatedReadingTime: 320,
      };

      (ChapterPlanner as jest.MockedClass<typeof ChapterPlanner>).mockImplementation(() => ({
        execute: jest.fn().mockResolvedValue(mockOutlines),
      } as any));

      const request = new NextRequest('http://localhost:3000/api/agents/generate', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          agentType: 'chapter-planner',
          context: {
            storyStructure: { title: 'Test' },
            characters: { protagonists: [] },
          },
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.result).toEqual(mockOutlines);
      expect(ChapterPlanner).toHaveBeenCalled();
    });

    it('should write a chapter', async () => {
      const mockChapter = {
        chapterNumber: 1,
        title: 'Chapter One',
        content: 'It was a dark and stormy night...',
        wordCount: 4000,
        scenes: [],
        characterVoices: [],
        themes: [],
        continuityNotes: [],
      };

      (WritingAgent as jest.MockedClass<typeof WritingAgent>).mockImplementation(() => ({
        execute: jest.fn().mockResolvedValue(mockChapter),
      } as any));

      const request = new NextRequest('http://localhost:3000/api/agents/generate', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          agentType: 'writing-agent',
          context: {
            currentChapter: 1,
            chapterOutlines: { chapters: [] },
            characters: { protagonists: [] },
          },
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.result).toEqual(mockChapter);
      expect(WritingAgent).toHaveBeenCalled();
    });

    it('should validate project ownership', async () => {
      (authenticateUser as jest.Mock).mockResolvedValue({ id: 'different-user-id' });

      const request = new NextRequest('http://localhost:3000/api/agents/generate', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          agentType: 'story-architect',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toContain('permission');
    });

    it('should handle invalid agent type', async () => {
      const request = new NextRequest('http://localhost:3000/api/agents/generate', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          agentType: 'invalid-agent',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Invalid agent type');
    });

    it('should handle missing required fields', async () => {
      const request = new NextRequest('http://localhost:3000/api/agents/generate', {
        method: 'POST',
        body: JSON.stringify({
          // Missing projectId
          agentType: 'story-architect',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('required');
    });

    it('should log agent execution', async () => {
      const mockStructure = { title: 'Test' };
      
      (StoryArchitect as jest.MockedClass<typeof StoryArchitect>).mockImplementation(() => ({
        execute: jest.fn().mockResolvedValue(mockStructure),
      } as any));

      const request = new NextRequest('http://localhost:3000/api/agents/generate', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          agentType: 'story-architect',
          context: {},
        }),
      });

      await POST(request);

      expect(mockSupabase.from).toHaveBeenCalledWith('agent_logs');
      expect(mockSupabase.from('agent_logs').insert).toHaveBeenCalledWith(
        expect.objectContaining({
          project_id: 'test-project-id',
          agent_type: 'story-architect',
          status: 'success',
          result: mockStructure,
        })
      );
    });

    it('should handle agent execution errors', async () => {
      (StoryArchitect as jest.MockedClass<typeof StoryArchitect>).mockImplementation(() => ({
        execute: jest.fn().mockRejectedValue(new Error('Agent failed')),
      } as any));

      const request = new NextRequest('http://localhost:3000/api/agents/generate', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          agentType: 'story-architect',
          context: {},
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('Agent execution failed');
      
      // Should log the error
      expect(mockSupabase.from('agent_logs').insert).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'error',
          error: expect.objectContaining({
            message: 'Agent failed',
          }),
        })
      );
    });

    it('should update project status after successful generation', async () => {
      const mockStructure = { title: 'Test' };
      
      (StoryArchitect as jest.MockedClass<typeof StoryArchitect>).mockImplementation(() => ({
        execute: jest.fn().mockResolvedValue(mockStructure),
      } as any));

      const request = new NextRequest('http://localhost:3000/api/agents/generate', {
        method: 'POST',
        body: JSON.stringify({
          projectId: 'test-project-id',
          agentType: 'story-architect',
          context: {},
          updateProjectStatus: true,
        }),
      });

      await POST(request);

      expect(mockSupabase.from('projects').update).toHaveBeenCalledWith(
        expect.objectContaining({
          last_generated_at: expect.any(String),
        })
      );
    });

    it('should handle concurrent agent requests', async () => {
      // Test that the API can handle multiple simultaneous requests
      const requests = Array.from({ length: 3 }, (_, i) => 
        new NextRequest('http://localhost:3000/api/agents/generate', {
          method: 'POST',
          body: JSON.stringify({
            projectId: `project-${i}`,
            agentType: 'story-architect',
            context: {},
          }),
        })
      );

      (StoryArchitect as jest.MockedClass<typeof StoryArchitect>).mockImplementation(() => ({
        execute: jest.fn().mockResolvedValue({ title: 'Test' }),
      } as any));

      const responses = await Promise.all(requests.map(req => POST(req)));
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
      
      expect(StoryArchitect).toHaveBeenCalledTimes(3);
    });
  });
});