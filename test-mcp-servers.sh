#!/bin/bash

echo "=== MCP Server Configuration Test ==="
echo "Date: $(date)"
echo ""

echo "1. Node.js Version Check:"
node --version
echo ""

echo "2. NPM Version Check:"
npm --version
echo ""

echo "3. Global MCP Packages:"
npm list -g --depth=0 | grep -E "(stripe|playwright|sentry|supabase|context7)" || echo "No MCP packages found"
echo ""

echo "4. Claude Desktop Config Location:"
CONFIG_FILE="$HOME/.config/claude-desktop/config.json"
if [ -f "$CONFIG_FILE" ]; then
    echo "✅ Config file found at: $CONFIG_FILE"
    echo "Number of MCP servers configured: $(jq '.mcpServers | length' "$CONFIG_FILE")"
    echo "Configured servers: $(jq -r '.mcpServers | keys | join(", ")' "$CONFIG_FILE")"
else
    echo "❌ Config file not found at: $CONFIG_FILE"
fi
echo ""

echo "5. Testing MCP Server Commands:"
echo "Testing @stripe/mcp..."
npx @stripe/mcp --help > /dev/null 2>&1 && echo "✅ Stripe MCP working" || echo "❌ Stripe MCP failed"

echo "Testing @playwright/mcp..."
npx @playwright/mcp --help > /dev/null 2>&1 && echo "✅ Playwright MCP working" || echo "❌ Playwright MCP failed"

echo "Testing @sentry/mcp-server..."
npx @sentry/mcp-server --help > /dev/null 2>&1 && echo "✅ Sentry MCP working" || echo "❌ Sentry MCP failed"

echo "Testing @upstash/context7-mcp..."
npx @upstash/context7-mcp --help > /dev/null 2>&1 && echo "✅ Context7 MCP working" || echo "❌ Context7 MCP failed"

echo "Testing @supabase/mcp-server-supabase..."
npx @supabase/mcp-server-supabase --help > /dev/null 2>&1 && echo "✅ Supabase MCP working" || echo "❌ Supabase MCP failed"

echo ""
echo "=== Test Complete ==="
echo ""
echo "🔄 To apply changes: Restart Claude Desktop"
echo "📍 Next step: Test MCP functionality in Claude Code"