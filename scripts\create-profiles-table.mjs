import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { persistSession: false }
});

async function createProfilesTable() {
  console.log('🚀 Creating profiles table with full schema...\n');

  const createTableSQL = `
    -- Create profiles table
    CREATE TABLE IF NOT EXISTS public.profiles (
      id UUID REFERENCES auth.users(id) PRIMARY KEY,
      email TEXT,
      full_name TEXT,
      username VARCHAR(50) UNIQUE,
      bio TEXT,
      location VARCHAR(255),
      website VARCHAR(500),
      avatar_url TEXT,
      stripe_customer_id TEXT,
      writing_goals JSONB DEFAULT '{"daily_words": 1000, "weekly_hours": 10, "genre_focus": "Fiction"}'::jsonb,
      preferences JSONB DEFAULT '{"public_profile": true, "email_notifications": true, "writing_reminders": true, "beta_features": false}'::jsonb,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_profiles_username ON public.profiles(username);
    CREATE INDEX IF NOT EXISTS idx_profiles_stripe_customer ON public.profiles(stripe_customer_id);

    -- Enable RLS
    ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

    -- Create RLS policies
    DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
    CREATE POLICY "Users can view own profile" ON public.profiles
      FOR SELECT USING (auth.uid() = id);

    DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
    CREATE POLICY "Users can update own profile" ON public.profiles
      FOR UPDATE USING (auth.uid() = id);

    DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
    CREATE POLICY "Users can insert own profile" ON public.profiles
      FOR INSERT WITH CHECK (auth.uid() = id);

    -- Function to handle new user creation
    CREATE OR REPLACE FUNCTION public.handle_new_user()
    RETURNS TRIGGER
    LANGUAGE plpgsql
    SECURITY DEFINER SET search_path = public
    AS $$
    BEGIN
      INSERT INTO public.profiles (id, email, full_name, avatar_url)
      VALUES (
        new.id,
        new.email,
        new.raw_user_meta_data->>'full_name',
        new.raw_user_meta_data->>'avatar_url'
      );
      RETURN new;
    END;
    $$;

    -- Trigger to automatically create profile on signup
    DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
    CREATE TRIGGER on_auth_user_created
      AFTER INSERT ON auth.users
      FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

    -- Function to update updated_at column
    CREATE OR REPLACE FUNCTION public.update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
      NEW.updated_at = NOW();
      RETURN NEW;
    END;
    $$ language 'plpgsql';

    -- Trigger for updated_at
    DROP TRIGGER IF EXISTS update_profiles_updated_at ON public.profiles;
    CREATE TRIGGER update_profiles_updated_at 
      BEFORE UPDATE ON public.profiles
      FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
  `;

  try {
    // Execute the SQL using a direct HTTP request to Supabase REST API
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseServiceKey,
        'Authorization': `Bearer ${supabaseServiceKey}`
      },
      body: JSON.stringify({ sql: createTableSQL })
    });

    if (!response.ok) {
      // If RPC doesn't work, try a more manual approach
      console.log('📝 Direct SQL execution via RPC not available');
      console.log('🔧 You need to run this SQL manually in your Supabase dashboard:');
      console.log('\n' + '='.repeat(60));
      console.log(createTableSQL);
      console.log('='.repeat(60));
      console.log('\n📋 Steps:');
      console.log('1. Go to https://supabase.com/dashboard/project/xvqeiwrpbzpiqvwuvtpj');
      console.log('2. Click on "SQL Editor" in the left sidebar');
      console.log('3. Copy and paste the SQL above');
      console.log('4. Click "Run" button');
      console.log('\n✅ After running, test with: node test-supabase.mjs');
      return;
    }

    const result = await response.json();
    if (result.error) {
      throw new Error(result.error.message);
    }

    console.log('✅ Profiles table created successfully!');
    
    // Test the table
    const { data, error } = await supabase
      .from('profiles')
      .select('id, email, username, writing_goals')
      .limit(1);
    
    if (error) {
      console.warn('⚠️ Warning testing table:', error.message);
    } else {
      console.log('✅ Table test passed!');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.log('\n🔧 Manual fallback required:');
    console.log('Copy this SQL to your Supabase SQL Editor:');
    console.log('\n' + '='.repeat(60));
    console.log(createTableSQL);
    console.log('='.repeat(60));
  }
}

createProfilesTable();