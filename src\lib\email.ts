import { logger } from './services/logger'

interface EmailOptions {
  to: string
  subject: string
  html: string
  text?: string
  from?: string
}

/**
 * Send email using your preferred email service
 * This is a placeholder implementation - replace with your actual email service
 * Options: SendGrid, AWS SES, Resend, Postmark, etc.
 */
export async function sendEmail(options: EmailOptions): Promise<void> {
  const { to, subject, html, text, from = '<EMAIL>' } = options
  
  // For development, just log the email
  if (process.env.NODE_ENV === 'development') {
    logger.info('Email sent (dev mode)', {
      to,
      from,
      subject,
      preview: text?.substring(0, 100) || html.substring(0, 100)
    })
    return
  }
  
  // TODO: Implement actual email sending
  // Example with SendGrid:
  /*
  const sgMail = require('@sendgrid/mail')
  sgMail.setApiKey(process.env.SENDGRID_API_KEY)
  
  const msg = {
    to,
    from,
    subject,
    text: text || html.replace(/<[^>]*>/g, ''), // Strip HTML for text version
    html,
  }
  
  try {
    await sgMail.send(msg)
    logger.info('Email sent successfully', { to, subject })
  } catch (error) {
    logger.error('Failed to send email', error)
    throw error
  }
  */
  
  // Example with Resend:
  /*
  const resend = new Resend(process.env.RESEND_API_KEY)
  
  try {
    const data = await resend.emails.send({
      from,
      to,
      subject,
      html,
      text: text || undefined,
    })
    logger.info('Email sent successfully', { to, subject, id: data.id })
  } catch (error) {
    logger.error('Failed to send email', error)
    throw error
  }
  */
  
  // For now, just log in production too
  logger.info('Email would be sent', {
    to,
    from,
    subject,
    preview: text?.substring(0, 100) || html.substring(0, 100)
  })
}

/**
 * Send collaboration invitation email
 */
export async function sendCollaborationInvite(
  email: string,
  projectTitle: string,
  inviterName: string,
  role: 'editor' | 'viewer',
  inviteUrl: string
): Promise<void> {
  const subject = `${inviterName} invited you to collaborate on "${projectTitle}"`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: #f7f3f0;
          padding: 30px;
          text-align: center;
          border-radius: 8px 8px 0 0;
        }
        .content {
          background-color: white;
          padding: 30px;
          border: 1px solid #e5e5e5;
          border-radius: 0 0 8px 8px;
        }
        .button {
          display: inline-block;
          padding: 12px 30px;
          background-color: #8B4513;
          color: white;
          text-decoration: none;
          border-radius: 6px;
          font-weight: 600;
          margin: 20px 0;
        }
        .button:hover {
          background-color: #6B3410;
        }
        .features {
          background-color: #f9f9f9;
          padding: 20px;
          border-radius: 6px;
          margin: 20px 0;
        }
        .features h3 {
          margin-top: 0;
          color: #8B4513;
        }
        .features ul {
          margin: 0;
          padding-left: 20px;
        }
        .footer {
          text-align: center;
          font-size: 12px;
          color: #666;
          margin-top: 30px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1 style="margin: 0; color: #8B4513;">BookScribe AI</h1>
        <p style="margin: 10px 0 0 0; color: #666;">AI-Powered Novel Writing</p>
      </div>
      
      <div class="content">
        <h2>You're invited to collaborate!</h2>
        
        <p><strong>${inviterName}</strong> has invited you to collaborate on their novel <strong>"${projectTitle}"</strong> as a${role === 'editor' ? 'n' : ''} <strong>${role}</strong>.</p>
        
        <div class="features">
          <h3>As a${role === 'editor' ? 'n' : ''} ${role}, you'll be able to:</h3>
          ${role === 'editor' ? `
            <ul>
              <li>View and edit all chapters in real-time</li>
              <li>Access the complete Story Bible</li>
              <li>Use AI-powered writing tools</li>
              <li>Track character development and plot threads</li>
              <li>Export the manuscript in multiple formats</li>
            </ul>
          ` : `
            <ul>
              <li>Read all chapters and story content</li>
              <li>View the Story Bible and character profiles</li>
              <li>Leave comments and feedback</li>
              <li>Export the manuscript for offline reading</li>
            </ul>
          `}
        </div>
        
        <div style="text-align: center;">
          <a href="${inviteUrl}" class="button">Accept Invitation</a>
        </div>
        
        <p><strong>Note:</strong> This invitation will expire in 7 days. If you don't have a BookScribe account yet, you'll be prompted to create one when you accept.</p>
      </div>
      
      <div class="footer">
        <p>This invitation was sent to ${email}. If you didn't expect this invitation, you can safely ignore this email.</p>
        <p>&copy; ${new Date().getFullYear()} BookScribe AI. All rights reserved.</p>
      </div>
    </body>
    </html>
  `
  
  const text = `
${inviterName} invited you to collaborate on "${projectTitle}"

You've been invited as a${role === 'editor' ? 'n' : ''} ${role} to collaborate on the novel "${projectTitle}".

Accept the invitation: ${inviteUrl}

This invitation will expire in 7 days.

If you don't have a BookScribe account yet, you'll be prompted to create one when you accept.
  `.trim()
  
  await sendEmail({
    to: email,
    subject,
    html,
    text
  })
}