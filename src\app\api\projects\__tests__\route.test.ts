import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { GET, POST } from '../route';
import { createClient } from '@/lib/supabase/server';
import { authenticateUser } from '@/lib/auth/auth-utils';

// Mock dependencies
jest.mock('@/lib/supabase/server');
jest.mock('@/lib/auth/auth-utils');

const mockSupabase = {
  from: jest.fn(),
  rpc: jest.fn(),
};

describe('Projects API Route', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (createClient as jest.Mock).mockResolvedValue(mockSupabase);
    (authenticateUser as jest.Mock).mockResolvedValue({ id: 'test-user-id' });
  });

  describe('GET /api/projects', () => {
    const mockProjects = [
      {
        id: 'project-1',
        title: 'First Novel',
        description: 'A fantasy adventure',
        primary_genre: 'fantasy',
        current_word_count: 50000,
        target_word_count: 80000,
        status: 'in_progress',
        created_at: '2024-01-01',
        updated_at: '2024-01-15',
      },
      {
        id: 'project-2',
        title: 'Second Novel',
        description: 'A mystery thriller',
        primary_genre: 'mystery',
        current_word_count: 30000,
        target_word_count: 70000,
        status: 'planning',
        created_at: '2024-01-10',
        updated_at: '2024-01-20',
      },
    ];

    beforeEach(() => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockProjects,
              error: null,
            }),
          }),
        }),
      });
    });

    it('should return all user projects', async () => {
      const request = new NextRequest('http://localhost:3000/api/projects');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(mockProjects);
      expect(mockSupabase.from).toHaveBeenCalledWith('projects');
      expect(mockSupabase.from('projects').select().eq).toHaveBeenCalledWith('user_id', 'test-user-id');
    });

    it('should filter projects by status', async () => {
      const request = new NextRequest('http://localhost:3000/api/projects?status=in_progress');

      await GET(request);

      expect(mockSupabase.from('projects').select().eq).toHaveBeenCalledWith('status', 'in_progress');
    });

    it('should filter projects by genre', async () => {
      const request = new NextRequest('http://localhost:3000/api/projects?genre=fantasy');

      await GET(request);

      expect(mockSupabase.from('projects').select().eq).toHaveBeenCalledWith('primary_genre', 'fantasy');
    });

    it('should handle pagination', async () => {
      const request = new NextRequest('http://localhost:3000/api/projects?page=2&limit=10');

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockReturnValue({
              range: jest.fn().mockResolvedValue({
                data: mockProjects.slice(0, 1),
                error: null,
              }),
            }),
          }),
        }),
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(mockSupabase.from('projects').select().eq('user_id', 'test-user-id').order().range)
        .toHaveBeenCalledWith(10, 19); // Page 2 with limit 10
    });

    it('should handle authentication failure', async () => {
      (authenticateUser as jest.Mock).mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/projects');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toContain('Unauthorized');
    });

    it('should handle database errors', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: null,
              error: new Error('Database error'),
            }),
          }),
        }),
      });

      const request = new NextRequest('http://localhost:3000/api/projects');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('retrieve projects');
    });
  });

  describe('POST /api/projects', () => {
    const newProject = {
      title: 'New Novel',
      description: 'An exciting new story',
      primary_genre: 'science-fiction',
      secondary_genres: ['thriller', 'mystery'],
      target_audience: 'adult',
      writing_style: 'descriptive',
      narrative_voice: 'first-person',
      tense: 'present',
      pacing: 'fast',
      violence_level: 'moderate',
      romance_level: 'high',
      profanity_level: 'mild',
      target_word_count: 90000,
      target_chapters: 25,
    };

    beforeEach(() => {
      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { id: 'new-project-id', ...newProject, user_id: 'test-user-id' },
              error: null,
            }),
          }),
        }),
      });
    });

    it('should create a new project', async () => {
      const request = new NextRequest('http://localhost:3000/api/projects', {
        method: 'POST',
        body: JSON.stringify(newProject),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.id).toBe('new-project-id');
      expect(data.title).toBe(newProject.title);
      expect(mockSupabase.from('projects').insert).toHaveBeenCalledWith(
        expect.objectContaining({
          ...newProject,
          user_id: 'test-user-id',
          status: 'planning',
          current_word_count: 0,
        })
      );
    });

    it('should validate required fields', async () => {
      const invalidProject = {
        // Missing title
        description: 'A story without a title',
        primary_genre: 'fantasy',
      };

      const request = new NextRequest('http://localhost:3000/api/projects', {
        method: 'POST',
        body: JSON.stringify(invalidProject),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Title is required');
    });

    it('should validate genre selection', async () => {
      const invalidProject = {
        ...newProject,
        primary_genre: 'invalid-genre',
      };

      const request = new NextRequest('http://localhost:3000/api/projects', {
        method: 'POST',
        body: JSON.stringify(invalidProject),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Invalid genre');
    });

    it('should validate word count targets', async () => {
      const invalidProject = {
        ...newProject,
        target_word_count: -1000,
      };

      const request = new NextRequest('http://localhost:3000/api/projects', {
        method: 'POST',
        body: JSON.stringify(invalidProject),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Invalid word count');
    });

    it('should validate chapter count', async () => {
      const invalidProject = {
        ...newProject,
        target_chapters: 0,
      };

      const request = new NextRequest('http://localhost:3000/api/projects', {
        method: 'POST',
        body: JSON.stringify(invalidProject),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('chapters');
    });

    it('should create initial chapters if requested', async () => {
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'projects') {
          return {
            insert: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: { id: 'new-project-id', ...newProject },
                  error: null,
                }),
              }),
            }),
          };
        }
        if (table === 'chapters') {
          return {
            insert: jest.fn().mockResolvedValue({
              data: [],
              error: null,
            }),
          };
        }
        return {};
      });

      const request = new NextRequest('http://localhost:3000/api/projects', {
        method: 'POST',
        body: JSON.stringify({
          ...newProject,
          createInitialChapters: true,
        }),
      });

      await POST(request);

      expect(mockSupabase.from('chapters').insert).toHaveBeenCalled();
      expect(mockSupabase.from('chapters').insert).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            project_id: 'new-project-id',
            chapter_number: 1,
            title: 'Chapter 1',
          }),
        ])
      );
    });

    it('should handle duplicate project titles', async () => {
      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { code: '23505', message: 'Duplicate key value' },
            }),
          }),
        }),
      });

      const request = new NextRequest('http://localhost:3000/api/projects', {
        method: 'POST',
        body: JSON.stringify(newProject),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(409);
      expect(data.error).toContain('already exists');
    });

    it('should sanitize HTML in project description', async () => {
      const projectWithHTML = {
        ...newProject,
        description: '<script>alert("XSS")</script>A safe description',
      };

      const request = new NextRequest('http://localhost:3000/api/projects', {
        method: 'POST',
        body: JSON.stringify(projectWithHTML),
      });

      await POST(request);

      expect(mockSupabase.from('projects').insert).toHaveBeenCalledWith(
        expect.objectContaining({
          description: 'A safe description', // HTML stripped
        })
      );
    });

    it('should handle authentication failure', async () => {
      (authenticateUser as jest.Mock).mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/projects', {
        method: 'POST',
        body: JSON.stringify(newProject),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toContain('Unauthorized');
    });
  });
});