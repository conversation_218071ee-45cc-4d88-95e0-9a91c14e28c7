export async function register() {
  // Temporarily disable instrumentation to debug 500 errors
  return;

  if (process.env.NEXT_RUNTIME === 'nodejs') {
    // Validate environment variables first
    const { validateEnvironment, validateServices, logConfiguration } = await import('@/lib/config/validate')
    
    try {
      console.log('[Instrumentation] Validating environment variables...')
      validateEnvironment()
      
      const serviceValidation = validateServices()
      if (!serviceValidation.valid) {
        console.warn('[Instrumentation] Service configuration warnings:')
        serviceValidation.errors.forEach(error => console.warn(`  - ${error}`))
      }
      
      if (process.env.NODE_ENV === 'development') {
        logConfiguration()
      }
    } catch (error) {
      console.error('[Instrumentation] Environment validation failed:', error)
      // In production, this would have already exited
    }
    
    // Then initialize services
    const { ServiceManager } = await import('@/lib/services/service-manager')
    const manager = ServiceManager.getInstance()
    
    // Only initialize in runtime, not during build
    // NEXT_PHASE is set during build time
    if (process.env.NODE_ENV !== 'production' || !process.env.NEXT_PHASE) {
      try {
        console.log('[Instrumentation] Initializing ServiceManager...')
        await manager.initialize()
        console.log('[Instrumentation] ServiceManager initialized successfully')
      } catch (error) {
        console.error('[Instrumentation] Failed to initialize ServiceManager:', error)
        // Don't throw - let the app start even if services fail
      }
    } else {
      console.log('[Instrumentation] Skipping ServiceManager initialization during build')
    }
  }
}