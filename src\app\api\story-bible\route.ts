import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { storyBibleQuerySchema } from '@/lib/validation/schemas'
import { z } from 'zod'
import type { Database } from '@/lib/db/types'

type StoryBible = Database['public']['Tables']['story_bible']['Row']

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = {
      project_id: searchParams.get('project_id'),
      entry_type: searchParams.get('entry_type'),
      is_active: searchParams.get('is_active') ? searchParams.get('is_active') === 'true' : undefined,
      chapter_introduced: searchParams.get('chapter_introduced') ? parseInt(searchParams.get('chapter_introduced')!) : undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined
    }

    const validatedQuery = storyBibleQuerySchema.parse(queryParams)

    // Build query with project ownership verification
    let query = supabase
      .from('story_bible')
      .select(`
        *,
        projects!inner (
          id,
          title,
          user_id
        )
      `, { count: 'exact' })
      .eq('projects.user_id', user.id)

    // Apply filters
    if (validatedQuery.project_id) {
      query = query.eq('project_id', validatedQuery.project_id)
    }
    if (validatedQuery.entry_type) {
      query = query.eq('entry_type', validatedQuery.entry_type)
    }
    if (validatedQuery.is_active !== undefined) {
      query = query.eq('is_active', validatedQuery.is_active)
    }
    if (validatedQuery.chapter_introduced) {
      query = query.eq('chapter_introduced', validatedQuery.chapter_introduced)
    }

    // Apply pagination
    if (validatedQuery.limit) {
      query = query.limit(validatedQuery.limit)
    }
    if (validatedQuery.offset) {
      query = query.range(validatedQuery.offset, (validatedQuery.offset + (validatedQuery.limit || 50)) - 1)
    }

    // Order by creation date
    query = query.order('created_at', { ascending: false })

    const { data: entries, error, count } = await query

    if (error) {
      console.error('Error fetching story bible entries:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Clean up the response to remove nested project data
    const cleanEntries = entries?.map(entry => {
      const { projects: _, ...storyBibleEntry } = entry
      // Suppress unused variable warning for intentionally unused destructured property
      void _
      return storyBibleEntry
    })

    // Group entries by type for easier consumption
    const entriesByType = cleanEntries?.reduce((acc, entry) => {
      if (!acc[entry.entry_type]) {
        acc[entry.entry_type] = []
      }
      acc[entry.entry_type].push(entry)
      return acc
    }, {} as Record<string, StoryBible[]>)

    return NextResponse.json({ 
      entries: cleanEntries || [],
      entriesByType: entriesByType || {},
      pagination: {
        total: count,
        limit: validatedQuery.limit || 50,
        offset: validatedQuery.offset || 0
      }
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid query parameters',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Error in story bible GET:', error)
    return NextResponse.json(
      { error: 'Failed to fetch story bible entries' },
      { status: 500 }
    )
  }
}