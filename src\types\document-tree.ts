export type DocumentNodeType = 'folder' | 'chapter' | 'scene' | 'note' | 'character' | 'location';

export interface DocumentNode {
  id: string;
  title: string;
  type: DocumentNodeType;
  parentId?: string;
  order: number;
  isExpanded?: boolean;
  isActive?: boolean;
  
  // Content metadata
  wordCount?: number;
  status?: 'draft' | 'in-progress' | 'completed' | 'published';
  lastModified?: Date;
  
  // Chapter-specific
  chapterNumber?: number;
  targetWordCount?: number;
  
  // Scene-specific
  sceneType?: 'action' | 'dialogue' | 'exposition' | 'transition';
  pov?: string;
  
  // Folder-specific
  folderType?: 'part' | 'section' | 'research' | 'notes';
  
  // Hierarchy
  children?: DocumentNode[];
  depth?: number;
}

export interface DocumentTreeState {
  nodes: DocumentNode[];
  selectedNodeId?: string;
  expandedNodeIds: Set<string>;
  draggedNodeId?: string;
  dropTargetId?: string;
}

export interface DocumentTreeActions {
  selectNode: (nodeId: string) => void;
  toggleExpanded: (nodeId: string) => void;
  createNode: (parentId: string | null, type: DocumentNodeType, title?: string) => void;
  deleteNode: (nodeId: string) => void;
  renameNode: (nodeId: string, newTitle: string) => void;
  moveNode: (nodeId: string, newParentId: string | null, newOrder: number) => void;
  duplicateNode: (nodeId: string) => void;
}

export interface DocumentTreeProps {
  projectId: string;
  initialNodes?: DocumentNode[];
  selectedNodeId?: string;
  onNodeSelect?: (node: DocumentNode) => void;
  onNodeCreate?: (node: DocumentNode) => void;
  onNodeUpdate?: (node: DocumentNode) => void;
  onNodeDelete?: (nodeId: string) => void;
  showWordCounts?: boolean;
  showStatus?: boolean;
  allowDragDrop?: boolean;
  readonly?: boolean;
}