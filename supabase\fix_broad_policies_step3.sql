-- ============================================================================
-- STEP 3: CREATE SECURE GRANULAR POLICIES - PART 2 (SYSTEM-MANAGED TABLES)
-- ============================================================================
-- These tables have restricted access patterns

-- ============================================================================
-- AGENT LOGS - Read-Only (System Managed)
-- ============================================================================
CREATE POLICY "agent_logs_select" ON agent_logs
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );
-- No INSERT/UPDATE/DELETE for users - system managed

-- ============================================================================
-- AI SUGGESTIONS - Read + Limited Update (Feedback Only)
-- ============================================================================
CREATE POLICY "ai_suggestions_select" ON ai_suggestions
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "ai_suggestions_update_feedback" ON ai_suggestions
  FOR UPDATE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  ) WITH CHECK (
    -- Only allow updating applied status and feedback
    OLD.suggestion_text = NEW.suggestion_text AND
    OLD.confidence_score = NEW.confidence_score
  );

-- ============================================================================
-- CHAPTER VERSIONS - Read-Only (System Managed)
-- ============================================================================
CREATE POLICY "chapter_versions_select" ON chapter_versions
  FOR SELECT USING (
    chapter_id IN (
      SELECT id FROM chapters WHERE project_id IN (
        SELECT id FROM projects WHERE user_id = auth.uid()
      )
    )
  );
-- No INSERT/UPDATE/DELETE for users - system managed

-- ============================================================================
-- CONTENT EMBEDDINGS - Read-Only (System Managed)
-- ============================================================================
CREATE POLICY "content_embeddings_select" ON content_embeddings
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );
-- No INSERT/UPDATE/DELETE for users - system managed

-- ============================================================================
-- PROJECT SNAPSHOTS - Read + Create Only
-- ============================================================================
CREATE POLICY "project_snapshots_select" ON project_snapshots
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "project_snapshots_insert" ON project_snapshots
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );
-- No UPDATE/DELETE - snapshots are immutable

-- ============================================================================
-- PROCESSING TASKS - Read-Only (System Updates Status)
-- ============================================================================
CREATE POLICY "processing_tasks_select" ON processing_tasks
  FOR SELECT USING (auth.uid() = user_id);
-- No INSERT/UPDATE/DELETE for users - system managed

-- ============================================================================
-- SELECTION ANALYTICS - Read-Only (System Managed)
-- ============================================================================
CREATE POLICY "selection_analytics_select" ON selection_analytics
  FOR SELECT USING (auth.uid() = user_id);
-- No INSERT/UPDATE/DELETE for users - system managed

-- ============================================================================
-- EDITING SESSIONS - Limited Update (Recent Only)
-- ============================================================================
CREATE POLICY "editing_sessions_select" ON editing_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "editing_sessions_insert" ON editing_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "editing_sessions_update_recent" ON editing_sessions
  FOR UPDATE USING (
    auth.uid() = user_id AND 
    created_at > NOW() - INTERVAL '24 hours'
  );
-- No DELETE - audit trail

-- ============================================================================
-- NOTIFICATIONS - Read + Mark Read Only
-- ============================================================================
CREATE POLICY "notifications_select" ON notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "notifications_mark_read" ON notifications
  FOR UPDATE USING (auth.uid() = user_id) 
  WITH CHECK (
    -- Only allow marking as read
    OLD.title = NEW.title AND
    OLD.message = NEW.message AND
    OLD.type = NEW.type AND
    NEW.read = true
  );
-- No INSERT/DELETE - system managed

-- ============================================================================
-- WRITING SESSIONS - Limited Update (Recent Only)
-- ============================================================================
CREATE POLICY "writing_sessions_select" ON writing_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "writing_sessions_insert" ON writing_sessions
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "writing_sessions_update_recent" ON writing_sessions
  FOR UPDATE USING (
    auth.uid() = user_id AND 
    created_at > NOW() - INTERVAL '7 days'
  );
-- No DELETE - analytics data

-- Verify new policies
SELECT 
  tablename,
  COUNT(*) as policy_count,
  STRING_AGG(cmd, ', ') as operations
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('agent_logs', 'ai_suggestions', 'chapter_versions', 'content_embeddings', 
                   'project_snapshots', 'processing_tasks', 'selection_analytics', 
                   'editing_sessions', 'notifications', 'writing_sessions')
GROUP BY tablename
ORDER BY tablename;
