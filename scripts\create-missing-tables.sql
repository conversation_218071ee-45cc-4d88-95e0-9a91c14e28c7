-- =====================================================
-- BOOKSCRIBE AI - MISSING TABLES CREATION SCRIPT
-- Run this in Supabase SQL Editor to create missing tables
-- =====================================================

-- 1. CREATE USER_ACHIEVEMENTS TABLE
-- =====================================================

-- Create achievements table for tracking user achievements
CREATE TABLE IF NOT EXISTS public.user_achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id TEXT NOT NULL,
  
  -- Achievement details
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('writing', 'consistency', 'quality', 'milestones', 'collaboration', 'special')),
  tier TEXT NOT NULL CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')),
  icon TEXT NOT NULL,
  
  -- Progress tracking
  current_value INTEGER DEFAULT 0,
  target_value INTEGER NOT NULL,
  unlocked_at TIMESTAMP WITH TIME ZONE,
  
  -- Metadata
  metadata JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique achievements per user
  UNIQUE(user_id, achievement_id)
);

-- Create achievement_definitions table for all possible achievements
CREATE TABLE IF NOT EXISTS public.achievement_definitions (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('writing', 'consistency', 'quality', 'milestones', 'collaboration', 'special')),
  tier TEXT NOT NULL CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')),
  icon TEXT NOT NULL,
  target_value INTEGER NOT NULL,
  
  -- Conditions for unlocking
  condition_type TEXT NOT NULL CHECK (condition_type IN ('word_count', 'streak', 'quality_score', 'chapters', 'projects', 'sessions', 'custom')),
  condition_params JSONB DEFAULT '{}',
  
  -- Display order and status
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  is_secret BOOLEAN DEFAULT false,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. CREATE QUALITY_METRICS TABLE
-- =====================================================

-- Create quality_metrics table to store chapter quality analysis results
CREATE TABLE IF NOT EXISTS public.quality_metrics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  chapter_id uuid REFERENCES public.chapters(id) ON DELETE CASCADE,
  project_id uuid REFERENCES public.projects(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Overall quality scores
  overall_score numeric(5,2) CHECK (overall_score >= 0 AND overall_score <= 100),
  
  -- Fundamental quality metrics
  coherence numeric(5,2) CHECK (coherence >= 0 AND coherence <= 100),
  style numeric(5,2) CHECK (style >= 0 AND style <= 100),
  grammar numeric(5,2) CHECK (grammar >= 0 AND grammar <= 100),
  creativity numeric(5,2) CHECK (creativity >= 0 AND creativity <= 100),
  pacing numeric(5,2) CHECK (pacing >= 0 AND pacing <= 100),
  character_consistency numeric(5,2) CHECK (character_consistency >= 0 AND character_consistency <= 100),
  plot_consistency numeric(5,2) CHECK (plot_consistency >= 0 AND plot_consistency <= 100),
  emotional_impact numeric(5,2) CHECK (emotional_impact >= 0 AND emotional_impact <= 100),
  readability numeric(5,2) CHECK (readability >= 0 AND readability <= 100),
  
  -- Bestseller-specific metrics
  show_dont_tell_ratio numeric(5,2) CHECK (show_dont_tell_ratio >= 0 AND show_dont_tell_ratio <= 100),
  sensory_engagement numeric(5,2) CHECK (sensory_engagement >= 0 AND sensory_engagement <= 100),
  dialogue_authenticity numeric(5,2) CHECK (dialogue_authenticity >= 0 AND dialogue_authenticity <= 100),
  hook_strength numeric(5,2) CHECK (hook_strength >= 0 AND hook_strength <= 100),
  pageturner_quality numeric(5,2) CHECK (pageturner_quality >= 0 AND pageturner_quality <= 100),
  literary_merit numeric(5,2) CHECK (literary_merit >= 0 AND literary_merit <= 100),
  market_potential numeric(5,2) CHECK (market_potential >= 0 AND market_potential <= 100),
  memorability numeric(5,2) CHECK (memorability >= 0 AND memorability <= 100),
  
  -- Analysis results
  strengths text[] DEFAULT ARRAY[]::text[],
  weaknesses text[] DEFAULT ARRAY[]::text[],
  suggestions text[] DEFAULT ARRAY[]::text[],
  
  -- Timestamps
  analyzed_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 3. CREATE INDEXES
-- =====================================================

-- Indexes for user_achievements
CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON public.user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_achievement_id ON public.user_achievements(achievement_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_unlocked_at ON public.user_achievements(unlocked_at);
CREATE INDEX IF NOT EXISTS idx_user_achievements_category ON public.user_achievements(category);

-- Indexes for quality_metrics
CREATE INDEX IF NOT EXISTS idx_quality_metrics_chapter_id ON public.quality_metrics(chapter_id);
CREATE INDEX IF NOT EXISTS idx_quality_metrics_project_id ON public.quality_metrics(project_id);
CREATE INDEX IF NOT EXISTS idx_quality_metrics_user_id ON public.quality_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_quality_metrics_analyzed_at ON public.quality_metrics(analyzed_at DESC);
CREATE INDEX IF NOT EXISTS idx_quality_metrics_overall_score ON public.quality_metrics(overall_score DESC);

-- Unique constraint for quality metrics
CREATE UNIQUE INDEX IF NOT EXISTS idx_quality_metrics_chapter_unique ON public.quality_metrics(chapter_id);

-- 4. ENABLE ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.achievement_definitions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quality_metrics ENABLE ROW LEVEL SECURITY;

-- 5. CREATE RLS POLICIES
-- =====================================================

-- RLS policies for user_achievements
CREATE POLICY IF NOT EXISTS "Users can view their own achievements" ON public.user_achievements
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "System can create user achievements" ON public.user_achievements
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "System can update user achievements" ON public.user_achievements
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS policies for achievement_definitions (public read)
CREATE POLICY IF NOT EXISTS "Anyone can view achievement definitions" ON public.achievement_definitions
  FOR SELECT USING (true);

-- RLS policies for quality_metrics
CREATE POLICY IF NOT EXISTS "Users can view their own quality metrics" ON public.quality_metrics
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can insert their own quality metrics" ON public.quality_metrics
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own quality metrics" ON public.quality_metrics
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own quality metrics" ON public.quality_metrics
  FOR DELETE USING (auth.uid() = user_id);

-- 6. INSERT DEFAULT ACHIEVEMENT DEFINITIONS
-- =====================================================

INSERT INTO public.achievement_definitions (id, title, description, category, tier, icon, target_value, condition_type, condition_params, display_order) VALUES
-- Writing achievements
('first_words', 'First Words', 'Write your first 100 words', 'writing', 'bronze', '✏️', 100, 'word_count', '{"total": true}', 1),
('prolific_writer', 'Prolific Writer', 'Write 10,000 words', 'writing', 'silver', '📝', 10000, 'word_count', '{"total": true}', 2),
('novelist', 'Novelist', 'Write 50,000 words', 'writing', 'gold', '📚', 50000, 'word_count', '{"total": true}', 3),
('epic_author', 'Epic Author', 'Write 100,000 words', 'writing', 'platinum', '🏆', 100000, 'word_count', '{"total": true}', 4),

-- Consistency achievements
('getting_started', 'Getting Started', 'Write for 3 consecutive days', 'consistency', 'bronze', '🔥', 3, 'streak', '{}', 10),
('habit_builder', 'Habit Builder', 'Write for 7 consecutive days', 'consistency', 'silver', '💪', 7, 'streak', '{}', 11),
('dedicated_writer', 'Dedicated Writer', 'Write for 30 consecutive days', 'consistency', 'gold', '⭐', 30, 'streak', '{}', 12),
('writing_master', 'Writing Master', 'Write for 100 consecutive days', 'consistency', 'platinum', '👑', 100, 'streak', '{}', 13),

-- Quality achievements
('quality_conscious', 'Quality Conscious', 'Achieve 80% quality score on a chapter', 'quality', 'bronze', '✨', 80, 'quality_score', '{"minimum": 80}', 20),
('excellence_seeker', 'Excellence Seeker', 'Achieve 90% quality score on a chapter', 'quality', 'silver', '🌟', 90, 'quality_score', '{"minimum": 90}', 21),
('perfectionist', 'Perfectionist', 'Achieve 95% quality score on a chapter', 'quality', 'gold', '💎', 95, 'quality_score', '{"minimum": 95}', 22),
('quality_master', 'Quality Master', 'Maintain 85%+ quality across 10 chapters', 'quality', 'platinum', '🏅', 10, 'quality_score', '{"minimum": 85, "consecutive": 10}', 23),

-- Milestone achievements
('chapter_one', 'Chapter One', 'Complete your first chapter', 'milestones', 'bronze', '📖', 1, 'chapters', '{}', 30),
('five_chapters', 'Five Chapters', 'Complete 5 chapters', 'milestones', 'silver', '📑', 5, 'chapters', '{}', 31),
('halfway_there', 'Halfway There', 'Complete 50% of your book', 'milestones', 'gold', '📊', 50, 'custom', '{"type": "project_progress"}', 32),
('book_complete', 'Book Complete', 'Complete your first book', 'milestones', 'platinum', '🎉', 100, 'custom', '{"type": "project_complete"}', 33),

-- Special achievements
('night_owl', 'Night Owl', 'Write after midnight', 'special', 'bronze', '🦉', 1, 'custom', '{"type": "time_based", "hour": 0}', 40),
('early_bird', 'Early Bird', 'Write before 6 AM', 'special', 'bronze', '🌅', 1, 'custom', '{"type": "time_based", "hour": 6}', 41),
('marathon_session', 'Marathon Session', 'Write for 4 hours straight', 'special', 'silver', '⏱️', 240, 'custom', '{"type": "session_duration"}', 42),
('speed_demon', 'Speed Demon', 'Write 1000 words in an hour', 'special', 'gold', '⚡', 1000, 'custom', '{"type": "words_per_hour"}', 43)
ON CONFLICT (id) DO NOTHING;

-- 7. SUCCESS MESSAGE
-- =====================================================

-- This will show in the results if everything worked
SELECT 'SUCCESS: All missing tables and data have been created!' as status;
