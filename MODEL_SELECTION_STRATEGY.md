# BookScribe AI Model Selection Strategy

## Overview
This document outlines the comprehensive model selection strategy for BookScribe AI, optimizing for cost efficiency while maintaining high quality output.

## Model Distribution

### 1. Generation Models

#### GPT-4.1 (Primary - $2/$8 per 1M tokens)
- **Context**: 1M tokens
- **Use Cases**:
  - Story structure creation
  - Character development
  - Creative chapter writing
  - Dialogue generation
- **Access**: Author tier and above for creative tasks

#### GPT-4.1-mini (Medium - $0.4/$1.6 per 1M tokens)
- **Context**: 128K tokens
- **Use Cases**:
  - Chapter planning
  - Content editing
  - Story analysis
  - General content generation
- **Access**: Writer tier and above

#### GPT-4o-mini (Fast - $0.15/$0.6 per 1M tokens)
- **Context**: 128K tokens
- **Use Cases**:
  - Quick suggestions
  - Adaptive planning
  - Simple continuations
  - Chat interactions
- **Access**: All tiers

### 2. Embedding Models

#### text-embedding-3-small ($0.00002 per 1K tokens)
- **Use Cases**:
  - Semantic search
  - Content similarity
  - Smart recommendations
  - Theme extraction
- **Access**: All tiers

#### text-embedding-3-large ($0.00013 per 1K tokens)
- **Use Cases**:
  - Advanced relationship analysis
  - Premium search accuracy
- **Access**: Professional tier and above

## Tier-Based Access

### Starter (Free - 10k words/month)
- **Generation**: GPT-4o-mini only
- **Embeddings**: text-embedding-3-small
- **Word Allocation**: ~8k generation + 2k embeddings

### Writer ($9 - 50k words/month)
- **Generation**:
  - Story/Character: GPT-4.1-mini
  - Other tasks: GPT-4o-mini
- **Embeddings**: text-embedding-3-small
- **Word Allocation**: ~40k generation + 10k embeddings

### Author ($29 - 150k words/month)
- **Generation**:
  - Creative: GPT-4.1
  - Analysis: GPT-4.1-mini
  - Simple: GPT-4o-mini
- **Embeddings**: Both models available
- **Word Allocation**: ~120k generation + 30k embeddings

### Professional ($49 - 300k words/month)
- **Generation**: Optimal model per task
- **Embeddings**: Both models with smart selection
- **Word Allocation**: ~250k generation + 50k embeddings

### Studio ($89 - 600k words/month)
- **Generation**: Unrestricted access
- **Embeddings**: Premium features
- **Word Allocation**: ~500k generation + 100k embeddings

## Cost Optimization Features

### 1. Embedding Cache
- Aggressive caching (embeddings are deterministic)
- 30-day retention
- Memory + database cache layers
- ~80% cache hit rate expected

### 2. Smart Word Counting
- Embeddings count as 10% of generation words
- Separate tracking for analytics
- Real-time usage monitoring

### 3. Task-Based Selection
- Automatic model selection based on task complexity
- Context-aware upgrades (short vs long content)
- Quality threshold retries

## Implementation Details

### Model Selection Logic
```typescript
// Example selection for Author tier
if (tierId === 'author') {
  if (isCreativeTask) return GPT_4_1
  if (isAnalysisTask) return GPT_4_1_MINI
  return GPT_4O_MINI
}
```

### Embedding Usage
- All search operations use embeddings
- Recommendations use similarity search
- Character relationships mapped via embeddings
- Theme extraction via semantic analysis

### Monitoring & Optimization
- Track usage per model type
- Monitor cache hit rates
- A/B test model performance
- Adjust thresholds based on quality scores

## Expected Savings

### Cost Reduction
- 60-80% reduction through smart selection
- 90%+ savings on search/similarity tasks
- 75% cache discount on repeated prompts

### Word Allocation Increase
- 30-40% more words for Writer tier
- 40-50% more words for Author tier
- 50-60% more words for Professional tier

## Future Optimizations

1. **Dynamic Model Selection**
   - Quality-based automatic upgrades
   - User preference learning
   - Peak time optimization

2. **Advanced Caching**
   - Semantic deduplication
   - Partial result caching
   - Cross-user pattern caching

3. **Batch Processing**
   - Group similar requests
   - Optimize API calls
   - Reduce overhead

## Grok-3 Fallback Models

All OpenAI models have Grok-3 equivalents as fallbacks:
- GPT-4.1 → grok-3
- GPT-4.1-mini → grok-3-mini
- GPT-4o-mini → grok-3-mini

This ensures service continuity if OpenAI experiences issues.