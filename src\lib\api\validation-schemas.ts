import { z } from 'zod';

// Common validation patterns
export const uuidSchema = z.string().uuid('Invalid UUID format');
export const emailSchema = z.string().email('Invalid email format');
export const urlSchema = z.string().url('Invalid URL format');
export const dateSchema = z.string().datetime('Invalid date format');

// Pagination schemas
export const paginationSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

// Project schemas
export const createProjectSchema = z.object({
  title: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  primary_genre: z.string().min(1).max(50),
  secondary_genre: z.string().max(50).optional(),
  target_word_count: z.number().int().positive().max(500000).default(80000),
  target_chapters: z.number().int().positive().max(100).default(20),
  settings: z.record(z.unknown()).optional()
});

export const updateProjectSchema = createProjectSchema.partial();

export const projectSettingsSchema = z.object({
  genre: z.string(),
  subGenres: z.array(z.string()).optional(),
  povType: z.string().optional(),
  narrativeVoice: z.string().optional(),
  tense: z.string().optional(),
  writingStyle: z.string().optional(),
  targetAudience: z.string().optional(),
  contentRating: z.string().optional(),
  themes: z.array(z.string()).optional()
});

// Chapter schemas
export const createChapterSchema = z.object({
  project_id: uuidSchema,
  chapter_number: z.number().int().positive(),
  title: z.string().min(1).max(200),
  content: z.string().optional(),
  target_word_count: z.number().int().positive().optional(),
  summary: z.string().max(1000).optional(),
  notes: z.string().optional()
});

export const updateChapterSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  content: z.string().optional(),
  status: z.enum(['draft', 'revised', 'final']).optional(),
  summary: z.string().max(1000).optional(),
  notes: z.string().optional(),
  target_word_count: z.number().int().positive().optional()
});

// Character schemas
export const createCharacterSchema = z.object({
  project_id: uuidSchema,
  name: z.string().min(1).max(100),
  role: z.enum(['protagonist', 'antagonist', 'supporting', 'minor']),
  description: z.string().max(500).optional(),
  backstory: z.string().optional(),
  personality: z.string().optional(),
  appearance: z.string().optional(),
  traits: z.record(z.unknown()).optional(),
  relationships: z.array(z.object({
    character_id: uuidSchema,
    relationship_type: z.string()
  })).optional()
});

export const updateCharacterSchema = createCharacterSchema.partial().omit({ project_id: true });

// AI request schemas
export const aiGenerateRequestSchema = z.object({
  projectId: uuidSchema,
  action: z.enum([
    'generate_structure', 
    'generate_chapter', 
    'expand_chapter', 
    'edit_chapter', 
    'write_chapter', 
    'generate_complete_story',
    'batch_write_chapters',
    'regenerate_part'
  ]),
  chapterNumber: z.number().int().positive().optional(),
  chapterNumbers: z.array(z.number().int().positive()).optional(),
  editInstructions: z.string().optional(),
  regenerateOption: z.enum(['structure', 'characters', 'events', 'chapter']).optional(),
  options: z.object({
    includeCharacters: z.boolean().optional(),
    includeWorldBuilding: z.boolean().optional(),
    includeStoryBible: z.boolean().optional(),
    regenerateExisting: z.boolean().optional(),
    maxConcurrent: z.number().int().min(1).max(5).optional(),
    expansionFactor: z.number().min(1.2).max(3).optional(),
    targetWordCount: z.number().int().positive().optional()
  }).optional()
});

export const aiChatRequestSchema = z.object({
  message: z.string().min(1).max(2000),
  projectId: uuidSchema.optional(),
  chapterId: uuidSchema.optional(),
  context: z.object({
    selectedText: z.string().optional(),
    chapterContent: z.string().optional(),
    characterNames: z.array(z.string()).optional()
  }).optional()
});

export const aiAnalysisRequestSchema = z.object({
  content: z.string().min(1),
  analysisType: z.enum(['quality', 'pacing', 'character', 'plot', 'style']),
  projectId: uuidSchema.optional(),
  options: z.object({
    detailed: z.boolean().optional(),
    compareToProject: z.boolean().optional()
  }).optional()
});

// Edit text schema
export const editTextSchema = z.object({
  action: z.enum(['improve', 'expand', 'rewrite', 'suggest', 'custom', 'continue', 'summarize', 'rephrase']),
  selectedText: z.string().optional(),
  beforeCursor: z.string().optional(),
  afterCursor: z.string().optional(),
  fullContent: z.string().optional(),
  prompt: z.string().optional(),
  customPrompt: z.string().optional()
});

// Writing session schemas
export const createWritingSessionSchema = z.object({
  project_id: uuidSchema,
  chapter_id: uuidSchema.optional(),
  word_count: z.number().int().min(0),
  duration: z.number().int().positive(),
  started_at: dateSchema,
  ended_at: dateSchema
});

// User analytics schemas
export const analyticsQuerySchema = z.object({
  userId: uuidSchema,
  projectId: uuidSchema.optional(),
  startDate: dateSchema.optional(),
  endDate: dateSchema.optional(),
  groupBy: z.enum(['day', 'week', 'month']).optional()
});

// Export schemas
export const exportRequestSchema = z.object({
  projectId: uuidSchema,
  format: z.enum(['pdf', 'docx', 'epub', 'txt', 'markdown']),
  options: z.object({
    includeChapters: z.array(z.number().int().positive()).optional(),
    includeMetadata: z.boolean().optional(),
    includeCoverPage: z.boolean().optional(),
    fontSize: z.number().min(8).max(24).optional(),
    lineHeight: z.number().min(1).max(3).optional()
  }).optional()
});

// Search schemas
export const searchRequestSchema = z.object({
  query: z.string().min(1).max(200),
  projectId: uuidSchema.optional(),
  searchIn: z.array(z.enum(['chapters', 'characters', 'notes', 'outlines'])).optional(),
  limit: z.number().int().positive().max(50).default(20)
});

// Settings schemas
export const userSettingsSchema = z.object({
  theme: z.string().optional(),
  typography: z.object({
    fontSize: z.number().min(12).max(24).optional(),
    fontFamily: z.string().optional(),
    lineHeight: z.number().min(1).max(3).optional()
  }).optional(),
  editor: z.object({
    autoSave: z.boolean().optional(),
    autoSaveInterval: z.number().min(30).max(600).optional(),
    spellCheck: z.boolean().optional(),
    wordWrap: z.boolean().optional()
  }).optional(),
  ai: z.object({
    autoSuggestions: z.boolean().optional(),
    suggestionDelay: z.number().min(500).max(5000).optional(),
    qualityThreshold: z.number().min(0).max(100).optional()
  }).optional()
});

// File upload schemas
export const fileUploadSchema = z.object({
  projectId: uuidSchema,
  title: z.string().min(1).max(100),
  description: z.string().max(1000).optional(),
  tags: z.array(z.string().max(50)).max(10).optional(),
  category: z.enum(['reference', 'image', 'document', 'other']).optional()
});

// Helper functions for common validations
export function validatePaginatedQuery(query: URLSearchParams) {
  return paginationSchema.parse({
    page: query.get('page'),
    limit: query.get('limit'),
    sortBy: query.get('sortBy'),
    sortOrder: query.get('sortOrder')
  });
}

export function validateDateRange(startDate?: string, endDate?: string) {
  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start > end) {
      throw new Error('Start date must be before end date');
    }
    
    // Limit to 1 year range
    const yearInMs = 365 * 24 * 60 * 60 * 1000;
    if (end.getTime() - start.getTime() > yearInMs) {
      throw new Error('Date range cannot exceed 1 year');
    }
  }
  
  return { startDate, endDate };
}

// Utility function to create bulk operation schemas
export function createBulkSchema<T extends z.ZodSchema>(itemSchema: T) {
  return z.object({
    items: z.array(itemSchema).min(1).max(100),
    options: z.object({
      skipErrors: z.boolean().optional(),
      returnCreated: z.boolean().optional()
    }).optional()
  });
}

// Export commonly used bulk schemas
export const bulkCreateChaptersSchema = createBulkSchema(createChapterSchema);
export const bulkUpdateChaptersSchema = createBulkSchema(
  updateChapterSchema.extend({ id: uuidSchema })
);
export const bulkDeleteSchema = z.object({
  ids: z.array(uuidSchema).min(1).max(100)
});

// Export all schemas as a single object for convenience
export const ValidationSchemas = {
  // Common
  uuid: uuidSchema,
  email: emailSchema,
  url: urlSchema,
  date: dateSchema,
  pagination: paginationSchema,

  // Projects
  createProject: createProjectSchema,
  updateProject: updateProjectSchema,
  projectSettings: projectSettingsSchema,

  // Chapters
  createChapter: createChapterSchema,
  updateChapter: updateChapterSchema,

  // Characters
  createCharacter: createCharacterSchema,
  updateCharacter: updateCharacterSchema,

  // AI schemas
  aiGenerateRequest: aiGenerateRequestSchema,
  aiChatRequest: aiChatRequestSchema,
  aiAnalysisRequest: aiAnalysisRequestSchema,
  editText: editTextSchema,

  // Writing sessions
  createWritingSession: createWritingSessionSchema,

  // User analytics
  analyticsQuery: analyticsQuerySchema,

  // Export
  exportRequest: exportRequestSchema,

  // Search
  searchRequest: searchRequestSchema,

  // Settings
  userSettings: userSettingsSchema,

  // File upload
  fileUpload: fileUploadSchema,

  // Bulk operations
  bulkCreateChapters: bulkCreateChaptersSchema,
  bulkUpdateChapters: bulkUpdateChaptersSchema,
  bulkDelete: bulkDeleteSchema
};