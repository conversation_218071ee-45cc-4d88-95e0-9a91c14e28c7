import { logger } from '@/lib/services/logger';

// Client-safe configuration helper
// This file provides safe access to config values in client components

const getEnvValue = (key: string, defaultValue: string = ''): string => {
  try {
    if (typeof window === 'undefined') {
      // Server-side: use process.env
      return process.env[key] || defaultValue;
    }
    
    // Client-side: only NEXT_PUBLIC_ variables are available
    if (key.startsWith('NEXT_PUBLIC_')) {
      return (window as Window & { __ENV?: Record<string, string> }).__ENV?.[key] || process.env[key] || defaultValue;
    }
    
    return defaultValue;
  } catch (error) {
    logger.warn(`Error accessing environment variable ${key}:`, error);
    return defaultValue;
  }
};

export const clientConfig = {
  app: {
    url: getEnvValue('NEXT_PUBLIC_APP_URL', 'http://localhost:3001'),
    isDemoMode: getEnvValue('NEXT_PUBLIC_DEMO_MODE', 'false') === 'true' ||
                (getEnvValue('NODE_ENV', 'development') === 'development' && 
                 !getEnvValue('NEXT_PUBLIC_SUPABASE_URL')),
  },
  supabase: {
    url: getEnvValue('NEXT_PUBLIC_SUPABASE_URL', 'https://demo.supabase.co'),
    anonKey: getEnvValue('NEXT_PUBLIC_SUPABASE_ANON_KEY', 'demo_anon_key'),
  },
  stripe: {
    publishableKey: getEnvValue('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY', 'demo_stripe_publishable_key'),
  },
  dev: {
    bypassAuth: getEnvValue('NEXT_PUBLIC_DEV_BYPASS_AUTH', 'false') === 'true',
  },
};

// Helper to check if we're in demo mode
export const isDemoMode = (): boolean => {
  return clientConfig.app.isDemoMode;
};

// Helper to show demo mode warning
export const showDemoWarning = (): void => {
  if (isDemoMode() && typeof window !== 'undefined') {
    logger.info(
      '%c🚧 DEMO MODE ACTIVE %c\nAPI features are disabled. To enable full functionality, set up your environment variables.',
      'background: #ff6b6b; color: white; padding: 2px 6px; border-radius: 3px; font-weight: bold;',
      'color: #ff6b6b;'
    );
  }
};