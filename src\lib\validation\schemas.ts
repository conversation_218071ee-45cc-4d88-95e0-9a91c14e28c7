import { z } from 'zod'
import type { 
  PersonalityTrait as _PersonalityTrait, 
  CharacterArc as _CharacterArc, 
  CharacterRelationship as _CharacterRelationship, 
  VoiceData as _VoiceData,
  StoryBibleEntryData as _StoryBibleEntryData,
  StoryBibleAINotes as _StoryBibleAINotes
} from '@/lib/types/story-bible'

// Common validation schemas
export const uuidSchema = z.string().uuid('Invalid UUID format')
export const positiveIntSchema = z.number().int().positive('Must be a positive integer')
export const nonEmptyStringSchema = z.string().min(1, 'Cannot be empty')

// Character validation schemas
export const personalityTraitsSchema = z.object({
  traits: z.array(z.string()).default([]),
  strengths: z.array(z.string()).default([]),
  flaws: z.array(z.string()).default([]),
  motivations: z.array(z.string()).default([])
}).optional()

export const characterArcSchema = z.object({
  starting_point: z.string().optional(),
  transformation: z.string().optional(),
  ending_point: z.string().optional(),
  key_moments: z.array(z.object({
    chapter: z.number().int().positive(),
    description: z.string()
  })).default([])
}).optional()

export const relationshipsSchema = z.record(
  z.string(),
  z.object({
    relationship_type: z.string(),
    description: z.string(),
    history: z.string().optional(),
    current_status: z.string().optional()
  })
).optional()

export const voiceDataSchema = z.object({
  speaking_style: z.string(),
  vocabulary: z.array(z.string()).default([]),
  mannerisms: z.array(z.string()).default([])
}).optional()

export const createCharacterSchema = z.object({
  project_id: uuidSchema,
  character_id: nonEmptyStringSchema,
  name: nonEmptyStringSchema,
  role: z.enum(['protagonist', 'antagonist', 'supporting', 'minor']),
  description: z.string().optional(),
  backstory: z.string().optional(),
  personality_traits: personalityTraitsSchema,
  character_arc: characterArcSchema,
  relationships: relationshipsSchema,
  voice_data: voiceDataSchema
})

export const updateCharacterSchema = createCharacterSchema.partial().omit({ project_id: true })

export const characterQuerySchema = z.object({
  project_id: uuidSchema.optional(),
  role: z.enum(['protagonist', 'antagonist', 'supporting', 'minor']).optional(),
  limit: z.number().int().positive().max(100).default(50).optional(),
  offset: z.number().int().min(0).default(0).optional()
})

// Story Bible validation schemas
export const storyBibleEntryDataSchema = z.record(z.unknown())

export const createStoryBibleEntrySchema = z.object({
  project_id: uuidSchema,
  entry_type: z.enum(['character_state', 'plot_point', 'world_rule', 'timeline_event']),
  entry_key: nonEmptyStringSchema,
  entry_data: storyBibleEntryDataSchema,
  chapter_introduced: z.number().int().positive().optional(),
  is_active: z.boolean().default(true)
})

export const updateStoryBibleEntrySchema = createStoryBibleEntrySchema.partial().omit({ project_id: true })

export const storyBibleQuerySchema = z.object({
  project_id: uuidSchema.optional(),
  entry_type: z.enum(['character_state', 'plot_point', 'world_rule', 'timeline_event']).optional(),
  is_active: z.boolean().optional(),
  chapter_introduced: z.number().int().positive().optional(),
  limit: z.number().int().positive().max(100).default(50).optional(),
  offset: z.number().int().min(0).default(0).optional()
})

// Chapter validation schemas
export const scenesDataSchema = z.object({
  scenes: z.array(z.object({
    number: z.number().int().positive(),
    setting: z.string(),
    characters: z.array(z.string()),
    objectives: z.array(z.string()),
    conflicts: z.array(z.string()),
    resolutions: z.array(z.string())
  }))
}).optional()

export const characterStatesSchema = z.record(
  z.string(),
  z.object({
    emotional_state: z.string(),
    goals: z.array(z.string()),
    knowledge: z.array(z.string()),
    relationships: z.record(z.string(), z.string())
  })
).optional()

export const plotAdvancementSchema = z.object({
  main_plot: z.object({
    threads: z.array(z.string()),
    advancement: z.string(),
    conflicts_introduced: z.array(z.string()),
    conflicts_resolved: z.array(z.string())
  }),
  subplots: z.array(z.object({
    name: z.string(),
    advancement: z.string(),
    status: z.enum(['active', 'resolved', 'paused'])
  }))
}).optional()

export const createChapterSchema = z.object({
  project_id: uuidSchema,
  chapter_number: positiveIntSchema,
  title: z.string().optional(),
  target_word_count: positiveIntSchema.optional(),
  actual_word_count: z.number().int().min(0).default(0),
  outline: z.string().optional(),
  content: z.string().optional(),
  scenes_data: scenesDataSchema,
  character_states: characterStatesSchema,
  status: z.enum(['planned', 'writing', 'review', 'complete']).default('planned'),
  ai_notes: z.record(z.unknown()).optional(),
  pov_character: z.string().optional(),
  plot_advancement: plotAdvancementSchema
})

export const updateChapterSchema = createChapterSchema.partial().omit({ project_id: true })

export const chapterQuerySchema = z.object({
  project_id: uuidSchema.optional(),
  status: z.enum(['planned', 'writing', 'review', 'complete']).optional(),
  limit: z.number().int().positive().max(100).default(50).optional(),
  offset: z.number().int().min(0).default(0).optional()
})

// Project validation schemas
export const updateProjectSchema = z.object({
  title: nonEmptyStringSchema.optional(),
  description: z.string().optional(),
  primary_genre: z.string().optional(),
  subgenre: z.string().optional(),
  custom_genre: z.string().optional(),
  narrative_voice: z.string().optional(),
  tense: z.string().optional(),
  tone_options: z.array(z.string()).optional(),
  writing_style: z.string().optional(),
  custom_style_description: z.string().optional(),
  structure_type: z.string().optional(),
  pacing_preference: z.string().optional(),
  chapter_structure: z.string().optional(),
  timeline_complexity: z.string().optional(),
  custom_structure_notes: z.string().optional(),
  protagonist_types: z.array(z.string()).optional(),
  antagonist_types: z.array(z.string()).optional(),
  character_complexity: z.string().optional(),
  character_arc_types: z.array(z.string()).optional(),
  custom_character_concepts: z.string().optional(),
  time_period: z.string().optional(),
  geographic_setting: z.string().optional(),
  world_type: z.string().optional(),
  magic_tech_level: z.string().optional(),
  custom_setting_description: z.string().optional(),
  major_themes: z.array(z.string()).optional(),
  philosophical_themes: z.array(z.string()).optional(),
  social_themes: z.array(z.string()).optional(),
  custom_themes: z.string().optional(),
  target_audience: z.string().optional(),
  content_rating: z.string().optional(),
  content_warnings: z.array(z.string()).optional(),
  cultural_sensitivity_notes: z.string().optional(),
  project_scope: z.string().optional(),
  series_type: z.string().optional(),
  interconnection_level: z.string().optional(),
  custom_scope_description: z.string().optional(),
  target_word_count: positiveIntSchema.optional(),
  current_word_count: z.number().int().min(0).optional(),
  target_chapters: positiveIntSchema.optional(),
  chapter_count_type: z.string().optional(),
  pov_character_count: positiveIntSchema.optional(),
  pov_character_type: z.string().optional(),
  research_needs: z.array(z.string()).optional(),
  fact_checking_level: z.string().optional(),
  custom_research_notes: z.string().optional(),
  initial_concept: z.string().optional(),
  status: z.enum(['planning', 'writing', 'editing', 'completed', 'paused', 'archived']).optional()
})

// Bulk operation schemas
export const bulkCharacterOperationSchema = z.object({
  operation: z.enum(['create', 'update', 'delete']),
  characters: z.array(z.union([
    createCharacterSchema,
    updateCharacterSchema.extend({ id: uuidSchema }),
    z.object({ id: uuidSchema })
  ]))
})

export const bulkStoryBibleOperationSchema = z.object({
  operation: z.enum(['create', 'update', 'delete']),
  entries: z.array(z.union([
    createStoryBibleEntrySchema,
    updateStoryBibleEntrySchema.extend({ id: uuidSchema }),
    z.object({ id: uuidSchema })
  ]))
})

// Response schemas for better type safety
export const characterResponseSchema = z.object({
  id: uuidSchema,
  project_id: uuidSchema,
  character_id: z.string(),
  name: z.string(),
  role: z.string(),
  description: z.string().nullable(),
  backstory: z.string().nullable(),
  personality_traits: personalityTraitsSchema.nullable(),
  character_arc: characterArcSchema.nullable(),
  relationships: relationshipsSchema.nullable(),
  voice_data: voiceDataSchema.nullable(),
  created_at: z.string(),
  updated_at: z.string()
})

export const storyBibleEntryResponseSchema = z.object({
  id: uuidSchema,
  project_id: uuidSchema,
  entry_type: z.string(),
  entry_key: z.string(),
  entry_data: storyBibleEntryDataSchema,
  chapter_introduced: z.number().nullable(),
  is_active: z.boolean(),
  created_at: z.string(),
  updated_at: z.string()
})

// Error response schema
export const errorResponseSchema = z.object({
  error: z.string(),
  details: z.unknown().optional(),
  code: z.string().optional()
})

// Success response schema
export const successResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.unknown().optional()
})