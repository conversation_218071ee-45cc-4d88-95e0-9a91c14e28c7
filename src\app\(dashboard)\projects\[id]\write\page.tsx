'use client'

import { useEffect, useState } from 'react'
import { logger } from '@/lib/services/logger';

import { useParams, useSearchParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { useEditorStore } from '@/stores/editor-store'
import { LazyMonacoEditor } from '@/components/editor/lazy-monaco-editor'
import { EnhancedDocumentNavigator } from '@/components/editor/enhanced-document-navigator'
import {
  LazyChapterReviewPanel
} from '@/components/lazy'
// import { KnowledgeInputPanel } from '@/components/editor/knowledge-input-panel'
import { EnhancedSidebarPanel } from '@/components/editor/enhanced-sidebar-panel'
import { SaveStatusIndicator, useOnlineStatus } from '@/components/version-history/save-status-indicator'
import { ErrorBoundary } from '@/components/ui/error-boundary'
import { useAutoSave, useUnsavedChanges } from '@/hooks/use-auto-save'
import { useChapterGeneration, UserChanges } from '@/hooks/use-chapter-generation'
import { But<PERSON> } from '@/components/ui/button'
import { BookImportDialog } from '@/components/import/book-import-dialog'
import { PanelManager, PanelConfig, LayoutPreset } from '@/components/editor/panel-manager'
import { toast } from '@/hooks/use-toast'
import { WritingSessionDisplay } from '@/components/editor/writing-session-display'
import { 
  Save, 
  FileText,
  ArrowLeft,
  BarChart3,
  Wand2,
  Eye,
  Upload,
  Brain
} from 'lucide-react'
import Link from 'next/link'
import { Database } from '@/lib/db/types'
import { User } from '@supabase/supabase-js'

type Project = Database['public']['Tables']['projects']['Row']
type Chapter = Database['public']['Tables']['chapters']['Row']

export default function WritePage() {
  const params = useParams()
  const searchParams = useSearchParams()
  const projectId = params.id as string
  const chapterId = searchParams.get('chapter')
  
  const {
    content,
    setContent,
    selectedText,
    showChapterNavigator,
    toggleChapterNavigator,
    currentChapter,
    setCurrentChapter,
    updateChapterList
  } = useEditorStore()

  const [showRightPanel, setShowRightPanel] = useState(true)
  const [rightPanelTab, setRightPanelTab] = useState('knowledge')
  const [project, setProject] = useState<Project | null>(null)
  const [currentChapterData, setCurrentChapterData] = useState<Chapter | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [showWritingStats, setShowWritingStats] = useState(false)
  const [originalGeneratedContent, setOriginalGeneratedContent] = useState<string>('')
  const [focusMode, setFocusMode] = useState(false)
  
  // Panel configuration
  const [panelConfigs, setPanelConfigs] = useState<PanelConfig[]>([
    { id: 'chapters', label: 'Chapters', icon: FileText, visible: showChapterNavigator, pinned: false, position: 'left' },
    { id: 'right-panel', label: 'Project Tools', icon: Brain, visible: showRightPanel, pinned: false, position: 'right' },
    { id: 'stats', label: 'Writing Stats', icon: BarChart3, visible: showWritingStats, pinned: false, position: 'bottom' }
  ])
  
  const supabase = createClient()
  const isOnline = useOnlineStatus()
  const { setUnsavedChanges } = useUnsavedChanges()

  // Chapter generation hook
  const chapterGeneration = useChapterGeneration(projectId, currentChapterData?.id || '')

  // Auto-save hook
  const { saveNow } = useAutoSave(
    {
      chapterId: currentChapterData?.id || '',
      content,
      wordCount: content.trim().split(/\s+/).filter(word => word.length > 0).length
    },
    {
      enabled: !!currentChapterData?.id && isOnline,
      delay: 3000,
      onSave: (success) => {
        if (success) {
          setLastSaved(new Date())
          setHasUnsavedChanges(false)
          setUnsavedChanges(false)
        }
      },
      onError: (error) => {
        logger.error('Auto-save failed:', error);
      }
    }
  )

  useEffect(() => {
    loadUser()
    loadProject()
    loadChapters()
  }, [projectId]) // eslint-disable-line react-hooks/exhaustive-deps

  const loadUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
  }

  // Track content changes for unsaved state
  useEffect(() => {
    if (currentChapterData && content !== (currentChapterData.content || '')) {
      setHasUnsavedChanges(true)
      setUnsavedChanges(true)
    }
  }, [content, currentChapterData, setUnsavedChanges])

  useEffect(() => {
    if (chapterId) {
      loadChapter(chapterId)
    } else {
      // Load or create first chapter
      loadFirstChapter()
    }
  }, [chapterId]) // eslint-disable-line react-hooks/exhaustive-deps

  const loadProject = async () => {
    const { data } = await supabase
      .from('projects')
      .select(`
        *,
        series_books!series_books_project_id_fkey(
          series_id,
          series:series(
            id,
            title
          )
        )
      `)
      .eq('id', projectId)
      .single()
    
    if (data) setProject(data)
  }

  const loadChapters = async () => {
    const { data } = await supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .order('chapter_number')
    
    if (data) {
      updateChapterList(data.map(ch => ({
        id: ch.id,
        number: ch.chapter_number,
        title: ch.title || `Chapter ${ch.chapter_number}`,
        status: ch.status,
        wordCount: ch.actual_word_count || 0
      })))
    }
  }

  const loadChapter = async (id: string) => {
    const { data } = await supabase
      .from('chapters')
      .select('*')
      .eq('id', id)
      .single()
    
    if (data) {
      setCurrentChapterData(data)
      setContent(data.content || '')
      setCurrentChapter(data.chapter_number)
    }
  }

  const loadFirstChapter = async () => {
    const { data } = await supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .order('chapter_number')
      .limit(1)
      .single()
    
    if (data) {
      setCurrentChapterData(data)
      setContent(data.content || '')
      setCurrentChapter(data.chapter_number)
    } else {
      // Create first chapter
      createNewChapter()
    }
  }


  const createNewChapter = async () => {
    const chapterNumber = currentChapter || 1
    const { data } = await supabase
      .from('chapters')
      .insert({
        project_id: projectId,
        chapter_number: chapterNumber,
        title: `Chapter ${chapterNumber}`,
        content: '',
        status: 'writing'
      })
      .select()
      .single()

    if (data) {
      setCurrentChapterData(data)
      setContent('')
      loadChapters()
    }
  }

  const saveChapter = async () => {
    if (!currentChapterData) return
    
    setIsSaving(true)
    try {
      const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length
      
      const { error } = await supabase
        .from('chapters')
        .update({
          content,
          actual_word_count: wordCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', currentChapterData.id)

      if (!error) {
        setLastSaved(new Date())
        loadChapters() // Refresh chapter list
      }
    } finally {
      setIsSaving(false)
    }
  }

  const handleChapterSelect = (id: string) => {
    window.history.pushState({}, '', `/projects/${projectId}/write?chapter=${id}`)
    loadChapter(id)
  }

  const handleCreateChapter = async () => {
    const newChapterNumber = Math.max(...useEditorStore.getState().chapters.map(ch => ch.number), 0) + 1
    
    const { data } = await supabase
      .from('chapters')
      .insert({
        project_id: projectId,
        chapter_number: newChapterNumber,
        title: `Chapter ${newChapterNumber}`,
        content: '',
        status: 'writing'
      })
      .select()
      .single()

    if (data) {
      loadChapters()
      handleChapterSelect(data.id)
    }
  }

  const handleGenerateChapter = async () => {
    if (!currentChapterData) return

    const generatedData = await chapterGeneration.generateChapter(currentChapterData.chapter_number)
    if (generatedData) {
      setOriginalGeneratedContent(generatedData.content)
      setContent(generatedData.content)
      
      // Start the review process
      chapterGeneration.startReview(generatedData.content, generatedData.content)
    }
  }

  const handleReviewChanges = () => {
    if (originalGeneratedContent && content !== originalGeneratedContent) {
      chapterGeneration.startReview(originalGeneratedContent, content)
    }
  }

  const handleSubmitChanges = async (changes: UserChanges) => {
    const success = await chapterGeneration.submitChanges(changes)
    if (success) {
      setOriginalGeneratedContent('')
      loadChapters() // Refresh chapter list
    }
    return success
  }

  const handleApproveGeneration = async () => {
    const success = await chapterGeneration.approveGeneration()
    if (success) {
      setOriginalGeneratedContent('')
      loadChapters() // Refresh chapter list
    }
    return success
  }

  // Panel management functions
  const handlePanelToggle = (panelId: string) => {
    switch (panelId) {
      case 'chapters':
        toggleChapterNavigator()
        break
      case 'right-panel':
        setShowRightPanel(!showRightPanel)
        break
      case 'stats':
        setShowWritingStats(!showWritingStats)
        break
    }
  }

  const handlePanelPin = (panelId: string) => {
    setPanelConfigs(configs => 
      configs.map(config => 
        config.id === panelId ? { ...config, pinned: !config.pinned } : config
      )
    )
  }

  const handleLayoutChange = (newPanels: PanelConfig[]) => {
    setPanelConfigs(newPanels)
    // Apply the layout changes
    newPanels.forEach(panel => {
      const currentPanel = panelConfigs.find(p => p.id === panel.id)
      if (currentPanel && currentPanel.visible !== panel.visible) {
        handlePanelToggle(panel.id)
      }
    })
  }

  const handlePresetSelect = (preset: LayoutPreset) => {
    // Apply preset logic here
    // For now, just toggle panels based on preset
    setPanelConfigs(preset.panels)
  }

  const handleRightPanelTabChange = (tab: string) => {
    setRightPanelTab(tab)
  }

  // Update panel configs when visibility changes
  useEffect(() => {
    setPanelConfigs(configs => configs.map(config => {
      switch (config.id) {
        case 'chapters': return { ...config, visible: showChapterNavigator }
        case 'right-panel': return { ...config, visible: showRightPanel }
        case 'stats': return { ...config, visible: showWritingStats }
        default: return config
      }
    }))
  }, [showChapterNavigator, showRightPanel, showWritingStats])

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <header className="border-b px-4 py-2 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href={`/projects/${projectId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Project
            </Button>
          </Link>
          
          <div className="flex items-center gap-2">
            <h1 className="text-lg font-semibold">
              {project?.title || 'Loading...'}
            </h1>
            {currentChapterData && (
              <span className="text-sm text-muted-foreground">
                • Chapter {currentChapterData.chapter_number}
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Writing Session Display */}
          {user && (
            <WritingSessionDisplay 
              userId={user.id}
              projectId={projectId}
              chapterId={currentChapterData?.id}
              variant="compact"
            />
          )}
          <SaveStatusIndicator 
            isSaving={isSaving}
            lastSaved={lastSaved}
            hasUnsavedChanges={hasUnsavedChanges}
            isOnline={isOnline}
            onManualSave={saveNow}
          />
          
          <BookImportDialog 
            projectId={projectId}
            onImportComplete={() => {
              loadChapters()
              toast({
                title: "Import complete",
                description: "Your chapters have been imported. Select a chapter to continue editing."
              })
            }}
            trigger={
              <Button variant="ghost" size="sm">
                <Upload className="h-4 w-4" />
              </Button>
            }
          />
          
          <PanelManager
            currentPanels={panelConfigs}
            onPanelToggle={handlePanelToggle}
            onPanelPin={handlePanelPin}
            onLayoutChange={handleLayoutChange}
            onPresetSelect={handlePresetSelect}
          />
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowRightPanel(!showRightPanel)}
            className={showRightPanel ? 'bg-warm-100 text-warm-800' : ''}
          >
            <Brain className="h-4 w-4" />
          </Button>

          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setShowWritingStats(!showWritingStats)}
          >
            <BarChart3 className="h-4 w-4" />
          </Button>

          {/* Chapter Generation Controls */}
          {currentChapterData?.status === 'planned' && (
            <Button 
              onClick={handleGenerateChapter}
              disabled={chapterGeneration.state.isGenerating}
              size="sm"
              variant="outline"
            >
              <Wand2 className="h-4 w-4 mr-2" />
              {chapterGeneration.state.isGenerating ? 'Generating...' : 'Generate Chapter'}
            </Button>
          )}

          {originalGeneratedContent && content !== originalGeneratedContent && (
            <Button 
              onClick={handleReviewChanges}
              size="sm"
              variant="outline"
            >
              <Eye className="h-4 w-4 mr-2" />
              Review Changes
            </Button>
          )}
          
          <Button 
            onClick={saveChapter}
            disabled={isSaving}
            size="sm"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Document Navigator */}
        {showChapterNavigator && (
          <div className="border-r">
            <ErrorBoundary>
              <EnhancedDocumentNavigator
                projectId={projectId}
                currentChapterId={currentChapterData?.id}
                content={content}
                onChapterSelect={handleChapterSelect}
                onCreateChapter={handleCreateChapter}
              />
            </ErrorBoundary>
          </div>
        )}

        {/* Main Editor Area */}
        <div className="flex-1 flex">
          {/* Editor */}
          <div className="flex-1">
            <LazyMonacoEditor
              initialContent={content}
              onContentChange={setContent}
              onSave={saveChapter}
              showToolbar={true}
              showStats={showWritingStats}
              showAISuggestions={true}
              focusMode={focusMode}
              onFocusModeToggle={setFocusMode}
              projectId={projectId}
              chapterNumber={currentChapterData?.chapter_number}
            />
          </div>

          {/* Enhanced Right Panel */}
          {showRightPanel && (
            <ErrorBoundary>
              <EnhancedSidebarPanel
                projectId={projectId}
                chapterId={currentChapterData?.id}
                selectedText={selectedText}
                content={content}
                userId={user?.id}
                seriesId={project?.series_books?.[0]?.series_id}
                onClose={() => setShowRightPanel(false)}
                defaultTab={rightPanelTab}
                onTabChange={handleRightPanelTabChange}
              />
            </ErrorBoundary>
          )}
        </div>
      </div>

      {/* Chapter Review Panel */}
      <LazyChapterReviewPanel
        isOpen={chapterGeneration.state.isReviewing}
        onClose={() => chapterGeneration.resetState()}
        originalContent={originalGeneratedContent}
        editedContent={content}
        chapterTitle={currentChapterData?.title || `Chapter ${currentChapterData?.chapter_number}`}
        chapterNumber={currentChapterData?.chapter_number || 1}
        projectId={projectId}
        chapterId={currentChapterData?.id || ''}
        onSubmitChanges={handleSubmitChanges}
        onApproveGeneration={handleApproveGeneration}
      />
    </div>
  )
}