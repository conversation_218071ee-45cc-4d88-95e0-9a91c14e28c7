import { STRIPE_PRICES } from './stripe'

export interface SubscriptionTier {
  id: string
  name: string
  description: string
  price: number
  currency: string
  interval: 'month' | 'year'
  features: string[]
  limits: {
    projects: number
    monthlyWords: number
    exportFormats: string[]
    storageGB: number
    collaborators: number
  }
  agents: string[] // List of available agent names
  stripePriceId: string
  popular?: boolean
  aiModels: {
    primary: string  // Primary model (gpt-4.1 or gpt-4o-mini)
    fast: string     // Fast model (always gpt-4o-mini)
    availableForTasks: string[] // Which task types can use GPT-4.1
  }
}

export const SUBSCRIPTION_TIERS: SubscriptionTier[] = [
  {
    id: 'starter',
    name: 'Starter',
    description: 'Perfect for trying out BookScribe AI',
    price: 0,
    currency: 'usd',
    interval: 'month',
    features: [
      '1 Active Project',
      '10,000 AI Words per month',
      'PDF export with watermark',
      'Community support',
      'Basic editor features',
      '500MB storage',
      'Essential AI intelligence'
    ],
    limits: {
      projects: 1,
      monthlyWords: 10000,
      exportFormats: ['pdf-watermark'], // Special format for watermarked PDFs
      storageGB: 0.5,
      collaborators: 0
    },
    agents: ['Story Architect', 'Writing Agent'],
    stripePriceId: '',
    aiModels: {
      primary: 'gpt-4o-mini',
      fast: 'gpt-4o-mini',
      availableForTasks: [] // No GPT-4.1 access
    }
  },
  {
    id: 'writer',
    name: 'Writer',
    description: 'For dedicated writers crafting their stories',
    price: 9,
    currency: 'usd',
    interval: 'month',
    features: [
      '3 Active Projects',
      '50,000 AI Words per month',
      'Export formats (TXT, Markdown, DOCX)',
      'Priority email support',
      'Story Bible Access',
      'Voice Consistency (Basic)',
      '5 AI Agents',
      '10GB storage',
      'Enhanced AI intelligence'
    ],
    limits: {
      projects: 3,
      monthlyWords: 50000,
      exportFormats: ['txt', 'markdown', 'docx'],
      storageGB: 10,
      collaborators: 0
    },
    agents: ['Story Architect', 'Character Developer', 'Writing Agent', 'Chapter Planner', 'Adaptive Planning'],
    stripePriceId: STRIPE_PRICES.basic,
    popular: true,
    aiModels: {
      primary: 'gpt-4.1-2025-04-14',
      fast: 'gpt-4o-mini',
      availableForTasks: ['story-structure', 'character-development'] // Limited GPT-4.1 access
    }
  },
  {
    id: 'author',
    name: 'Author',
    description: 'For serious authors building their craft',
    price: 29,
    currency: 'usd',
    interval: 'month',
    features: [
      '10 Active Projects',
      '150,000 AI Words per month',
      'Export formats (TXT, Markdown, DOCX, PDF)',
      'Priority chat support',
      'Full Story Bible & Analytics',
      'Series Management (3 series)',
      'Access to all AI Agents',
      '25GB storage',
      'Premium AI intelligence'
    ],
    limits: {
      projects: 10,
      monthlyWords: 150000,
      exportFormats: ['txt', 'markdown', 'docx', 'pdf'],
      storageGB: 25,
      collaborators: 0
    },
    agents: ['Story Architect', 'Character Developer', 'Writing Agent', 'Editor Agent', 'Chapter Planner', 'Adaptive Planning'],
    stripePriceId: STRIPE_PRICES.pro,
    aiModels: {
      primary: 'gpt-4.1-2025-04-14',
      fast: 'gpt-4o-mini',
      availableForTasks: ['story-structure', 'character-development', 'chapter-writing', 'editing']
    }
  },
  {
    id: 'professional',
    name: 'Professional',
    description: 'For professional authors and small teams',
    price: 49,
    currency: 'usd',
    interval: 'month',
    features: [
      'Unlimited Projects',
      '300,000 AI Words per month',
      'All export formats (TXT, Markdown, DOCX, PDF, EPUB)',
      'Priority chat support',
      'Full Story Bible & Analytics',
      'Unlimited Series Management',
      'Character Sharing',
      'Universe Creation',
      '2 Team Members',
      '100GB storage',
      'Advanced AI intelligence'
    ],
    limits: {
      projects: -1,
      monthlyWords: 300000,
      exportFormats: ['txt', 'markdown', 'docx', 'pdf', 'epub'],
      storageGB: 100,
      collaborators: 2
    },
    agents: ['Story Architect', 'Character Developer', 'Writing Agent', 'Editor Agent', 'Chapter Planner', 'Adaptive Planning'],
    stripePriceId: STRIPE_PRICES.pro_plus || STRIPE_PRICES.pro,
    aiModels: {
      primary: 'gpt-4.1-2025-04-14',
      fast: 'gpt-4o-mini',
      availableForTasks: ['story-structure', 'character-development', 'chapter-writing', 'editing', 'analysis']
    }
  },
  {
    id: 'studio',
    name: 'Studio',
    description: 'For publishing houses and writing teams',
    price: 89,
    currency: 'usd',
    interval: 'month',
    features: [
      'Unlimited Projects',
      '600,000 AI Words per month',
      'All exports + custom formats',
      'White-glove support',
      'Shared Universe Creation',
      'Cross-Series Characters',
      'Advanced Analytics',
      'Early Access Features',
      '5 Team Members + Real-time Collaboration',
      '500GB storage',
      'Ultimate AI intelligence'
    ],
    limits: {
      projects: -1, // unlimited
      monthlyWords: 600000,
      exportFormats: ['txt', 'markdown', 'docx', 'pdf', 'epub', 'custom'],
      storageGB: 500,
      collaborators: 5
    },
    agents: ['Story Architect', 'Character Developer', 'Writing Agent', 'Editor Agent', 'Chapter Planner', 'Adaptive Planning'],
    stripePriceId: STRIPE_PRICES.enterprise,
    aiModels: {
      primary: 'gpt-4.1-2025-04-14',
      fast: 'gpt-4o-mini',
      availableForTasks: ['all'] // Unrestricted access
    }
  }
]

export interface UserSubscription {
  id: string
  userId: string
  tierId: string
  status: 'active' | 'canceled' | 'past_due' | 'incomplete'
  stripeSubscriptionId?: string
  stripeCustomerId?: string
  currentPeriodStart: Date
  currentPeriodEnd: Date
  cancelAtPeriodEnd: boolean
  usage: {
    wordsUsed: number // Changed from aiGenerations
    projects: number
    storage: number
  }
  createdAt: Date
  updatedAt: Date
}

export function getTierById(tierId: string): SubscriptionTier | undefined {
  return SUBSCRIPTION_TIERS.find(tier => tier.id === tierId)
}

export function getUserTier(subscription: UserSubscription | null): SubscriptionTier {
  if (!subscription || subscription.status !== 'active') {
    const freeTier = SUBSCRIPTION_TIERS[0]
    if (!freeTier) {
      throw new Error('Free tier not found in subscription tiers')
    }
    return freeTier
  }
  const tier = getTierById(subscription.tierId) || SUBSCRIPTION_TIERS[0]
  if (!tier) {
    throw new Error('No valid subscription tier found')
  }
  return tier
}

export function checkUsageLimit(
  subscription: UserSubscription | null,
  limitType: keyof SubscriptionTier['limits'],
  currentUsage: number
): { allowed: boolean; limit: number; remaining: number } {
  const tier = getUserTier(subscription)
  const limit = tier.limits[limitType]
  
  if (typeof limit === 'number') {
    if (limit === -1) {
      // Unlimited
      return { allowed: true, limit: -1, remaining: -1 }
    }
    
    const remaining = Math.max(0, limit - currentUsage)
    return {
      allowed: currentUsage < limit,
      limit,
      remaining
    }
  }
  
  // For array limits (like exportFormats), check if the feature is included
  if (Array.isArray(limit)) {
    return {
      allowed: true,
      limit: limit.length,
      remaining: limit.length - currentUsage
    }
  }
  
  return { allowed: false, limit: 0, remaining: 0 }
}

export function canUseFeature(
  subscription: UserSubscription | null,
  feature: string
): boolean {
  const tier = getUserTier(subscription)
  
  // Map features to tier checks
  switch (feature) {
    case 'story_bible':
      return ['writer', 'author', 'professional', 'studio'].includes(tier.id)
    case 'character_profiles':
      return ['writer', 'author', 'professional', 'studio'].includes(tier.id)
    case 'voice_consistency':
      return ['writer', 'author', 'professional', 'studio'].includes(tier.id)
    case 'advanced_analytics':
      return ['author', 'professional', 'studio'].includes(tier.id)
    case 'series_management':
      return ['author', 'professional', 'studio'].includes(tier.id)
    case 'character_sharing':
      return ['professional', 'studio'].includes(tier.id)
    case 'collaboration':
      return ['author', 'professional', 'studio'].includes(tier.id)
    case 'shared_universe':
      return ['professional', 'studio'].includes(tier.id)
    case 'cross_series_characters':
      return tier.id === 'studio'
    case 'real_time_collaboration':
      return tier.id === 'studio'
    case 'epub_export':
      return ['professional', 'studio'].includes(tier.id)
    case 'pdf_export':
      return ['author', 'professional', 'studio'].includes(tier.id)
    case 'pdf_watermark_export':
      return tier.id === 'starter' // Only starter gets watermarked PDFs
    default:
      return true
  }
}

// Check if a user can use a specific AI agent
export function canUseAgent(
  subscription: UserSubscription | null,
  agentName: string
): boolean {
  const tier = getUserTier(subscription)
  return tier.agents.includes(agentName)
}

// Get list of available agents for a tier
export function getAvailableAgents(subscription: UserSubscription | null): string[] {
  const tier = getUserTier(subscription)
  return tier.agents
}

export function formatPrice(price: number, currency: string = 'usd'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase()
  }).format(price)
}

// Helper function to check if EPUB export is allowed for a tier
export function canExportEpub(tierId: string): boolean {
  return tierId === 'professional' || tierId === 'studio'
}

// Helper function to get AI model based on tier and task type
export function getAIModelForTask(
  tierId: string,
  taskType: string
): string {
  const tier = getTierById(tierId)
  if (!tier) return 'gpt-4o-mini' // default fallback
  
  // Import models for easy reference
  const { AI_MODELS } = require('./config/ai-settings')
  
  // Starter tier - all tasks use fast model for maximum word allocation
  if (tierId === 'starter') {
    return AI_MODELS.FAST
  }
  
  // Writer tier - strategic model selection
  if (tierId === 'writer') {
    // Story & character tasks get medium model
    if (['story-structure', 'character-development'].includes(taskType)) {
      return AI_MODELS.MEDIUM
    }
    // Everything else uses fast model
    return AI_MODELS.FAST
  }
  
  // Author tier - mixed model access
  if (tierId === 'author') {
    // Creative tasks get primary model
    if (['chapter-writing', 'creative-writing', 'dialogue-generation'].includes(taskType)) {
      return AI_MODELS.PRIMARY
    }
    // Analysis tasks get medium model
    if (['editing', 'analysis', 'chapter-planning'].includes(taskType)) {
      return AI_MODELS.MEDIUM
    }
    // Simple tasks use fast model
    return AI_MODELS.FAST
  }
  
  // Professional & Studio - optimal model for each task
  if (['professional', 'studio'].includes(tierId)) {
    // High complexity tasks
    if ([
      'story-structure',
      'character-development',
      'chapter-writing',
      'creative-writing',
      'dialogue-generation'
    ].includes(taskType)) {
      return AI_MODELS.PRIMARY
    }
    
    // Medium complexity tasks
    if ([
      'chapter-planning',
      'editing',
      'content-generation',
      'analysis'
    ].includes(taskType)) {
      return AI_MODELS.MEDIUM
    }
    
    // Low complexity tasks
    return AI_MODELS.FAST
  }
  
  // Default fallback
  return AI_MODELS.FAST
}

// Helper function to check if real-time collaboration is available
export function hasRealTimeCollaboration(tierId: string): boolean {
  return tierId === 'studio'
}

// Helper function to get available export formats for a tier
export function getAvailableExportFormats(tierId: string): string[] {
  const tier = getTierById(tierId)
  if (!tier) return [] // No formats for invalid tier
  
  return tier.limits.exportFormats
}

// Helper function to check if a specific export format is allowed
export function canExportFormat(tierId: string, format: string): boolean {
  const availableFormats = getAvailableExportFormats(tierId)
  return availableFormats.includes(format) || 
         (format === 'pdf' && availableFormats.includes('pdf-watermark'))
}

// Helper to check if export needs watermark
export function needsWatermark(tierId: string, format: string): boolean {
  return tierId === 'starter' && format === 'pdf'
}