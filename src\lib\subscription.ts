import { STRIPE_PRICES } from './stripe'

export interface SubscriptionTier {
  id: string
  name: string
  description: string
  price: number
  currency: string
  interval: 'month' | 'year'
  features: string[]
  limits: {
    projects: number
    aiGenerations: number
    exportFormats: string[]
    storageGB: number
    collaborators: number
  }
  stripePriceId: string
  popular?: boolean
  aiModels: {
    primary: string  // Primary model (gpt-4.1 or gpt-4o-mini)
    fast: string     // Fast model (always gpt-4o-mini)
    modelRatio?: {   // Ratio of mini vs GPT-4.1 usage
      mini: number
      gpt4: number
    }
  }
}

export const SUBSCRIPTION_TIERS: SubscriptionTier[] = [
  {
    id: 'starter',
    name: 'Starter',
    description: 'Perfect for trying out BookScribe AI',
    price: 0,
    currency: 'usd',
    interval: 'month',
    features: [
      '1 Active Project',
      '20 AI Generations per month',
      'Basic export (TXT, Markdown)',
      'Community support',
      'Basic editor features',
      '500MB storage',
      'GPT-4o Mini only'
    ],
    limits: {
      projects: 1,
      aiGenerations: 20,
      exportFormats: ['txt', 'markdown'],
      storageGB: 0.5,
      collaborators: 0
    },
    stripePriceId: '',
    aiModels: {
      primary: 'gpt-4o-mini',
      fast: 'gpt-4o-mini',
      modelRatio: { mini: 100, gpt4: 0 }
    }
  },
  {
    id: 'writer',
    name: 'Writer',
    description: 'For dedicated writers crafting their stories',
    price: 19,
    currency: 'usd',
    interval: 'month',
    features: [
      '3 Active Projects',
      '100 AI Generations per month',
      'Export formats (TXT, Markdown, DOCX)',
      'Priority email support',
      'Story Bible Access',
      'Voice Consistency (Basic)',
      '5 of 6 AI Agents',
      '10GB storage',
      '80% GPT-4o Mini, 20% GPT-4.1'
    ],
    limits: {
      projects: 3,
      aiGenerations: 100,
      exportFormats: ['txt', 'markdown', 'docx'],
      storageGB: 10,
      collaborators: 0
    },
    stripePriceId: STRIPE_PRICES.basic,
    popular: true,
    aiModels: {
      primary: 'gpt-4o-mini',
      fast: 'gpt-4o-mini',
      modelRatio: { mini: 80, gpt4: 20 }
    }
  },
  {
    id: 'author',
    name: 'Author',
    description: 'For serious authors building their craft',
    price: 39,
    currency: 'usd',
    interval: 'month',
    features: [
      '10 Active Projects',
      '300 AI Generations per month',
      'Export formats (TXT, Markdown, DOCX, PDF)',
      'Priority chat support',
      'Full Story Bible & Analytics',
      'Series Management (3 series)',
      'All 6 AI Agents',
      'Team Collaboration (2 members)',
      '25GB storage',
      '50% GPT-4o Mini, 50% GPT-4.1'
    ],
    limits: {
      projects: 10,
      aiGenerations: 300,
      exportFormats: ['txt', 'markdown', 'docx', 'pdf'],
      storageGB: 25,
      collaborators: 2
    },
    stripePriceId: STRIPE_PRICES.pro,
    aiModels: {
      primary: 'gpt-4.1-2025-04-14',
      fast: 'gpt-4o-mini',
      modelRatio: { mini: 50, gpt4: 50 }
    }
  },
  {
    id: 'professional',
    name: 'Professional',
    description: 'For professional authors and small teams',
    price: 59,
    currency: 'usd',
    interval: 'month',
    features: [
      'Unlimited Projects',
      '600 AI Generations per month',
      'All export formats (PDF, DOCX, TXT, EPUB)',
      'Priority chat support',
      'Full Story Bible & Analytics',
      'Unlimited Series Management',
      'Character Sharing',
      'Universe Creation',
      'Team Collaboration (5 members)',
      '100GB storage',
      '30% GPT-4o Mini, 70% GPT-4.1'
    ],
    limits: {
      projects: -1,
      aiGenerations: 600,
      exportFormats: ['txt', 'markdown', 'docx', 'pdf', 'epub'],
      storageGB: 100,
      collaborators: 5
    },
    stripePriceId: STRIPE_PRICES.pro_plus || STRIPE_PRICES.pro,
    aiModels: {
      primary: 'gpt-4.1-2025-04-14',
      fast: 'gpt-4o-mini',
      modelRatio: { mini: 30, gpt4: 70 }
    }
  },
  {
    id: 'studio',
    name: 'Studio',
    description: 'For publishing houses and writing teams',
    price: 79,
    currency: 'usd',
    interval: 'month',
    features: [
      'Unlimited Projects',
      '1,200 AI Generations per month',
      'All exports + custom formats',
      'White-glove support',
      'Shared Universe Creation',
      'Cross-Series Characters',
      'Advanced Analytics',
      'Unlimited Collaborators',
      'Real-Time Collaboration',
      'Early Access Features',
      '500GB storage',
      '20% GPT-4o Mini, 80% GPT-4.1'
    ],
    limits: {
      projects: -1, // unlimited
      aiGenerations: 1200,
      exportFormats: ['txt', 'markdown', 'docx', 'pdf', 'epub', 'custom'],
      storageGB: 500,
      collaborators: -1 // unlimited
    },
    stripePriceId: STRIPE_PRICES.enterprise,
    aiModels: {
      primary: 'gpt-4.1-2025-04-14',
      fast: 'gpt-4o-mini',
      modelRatio: { mini: 20, gpt4: 80 }
    }
  }
]

export interface UserSubscription {
  id: string
  userId: string
  tierId: string
  status: 'active' | 'canceled' | 'past_due' | 'incomplete'
  stripeSubscriptionId?: string
  stripeCustomerId?: string
  currentPeriodStart: Date
  currentPeriodEnd: Date
  cancelAtPeriodEnd: boolean
  usage: {
    aiGenerations: number
    projects: number
    storage: number
  }
  createdAt: Date
  updatedAt: Date
}

export function getTierById(tierId: string): SubscriptionTier | undefined {
  return SUBSCRIPTION_TIERS.find(tier => tier.id === tierId)
}

export function getUserTier(subscription: UserSubscription | null): SubscriptionTier {
  if (!subscription || subscription.status !== 'active') {
    const freeTier = SUBSCRIPTION_TIERS[0]
    if (!freeTier) {
      throw new Error('Free tier not found in subscription tiers')
    }
    return freeTier
  }
  const tier = getTierById(subscription.tierId) || SUBSCRIPTION_TIERS[0]
  if (!tier) {
    throw new Error('No valid subscription tier found')
  }
  return tier
}

export function checkUsageLimit(
  subscription: UserSubscription | null,
  limitType: keyof SubscriptionTier['limits'],
  currentUsage: number
): { allowed: boolean; limit: number; remaining: number } {
  const tier = getUserTier(subscription)
  const limit = tier.limits[limitType]
  
  if (typeof limit === 'number') {
    if (limit === -1) {
      // Unlimited
      return { allowed: true, limit: -1, remaining: -1 }
    }
    
    const remaining = Math.max(0, limit - currentUsage)
    return {
      allowed: currentUsage < limit,
      limit,
      remaining
    }
  }
  
  // For array limits (like exportFormats), check if the feature is included
  if (Array.isArray(limit)) {
    return {
      allowed: true,
      limit: limit.length,
      remaining: limit.length - currentUsage
    }
  }
  
  return { allowed: false, limit: 0, remaining: 0 }
}

export function canUseFeature(
  subscription: UserSubscription | null,
  feature: string
): boolean {
  const tier = getUserTier(subscription)
  
  // Map features to tier checks
  switch (feature) {
    case 'story_bible':
      return ['writer', 'author', 'professional', 'studio'].includes(tier.id)
    case 'character_profiles':
      return ['writer', 'author', 'professional', 'studio'].includes(tier.id)
    case 'ai_agents_partial': // 5 of 6 agents
      return tier.id === 'writer'
    case 'ai_agents_all': // All 6 agents
      return ['author', 'professional', 'studio'].includes(tier.id)
    case 'voice_consistency':
      return ['writer', 'author', 'professional', 'studio'].includes(tier.id)
    case 'advanced_analytics':
      return ['author', 'professional', 'studio'].includes(tier.id)
    case 'series_management':
      return ['author', 'professional', 'studio'].includes(tier.id)
    case 'character_sharing':
      return ['professional', 'studio'].includes(tier.id)
    case 'collaboration':
      return ['author', 'professional', 'studio'].includes(tier.id)
    case 'shared_universe':
      return ['professional', 'studio'].includes(tier.id)
    case 'cross_series_characters':
      return tier.id === 'studio'
    case 'real_time_collaboration':
      return tier.id === 'studio'
    case 'epub_export':
      return ['professional', 'studio'].includes(tier.id)
    case 'pdf_export':
      return ['author', 'professional', 'studio'].includes(tier.id)
    default:
      return true
  }
}

export function formatPrice(price: number, currency: string = 'usd'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase()
  }).format(price)
}

// Helper function to check if EPUB/PDF export is allowed for a tier
export function canExportPremiumFormats(tierId: string): boolean {
  return tierId === 'professional' || tierId === 'studio'
}

// Helper function to get AI model based on tier and task type
export function getAIModelForTier(
  tierId: string,
  taskType: 'primary' | 'fast' = 'primary'
): string {
  const tier = getTierById(tierId)
  if (!tier) return 'gpt-4o-mini' // default fallback
  
  return taskType === 'fast' ? tier.aiModels.fast : tier.aiModels.primary
}

// Helper function to check if real-time collaboration is available
export function hasRealTimeCollaboration(tierId: string): boolean {
  return tierId === 'studio'
}

// Helper function to determine which model to use based on usage ratio
export function selectModelByRatio(tierId: string): string {
  const tier = getTierById(tierId)
  if (!tier || !tier.aiModels.modelRatio) return tier?.aiModels.primary || 'gpt-4o-mini'
  
  // Random selection based on ratio
  const random = Math.random() * 100
  return random < tier.aiModels.modelRatio.mini ? 'gpt-4o-mini' : 'gpt-4.1-2025-04-14'
}

// Helper function to get available export formats for a tier
export function getAvailableExportFormats(tierId: string): string[] {
  const tier = getTierById(tierId)
  if (!tier) return ['txt', 'markdown'] // Default minimum formats
  
  return tier.limits.exportFormats
}

// Helper function to check if a specific export format is allowed
export function canExportFormat(tierId: string, format: string): boolean {
  const availableFormats = getAvailableExportFormats(tierId)
  return availableFormats.includes(format)
}