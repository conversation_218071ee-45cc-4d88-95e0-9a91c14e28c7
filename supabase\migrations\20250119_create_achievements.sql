-- Create achievements table for tracking user achievements
CREATE TABLE IF NOT EXISTS public.user_achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id TEXT NOT NULL,
  
  -- Achievement details
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('writing', 'consistency', 'quality', 'milestones', 'collaboration', 'special')),
  tier TEXT NOT NULL CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')),
  icon TEXT NOT NULL,
  
  -- Progress tracking
  current_value INTEGER DEFAULT 0,
  target_value INTEGER NOT NULL,
  unlocked_at TIMESTAMP WITH TIME ZONE,
  
  -- Metadata
  metadata JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique achievements per user
  UNIQUE(user_id, achievement_id)
);

-- Create achievement_definitions table for all possible achievements
CREATE TABLE IF NOT EXISTS public.achievement_definitions (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('writing', 'consistency', 'quality', 'milestones', 'collaboration', 'special')),
  tier TEXT NOT NULL CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')),
  icon TEXT NOT NULL,
  target_value INTEGER NOT NULL,
  
  -- Conditions for unlocking
  condition_type TEXT NOT NULL CHECK (condition_type IN ('word_count', 'streak', 'quality_score', 'chapters', 'projects', 'sessions', 'custom')),
  condition_params JSONB DEFAULT '{}',
  
  -- Display order and status
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  is_secret BOOLEAN DEFAULT false,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default achievement definitions
INSERT INTO public.achievement_definitions (id, title, description, category, tier, icon, target_value, condition_type, condition_params, display_order) VALUES
-- Writing achievements
('first_words', 'First Words', 'Write your first 100 words', 'writing', 'bronze', '✏️', 100, 'word_count', '{"total": true}', 1),
('prolific_writer', 'Prolific Writer', 'Write 10,000 words', 'writing', 'silver', '📝', 10000, 'word_count', '{"total": true}', 2),
('novelist', 'Novelist', 'Write 50,000 words', 'writing', 'gold', '📚', 50000, 'word_count', '{"total": true}', 3),
('epic_author', 'Epic Author', 'Write 100,000 words', 'writing', 'platinum', '🏆', 100000, 'word_count', '{"total": true}', 4),

-- Consistency achievements
('getting_started', 'Getting Started', 'Write for 3 consecutive days', 'consistency', 'bronze', '🔥', 3, 'streak', '{}', 10),
('habit_builder', 'Habit Builder', 'Write for 7 consecutive days', 'consistency', 'silver', '💪', 7, 'streak', '{}', 11),
('dedicated_writer', 'Dedicated Writer', 'Write for 30 consecutive days', 'consistency', 'gold', '⭐', 30, 'streak', '{}', 12),
('writing_master', 'Writing Master', 'Write for 100 consecutive days', 'consistency', 'platinum', '👑', 100, 'streak', '{}', 13),

-- Quality achievements
('quality_conscious', 'Quality Conscious', 'Achieve 80% quality score on a chapter', 'quality', 'bronze', '✨', 80, 'quality_score', '{"minimum": 80}', 20),
('excellence_seeker', 'Excellence Seeker', 'Achieve 90% quality score on a chapter', 'quality', 'silver', '🌟', 90, 'quality_score', '{"minimum": 90}', 21),
('perfectionist', 'Perfectionist', 'Achieve 95% quality score on a chapter', 'quality', 'gold', '💎', 95, 'quality_score', '{"minimum": 95}', 22),
('quality_master', 'Quality Master', 'Maintain 85%+ quality across 10 chapters', 'quality', 'platinum', '🏅', 10, 'quality_score', '{"minimum": 85, "consecutive": 10}', 23),

-- Milestone achievements
('chapter_one', 'Chapter One', 'Complete your first chapter', 'milestones', 'bronze', '📖', 1, 'chapters', '{}', 30),
('five_chapters', 'Five Chapters', 'Complete 5 chapters', 'milestones', 'silver', '📑', 5, 'chapters', '{}', 31),
('halfway_there', 'Halfway There', 'Complete 50% of your book', 'milestones', 'gold', '📊', 50, 'custom', '{"type": "project_progress"}', 32),
('book_complete', 'Book Complete', 'Complete your first book', 'milestones', 'platinum', '🎉', 100, 'custom', '{"type": "project_complete"}', 33),

-- Special achievements
('night_owl', 'Night Owl', 'Write after midnight', 'special', 'bronze', '🦉', 1, 'custom', '{"type": "time_based", "hour": 0}', 40),
('early_bird', 'Early Bird', 'Write before 6 AM', 'special', 'bronze', '🌅', 1, 'custom', '{"type": "time_based", "hour": 6}', 41),
('marathon_session', 'Marathon Session', 'Write for 4 hours straight', 'special', 'silver', '⏱️', 240, 'custom', '{"type": "session_duration"}', 42),
('speed_demon', 'Speed Demon', 'Write 1000 words in an hour', 'special', 'gold', '⚡', 1000, 'custom', '{"type": "words_per_hour"}', 43);

-- Create indexes
CREATE INDEX idx_user_achievements_user_id ON public.user_achievements(user_id);
CREATE INDEX idx_user_achievements_achievement_id ON public.user_achievements(achievement_id);
CREATE INDEX idx_user_achievements_unlocked_at ON public.user_achievements(unlocked_at);
CREATE INDEX idx_user_achievements_category ON public.user_achievements(category);

-- Enable RLS
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.achievement_definitions ENABLE ROW LEVEL SECURITY;

-- RLS policies for user_achievements
CREATE POLICY "Users can view their own achievements" ON public.user_achievements
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can create user achievements" ON public.user_achievements
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "System can update user achievements" ON public.user_achievements
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS policies for achievement_definitions (public read)
CREATE POLICY "Anyone can view achievement definitions" ON public.achievement_definitions
  FOR SELECT USING (true);

-- Function to check and unlock achievements
CREATE OR REPLACE FUNCTION check_and_unlock_achievements(p_user_id UUID)
RETURNS TABLE(newly_unlocked TEXT[]) AS $$
DECLARE
  v_achievement RECORD;
  v_unlocked TEXT[] := '{}';
  v_current_value INTEGER;
  v_should_unlock BOOLEAN;
BEGIN
  -- Check each achievement definition
  FOR v_achievement IN 
    SELECT ad.* 
    FROM achievement_definitions ad
    WHERE ad.is_active = true
    AND NOT EXISTS (
      SELECT 1 FROM user_achievements ua 
      WHERE ua.user_id = p_user_id 
      AND ua.achievement_id = ad.id 
      AND ua.unlocked_at IS NOT NULL
    )
  LOOP
    v_should_unlock := false;
    v_current_value := 0;
    
    -- Check based on condition type
    CASE v_achievement.condition_type
      WHEN 'word_count' THEN
        SELECT COALESCE(SUM(word_count), 0) INTO v_current_value
        FROM writing_sessions
        WHERE user_id = p_user_id;
        v_should_unlock := v_current_value >= v_achievement.target_value;
        
      WHEN 'streak' THEN
        -- Calculate current streak
        WITH consecutive_days AS (
          SELECT DISTINCT created_at::date as write_date
          FROM writing_sessions
          WHERE user_id = p_user_id
          ORDER BY write_date DESC
        ),
        streak_calc AS (
          SELECT 
            write_date,
            write_date - ROW_NUMBER() OVER (ORDER BY write_date DESC)::INTEGER AS grp
          FROM consecutive_days
        )
        SELECT COUNT(*) INTO v_current_value
        FROM streak_calc
        WHERE grp = (SELECT grp FROM streak_calc LIMIT 1);
        
        v_should_unlock := v_current_value >= v_achievement.target_value;
        
      WHEN 'chapters' THEN
        SELECT COUNT(*) INTO v_current_value
        FROM chapters
        WHERE user_id = p_user_id
        AND status = 'completed';
        v_should_unlock := v_current_value >= v_achievement.target_value;
    END CASE;
    
    -- Insert or update achievement progress
    IF v_should_unlock OR v_current_value > 0 THEN
      INSERT INTO user_achievements (
        user_id, achievement_id, title, description, 
        category, tier, icon, current_value, target_value,
        unlocked_at
      ) VALUES (
        p_user_id, v_achievement.id, v_achievement.title, v_achievement.description,
        v_achievement.category, v_achievement.tier, v_achievement.icon, 
        v_current_value, v_achievement.target_value,
        CASE WHEN v_should_unlock THEN NOW() ELSE NULL END
      )
      ON CONFLICT (user_id, achievement_id) DO UPDATE
      SET 
        current_value = EXCLUDED.current_value,
        unlocked_at = CASE 
          WHEN user_achievements.unlocked_at IS NULL AND v_should_unlock 
          THEN NOW() 
          ELSE user_achievements.unlocked_at 
        END,
        updated_at = NOW();
      
      -- Track newly unlocked achievements
      IF v_should_unlock AND NOT EXISTS (
        SELECT 1 FROM user_achievements 
        WHERE user_id = p_user_id 
        AND achievement_id = v_achievement.id 
        AND unlocked_at IS NOT NULL
      ) THEN
        v_unlocked := array_append(v_unlocked, v_achievement.id);
      END IF;
    END IF;
  END LOOP;
  
  RETURN QUERY SELECT v_unlocked;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;