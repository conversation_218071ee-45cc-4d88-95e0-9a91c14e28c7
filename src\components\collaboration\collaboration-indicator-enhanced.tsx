'use client'

import { useState, useEffect } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { 
  Users, 
  Wifi, 
  WifiOff, 
  Circle,
  Crown,
  Sparkles,
  Copy,
  UserPlus,
  Edit3,
  Eye,
  Shield,
  Clock,
  Link2,
  CheckCircle2
} from 'lucide-react'
import type { CollaborationUser } from '@/lib/services/collaboration-service-realtime'

interface CollaborationIndicatorProps {
  sessionId: string
  isConnected: boolean
  collaborators: CollaborationUser[]
  currentUserId: string
  canInvite?: boolean
  onToggleCollaboration?: (enabled: boolean) => void
  onInviteUser?: () => void
  className?: string
}

export function CollaborationIndicatorEnhanced({
  sessionId,
  isConnected,
  collaborators,
  currentUserId,
  canInvite = false,
  onToggleCollaboration,
  onInviteUser,
  className
}: CollaborationIndicatorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [collaborationEnabled, setCollaborationEnabled] = useState(isConnected)
  const [showStatusAnimation, setShowStatusAnimation] = useState(false)
  const [copiedLink, setCopiedLink] = useState(false)
  const { toast } = useToast()
  
  useEffect(() => {
    setCollaborationEnabled(isConnected)
  }, [isConnected])
  
  // Show connection status animation
  useEffect(() => {
    if (isConnected) {
      setShowStatusAnimation(true)
      const timer = setTimeout(() => setShowStatusAnimation(false), 2000)
      return () => clearTimeout(timer)
    }
  }, [isConnected])
  
  const handleToggleCollaboration = (enabled: boolean) => {
    setCollaborationEnabled(enabled)
    onToggleCollaboration?.(enabled)
    
    toast({
      title: enabled ? "Collaboration Enabled" : "Collaboration Disabled",
      description: enabled 
        ? "Real-time collaboration is now active" 
        : "You're now working offline",
      duration: 3000
    })
  }
  
  const copySessionLink = async () => {
    const projectId = sessionId.split(':')[1]
    const link = `${window.location.origin}/projects/${projectId}/join`
    
    try {
      await navigator.clipboard.writeText(link)
      setCopiedLink(true)
      setTimeout(() => setCopiedLink(false), 2000)
      
      toast({
        title: "Link Copied!",
        description: "Share this link to invite collaborators",
        duration: 3000
      })
    } catch (error) {
      logger.error('Failed to copy link', error)
      toast({
        title: "Failed to copy",
        description: "Please try again",
        variant: "destructive"
      })
    }
  }
  
  const getInitials = (name: string, email: string) => {
    if (name && name.trim()) {
      return name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2)
    }
    return email.substring(0, 2).toUpperCase()
  }
  
  const getRoleIcon = (user: CollaborationUser) => {
    // You could extend CollaborationUser to include role
    // For now, we'll just show Edit icon for all
    return <Edit3 className="h-3 w-3" />
  }
  
  const activeCollaborators = collaborators.filter(c => c.id !== currentUserId)
  const displayedCollaborators = activeCollaborators.slice(0, 3)
  const remainingCount = activeCollaborators.length - 3
  
  return (
    <TooltipProvider>
      <div className={cn("flex items-center gap-2", className)}>
        {/* Connection Status Indicator */}
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "h-8 px-2 gap-1.5 transition-colors",
                  isConnected ? "text-green-600 dark:text-green-400" : "text-muted-foreground"
                )}
              >
                {isConnected ? (
                  <Wifi className="h-4 w-4" />
                ) : (
                  <WifiOff className="h-4 w-4" />
                )}
                <span className="text-xs font-medium">
                  {isConnected ? 'Live' : 'Offline'}
                </span>
              </Button>
              
              {/* Connection Animation */}
              <AnimatePresence>
                {showStatusAnimation && isConnected && (
                  <motion.div
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1.5, opacity: 0 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 1 }}
                    className="absolute inset-0 rounded-md bg-green-500/20 pointer-events-none"
                  />
                )}
              </AnimatePresence>
            </div>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p className="text-xs">
              {isConnected 
                ? 'Connected to real-time collaboration' 
                : 'Working offline - changes saved locally'}
            </p>
          </TooltipContent>
        </Tooltip>
        
        {/* Collaborator Avatars */}
        {activeCollaborators.length > 0 && (
          <div className="flex -space-x-2">
            <AnimatePresence mode="popLayout">
              {displayedCollaborators.map((collaborator, index) => (
                <Tooltip key={collaborator.id}>
                  <TooltipTrigger asChild>
                    <motion.div
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0, opacity: 0 }}
                      transition={{ delay: index * 0.05 }}
                    >
                      <Avatar 
                        className="h-8 w-8 border-2 border-background ring-2 ring-background hover:z-10 transition-transform hover:scale-110"
                        style={{ 
                          borderColor: collaborator.color,
                          boxShadow: `0 0 0 2px ${collaborator.color}20`
                        }}
                      >
                        <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${collaborator.email}`} />
                        <AvatarFallback 
                          style={{ backgroundColor: collaborator.color }}
                          className="text-[10px] text-white font-bold"
                        >
                          {getInitials(collaborator.name, collaborator.email)}
                        </AvatarFallback>
                      </Avatar>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="flex items-center gap-2">
                    <Circle 
                      className="h-2 w-2 fill-current" 
                      style={{ color: collaborator.color }}
                    />
                    <span className="text-xs font-medium">{collaborator.name}</span>
                    {getRoleIcon(collaborator)}
                  </TooltipContent>
                </Tooltip>
              ))}
            </AnimatePresence>
            
            {remainingCount > 0 && (
              <motion.div
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.15 }}
              >
                <Avatar className="h-8 w-8 border-2 border-background bg-muted">
                  <AvatarFallback className="text-[10px] font-bold">
                    +{remainingCount}
                  </AvatarFallback>
                </Avatar>
              </motion.div>
            )}
          </div>
        )}
        
        {/* Collaboration Menu Button */}
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button 
              variant="ghost" 
              size="sm"
              className="h-8 gap-1.5"
            >
              <Users className="h-4 w-4" />
              <span className="text-xs font-medium">
                {activeCollaborators.length + 1}
              </span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[360px]" align="end">
            <div className="space-y-4">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <h4 className="font-semibold">Real-Time Collaboration</h4>
                  <Badge variant="outline" className="text-[10px] px-1.5 py-0">
                    <Crown className="h-3 w-3 mr-0.5" />
                    Pro
                  </Badge>
                </div>
                <Badge 
                  variant={isConnected ? "default" : "secondary"}
                  className="text-[10px]"
                >
                  {isConnected ? (
                    <>
                      <Circle className="h-2 w-2 mr-1 fill-current" />
                      Live
                    </>
                  ) : (
                    <>
                      <Circle className="h-2 w-2 mr-1" />
                      Offline
                    </>
                  )}
                </Badge>
              </div>
              
              {/* Toggle Switch */}
              <div className="flex items-center justify-between py-2">
                <div className="space-y-0.5">
                  <Label htmlFor="collab-toggle" className="text-sm font-medium">
                    Enable Collaboration
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Allow team members to edit in real-time
                  </p>
                </div>
                <Switch
                  id="collab-toggle"
                  checked={collaborationEnabled}
                  onCheckedChange={handleToggleCollaboration}
                />
              </div>
              
              <Separator />
              
              {/* Active Collaborators */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h5 className="text-sm font-medium">Active Collaborators</h5>
                  <span className="text-xs text-muted-foreground">
                    {collaborators.length} online
                  </span>
                </div>
                
                <div className="space-y-2 max-h-[200px] overflow-y-auto">
                  {collaborators.map((collaborator) => (
                    <motion.div 
                      key={collaborator.id}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      className={cn(
                        "flex items-center gap-3 p-2 rounded-lg transition-colors",
                        collaborator.id === currentUserId 
                          ? "bg-primary/5" 
                          : "hover:bg-muted/50"
                      )}
                    >
                      <Avatar className="h-9 w-9">
                        <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${collaborator.email}`} />
                        <AvatarFallback 
                          style={{ backgroundColor: collaborator.color }}
                          className="text-xs text-white font-medium"
                        >
                          {getInitials(collaborator.name, collaborator.email)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {collaborator.name}
                          {collaborator.id === currentUserId && (
                            <span className="text-muted-foreground ml-1">(You)</span>
                          )}
                        </p>
                        <p className="text-xs text-muted-foreground truncate">
                          {collaborator.email}
                        </p>
                      </div>
                      <div className="flex items-center gap-1">
                        {collaborator.cursor && (
                          <Tooltip>
                            <TooltipTrigger>
                              <div className="text-xs text-muted-foreground font-mono">
                                L{collaborator.cursor.line}:C{collaborator.cursor.column}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="text-xs">Cursor position</p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                        <Circle 
                          className="h-2 w-2 fill-current" 
                          style={{ color: collaborator.color }}
                        />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
              
              <Separator />
              
              {/* Action Buttons */}
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={copySessionLink}
                >
                  {copiedLink ? (
                    <>
                      <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                      Link Copied!
                    </>
                  ) : (
                    <>
                      <Link2 className="h-4 w-4 mr-2" />
                      Copy Session Link
                    </>
                  )}
                </Button>
                
                {canInvite && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={onInviteUser}
                  >
                    <UserPlus className="h-4 w-4 mr-2" />
                    Invite Team Member
                  </Button>
                )}
              </div>
              
              {/* Pro Feature Notice */}
              <div className="bg-primary/5 rounded-lg p-3">
                <div className="flex gap-2">
                  <Sparkles className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <div className="space-y-1">
                    <p className="text-xs font-medium">Professional Feature</p>
                    <p className="text-xs text-muted-foreground">
                      Real-time collaboration allows multiple writers to work together 
                      with live cursors, instant updates, and conflict-free editing.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </TooltipProvider>
  )
}