import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, ExternalLink } from 'lucide-react'
import Link from 'next/link'

interface ServiceErrorProps {
  service: 'openai' | 'supabase' | 'stripe'
  error?: string
}

export function ServiceError({ service, error }: ServiceErrorProps) {
  const serviceInfo = {
    openai: {
      title: 'OpenAI API Key Required',
      description: 'To use AI features, you need to configure your OpenAI API key.',
      setupUrl: 'https://platform.openai.com/api-keys',
      envVar: 'OPENAI_API_KEY',
      steps: [
        'Go to OpenAI Platform',
        'Create a new API key',
        'Copy the key',
        'Update OPENAI_API_KEY in your .env.local file',
        'Restart the development server'
      ]
    },
    supabase: {
      title: 'Supabase Configuration Required',
      description: 'Database operations require proper Supabase configuration.',
      setupUrl: 'https://supabase.com/dashboard',
      envVar: 'SUPABASE_SERVICE_ROLE_KEY',
      steps: [
        'Go to your Supabase project',
        'Navigate to Settings → API',
        'Copy the service_role key',
        'Update SUPABASE_SERVICE_ROLE_KEY in .env.local',
        'Restart the development server'
      ]
    },
    stripe: {
      title: 'Stripe Configuration Required',
      description: 'Payment features require Stripe to be properly configured.',
      setupUrl: 'https://dashboard.stripe.com',
      envVar: 'STRIPE_WEBHOOK_SECRET',
      steps: [
        'Go to Stripe Dashboard',
        'Set up webhook endpoint',
        'Copy the webhook secret',
        'Update Stripe environment variables',
        'Restart the development server'
      ]
    }
  }

  const info = serviceInfo[service]

  return (
    <Card className="border-destructive/50 bg-destructive/5">
      <CardHeader>
        <div className="flex items-start gap-2">
          <AlertCircle className="h-5 w-5 text-destructive mt-0.5" />
          <div className="flex-1">
            <CardTitle className="text-lg">{info.title}</CardTitle>
            <CardDescription>{info.description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertTitle>Error Details</AlertTitle>
            <AlertDescription className="font-mono text-sm">
              {error}
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <h4 className="font-medium text-sm">Setup Instructions:</h4>
          <ol className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
            {info.steps.map((step, index) => (
              <li key={index}>{step}</li>
            ))}
          </ol>
        </div>

        <div className="flex items-center gap-2 pt-2">
          <Link href={info.setupUrl} target="_blank" rel="noopener noreferrer">
            <Button variant="outline" size="sm">
              <ExternalLink className="h-4 w-4 mr-2" />
              Open {service === 'openai' ? 'OpenAI' : service === 'supabase' ? 'Supabase' : 'Stripe'} Dashboard
            </Button>
          </Link>
          <code className="text-xs bg-muted px-2 py-1 rounded">
            {info.envVar}
          </code>
        </div>
      </CardContent>
    </Card>
  )
}