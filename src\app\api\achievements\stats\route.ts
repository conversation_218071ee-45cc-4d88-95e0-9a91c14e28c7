import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 })
    }
    
    const supabase = await createClient()
    
    // Get total achievements
    const { data: allAchievements, error: achievementsError } = await supabase
      .from('achievement_definitions')
      .select('id')
    
    if (achievementsError) throw achievementsError
    
    // Get user's unlocked achievements
    const { data: unlockedAchievements, error: unlockedError } = await supabase
      .from('user_achievements')
      .select('achievement_id, unlocked_at, achievement_definitions!inner(name)')
      .eq('user_id', userId)
      .eq('unlocked', true)
      .order('unlocked_at', { ascending: false })
    
    if (unlockedError) throw unlockedError
    
    // Get next milestone achievement
    const { data: progressAchievements, error: progressError } = await supabase
      .from('user_achievements')
      .select(`
        achievement_id,
        progress,
        achievement_definitions!inner(
          name,
          requirement,
          category
        )
      `)
      .eq('user_id', userId)
      .eq('unlocked', false)
      .gt('progress', 0)
      .order('progress', { ascending: false })
      .limit(1)
    
    if (progressError) throw progressError
    
    const stats = {
      totalUnlocked: unlockedAchievements?.length || 0,
      totalAchievements: allAchievements?.length || 0,
      recentAchievement: unlockedAchievements?.[0] ? {
        name: unlockedAchievements[0].achievement_definitions.name,
        unlockedAt: unlockedAchievements[0].unlocked_at
      } : undefined,
      nextMilestone: progressAchievements?.[0] ? {
        name: progressAchievements[0].achievement_definitions.name,
        progress: progressAchievements[0].progress || 0,
        requirement: progressAchievements[0].achievement_definitions.requirement || 1
      } : undefined
    }
    
    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching achievement stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch achievement stats' },
      { status: 500 }
    )
  }
}