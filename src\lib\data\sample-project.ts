import { logger } from '@/lib/services/logger';
import { SupabaseClient } from '@supabase/supabase-js';

// Sample project data for new users
export const SAMPLE_PROJECT = {
  title: "The Last Lighthouse Keeper",
  description: "A haunting tale of isolation and redemption set in a remote lighthouse on the edge of the world.",
  genre: "Literary Fiction",
  subgenre: "Psychological Drama",
  targetWordCount: 80000,
  
  outline: {
    premise: "An aging lighthouse keeper, haunted by a tragic past, must confront his demons when a mysterious woman washes ashore during a violent storm.",
    themes: ["Isolation", "Redemption", "The weight of the past", "Human connection"],
    setting: "Remote lighthouse on the Scottish coast, present day",
    
    acts: [
      {
        number: 1,
        title: "The Storm Arrives",
        description: "Introduction to <PERSON>, the lighthouse keeper, and the arrival of the mysterious woman",
        chapters: [
          {
            number: 1,
            title: "The Light That Never Fails",
            summary: "<PERSON> maintains his solitary routine at the lighthouse, haunted by memories",
            scenes: [
              "Morning routine and lighthouse maintenance",
              "Flashback to the accident that drove him to isolation",
              "Storm brewing on the horizon"
            ]
          },
          {
            number: 2,
            title: "From the Depths",
            summary: "During a violent storm, <PERSON> discovers an unconscious woman on the rocks",
            scenes: [
              "The storm intensifies",
              "Discovery of the woman",
              "Bringing her to safety"
            ]
          }
        ]
      }
    ]
  },
  
  characters: [
    {
      name: "<PERSON> <PERSON>",
      role: "Protagonist",
      age: 62,
      description: "Weathered and solitary, with deep-set gray eyes that have seen too much",
      personality: "Stoic, haunted, methodical, secretly yearning for redemption",
      backstory: "Former ship captain who caused an accident that cost lives. Retreated to lighthouse keeping as penance.",
      arc: "From isolation and guilt to acceptance and human connection",
      voice: "Measured, poetic when discussing the sea, terse in conversation"
    },
    {
      name: "Elena Vasquez",
      role: "Catalyst",
      age: 35,
      description: "Dark-haired with an otherworldly quality, seems both fragile and resilient",
      personality: "Mysterious, intuitive, carrying her own secrets, surprisingly strong",
      backstory: "Running from something, but what? Her past unfolds slowly",
      arc: "From mystery to revelation, teaching Thomas about forgiveness",
      voice: "Lyrical, often speaks in metaphors, accent hints at far-away origins"
    }
  ],
  
  sampleChapter: {
    number: 1,
    title: "The Light That Never Fails",
    content: `The beam swept across the darkening waters with mechanical precision, as it had every night for the past fifteen years. Thomas MacBride watched its progress from the lamp room, his weathered hands steady on the brass railing despite the tremor that had recently begun to plague them in the mornings.

One hundred and fifty-seven steps from his quarters to the lamp room. He'd counted them every day, twice a day, for five thousand four hundred and seventy-nine days. The number should have been higher, but there had been those three days when the fever kept him in bed, when young Jamie from the village had climbed those steps in his stead.

The horizon was already blurring, sea and sky melding into a gray wash that promised foul weather. Thomas had felt it in his bones since dawn—that peculiar ache that had never failed him, not once in forty years at sea and another fifteen perched on this godforsaken rock.

He descended the spiral staircase, each footfall echoing in the tower's hollow core. In the kitchen, he put the kettle on and opened his logbook to the day's entry. The words came sparse and factual:

*October 15th. Barometer falling. Wind NNE, strengthening. Fog bank approaching from west. All systems operational.*

His pen hovered over the page. There was more he could write—about the strange dream that had woken him at three, about the photograph he'd found tucked behind the pantry shelf, about the way the gulls had gone silent just after noon. But the log was for facts, not fancies.

The kettle's whistle pierced the air, sharp as a bosun's call. As Thomas reached for it, his hand trembled, and for just a moment, he was back on the bridge of the Cassandra, rain lashing the windows, the rocks looming out of the darkness like teeth...

He gripped the counter, forcing the memory down. Fifteen years. It should have been enough time. It would never be enough time.

Outside, the first drops of rain began to spatter against the windows. Thomas made his tea and climbed back to the lamp room. Someone had to keep the light burning. Someone had to watch over the dark waters. Someone had to remember.

The storm was coming.`,
    wordCount: 389,
    quality: {
      overall: 88,
      pacing: 85,
      characterization: 90,
      atmosphere: 92,
      prose: 87
    }
  },
  
  metadata: {
    createdAt: new Date().toISOString(),
    lastModified: new Date().toISOString(),
    status: 'in_progress',
    completionPercentage: 5,
    tags: ['literary', 'psychological', 'redemption', 'isolation'],
    notes: "This is a sample project to help you explore BookScribe's features. Feel free to modify or delete it."
  }
};

// Function to create a sample project for a new user
export async function createSampleProject(userId: string, supabase: SupabaseClient) {
  try {
    // Create the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        user_id: userId,
        title: SAMPLE_PROJECT.title,
        description: SAMPLE_PROJECT.description,
        primary_genre: SAMPLE_PROJECT.genre,
        secondary_genre: SAMPLE_PROJECT.subgenre,
        target_word_count: SAMPLE_PROJECT.targetWordCount,
        current_word_count: SAMPLE_PROJECT.sampleChapter.wordCount,
        status: 'active',
        metadata: {
          isSampleProject: true,
          ...SAMPLE_PROJECT.metadata
        }
      })
      .select()
      .single();

    if (projectError) throw projectError;

    // Create characters
    for (const character of SAMPLE_PROJECT.characters) {
      await supabase
        .from('characters')
        .insert({
          project_id: project.id,
          name: character.name,
          role: character.role,
          description: character.description,
          personality: character.personality,
          backstory: character.backstory,
          character_arc: character.arc,
          relationships: {},
          traits: {
            age: character.age,
            voice: character.voice
          }
        });
    }

    // Create the sample chapter
    const { data: chapter, error: chapterError } = await supabase
      .from('chapters')
      .insert({
        project_id: project.id,
        chapter_number: SAMPLE_PROJECT.sampleChapter.number,
        title: SAMPLE_PROJECT.sampleChapter.title,
        content: SAMPLE_PROJECT.sampleChapter.content,
        actual_word_count: SAMPLE_PROJECT.sampleChapter.wordCount,
        status: 'draft',
        summary: SAMPLE_PROJECT.outline.acts[0].chapters[0].summary
      })
      .select()
      .single();

    if (chapterError) throw chapterError;

    // Create quality metrics for the sample chapter
    await supabase
      .from('quality_metrics')
      .insert({
        chapter_id: chapter.id,
        project_id: project.id,
        user_id: userId,
        overall_score: SAMPLE_PROJECT.sampleChapter.quality.overall,
        pacing: SAMPLE_PROJECT.sampleChapter.quality.pacing,
        character_consistency: SAMPLE_PROJECT.sampleChapter.quality.characterization,
        emotional_impact: SAMPLE_PROJECT.sampleChapter.quality.atmosphere,
        style: SAMPLE_PROJECT.sampleChapter.quality.prose,
        coherence: 88,
        grammar: 95,
        creativity: 86,
        plot_consistency: 89,
        readability: 91,
        show_dont_tell_ratio: 87,
        sensory_engagement: 90,
        dialogue_authenticity: 85,
        hook_strength: 92,
        pageturner_quality: 86,
        literary_merit: 89,
        market_potential: 84,
        memorability: 88,
        strengths: [
          "Exceptional atmospheric writing that immediately sets the mood",
          "Strong character voice established from the first paragraph",
          "Effective use of specific details (counting steps, dates)",
          "Haunting backstory revealed through subtle hints"
        ],
        weaknesses: [
          "Pacing could be slightly faster in the middle section",
          "Some readers might find the introspection heavy"
        ],
        suggestions: [
          "Consider adding more sensory details about the storm",
          "The flashback could be slightly more visceral"
        ]
      });

    // Create a sample writing session
    await supabase
      .from('writing_sessions')
      .insert({
        user_id: userId,
        project_id: project.id,
        chapter_id: chapter.id,
        word_count: SAMPLE_PROJECT.sampleChapter.wordCount,
        duration: 1800, // 30 minutes
        started_at: new Date(Date.now() - 1800000).toISOString(),
        ended_at: new Date().toISOString()
      });

    return project;
  } catch (error) {
    logger.error('Failed to create sample project:', error);
    return null;
  }
}