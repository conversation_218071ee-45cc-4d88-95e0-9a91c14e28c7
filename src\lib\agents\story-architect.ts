import { BaseAgent } from './base-agent'
import { logger } from '@/lib/services/logger';

import { BookContext } from './types'
import { 
  StoryArchitectInput, 
  StoryStructure, 
  AgentResponse 
} from '../types/agents'
import { getAIConfig } from '../config/ai-settings'
import { storyStructureSchema } from '../schemas/agent-schemas'

export class StoryArchitectAgent extends BaseAgent {
  constructor(context: BookContext) {
    super(context);
  }

  private getConfig() {
    return getAIConfig('STORY_STRUCTURE');
  }

  async generateStoryStructure(input: StoryArchitectInput): Promise<AgentResponse<StoryStructure>> {
    const startTime = Date.now()
    
    try {
      const systemPrompt = this.buildSystemPrompt(input)
      const userPrompt = this.buildUserPrompt(input)
      const config = this.getConfig();

      // Use structured completion from base class
      const result = await this.createStructuredCompletion(
        [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        storyStructureSchema,
        'story_structure',
        config.model,
        config.temperature,
        config.max_tokens
      );

      if (!result.success) {
        // If refusal occurred, include it in the error message
        const errorMessage = result.refusal 
          ? `Model refused to generate content: ${result.refusal}`
          : result.error || 'Failed to generate story structure';
        
        return {
          success: false,
          error: errorMessage,
          executionTime: Date.now() - startTime
        };
      }

      return {
        success: true,
        data: result.data!,
        executionTime: Date.now() - startTime,
        // Note: token usage would need to be tracked separately
        // as structured outputs don't return usage in the same way
      }
    } catch (error) {
      logger.error('Story structure generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime
      }
    }
  }

  private buildSystemPrompt(input: StoryArchitectInput): string {
    return `You are a master Story Architect AI agent specializing in creating NYT bestseller-quality story structures. Your expertise rivals that of James Patterson's plotting, Stephen King's emotional depth, and Gillian Flynn's psychological complexity. Your task is to craft a story structure that could compete with award-winning novels.

LITERARY EXCELLENCE STANDARDS:
- Create a structure worthy of major literary awards and bestseller lists
- Design plot twists that rival "Gone Girl" or "The Sixth Sense"
- Build emotional arcs that resonate like "The Kite Runner" or "Me Before You"
- Craft themes with the depth of literary fiction and the accessibility of commercial fiction
- Ensure every act contains multiple "unputdownable" moments

GENRE MASTERY for ${input.projectSelections.primaryGenre}:
- Study and exceed the best examples in this genre
- Subvert expectations while honoring reader satisfaction
- Layer in cross-genre appeal for broader market reach
- Include elements that book clubs would discuss for months

BESTSELLER STRUCTURE REQUIREMENTS:
- Opening: Hook readers within the first 3 pages like "The Girl on the Train"
- Rising Action: Build tension using techniques from thriller masters
- Midpoint Reversal: Create a shocking turn that reframes everything
- Climax: Design a satisfying yet surprising resolution
- Denouement: Leave readers emotionally satisfied yet wanting more

PACING & ENGAGEMENT (${input.projectSelections.pacingPreference}):
- Every chapter must end with a reason to read "just one more"
- Balance fast-paced scenes with moments of emotional depth
- Use the "Scene-Sequel" pattern for maximum reader engagement
- Ensure no more than 2 pages without conflict or tension

EMOTIONAL RESONANCE:
- Create themes that tap into universal human experiences
- Design character arcs that mirror readers' deepest desires and fears
- Build in moments that will make readers cry, laugh, and gasp
- Craft revelations that readers will discuss with friends

TARGET: ${input.targetWordCount} words across ${input.targetChapters} chapters
AUDIENCE: ${input.projectSelections.targetAudience} (but with crossover appeal)

Remember: You're not just creating a story structure—you're architecting a potential bestseller that could define careers and touch millions of readers' lives.`
  }

  private buildUserPrompt(input: StoryArchitectInput): string {
    return `Please create a comprehensive story structure for this novel project:

STORY CONCEPT:
${input.storyPrompt}

PROJECT DETAILS:
- Genre: ${input.projectSelections.primaryGenre}${input.projectSelections.subgenre ? ` (${input.projectSelections.subgenre})` : ''}
- Target Word Count: ${input.targetWordCount.toLocaleString()} words
- Target Chapters: ${input.targetChapters}
- Structure Type: ${input.projectSelections.structureType}
- Narrative Voice: ${input.projectSelections.narrativeVoice}
- Tense: ${input.projectSelections.tense}
- Tone: ${input.projectSelections.toneOptions?.join(', ') || 'Not specified'}
- Target Audience: ${input.projectSelections.targetAudience}
- Pacing: ${input.projectSelections.pacingPreference}

ADDITIONAL CONTEXT:
${input.projectSelections.customGenre ? `Custom Genre Elements: ${input.projectSelections.customGenre}` : ''}
${input.projectSelections.customStyleDescription ? `Style Notes: ${input.projectSelections.customStyleDescription}` : ''}
${input.projectSelections.customStructureNotes ? `Structure Notes: ${input.projectSelections.customStructureNotes}` : ''}

Generate a story structure that transforms this concept into a compelling, well-structured novel that meets all the specified requirements.`
  }
}