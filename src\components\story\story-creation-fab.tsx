"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Plus, Wand2 } from "lucide-react";
import { UnifiedProjectWizard } from "@/components/wizard/unified-project-wizard";
import { motion } from "framer-motion";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export function StoryCreationFAB() {
  const [open, setOpen] = useState(false);

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <motion.div
              className="fixed bottom-6 right-6 z-50"
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.5, type: "spring", stiffness: 200 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                size="lg"
                className="h-14 w-14 rounded-full shadow-lg bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
                onClick={() => setOpen(true)}
              >
                <Plus className="h-6 w-6" />
                <span className="sr-only">Create new story</span>
              </Button>
            </motion.div>
          </TooltipTrigger>
          <TooltipContent side="left" className="flex items-center gap-2">
            <Wand2 className="h-4 w-4" />
            Create New Story
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <UnifiedProjectWizard 
        mode="live"
        display="modal" 
        open={open} 
        onOpenChange={setOpen} 
      />
    </>
  );
}