import { getUserTier, getAIModelForTask } from '@/lib/subscription'
import { AI_MODELS } from '@/lib/config/ai-settings'
import type { UserSubscription } from '@/lib/subscription'
import { logger } from './logger'

export interface AIModelSelection {
  model: string
  isRestricted: boolean
  tierName: string
  reason?: string
}

// Define task types for model selection
export const TASK_TYPES = {
  // Complex tasks that benefit from GPT-4.1
  STORY_STRUCTURE: 'story-structure',
  CHARACTER_DEVELOPMENT: 'character-development',
  CHAPTER_WRITING: 'chapter-writing',
  CREATIVE_WRITING: 'creative-writing',
  DIALOGUE_GENERATION: 'dialogue-generation',
  
  // Medium complexity tasks for GPT-4.1-mini
  CHAPTER_PLANNING: 'chapter-planning',
  EDITING: 'editing',
  CONTENT_GENERATION: 'content-generation',
  ANALYSIS: 'analysis',
  
  // Simple tasks that should use GPT-4o-mini
  ADAPTIVE_PLANNING: 'adaptive-planning',
  SUGGESTIONS: 'suggestions',
  QUICK_EDIT: 'quick-edit',
  SIMPLE_CONTINUATION: 'simple-continuation',
  FORMATTING: 'formatting',
  METADATA: 'metadata',
  SUMMARY: 'summary',
  
  // Embedding tasks for semantic operations
  SEMANTIC_SEARCH: 'semantic-search',
  CONTENT_SIMILARITY: 'content-similarity',
  RECOMMENDATIONS: 'recommendations',
  RELATIONSHIP_ANALYSIS: 'relationship-analysis',
  THEME_EXTRACTION: 'theme-extraction'
} as const

export type TaskType = typeof TASK_TYPES[keyof typeof TASK_TYPES]

/**
 * Selects the appropriate AI model based on user subscription and task type
 */
export class AIModelSelector {
  private static instance: AIModelSelector
  
  private constructor() {}
  
  static getInstance(): AIModelSelector {
    if (!AIModelSelector.instance) {
      AIModelSelector.instance = new AIModelSelector()
    }
    return AIModelSelector.instance
  }

  /**
   * Select AI model based on subscription tier and task type
   */
  selectModel(
    subscription: UserSubscription | null,
    requestedModel: string,
    taskType: TaskType | string
  ): AIModelSelection {
    const tier = getUserTier(subscription)
    
    // Check if this is an embedding task
    if (this.isEmbeddingTask(taskType)) {
      return this.selectEmbeddingModel(tier, taskType)
    }
    
    // Get the appropriate model for this tier and task
    const selectedModel = getAIModelForTask(tier.id, taskType)
    
    // Check if user requested a specific model but we're using a different one
    const isRestricted = requestedModel !== selectedModel && 
                        this.isPremiumModel(requestedModel) && 
                        (selectedModel === AI_MODELS.FAST || selectedModel === AI_MODELS.MEDIUM)
    
    if (isRestricted) {
      logger.info('Model restricted by tier or task type', {
        requested: requestedModel,
        selected: selectedModel,
        tier: tier.id,
        taskType
      })
      
      return {
        model: selectedModel,
        isRestricted: true,
        tierName: tier.name,
        reason: `${tier.name} tier uses ${this.getModelDisplayName(selectedModel)} for ${this.getTaskDisplayName(taskType)}`
      }
    }
    
    return {
      model: selectedModel,
      isRestricted: false,
      tierName: tier.name
    }
  }

  /**
   * Determine task type from context clues
   */
  inferTaskType(
    prompt: string,
    systemPrompt?: string,
    agentType?: string
  ): TaskType {
    const combinedPrompt = `${systemPrompt || ''} ${prompt}`.toLowerCase()
    
    // Check agent type first
    if (agentType) {
      switch (agentType) {
        case 'StoryArchitect':
          return TASK_TYPES.STORY_STRUCTURE
        case 'CharacterDeveloper':
          return TASK_TYPES.CHARACTER_DEVELOPMENT
        case 'WritingAgent':
        case 'VoiceAwareWritingAgent':
          return TASK_TYPES.CHAPTER_WRITING
        case 'EditorAgent':
          return TASK_TYPES.EDITING
        case 'ChapterPlanner':
          return TASK_TYPES.STORY_STRUCTURE
        case 'AdaptivePlanning':
          return TASK_TYPES.ANALYSIS
      }
    }
    
    // Infer from prompt content
    if (combinedPrompt.includes('story structure') || 
        combinedPrompt.includes('plot') || 
        combinedPrompt.includes('three act')) {
      return TASK_TYPES.STORY_STRUCTURE
    }
    
    if (combinedPrompt.includes('character') || 
        combinedPrompt.includes('personality') || 
        combinedPrompt.includes('backstory')) {
      return TASK_TYPES.CHARACTER_DEVELOPMENT
    }
    
    if (combinedPrompt.includes('write chapter') || 
        combinedPrompt.includes('continue writing') ||
        combinedPrompt.includes('scene')) {
      return TASK_TYPES.CHAPTER_WRITING
    }
    
    if (combinedPrompt.includes('edit') || 
        combinedPrompt.includes('review') || 
        combinedPrompt.includes('improve')) {
      return TASK_TYPES.EDITING
    }
    
    if (combinedPrompt.includes('analyze') || 
        combinedPrompt.includes('suggest') || 
        combinedPrompt.includes('feedback')) {
      return TASK_TYPES.ANALYSIS
    }
    
    if (combinedPrompt.includes('format') || 
        combinedPrompt.includes('convert')) {
      return TASK_TYPES.FORMATTING
    }
    
    if (combinedPrompt.includes('summarize') || 
        combinedPrompt.includes('brief')) {
      return TASK_TYPES.SUMMARY
    }
    
    // Default to simple continuation for unknown tasks
    return TASK_TYPES.SIMPLE_CONTINUATION
  }

  /**
   * Check if model requires upgrade
   */
  requiresUpgrade(
    subscription: UserSubscription | null,
    taskType: TaskType | string
  ): boolean {
    const tier = getUserTier(subscription)
    
    // Check if the task requires GPT-4.1
    const complexTasks = [
      TASK_TYPES.STORY_STRUCTURE,
      TASK_TYPES.CHARACTER_DEVELOPMENT,
      TASK_TYPES.CHAPTER_WRITING,
      TASK_TYPES.EDITING,
      TASK_TYPES.ANALYSIS
    ]
    
    if (!complexTasks.includes(taskType as TaskType)) {
      return false // Simple tasks don't require upgrade
    }
    
    // Check if tier has access to GPT-4.1 for this task
    return !tier.aiModels.availableForTasks.includes(taskType) && 
           !tier.aiModels.availableForTasks.includes('all')
  }

  /**
   * Get upgrade recommendation for a task
   */
  getUpgradeRecommendation(
    subscription: UserSubscription | null,
    taskType: TaskType | string
  ): string | null {
    if (!this.requiresUpgrade(subscription, taskType)) {
      return null
    }
    
    const tier = getUserTier(subscription)
    const taskName = this.getTaskDisplayName(taskType)
    
    if (tier.id === 'starter') {
      return `Upgrade to Writer plan ($9/mo) for GPT-4.1 access for ${taskName}`
    } else if (tier.id === 'writer') {
      if (taskType === TASK_TYPES.CHAPTER_WRITING || taskType === TASK_TYPES.EDITING) {
        return `Upgrade to Author plan ($29/mo) for GPT-4.1 access for ${taskName}`
      }
    }
    
    return null
  }

  /**
   * Select embedding model based on tier
   */
  private selectEmbeddingModel(tier: any, taskType: string): AIModelSelection {
    // Professional and Studio tiers can use large embeddings for premium features
    const canUseLargeEmbeddings = ['professional', 'studio'].includes(tier.id)
    
    // Use large embeddings for relationship analysis in premium tiers
    const shouldUseLarge = canUseLargeEmbeddings && 
                          taskType === TASK_TYPES.RELATIONSHIP_ANALYSIS
    
    const model = shouldUseLarge ? AI_MODELS.EMBEDDING_LARGE : AI_MODELS.EMBEDDING
    
    return {
      model,
      isRestricted: false,
      tierName: tier.name
    }
  }
  
  /**
   * Check if task requires embedding model
   */
  private isEmbeddingTask(taskType: TaskType | string): boolean {
    const embeddingTasks = [
      TASK_TYPES.SEMANTIC_SEARCH,
      TASK_TYPES.CONTENT_SIMILARITY,
      TASK_TYPES.RECOMMENDATIONS,
      TASK_TYPES.RELATIONSHIP_ANALYSIS,
      TASK_TYPES.THEME_EXTRACTION
    ]
    return embeddingTasks.includes(taskType as TaskType)
  }
  
  /**
   * Check if a model is considered premium (GPT-4.1)
   */
  private isPremiumModel(model: string): boolean {
    return model.includes('gpt-4.1') && !model.includes('mini')
  }

  /**
   * Get display name for model
   */
  private getModelDisplayName(model: string): string {
    if (model === AI_MODELS.PRIMARY) {
      return 'GPT-4.1'
    } else if (model === AI_MODELS.MEDIUM) {
      return 'GPT-4.1 Mini'
    } else if (model === AI_MODELS.FAST) {
      return 'GPT-4o Mini'
    } else if (model === AI_MODELS.EMBEDDING) {
      return 'Text Embedding Small'
    } else if (model === AI_MODELS.EMBEDDING_LARGE) {
      return 'Text Embedding Large'
    }
    return model
  }

  /**
   * Get display name for task type
   */
  private getTaskDisplayName(taskType: TaskType | string): string {
    const displayNames: Record<string, string> = {
      [TASK_TYPES.STORY_STRUCTURE]: 'story structure planning',
      [TASK_TYPES.CHARACTER_DEVELOPMENT]: 'character development',
      [TASK_TYPES.CHAPTER_WRITING]: 'chapter writing',
      [TASK_TYPES.CREATIVE_WRITING]: 'creative writing',
      [TASK_TYPES.DIALOGUE_GENERATION]: 'dialogue generation',
      [TASK_TYPES.CHAPTER_PLANNING]: 'chapter planning',
      [TASK_TYPES.EDITING]: 'editing and review',
      [TASK_TYPES.CONTENT_GENERATION]: 'content generation',
      [TASK_TYPES.ANALYSIS]: 'story analysis',
      [TASK_TYPES.ADAPTIVE_PLANNING]: 'adaptive planning',
      [TASK_TYPES.SUGGESTIONS]: 'suggestions',
      [TASK_TYPES.QUICK_EDIT]: 'quick edits',
      [TASK_TYPES.SIMPLE_CONTINUATION]: 'simple continuations',
      [TASK_TYPES.FORMATTING]: 'formatting',
      [TASK_TYPES.METADATA]: 'metadata operations',
      [TASK_TYPES.SUMMARY]: 'summarization',
      [TASK_TYPES.SEMANTIC_SEARCH]: 'semantic search',
      [TASK_TYPES.CONTENT_SIMILARITY]: 'content similarity',
      [TASK_TYPES.RECOMMENDATIONS]: 'recommendations',
      [TASK_TYPES.RELATIONSHIP_ANALYSIS]: 'relationship analysis',
      [TASK_TYPES.THEME_EXTRACTION]: 'theme extraction'
    }
    
    return displayNames[taskType] || taskType
  }

  /**
   * Track model usage for analytics
   */
  async trackModelUsage(
    userId: string,
    model: string,
    tokens: number,
    taskType: string,
    wordCount?: number
  ): Promise<void> {
    try {
      // This would integrate with analytics service
      logger.info('AI model usage', {
        userId,
        model,
        tokens,
        taskType,
        wordCount,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      logger.error('Failed to track model usage', error)
    }
  }
}

export const aiModelSelector = AIModelSelector.getInstance()