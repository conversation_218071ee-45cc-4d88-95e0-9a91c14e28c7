import { getUserTier, getAIModelForTier, selectModelByRatio } from '@/lib/subscription'
import { AI_MODELS } from '@/lib/config/ai-settings'
import type { UserSubscription } from '@/lib/subscription'
import { logger } from './logger'

export interface AIModelSelection {
  model: string
  isRestricted: boolean
  tierName: string
  reason?: string
}

/**
 * Selects the appropriate AI model based on user subscription and task type
 */
export class AIModelSelector {
  private static instance: AIModelSelector
  
  private constructor() {}
  
  static getInstance(): AIModelSelector {
    if (!AIModelSelector.instance) {
      AIModelSelector.instance = new AIModelSelector()
    }
    return AIModelSelector.instance
  }

  /**
   * Select AI model based on subscription tier and requested model
   */
  selectModel(
    subscription: UserSubscription | null,
    requestedModel: string,
    taskType: 'primary' | 'fast' = 'primary'
  ): AIModelSelection {
    const tier = getUserTier(subscription)
    
    // Check if user is trying to use a model above their tier
    const isPremiumModel = this.isPremiumModel(requestedModel)
    const tierAllowsPremium = this.tierAllowsPremiumModels(tier.id)
    
    // If user requests premium model but tier doesn't allow it
    if (isPremiumModel && !tierAllowsPremium) {
      const fallbackModel = getAIModelForTier(tier.id, taskType)
      logger.info('Model restricted by tier', {
        requested: requestedModel,
        allowed: fallbackModel,
        tier: tier.id
      })
      
      return {
        model: fallbackModel,
        isRestricted: true,
        tierName: tier.name,
        reason: `${tier.name} tier only supports ${this.getModelDisplayName(fallbackModel)}`
      }
    }
    
    // For tiers with mixed usage, select based on ratio
    if (this.hasMixedUsage(tier.id)) {
      const selectedModel = selectModelByRatio(tier.id)
      return {
        model: selectedModel,
        isRestricted: false,
        tierName: tier.name
      }
    }
    
    // Otherwise use the tier's designated model
    const allowedModel = getAIModelForTier(tier.id, taskType)
    return {
      model: allowedModel,
      isRestricted: false,
      tierName: tier.name
    }
  }

  /**
   * Check if model requires upgrade
   */
  requiresUpgrade(
    subscription: UserSubscription | null,
    requestedModel: string
  ): boolean {
    const tier = getUserTier(subscription)
    const isPremium = this.isPremiumModel(requestedModel)
    const tierAllows = this.tierAllowsPremiumModels(tier.id)
    
    return isPremium && !tierAllows
  }

  /**
   * Get upgrade recommendation for a model
   */
  getUpgradeRecommendation(
    subscription: UserSubscription | null,
    requestedModel: string
  ): string | null {
    if (!this.requiresUpgrade(subscription, requestedModel)) {
      return null
    }
    
    const tier = getUserTier(subscription)
    
    // Recommend the lowest tier that supports the requested model
    if (this.isPremiumModel(requestedModel)) {
      if (tier.id === 'starter') {
        return 'Upgrade to Writer plan or higher for GPT-4.1 access'
      } else if (tier.id === 'writer') {
        return 'Upgrade to Author plan for 50% GPT-4.1 usage'
      }
    }
    
    return null
  }

  /**
   * Check if a model is considered premium (GPT-4.1)
   */
  private isPremiumModel(model: string): boolean {
    return model.includes('gpt-4.1') || model === AI_MODELS.PRIMARY
  }

  /**
   * Check if tier allows any premium model usage
   */
  private tierAllowsPremiumModels(tierId: string): boolean {
    // Starter tier has no GPT-4.1 access
    return tierId !== 'starter'
  }

  /**
   * Check if tier has mixed model usage
   */
  private hasMixedUsage(tierId: string): boolean {
    return ['writer', 'author', 'professional', 'studio'].includes(tierId)
  }

  /**
   * Get display name for model
   */
  private getModelDisplayName(model: string): string {
    if (model.includes('gpt-4.1')) {
      return 'GPT-4.1'
    } else if (model.includes('gpt-4o-mini')) {
      return 'GPT-4o Mini'
    }
    return model
  }

  /**
   * Track model usage for analytics
   */
  async trackModelUsage(
    userId: string,
    model: string,
    tokens: number,
    taskType: string
  ): Promise<void> {
    try {
      // This would integrate with analytics service
      logger.info('AI model usage', {
        userId,
        model,
        tokens,
        taskType,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      logger.error('Failed to track model usage', error)
    }
  }
}

export const aiModelSelector = AIModelSelector.getInstance()