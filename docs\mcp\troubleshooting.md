# MCP Troubleshooting Guide

This guide helps you diagnose and resolve common issues with MCP servers in BookScribe.

## Quick Diagnostics

### Check MCP Server Status

1. **Restart Claude Desktop** - Many issues are resolved by restarting
2. **Check Configuration File** - Verify JSON syntax and paths
3. **Test Individual Servers** - Isolate problematic servers
4. **Review Logs** - Check console output for errors

### Basic Health Check

```bash
# Verify configuration file exists and is valid JSON
cat ~/.config/claude-desktop/config.json | jq .

# Check if MCP packages are installed
npm list -g | grep -E "(sentry|stripe|supabase|playwright|context7)"

# Test connectivity (replace with your actual values)
curl -H "Authorization: Bearer $STRIPE_SECRET_KEY" https://api.stripe.com/v1/account
```

## Common Issues and Solutions

### 1. MCP Servers Not Loading

**Symptoms:**
- <PERSON> doesn't show MCP tools
- No additional capabilities available
- Missing server functions

**Causes & Solutions:**

#### Invalid JSON Configuration
```bash
# Test JSON validity
cat ~/.config/claude-desktop/config.json | jq .

# If error, check for:
# - Missing commas
# - Unescaped quotes  
# - Trailing commas
# - Incorrect brackets
```

#### Incorrect File Path
```bash
# Verify config location
ls -la ~/.config/claude-desktop/config.json

# If missing, create directory
mkdir -p ~/.config/claude-desktop
```

#### Package Installation Issues
```bash
# Reinstall MCP packages
npm install -g @sentry/mcp-server @stripe/mcp @supabase/mcp-server-supabase @playwright/mcp @upstash/context7-mcp

# Clear npm cache if needed
npm cache clean --force
```

### 2. Authentication Failures

**Symptoms:**
- "Authentication failed" errors
- "Invalid API key" messages
- Permission denied errors

**Solutions by Server:**

#### Sentry Authentication
```bash
# Verify token has correct scopes
curl -H "Authorization: Bearer $SENTRY_AUTH_TOKEN" \
     "https://sentry.io/api/0/organizations/"

# Check organization/project slugs
# Org slug: Settings → General
# Project slug: Project Settings → General
```

#### Stripe Authentication
```bash
# Test API key
curl -u $STRIPE_SECRET_KEY: https://api.stripe.com/v1/account

# Common issues:
# - Using publishable key instead of secret key
# - Key doesn't match environment (test vs live)
# - Key has expired or been rotated
```

#### Supabase Authentication
```bash
# Test connection
curl -H "apikey: $SUPABASE_ANON_KEY" \
     -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
     "$SUPABASE_URL/rest/v1/"

# Common issues:
# - Wrong project URL
# - Anon key vs service role key confusion
# - RLS policies blocking access
```

### 3. Network and Connectivity Issues

**Symptoms:**
- Timeout errors
- Connection refused
- DNS resolution failures

**Solutions:**

#### Check Network Connectivity
```bash
# Test internet connection
ping *******

# Test specific service endpoints
curl -I https://api.stripe.com
curl -I https://sentry.io
curl -I your-project.supabase.co
```

#### Proxy and Firewall Issues
```bash
# Check proxy settings
echo $HTTP_PROXY
echo $HTTPS_PROXY

# Test without proxy
unset HTTP_PROXY HTTPS_PROXY
# Retry MCP operations
```

#### DNS Issues
```bash
# Test DNS resolution
nslookup api.stripe.com
nslookup sentry.io

# Use different DNS servers if needed
echo "nameserver *******" | sudo tee /etc/resolv.conf
```

### 4. Permission and Access Issues

**Symptoms:**
- "Access denied" errors
- "Insufficient permissions" messages
- Some operations work, others don't

**Solutions:**

#### Sentry Permissions
- Verify organization membership
- Check project access permissions
- Ensure auth token has required scopes:
  - `project:read` - Read project data
  - `event:read` - Read error events
  - `org:read` - Read organization data

#### Stripe Permissions
- Use correct API key type (secret vs publishable)
- Check account permissions for connected accounts
- Verify webhook endpoint permissions

#### Supabase Permissions
- Check Row Level Security (RLS) policies
- Verify user authentication status
- Use service role key for admin operations

### 5. Rate Limiting Issues

**Symptoms:**
- "Too many requests" errors
- Temporary operation failures
- Slow response times

**Solutions:**

#### Implement Retry Logic
```json
{
  "mcpServers": {
    "stripe": {
      "env": {
        "RETRY_ATTEMPTS": "3",
        "RETRY_DELAY": "1000"
      }
    }
  }
}
```

#### Monitor Usage
- Check API usage in service dashboards
- Implement request throttling
- Use bulk operations when possible

### 6. Node.js and Package Issues

**Symptoms:**
- "Module not found" errors
- Version compatibility warnings
- Installation failures

**Solutions:**

#### Node.js Version Issues
```bash
# Check Node.js version
node --version

# Some MCP servers require Node.js 18+
# Update Node.js if needed
nvm install node
nvm use node
```

#### Package Version Conflicts
```bash
# Clear npm cache
npm cache clean --force

# Reinstall packages
npm uninstall -g @sentry/mcp-server
npm install -g @sentry/mcp-server

# Check for global package issues
npm doctor
```

#### Permission Issues (Linux/macOS)
```bash
# Fix npm permissions
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) /usr/local/lib/node_modules

# Or use nvm to avoid sudo
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
```

### 7. Browser and Playwright Issues

**Symptoms:**
- Browser launch failures
- Screenshot capture errors
- Page navigation timeouts

**Solutions:**

#### Browser Installation
```bash
# Install browser dependencies
npx playwright install

# Install system dependencies (Linux)
npx playwright install-deps

# Check browser availability
npx playwright --version
```

#### Headless Mode Issues
```json
{
  "playwright": {
    "env": {
      "PLAYWRIGHT_HEADLESS": "false",
      "PLAYWRIGHT_SLOWMO": "1000"
    }
  }
}
```

#### Display Issues (Linux)
```bash
# Install virtual display for headless environments
sudo apt-get install xvfb

# Set display environment
export DISPLAY=:99
Xvfb :99 -screen 0 1024x768x24 &
```

## Debugging Techniques

### Enable Debug Logging

#### Global Debug Mode
```json
{
  "mcpServers": {
    "stripe": {
      "env": {
        "DEBUG": "*",
        "NODE_ENV": "development"
      }
    }
  }
}
```

#### Server-Specific Debugging
```json
{
  "mcpServers": {
    "sentry": {
      "env": {
        "DEBUG": "sentry:*"
      }
    },
    "stripe": {
      "env": {
        "DEBUG": "stripe:*"
      }
    }
  }
}
```

### Manual Server Testing

#### Test Individual MCP Servers
```bash
# Test Stripe MCP
npx @stripe/mcp --api-key sk_test_your_key

# Test Context7 MCP  
npx @upstash/context7-mcp

# Test Playwright MCP
npx @playwright/mcp
```

### Configuration Validation

#### JSON Schema Validation
```bash
# Install JSON schema validator
npm install -g ajv-cli

# Validate configuration (if schema available)
ajv validate -s mcp-schema.json -d ~/.config/claude-desktop/config.json
```

#### Manual Configuration Check
```bash
# Check configuration structure
cat ~/.config/claude-desktop/config.json | jq '.mcpServers | keys'

# Verify environment variables
cat ~/.config/claude-desktop/config.json | jq '.mcpServers.stripe.env'
```

## Performance Troubleshooting

### Slow Response Times

#### Optimize Configuration
```json
{
  "mcpServers": {
    "playwright": {
      "env": {
        "PLAYWRIGHT_TIMEOUT": "5000",
        "PLAYWRIGHT_VIEWPORT": "800x600"
      }
    }
  }
}
```

#### Monitor Resource Usage
```bash
# Check memory usage
ps aux | grep node

# Monitor network activity
netstat -an | grep ESTABLISHED

# Check disk space
df -h
```

### Memory Issues

#### Increase Node.js Memory Limit
```json
{
  "mcpServers": {
    "playwright": {
      "command": "node",
      "args": ["--max-old-space-size=4096", "path/to/mcp-server"],
      "env": {}
    }
  }
}
```

## Recovery Procedures

### Reset Configuration

#### Backup Current Config
```bash
cp ~/.config/claude-desktop/config.json ~/.config/claude-desktop/config.json.backup
```

#### Restore Default Configuration
```bash
# Remove problematic configuration
rm ~/.config/claude-desktop/config.json

# Restart Claude Desktop
# Reconfigure servers one by one
```

### Clean Installation

#### Complete Reset
```bash
# Uninstall all MCP packages
npm uninstall -g @sentry/mcp-server @stripe/mcp @supabase/mcp-server-supabase @playwright/mcp @upstash/context7-mcp

# Clear npm cache
npm cache clean --force

# Remove configuration
rm -rf ~/.config/claude-desktop

# Reinstall everything
mkdir -p ~/.config/claude-desktop
# Add configuration and reinstall packages
```

## Getting Help

### Log Collection

#### Gather System Information
```bash
# System info
uname -a
node --version
npm --version

# Package versions
npm list -g | grep -E "(sentry|stripe|supabase|playwright|context7)"

# Configuration (remove sensitive data!)
cat ~/.config/claude-desktop/config.json | jq 'del(.mcpServers[].env)'
```

### Community Resources

- **GitHub Issues**: Check individual MCP server repositories
- **Discord/Slack**: Claude community channels
- **Stack Overflow**: Use tags like `claude-mcp`, `model-context-protocol`
- **Documentation**: Official MCP documentation

### Creating Bug Reports

Include:
1. **System information** (OS, Node.js version, etc.)
2. **Configuration** (sanitized, no secrets)
3. **Error messages** (full stack traces)
4. **Steps to reproduce**
5. **Expected vs actual behavior**

This troubleshooting guide should help you resolve most common MCP issues in BookScribe.