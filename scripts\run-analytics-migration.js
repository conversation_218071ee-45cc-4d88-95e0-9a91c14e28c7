const { execSync } = require('child_process');
const path = require('path');

console.log('Running analytics migration...');

try {
  // Run the quality metrics migration
  const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250119_create_quality_metrics.sql');
  
  console.log('Creating quality metrics tables...');
  execSync(`npx supabase db push --include ${migrationPath}`, {
    stdio: 'inherit'
  });
  
  console.log('✅ Analytics migration completed successfully!');
  console.log('\nNew tables created:');
  console.log('- quality_metrics: Stores chapter-level quality analysis');
  console.log('- project_quality_metrics: Stores project-level aggregated metrics');
  console.log('\nThe analytics dashboard will now show:');
  console.log('- Real-time writing session tracking');
  console.log('- Automatic quality analysis on chapter saves');
  console.log('- Project quality trends and metrics');
  console.log('- Comprehensive writing analytics');
  
} catch (error) {
  console.error('❌ Migration failed:', error.message);
  process.exit(1);
}