-- ============================================================================
-- STEP 4: CREATE SECURE GRANULAR POLICIES - PART 3 (REMAINING TABLES)
-- ============================================================================
-- Final batch of tables with specific access patterns

-- ============================================================================
-- SERIES BOOKS - Linked to User's Series
-- ============================================================================
CREATE POLICY "series_books_select" ON series_books
  FOR SELECT USING (
    series_id IN (SELECT id FROM series WHERE user_id = auth.uid())
  );

CREATE POLICY "series_books_insert" ON series_books
  FOR INSERT WITH CHECK (
    series_id IN (SELECT id FROM series WHERE user_id = auth.uid()) AND
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "series_books_delete" ON series_books
  FOR DELETE USING (
    series_id IN (SELECT id FROM series WHERE user_id = auth.uid())
  );
-- No UPDATE - book order is managed by book_number

-- ============================================================================
-- WRITING GOAL PROGRESS - Linked to User's Goals
-- ============================================================================
CREATE POLICY "writing_goal_progress_select" ON writing_goal_progress
  FOR SELECT USING (
    goal_id IN (SELECT id FROM writing_goals WHERE user_id = auth.uid())
  );

CREATE POLICY "writing_goal_progress_insert" ON writing_goal_progress
  FOR INSERT WITH CHECK (
    goal_id IN (SELECT id FROM writing_goals WHERE user_id = auth.uid())
  );

CREATE POLICY "writing_goal_progress_update_recent" ON writing_goal_progress
  FOR UPDATE USING (
    goal_id IN (SELECT id FROM writing_goals WHERE user_id = auth.uid()) AND
    created_at > NOW() - INTERVAL '7 days'
  );
-- No DELETE - progress tracking data

-- ============================================================================
-- COLLABORATION SESSIONS - Project Owner + Participants
-- ============================================================================
CREATE POLICY "collaboration_sessions_select" ON collaboration_sessions
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid()) OR
    id IN (SELECT session_id FROM collaboration_participants WHERE user_id = auth.uid())
  );

CREATE POLICY "collaboration_sessions_insert" ON collaboration_sessions
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid()) AND
    auth.uid() = created_by
  );

CREATE POLICY "collaboration_sessions_update" ON collaboration_sessions
  FOR UPDATE USING (
    auth.uid() = created_by
  );

CREATE POLICY "collaboration_sessions_delete" ON collaboration_sessions
  FOR DELETE USING (
    auth.uid() = created_by
  );

-- ============================================================================
-- COLLABORATION PARTICIPANTS - Session Access Control
-- ============================================================================
CREATE POLICY "collaboration_participants_select" ON collaboration_participants
  FOR SELECT USING (
    session_id IN (
      SELECT id FROM collaboration_sessions WHERE 
      project_id IN (SELECT id FROM projects WHERE user_id = auth.uid()) OR
      id IN (SELECT session_id FROM collaboration_participants WHERE user_id = auth.uid())
    )
  );

CREATE POLICY "collaboration_participants_insert" ON collaboration_participants
  FOR INSERT WITH CHECK (
    session_id IN (
      SELECT id FROM collaboration_sessions WHERE 
      project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
    )
  );

CREATE POLICY "collaboration_participants_update" ON collaboration_participants
  FOR UPDATE USING (
    session_id IN (
      SELECT id FROM collaboration_sessions WHERE 
      project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
    ) OR auth.uid() = user_id
  );

CREATE POLICY "collaboration_participants_delete" ON collaboration_participants
  FOR DELETE USING (
    session_id IN (
      SELECT id FROM collaboration_sessions WHERE 
      project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
    ) OR auth.uid() = user_id
  );

-- ============================================================================
-- SELECTION PROFILES - Public Read + Owner Write (Fix Existing)
-- ============================================================================
-- Drop the broad policy first
DROP POLICY IF EXISTS "Users can manage own selection profiles" ON selection_profiles;

CREATE POLICY "selection_profiles_insert" ON selection_profiles
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "selection_profiles_update" ON selection_profiles
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "selection_profiles_delete" ON selection_profiles
  FOR DELETE USING (auth.uid() = user_id);

-- Keep the existing SELECT policy for public + own profiles

-- ============================================================================
-- FINAL VERIFICATION
-- ============================================================================
SELECT 
  tablename,
  COUNT(*) as policy_count,
  STRING_AGG(DISTINCT cmd, ', ') as operations,
  CASE 
    WHEN COUNT(*) = 1 AND STRING_AGG(cmd, '') = 'SELECT' THEN 'Read-Only'
    WHEN COUNT(*) = 4 THEN 'Full CRUD'
    WHEN COUNT(*) = 3 THEN 'Limited CRUD'
    WHEN COUNT(*) = 2 THEN 'Read + Limited Write'
    ELSE 'Custom'
  END as access_pattern
FROM pg_policies 
WHERE schemaname = 'public'
  AND tablename NOT IN ('usage_events', 'usage_tracking', 'user_subscriptions', 'profiles', 'projects', 'chapters')
GROUP BY tablename
ORDER BY tablename;

-- Show summary of all policies
SELECT 
  'Total Tables with Policies' as metric,
  COUNT(DISTINCT tablename) as count
FROM pg_policies 
WHERE schemaname = 'public'
UNION ALL
SELECT 
  'Total Policies Created' as metric,
  COUNT(*) as count
FROM pg_policies 
WHERE schemaname = 'public';
