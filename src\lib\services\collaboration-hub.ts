import { BaseService } from './base-service';
import { CollaborationSession, ServiceResponse } from './types';

export class CollaborationHub extends BaseService {
  private activeSessions: Map<string, CollaborationSession> = new Map();
  private userConnections: Map<string, WebSocket[]> = new Map();
  private documentLocks: Map<string, { userId: string; section: string; timestamp: number }> = new Map();

  constructor() {
    super({
      name: 'collaboration-hub',
      version: '1.0.0',
      status: 'inactive',
      endpoints: ['/api/collaboration/sessions', '/api/collaboration/changes'],
      dependencies: ['context-manager'],
      healthCheck: '/api/collaboration/health'
    });
  }

  async initialize(): Promise<void> {
    this.startSessionCleanup();
    this.startConflictResolution();
    this.isInitialized = true;
    this.setStatus('active');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string; uptime: number }>> {
    const activeUsers = Array.from(this.activeSessions.values())
      .reduce((count, session) => count + session.participants.filter(p => p.status === 'online').length, 0);

    return this.createResponse(true, {
      status: `${this.activeSessions.size} active sessions, ${activeUsers} online users`,
      uptime: Date.now() - (this.isInitialized ? Date.now() - 1000 : Date.now()),
    });
  }

  async shutdown(): Promise<void> {
    this.activeSessions.clear();
    this.userConnections.clear();
    this.documentLocks.clear();
    this.setStatus('inactive');
  }

  async createSession(projectId: string, ownerId: string): Promise<ServiceResponse<string>> {
    return this.withErrorHandling(async () => {
      const sessionId = `collab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const session: CollaborationSession = {
        id: sessionId,
        projectId,
        participants: [{
          userId: ownerId,
          role: 'owner',
          status: 'online',
          cursor: { line: 1, column: 1 }
        }],
        document: {
          content: '',
          version: 1,
          lastModified: Date.now(),
          locks: []
        },
        changes: []
      };

      this.activeSessions.set(sessionId, session);
      return sessionId;
    });
  }

  async joinSession(sessionId: string, userId: string, role: 'editor' | 'viewer' | 'commenter' = 'viewer'): Promise<ServiceResponse<CollaborationSession>> {
    return this.withErrorHandling(async () => {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        throw new Error('Session not found');
      }

      // Check if user is already in session
      const existingParticipant = session.participants.find(p => p.userId === userId);
      if (existingParticipant) {
        existingParticipant.status = 'online';
        existingParticipant.role = role;
      } else {
        session.participants.push({
          userId,
          role,
          status: 'online',
          cursor: { line: 1, column: 1 }
        });
      }

      // Broadcast user joined event
      this.broadcastToSession(sessionId, {
        type: 'user_joined',
        userId,
        role,
        timestamp: Date.now()
      });

      return session;
    });
  }

  async leaveSession(sessionId: string, userId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const session = this.activeSessions.get(sessionId);
      if (!session) return false;

      const participant = session.participants.find(p => p.userId === userId);
      if (participant) {
        participant.status = 'offline';
        
        // Release any locks held by this user
        session.document.locks = session.document.locks.filter(lock => lock.userId !== userId);
        
        // Broadcast user left event
        this.broadcastToSession(sessionId, {
          type: 'user_left',
          userId,
          timestamp: Date.now()
        });
      }

      return true;
    });
  }

  async applyChange(sessionId: string, userId: string, change: {
    type: 'insert' | 'delete' | 'format' | 'comment';
    position: { line: number; column: number };
    content?: string;
    length?: number;
    data?: Record<string, unknown>;
  }): Promise<ServiceResponse<{ version: number; conflictResolution?: Record<string, unknown> }>> {
    return this.withErrorHandling(async () => {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        throw new Error('Session not found');
      }

      const participant = session.participants.find(p => p.userId === userId);
      if (!participant || participant.status !== 'online') {
        throw new Error('User not active in session');
      }

      // Check permissions
      if (!this.hasEditPermission(participant.role, change.type)) {
        throw new Error('Insufficient permissions for this action');
      }

      // Check for conflicts
      const conflictResolution = await this.checkConflicts(session, change);

      // Apply the change
      const changeRecord = {
        id: `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        type: change.type,
        data: {
          position: change.position,
          content: change.content,
          length: change.length,
          ...change.data
        },
        timestamp: Date.now()
      };

      session.changes.push(changeRecord);
      session.document.version++;
      session.document.lastModified = Date.now();

      // Apply change to document content
      if (change.type === 'insert' && change.content) {
        session.document.content = this.insertContent(
          session.document.content,
          change.position,
          change.content
        );
      } else if (change.type === 'delete' && change.length) {
        session.document.content = this.deleteContent(
          session.document.content,
          change.position,
          change.length
        );
      }

      // Broadcast change to other participants
      this.broadcastToSession(sessionId, {
        type: 'document_changed',
        change: changeRecord,
        version: session.document.version,
        userId
      }, userId);

      return {
        version: session.document.version,
        conflictResolution
      };
    });
  }

  async updateCursor(sessionId: string, userId: string, position: { line: number; column: number }): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const session = this.activeSessions.get(sessionId);
      if (!session) return false;

      const participant = session.participants.find(p => p.userId === userId);
      if (participant) {
        participant.cursor = position;
        
        // Broadcast cursor update
        this.broadcastToSession(sessionId, {
          type: 'cursor_moved',
          userId,
          position,
          timestamp: Date.now()
        }, userId);
      }

      return true;
    });
  }

  async lockSection(sessionId: string, userId: string, section: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const session = this.activeSessions.get(sessionId);
      if (!session) throw new Error('Session not found');

      const lockKey = `${sessionId}_${section}`;
      const existingLock = this.documentLocks.get(lockKey);

      if (existingLock && existingLock.userId !== userId) {
        // Check if lock is still valid (5 minutes timeout)
        if (Date.now() - existingLock.timestamp < 300000) {
          throw new Error('Section is locked by another user');
        }
      }

      this.documentLocks.set(lockKey, {
        userId,
        section,
        timestamp: Date.now()
      });

      session.document.locks.push({
        section,
        userId,
        timestamp: Date.now()
      });

      this.broadcastToSession(sessionId, {
        type: 'section_locked',
        userId,
        section,
        timestamp: Date.now()
      }, userId);

      return true;
    });
  }

  async unlockSection(sessionId: string, userId: string, section: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const session = this.activeSessions.get(sessionId);
      if (!session) return false;

      const lockKey = `${sessionId}_${section}`;
      const existingLock = this.documentLocks.get(lockKey);

      if (existingLock && existingLock.userId === userId) {
        this.documentLocks.delete(lockKey);
        session.document.locks = session.document.locks.filter(
          lock => !(lock.section === section && lock.userId === userId)
        );

        this.broadcastToSession(sessionId, {
          type: 'section_unlocked',
          userId,
          section,
          timestamp: Date.now()
        }, userId);
      }

      return true;
    });
  }

  async addComment(sessionId: string, userId: string, comment: {
    position: { line: number; column: number };
    text: string;
    thread?: string;
  }): Promise<ServiceResponse<string>> {
    return this.withErrorHandling(async () => {
      const commentId = await this.applyChange(sessionId, userId, {
        type: 'comment',
        position: comment.position,
        data: {
          text: comment.text,
          thread: comment.thread || `thread_${Date.now()}`
        }
      });

      return commentId.data?.version.toString() || '';
    });
  }

  async resolveComment(sessionId: string, userId: string, commentId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const session = this.activeSessions.get(sessionId);
      if (!session) return false;

      const comment = session.changes.find(c => c.id === commentId && c.type === 'comment');
      if (comment) {
        comment.data.resolved = true;
        comment.data.resolvedBy = userId;
        comment.data.resolvedAt = Date.now();

        this.broadcastToSession(sessionId, {
          type: 'comment_resolved',
          commentId,
          userId,
          timestamp: Date.now()
        });
      }

      return true;
    });
  }

  async getSessionHistory(sessionId: string, limit = 100): Promise<ServiceResponse<Array<Record<string, unknown>>>> {
    return this.withErrorHandling(async () => {
      const session = this.activeSessions.get(sessionId);
      if (!session) return [];

      return session.changes
        .slice(-limit)
        .map(change => ({
          id: change.id,
          userId: change.userId,
          type: change.type,
          timestamp: change.timestamp,
          data: change.data
        }));
    });
  }

  async exportSession(sessionId: string): Promise<ServiceResponse<{
    document: string;
    metadata: Record<string, unknown>;
    participants: Array<Record<string, unknown>>;
    changeHistory: Array<Record<string, unknown>>;
  }>> {
    return this.withErrorHandling(async () => {
      const session = this.activeSessions.get(sessionId);
      if (!session) throw new Error('Session not found');

      return {
        document: session.document.content,
        metadata: {
          projectId: session.projectId,
          version: session.document.version,
          lastModified: session.document.lastModified,
          totalChanges: session.changes.length
        },
        participants: session.participants.map(p => ({
          userId: p.userId,
          role: p.role,
          status: p.status
        })),
        changeHistory: session.changes.map(c => ({
          id: c.id,
          userId: c.userId,
          type: c.type,
          timestamp: c.timestamp
        }))
      };
    });
  }

  private hasEditPermission(role: string, changeType: string): boolean {
    switch (role) {
      case 'owner':
      case 'editor':
        return true;
      case 'commenter':
        return changeType === 'comment';
      case 'viewer':
        return false;
      default:
        return false;
    }
  }

  private async checkConflicts(session: CollaborationSession, change: Record<string, unknown>): Promise<Record<string, unknown>> {
    // Simple conflict detection - in production would be more sophisticated
    const recentChanges = session.changes.filter(c => 
      Date.now() - c.timestamp < 5000 && // Last 5 seconds
      c.type === change.type &&
      Math.abs((c.data as { position?: { line: number } }).position?.line || 0 - (change.position as { line: number })?.line || 0) <= 2
    );

    if (recentChanges.length > 0) {
      return {
        hasConflict: true,
        conflictingChanges: recentChanges.map(c => c.id),
        resolution: 'manual' // Could implement automatic resolution
      };
    }

    return { hasConflict: false };
  }

  private insertContent(document: string, position: { line: number; column: number }, content: string): string {
    const lines = document.split('\n');
    
    if (position.line > lines.length) {
      // Add empty lines if needed
      while (lines.length < position.line) {
        lines.push('');
      }
    }

    const line = lines[position.line - 1] || '';
    const before = line.substring(0, position.column - 1);
    const after = line.substring(position.column - 1);
    
    lines[position.line - 1] = before + content + after;
    
    return lines.join('\n');
  }

  private deleteContent(document: string, position: { line: number; column: number }, length: number): string {
    const lines = document.split('\n');
    
    if (position.line > lines.length) return document;
    
    const line = lines[position.line - 1] || '';
    const before = line.substring(0, position.column - 1);
    const after = line.substring(position.column - 1 + length);
    
    lines[position.line - 1] = before + after;
    
    return lines.join('\n');
  }

  private broadcastToSession(sessionId: string, message: Record<string, unknown>, excludeUserId?: string): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    session.participants.forEach(participant => {
      if (participant.status === 'online' && participant.userId !== excludeUserId) {
        const connections = this.userConnections.get(participant.userId);
        if (connections) {
          connections.forEach(ws => {
            if (ws.readyState === 1) { // WebSocket.OPEN
              ws.send(JSON.stringify({
                type: 'collaboration_event',
                sessionId,
                ...message
              }));
            }
          });
        }
      }
    });
  }

  private startSessionCleanup(): void {
    setInterval(() => {
      const now = Date.now();
      const inactiveThreshold = 30 * 60 * 1000; // 30 minutes

      Array.from(this.activeSessions.entries()).forEach(([sessionId, session]) => {
        const hasActiveParticipants = session.participants.some(p => p.status === 'online');
        const isOld = now - session.document.lastModified > inactiveThreshold;

        if (!hasActiveParticipants && isOld) {
          this.activeSessions.delete(sessionId);
        }
      });
    }, 5 * 60 * 1000); // Check every 5 minutes
  }

  private startConflictResolution(): void {
    setInterval(() => {
      // Clean up expired locks
      const now = Date.now();
      const lockTimeout = 5 * 60 * 1000; // 5 minutes

      Array.from(this.documentLocks.entries()).forEach(([lockKey, lock]) => {
        if (now - lock.timestamp > lockTimeout) {
          this.documentLocks.delete(lockKey);
        }
      });
    }, 60 * 1000); // Check every minute
  }

  // WebSocket connection management
  addUserConnection(userId: string, ws: WebSocket): void {
    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, []);
    }
    this.userConnections.get(userId)!.push(ws);

    ws.onclose = () => {
      this.removeUserConnection(userId, ws);
    };
  }

  private removeUserConnection(userId: string, ws: WebSocket): void {
    const connections = this.userConnections.get(userId);
    if (connections) {
      const index = connections.indexOf(ws);
      if (index > -1) {
        connections.splice(index, 1);
      }
      if (connections.length === 0) {
        this.userConnections.delete(userId);
      }
    }
  }
}