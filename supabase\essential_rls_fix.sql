-- ============================================================================
-- ESSENTIAL RLS POLICIES FIX FOR BOOKSCRIBE AI
-- ============================================================================
-- Run this directly in Supabase SQL Editor to fix 406/403 errors
-- This creates the minimum required policies for the app to work

-- ============================================================================
-- 1. ENABLE RLS ON ALL TABLES (if not already enabled)
-- ============================================================================

ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_arcs ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_logs ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 2. DROP ANY EXISTING CONFLICTING POLICIES
-- ============================================================================

-- Profiles
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;

-- Projects  
DROP POLICY IF EXISTS "Users can view own projects" ON projects;
DROP POLICY IF EXISTS "Users can insert own projects" ON projects;
DROP POLICY IF EXISTS "Users can update own projects" ON projects;
DROP POLICY IF EXISTS "Users can delete own projects" ON projects;

-- Chapters (the main problem)
DROP POLICY IF EXISTS "Users can manage own chapters" ON chapters;
DROP POLICY IF EXISTS "Users can access own project chapters" ON chapters;
DROP POLICY IF EXISTS "Users can view own project chapters" ON chapters;
DROP POLICY IF EXISTS "Users can insert own project chapters" ON chapters;
DROP POLICY IF EXISTS "Users can update own project chapters" ON chapters;
DROP POLICY IF EXISTS "Users can delete own project chapters" ON chapters;

-- Characters
DROP POLICY IF EXISTS "Users can access own project characters" ON characters;

-- Story Arcs
DROP POLICY IF EXISTS "Users can access own project story_arcs" ON story_arcs;

-- Agent Logs
DROP POLICY IF EXISTS "Users can access own project agent_logs" ON agent_logs;

-- ============================================================================
-- 3. CREATE ESSENTIAL RLS POLICIES
-- ============================================================================

-- PROFILES: Users can only access their own profile
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- PROJECTS: Users can manage their own projects
CREATE POLICY "Users can view own projects" ON projects
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own projects" ON projects
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own projects" ON projects
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own projects" ON projects
  FOR DELETE USING (auth.uid() = user_id);

-- CHAPTERS: Users can manage chapters in their own projects (CRITICAL FIX)
CREATE POLICY "Users can view own project chapters" ON chapters
  FOR SELECT USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can insert own project chapters" ON chapters
  FOR INSERT WITH CHECK (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can update own project chapters" ON chapters
  FOR UPDATE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can delete own project chapters" ON chapters
  FOR DELETE USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- CHARACTERS: Users can manage characters in their own projects
CREATE POLICY "Users can access own project characters" ON characters
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- STORY ARCS: Users can manage story arcs in their own projects
CREATE POLICY "Users can access own project story_arcs" ON story_arcs
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- AGENT LOGS: Users can view AI agent logs for their own projects
CREATE POLICY "Users can access own project agent_logs" ON agent_logs
  FOR ALL USING (
    project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
  );

-- ============================================================================
-- 4. OPTIONAL: ADD POLICIES FOR OTHER EXISTING TABLES
-- ============================================================================

-- Only add these if the tables exist and you want to secure them

-- Selection Profiles (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'selection_profiles') THEN
    ALTER TABLE selection_profiles ENABLE ROW LEVEL SECURITY;
    
    DROP POLICY IF EXISTS "Users can view selection profiles" ON selection_profiles;
    DROP POLICY IF EXISTS "Users can manage own selection profiles" ON selection_profiles;
    
    CREATE POLICY "Users can view selection profiles" ON selection_profiles
      FOR SELECT USING (auth.uid() = user_id OR is_public = true);
    
    CREATE POLICY "Users can manage own selection profiles" ON selection_profiles
      FOR ALL USING (auth.uid() = user_id);
  END IF;
END $$;

-- Reference Materials (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'reference_materials') THEN
    ALTER TABLE reference_materials ENABLE ROW LEVEL SECURITY;
    
    DROP POLICY IF EXISTS "Users can access own project reference_materials" ON reference_materials;
    
    CREATE POLICY "Users can access own project reference_materials" ON reference_materials
      FOR ALL USING (
        project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
      );
  END IF;
END $$;

-- Story Bible (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'story_bible') THEN
    ALTER TABLE story_bible ENABLE ROW LEVEL SECURITY;
    
    DROP POLICY IF EXISTS "Users can access own project story_bible" ON story_bible;
    
    CREATE POLICY "Users can access own project story_bible" ON story_bible
      FOR ALL USING (
        project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
      );
  END IF;
END $$;

-- ============================================================================
-- 5. VERIFICATION
-- ============================================================================

-- This will show you what policies were created
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'projects', 'chapters', 'characters', 'story_arcs', 'agent_logs')
ORDER BY tablename, policyname;

-- ============================================================================
-- DONE!
-- ============================================================================
