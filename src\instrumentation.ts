// Temporarily disable <PERSON><PERSON> to debug 500 errors
// import * as Sen<PERSON> from '@sentry/nextjs';

// export async function register() {
//   if (process.env.NEXT_RUNTIME === 'nodejs') {
//     await import('../sentry.server.config');
//   }

//   if (process.env.NEXT_RUNTIME === 'edge') {
//     await import('../sentry.edge.config');
//   }
// }

// export const onRequestError = Sentry.captureRequestError;
