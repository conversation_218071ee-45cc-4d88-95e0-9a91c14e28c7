-- ============================================================================
-- STEP 1: DROP ALL BROAD "ALL" POLICIES
-- ============================================================================
-- This removes the overly permissive policies so we can replace them

-- Drop broad "ALL" policies that need to be replaced
DROP POLICY IF EXISTS "Users can access own project agent_logs" ON agent_logs;
DROP POLICY IF EXISTS "Users can access own ai_suggestions" ON ai_suggestions;
DROP POLICY IF EXISTS "Users can access own chapter_versions" ON chapter_versions;
DROP POLICY IF EXISTS "Users can access own project characters" ON characters;
DROP POLICY IF EXISTS "Users can access collaboration_participants" ON collaboration_participants;
DROP POLICY IF EXISTS "Users can access collaboration_sessions" ON collaboration_sessions;
DROP POLICY IF EXISTS "Users can access own content_embeddings" ON content_embeddings;
DROP POLICY IF EXISTS "Users can access own editing_sessions" ON editing_sessions;
DROP POLICY IF EXISTS "Users can access own notifications" ON notifications;
DROP POLICY IF EXISTS "Users can access own processing_tasks" ON processing_tasks;
DROP POLICY IF EXISTS "Users can access own project_snapshots" ON project_snapshots;
DROP POLICY IF EXISTS "Users can access own project reference_materials" ON reference_materials;
DROP POLICY IF EXISTS "Users can access own selection_analytics" ON selection_analytics;
DROP POLICY IF EXISTS "Users can manage own selection profiles" ON selection_profiles;
DROP POLICY IF EXISTS "Users can access own series" ON series;
DROP POLICY IF EXISTS "Users can access own series_books" ON series_books;
DROP POLICY IF EXISTS "Users can access own project story_arcs" ON story_arcs;
DROP POLICY IF EXISTS "Users can access own project story_bible" ON story_bible;
DROP POLICY IF EXISTS "Users can access own writing_goal_progress" ON writing_goal_progress;
DROP POLICY IF EXISTS "Users can access own writing_goals" ON writing_goals;
DROP POLICY IF EXISTS "Users can access own writing_sessions" ON writing_sessions;

-- Verify policies were dropped
SELECT 
  tablename,
  policyname,
  cmd
FROM pg_policies 
WHERE schemaname = 'public' 
  AND policyname LIKE '%access%'
ORDER BY tablename;
