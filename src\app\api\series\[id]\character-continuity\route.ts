import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { supabase } from '@/lib/supabase/client';
import { VoiceProfileManager } from '@/lib/services/voice-profile-manager';

const voiceProfileManager = new VoiceProfileManager();

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;
    const supabaseClient = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify user owns this series
    const { data: series, error: seriesError } = await supabase
      .from('series')
      .select('user_id')
      .eq('id', seriesId)
      .single();

    if (seriesError || !series || series.user_id !== user.id) {
      return NextResponse.json({ error: 'Series not found or unauthorized' }, { status: 404 });
    }

    // Get character continuity data
    const continuity = await voiceProfileManager.getSeriesCharacterContinuity(seriesId);

    // Get all books in the series with their characters
    const { data: seriesBooks, error: booksError } = await supabase
      .from('series_books')
      .select(`
        book_number,
        project_id,
        project:projects!series_books_project_id_fkey(
          id,
          title,
          characters(
            id,
            name,
            role,
            description
          )
        )
      `)
      .eq('series_id', seriesId)
      .order('book_number');

    if (booksError) throw booksError;

    // Build character timeline
    const characterTimeline: Record<string, any> = {};
    
    seriesBooks?.forEach((book) => {
      const project = book.project;
      if (!project) return;

      project.characters?.forEach((character: any) => {
        if (!characterTimeline[character.name]) {
          characterTimeline[character.name] = {
            name: character.name,
            appearances: [],
            firstAppearance: book.book_number,
            lastAppearance: book.book_number,
            totalAppearances: 0
          };
        }

        characterTimeline[character.name].appearances.push({
          bookNumber: book.book_number,
          bookTitle: project.title,
          role: character.role,
          description: character.description
        });

        characterTimeline[character.name].lastAppearance = book.book_number;
        characterTimeline[character.name].totalAppearances++;
      });
    });

    return NextResponse.json({
      continuity,
      characterTimeline,
      totalBooks: seriesBooks?.length || 0
    });
  } catch (error) {
    console.error('Error fetching character continuity:', error);
    return NextResponse.json(
      { error: 'Failed to fetch character continuity' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;
    const supabaseClient = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify user owns this series
    const { data: series, error: seriesError } = await supabase
      .from('series')
      .select('user_id')
      .eq('id', seriesId)
      .single();

    if (seriesError || !series || series.user_id !== user.id) {
      return NextResponse.json({ error: 'Series not found or unauthorized' }, { status: 404 });
    }

    const body = await request.json();
    const { characterName, status, lastAppearanceBook, statusChangeReason, voiceProfileId } = body;

    if (!characterName) {
      return NextResponse.json(
        { error: 'Character name is required' },
        { status: 400 }
      );
    }

    const continuity = await voiceProfileManager.updateCharacterContinuity(
      seriesId,
      characterName,
      {
        status,
        lastAppearanceBook,
        statusChangeBook: lastAppearanceBook,
        statusChangeReason,
        voiceProfileId
      }
    );

    if (!continuity) {
      return NextResponse.json(
        { error: 'Failed to update character continuity' },
        { status: 500 }
      );
    }

    return NextResponse.json({ continuity });
  } catch (error) {
    console.error('Error updating character continuity:', error);
    return NextResponse.json(
      { error: 'Failed to update character continuity' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;
    const { searchParams } = new URL(request.url);
    const characterName = searchParams.get('character');
    
    if (!characterName) {
      return NextResponse.json(
        { error: 'Character name is required' },
        { status: 400 }
      );
    }

    const supabaseClient = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { characterStates, relationshipChanges } = body;

    // Update character states and relationships
    const { data: continuity, error: updateError } = await supabase
      .from('series_character_continuity')
      .update({
        character_states: characterStates,
        relationship_changes: relationshipChanges,
        updated_at: new Date().toISOString()
      })
      .eq('series_id', seriesId)
      .eq('character_name', characterName)
      .select()
      .single();

    if (updateError) {
      throw updateError;
    }

    return NextResponse.json({ continuity });
  } catch (error) {
    console.error('Error updating character states:', error);
    return NextResponse.json(
      { error: 'Failed to update character states' },
      { status: 500 }
    );
  }
}