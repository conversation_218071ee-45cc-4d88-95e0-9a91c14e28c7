# BookScribe Setup Guide - Required API Keys

This guide will help you obtain the necessary API keys to run BookScribe with full functionality.

## Critical Services (Required)

### 1. Supabase Service Role Key
**Current Status**: ❌ Missing (using placeholder)

To get your service role key:
1. Go to your Supabase project dashboard
2. Navigate to Settings → API
3. Copy the `service_role` key (starts with `eyJ...`)
4. Replace `your_supabase_service_role_key` in `.env.local`

⚠️ **Important**: The service role key bypasses Row Level Security. Keep it secret!

### 2. OpenAI API Key
**Current Status**: ❌ Missing (using placeholder)

To get your OpenAI API key:
1. Go to https://platform.openai.com/api-keys
2. Click "Create new secret key"
3. Copy the key (starts with `sk-...`)
4. Replace `your_openai_api_key` in `.env.local`

💡 **Note**: OpenAI charges per API usage. Set up usage limits in your OpenAI dashboard.

### 3. Stripe Configuration
**Current Status**: ⚠️ Partial (missing webhook secret and price IDs)

You need to add:
- **Webhook Secret**: 
  1. Go to https://dashboard.stripe.com/webhooks
  2. Create a webhook endpoint pointing to `http://localhost:3000/api/webhooks/stripe`
  3. Copy the signing secret (starts with `whsec_...`)
  4. Replace `whsec_your_stripe_webhook_secret` in `.env.local`

- **Price IDs**:
  1. Go to https://dashboard.stripe.com/products
  2. Create products for Basic, Pro, and Enterprise tiers
  3. Copy each price ID (starts with `price_...`)
  4. Replace the placeholder price IDs in `.env.local`

## Optional Services

### Google Gemini API Key
- Only needed if you want to use Google's AI models as an alternative to OpenAI
- Get it from Google AI Studio

### Supabase Webhook Secret & JWT Secret
- Only needed for advanced webhook verification
- Found in your Supabase dashboard under Settings → API

## Quick Checklist

Before running the app, ensure you have:
- [ ] Valid Supabase service role key
- [ ] Valid OpenAI API key
- [ ] Stripe webhook secret configured
- [ ] Stripe price IDs for all tiers
- [ ] Restarted the Next.js dev server after updating `.env.local`

## Testing Your Setup

1. Restart your development server:
   ```bash
   npm run dev
   ```

2. Try creating a new project - this will test:
   - Supabase database connection
   - OpenAI API integration
   - Project creation workflow

3. Check the browser console and terminal for any errors

## Troubleshooting

### "Invalid API Key" Errors
- Double-check you copied the entire key
- Ensure there are no extra spaces or quotes
- Verify the key hasn't been revoked

### Database Connection Issues
- Confirm your Supabase project is active
- Check if the service role key matches your project
- Verify the Supabase URL is correct

### AI Generation Not Working
- Check OpenAI API key is valid
- Verify you have credits in your OpenAI account
- Look for rate limit errors in the console

## Demo Mode Alternative

If you want to explore the UI without setting up services:
1. Set `NEXT_PUBLIC_DEMO_MODE=true` in `.env.local`
2. This will use mock data but disable all API functionality