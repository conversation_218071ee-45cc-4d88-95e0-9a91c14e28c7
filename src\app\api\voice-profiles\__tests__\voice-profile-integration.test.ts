import { describe, it, expect, beforeEach, vi } from 'vitest';
import { NextRequest } from 'next/server';
import * as profileRoute from '../route';
import * as trainRoute from '../[id]/train/route';
import * as consistencyRoute from '../../analysis/voice-consistency/route';
import * as projectVoiceRoute from '../../projects/[id]/voice-profile/route';

// Mock Supabase client
vi.mock('@/lib/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null }),
    })),
  },
}));

// Mock auth helpers
vi.mock('@supabase/auth-helpers-nextjs', () => ({
  createRouteHandlerClient: vi.fn(() => ({
    auth: {
      getUser: vi.fn().mockResolvedValue({
        data: { user: { id: 'test-user-id' } },
        error: null,
      }),
    },
  })),
}));

// Mock VoiceProfileManager
vi.mock('@/lib/services/voice-profile-manager', () => ({
  VoiceProfileManager: vi.fn().mockImplementation(() => ({
    createVoiceProfile: vi.fn().mockResolvedValue({
      id: 'test-profile-id',
      name: 'Test Profile',
      type: 'author',
      confidence: 0,
    }),
    trainVoiceProfile: vi.fn().mockResolvedValue(true),
    analyzeVoiceConsistency: vi.fn().mockResolvedValue({
      score: 0.85,
      suggestions: [],
      deviations: [],
    }),
    getUserVoiceProfiles: vi.fn().mockResolvedValue([
      {
        id: 'test-profile-id',
        name: 'Test Profile',
        type: 'author',
        confidence: 0.75,
      },
    ]),
  })),
}));

describe('Voice Profile API Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Voice Profile CRUD', () => {
    it('should create a voice profile', async () => {
      const request = new NextRequest('http://localhost/api/voice-profiles', {
        method: 'POST',
        body: JSON.stringify({
          name: 'My Writing Voice',
          description: 'Test voice profile',
          type: 'author',
          projectId: 'test-project-id',
        }),
      });

      const response = await profileRoute.POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.profile).toMatchObject({
        id: 'test-profile-id',
        name: 'Test Profile',
        type: 'author',
      });
    });

    it('should fetch user voice profiles', async () => {
      const request = new NextRequest('http://localhost/api/voice-profiles?type=author');
      
      const response = await profileRoute.GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.profiles).toHaveLength(1);
      expect(data.profiles[0]).toMatchObject({
        id: 'test-profile-id',
        name: 'Test Profile',
        type: 'author',
      });
    });
  });

  describe('Voice Profile Training', () => {
    it('should train a voice profile with text samples', async () => {
      const request = new NextRequest('http://localhost/api/voice-profiles/test-profile-id/train', {
        method: 'POST',
        body: JSON.stringify({
          texts: [
            'This is a sample of my writing style. I tend to use descriptive language.',
            'Another example of how I write. My sentences vary in length and complexity.',
          ],
          source: 'manual_entry',
        }),
      });

      const params = Promise.resolve({ id: 'test-profile-id' });
      const response = await trainRoute.POST(request, { params });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.trainedSamples).toBe(2);
    });
  });

  describe('Voice Consistency Analysis', () => {
    it('should analyze voice consistency', async () => {
      const request = new NextRequest('http://localhost/api/analysis/voice-consistency', {
        method: 'POST',
        body: JSON.stringify({
          content: 'This is some content to analyze for voice consistency.',
          profileId: 'test-profile-id',
          projectId: 'test-project-id',
        }),
      });

      const response = await consistencyRoute.POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.consistencyScore).toBe(0.85);
      expect(data.suggestions).toEqual([]);
    });
  });

  describe('Project Voice Profile', () => {
    it('should assign a voice profile to a project', async () => {
      const request = new NextRequest('http://localhost/api/projects/test-project-id/voice-profile', {
        method: 'POST',
        body: JSON.stringify({
          voiceProfileId: 'test-profile-id',
        }),
      });

      const params = Promise.resolve({ id: 'test-project-id' });
      const response = await projectVoiceRoute.POST(request, { params });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.profile).toBeDefined();
    });

    it('should fetch project voice profile', async () => {
      const request = new NextRequest('http://localhost/api/projects/test-project-id/voice-profile');
      
      const params = Promise.resolve({ id: 'test-project-id' });
      const response = await projectVoiceRoute.GET(request, { params });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.profile).toBeDefined();
    });
  });
});