import { format } from 'date-fns';

interface AnalyticsData {
  user: {
    id: string;
    name: string;
  };
  dateRange: {
    from: string;
    to: string;
  };
  overview: {
    totalWords: number;
    currentStreak: number;
    avgDailyWords: number;
    activeProjects: number;
  };
  activity: {
    dailyWordCount: Array<{ date: string; value: number }>;
  };
  goals: {
    activeGoals: Array<{
      title: string;
      current: number;
      target: number;
      progress: number;
      unit: string;
    }>;
  };
  projects: Array<{
    title: string;
    primary_genre: string;
    current_word_count: number;
    target_word_count: number;
  }>;
}

export async function generateCSVData(data: AnalyticsData): Promise<string> {
  const lines: string[] = [];
  
  // Header
  lines.push('BookScribe Analytics Report');
  lines.push(`User: ${data.user.name}`);
  lines.push(`Period: ${format(new Date(data.dateRange.from), 'MMM d, yyyy')} - ${format(new Date(data.dateRange.to), 'MMM d, yyyy')}`);
  lines.push('');
  
  // Overview Section
  lines.push('OVERVIEW');
  lines.push('Metric,Value');
  lines.push(`Total Words Written,${data.overview.totalWords}`);
  lines.push(`Current Writing Streak,${data.overview.currentStreak} days`);
  lines.push(`Average Daily Words,${data.overview.avgDailyWords}`);
  lines.push(`Active Projects,${data.overview.activeProjects}`);
  lines.push('');
  
  // Daily Word Count
  lines.push('DAILY WORD COUNT');
  lines.push('Date,Words Written');
  data.activity.dailyWordCount.forEach(day => {
    lines.push(`${day.date},${day.value}`);
  });
  lines.push('');
  
  // Projects
  lines.push('PROJECTS');
  lines.push('Title,Genre,Current Words,Target Words,Progress %');
  data.projects.forEach(project => {
    const progress = ((project.current_word_count / project.target_word_count) * 100).toFixed(1);
    lines.push(`"${project.title}",${project.primary_genre},${project.current_word_count},${project.target_word_count},${progress}`);
  });
  lines.push('');
  
  // Goals
  lines.push('ACTIVE GOALS');
  lines.push('Goal,Current,Target,Unit,Progress %');
  data.goals.activeGoals.forEach(goal => {
    lines.push(`"${goal.title}",${goal.current},${goal.target},${goal.unit},${goal.progress}`);
  });
  lines.push('');
  
  // Footer
  lines.push(`Generated on ${format(new Date(), 'MMMM d, yyyy h:mm a')}`);
  
  return lines.join('\n');
}