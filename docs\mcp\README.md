# MCP (Model Context Protocol) Servers

This directory contains documentation for the MCP servers configured in BookScribe. MCP servers extend <PERSON>'s capabilities by providing access to external tools, resources, and APIs.

## Overview

Model Context Protocol (MCP) is an open standard for connecting AI assistants to external data sources and tools. It enables <PERSON> to interact with databases, APIs, file systems, and other services in a secure and controlled manner.

## Configured MCP Servers

BookScribe has the following MCP servers configured:

| Server | Purpose | Status | Documentation |
|--------|---------|---------|---------------|
| **Sentry** | Error monitoring and debugging | ✅ Active | [sentry.md](./sentry.md) |
| **Stripe** | Payment processing and billing | ✅ Active | [stripe.md](./stripe.md) |
| **Supabase** | Database operations and auth | ✅ Active | [supabase.md](./supabase.md) |
| **Playwright** | Browser automation and testing | ✅ Active | [playwright.md](./playwright.md) |
| **Context7** | Documentation and context management | ✅ Active | [context7.md](./context7.md) |

## Configuration Location

MCP servers are configured in:
```
~/.config/claude-desktop/config.json
```

## Quick Start

1. All MCP servers are pre-configured and ready to use
2. Some servers require environment variables (API keys, URLs)
3. See individual server documentation for specific setup requirements
4. Restart Claude Desktop after making configuration changes

## Common Use Cases

### Development & Debugging
- **Sentry**: Monitor application errors and performance
- **Playwright**: Automate browser testing and screenshots

### Data & Integration  
- **Supabase**: Query database, manage users and auth
- **Stripe**: Handle payments, subscriptions, and billing

### Documentation & Research
- **Context7**: Access library documentation and code examples

## Configuration Examples

See [configuration.md](./configuration.md) for detailed setup examples and troubleshooting.

## Security Notes

- MCP servers run with limited permissions
- API keys should be stored as environment variables
- Never commit sensitive credentials to version control
- Review server permissions before deployment

## Troubleshooting

Common issues and solutions are documented in [troubleshooting.md](./troubleshooting.md).