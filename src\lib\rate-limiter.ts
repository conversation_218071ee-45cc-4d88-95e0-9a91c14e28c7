import { LRUCache } from 'lru-cache'

type RateLimitOptions = {
  interval: number // Time window in milliseconds
  uniqueTokenPerInterval: number // Max unique tokens per interval
}

export class RateLimiter {
  private cache: LRUCache<string, number[]>
  private options: RateLimitOptions

  constructor(options: RateLimitOptions) {
    this.options = options
    this.cache = new LRUCache({
      max: options.uniqueTokenPerInterval,
      ttl: options.interval
    })
  }

  check(limit: number, token: string): { success: boolean; remaining: number; reset: number } {
    const now = Date.now()
    const tokenCount = this.cache.get(token) || []
    
    // Remove old entries outside the current window
    const validEntries = tokenCount.filter(timestamp => now - timestamp < this.options.interval)
    
    // Check if limit exceeded
    if (validEntries.length >= limit) {
      return {
        success: false,
        remaining: 0,
        reset: Math.min(...validEntries) + this.options.interval
      }
    }

    // Add current request
    validEntries.push(now)
    this.cache.set(token, validEntries)

    return {
      success: true,
      remaining: limit - validEntries.length,
      reset: now + this.options.interval
    }
  }
}

// Different rate limiters for different endpoints
export const authLimiter = new RateLimiter({
  interval: 15 * 60 * 1000, // 15 minutes
  uniqueTokenPerInterval: 100 // Reduced from 500
})

export const aiLimiter = new RateLimiter({
  interval: 60 * 60 * 1000, // 1 hour
  uniqueTokenPerInterval: 100 // Reduced from 500 - AI requests are expensive
})

export const generalLimiter = new RateLimiter({
  interval: 15 * 60 * 1000, // 15 minutes
  uniqueTokenPerInterval: 100 // Reduced from 500
})

// Global limiter for general API usage
export const globalLimiter = new RateLimiter({
  interval: 60 * 60 * 1000, // 1 hour
  uniqueTokenPerInterval: 300 // Reduced from 1000
})

// Admin limiter for administrative endpoints
export const adminLimiter = new RateLimiter({
  interval: 60 * 60 * 1000, // 1 hour
  uniqueTokenPerInterval: 50 // Reduced from 100
})

export function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    const firstIP = forwarded.split(',')[0];
    return firstIP ? firstIP.trim() : 'unknown';
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}

export function createRateLimitResponse(remaining: number, reset: number) {
  return new Response(
    JSON.stringify({
      error: 'Too many requests',
      message: 'You have exceeded the rate limit. Please try again later.'
    }),
    {
      status: 429,
      headers: {
        'Content-Type': 'application/json',
        'X-RateLimit-Remaining': remaining.toString(),
        'X-RateLimit-Reset': new Date(reset).toISOString(),
        'Retry-After': Math.round((reset - Date.now()) / 1000).toString()
      }
    }
  )
}