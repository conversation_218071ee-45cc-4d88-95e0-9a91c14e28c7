import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { logger } from '@/lib/services/logger';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      universe_id,
      event_name,
      description,
      event_date,
      relative_date,
      event_type,
      importance,
      affected_series
    } = body;

    if (!universe_id || !event_name) {
      return NextResponse.json({ 
        error: 'Universe ID and event name are required' 
      }, { status: 400 });
    }

    // Verify user owns this universe
    const { data: universe, error: universeError } = await supabase
      .from('universes')
      .select('created_by')
      .eq('id', universe_id)
      .single();

    if (universeError || !universe || universe.created_by !== user.user.id) {
      return NextResponse.json({ 
        error: 'Universe not found or unauthorized' 
      }, { status: 404 });
    }

    // Create the timeline event
    const { data: event, error } = await supabase
      .from('universe_timeline_events')
      .insert({
        universe_id,
        event_name,
        description,
        event_date,
        relative_date,
        event_type: event_type || 'historical',
        importance: importance || 'major',
        affected_series: affected_series || []
      })
      .select()
      .single();

    if (error) {
      logger.error('Error creating timeline event:', error);
      return NextResponse.json({ 
        error: 'Failed to create timeline event' 
      }, { status: 500 });
    }

    return NextResponse.json({ event });
  } catch (error) {
    logger.error('Error in POST /api/universes/timeline-events:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const universeId = searchParams.get('universe_id');

    if (!universeId) {
      return NextResponse.json({ 
        error: 'Universe ID is required' 
      }, { status: 400 });
    }

    // Verify user has access to this universe
    const { data: universe, error: universeError } = await supabase
      .from('universes')
      .select('created_by')
      .eq('id', universeId)
      .single();

    if (universeError) {
      return NextResponse.json({ 
        error: 'Universe not found' 
      }, { status: 404 });
    }

    // Check if user owns the universe or has a series in it
    const hasAccess = universe.created_by === user.user.id || 
      await checkUserHasSeriesInUniverse(supabase, user.user.id, universeId);

    if (!hasAccess) {
      return NextResponse.json({ 
        error: 'Unauthorized' 
      }, { status: 403 });
    }

    // Get timeline events
    const { data: events, error } = await supabase
      .from('universe_timeline_events')
      .select('*')
      .eq('universe_id', universeId)
      .order('event_date', { ascending: true, nullsFirst: false })
      .order('created_at', { ascending: true });

    if (error) {
      logger.error('Error fetching timeline events:', error);
      return NextResponse.json({ 
        error: 'Failed to fetch timeline events' 
      }, { status: 500 });
    }

    return NextResponse.json({ events });
  } catch (error) {
    logger.error('Error in GET /api/universes/timeline-events:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

async function checkUserHasSeriesInUniverse(
  supabase: any,
  userId: string,
  universeId: string
): Promise<boolean> {
  const { data } = await supabase
    .from('series')
    .select('id')
    .eq('user_id', userId)
    .eq('universe_id', universeId)
    .limit(1);

  return data && data.length > 0;
}