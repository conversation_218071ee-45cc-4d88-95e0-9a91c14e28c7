'use client'

import { useState, useEffect } from 'react'
import { X, AlertCircle, ExternalLink } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { isDemoMode } from '@/lib/config/client-config'

export function DemoModeBanner() {
  const [isVisible, setIsVisible] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)

  useEffect(() => {
    // Check if demo mode and not previously dismissed this session
    if (isDemoMode() && !sessionStorage.getItem('demo-banner-dismissed')) {
      setIsVisible(true)
    }
  }, [])

  const handleDismiss = () => {
    setIsDismissed(true)
    sessionStorage.setItem('demo-banner-dismissed', 'true')
    setTimeout(() => setIsVisible(false), 300)
  }

  if (!isVisible) return null

  return (
    <div 
      className={`fixed bottom-4 right-4 max-w-md z-50 transition-all duration-300 ${
        isDismissed ? 'opacity-0 translate-y-2' : 'opacity-100 translate-y-0'
      }`}
    >
      <div className="bg-accent border border-primary/30 rounded-lg shadow-lg p-4">
        <div className="flex items-start gap-3">
          <AlertCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
          <div className="flex-1">
            <h3 className="font-semibold text-foreground mb-1">
              Demo Mode Active
            </h3>
            <p className="text-sm text-muted-foreground mb-3">
              You&apos;re running BookScribe in demo mode. UI features are fully functional, 
              but API integrations (AI, database, payments) are disabled.
            </p>
            <div className="flex flex-col gap-2 text-sm">
              <a 
                href="https://github.com/BookScribe/BookScribe#setup"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-primary hover:text-primary/80 transition-colors"
              >
                View setup instructions
                <ExternalLink className="h-3 w-3" />
              </a>
              <code className="bg-muted px-2 py-1 rounded text-xs font-mono">
                cp .env.example .env.local
              </code>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="text-muted-foreground hover:text-foreground"
            aria-label="Dismiss demo mode banner"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}