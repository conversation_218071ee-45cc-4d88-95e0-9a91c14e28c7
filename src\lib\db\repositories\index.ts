import { SupabaseClient } from '@supabase/supabase-js'
import { ProjectRepository } from './project-repository'
import { ChapterRepository } from './chapter-repository'
import { CharacterRepository } from './character-repository'

export * from './base-repository'
export * from './project-repository'
export * from './chapter-repository'
export * from './character-repository'

/**
 * Create repository instances with a Supabase client
 */
export function createRepositories(supabase: SupabaseClient) {
  return {
    projects: new ProjectRepository(supabase),
    chapters: new ChapterRepository(supabase),
    characters: new CharacterRepository(supabase)
  }
}

/**
 * Repository factory for dependency injection
 */
export class RepositoryFactory {
  private static instances: Map<SupabaseClient, ReturnType<typeof createRepositories>> = new Map()

  static get(supabase: SupabaseClient) {
    if (!this.instances.has(supabase)) {
      this.instances.set(supabase, createRepositories(supabase))
    }
    return this.instances.get(supabase)!
  }

  static clear() {
    this.instances.clear()
  }
}